"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-error-boundary@4.1.2_react@19.1.0";
exports.ids = ["vendor-chunks/react-error-boundary@4.1.2_react@19.1.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/react-error-boundary@4.1.2_react@19.1.0/node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-error-boundary@4.1.2_react@19.1.0/node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   ErrorBoundaryContext: () => (/* binding */ ErrorBoundaryContext),\n/* harmony export */   useErrorBoundary: () => (/* binding */ useErrorBoundary),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,ErrorBoundaryContext,useErrorBoundary,withErrorBoundary auto */ \nconst ErrorBoundaryContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nconst initialState = {\n    didCatch: false,\n    error: null\n};\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    constructor(props){\n        super(props);\n        this.resetErrorBoundary = this.resetErrorBoundary.bind(this);\n        this.state = initialState;\n    }\n    static getDerivedStateFromError(error) {\n        return {\n            didCatch: true,\n            error\n        };\n    }\n    resetErrorBoundary() {\n        const { error } = this.state;\n        if (error !== null) {\n            var _this$props$onReset, _this$props;\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            (_this$props$onReset = (_this$props = this.props).onReset) === null || _this$props$onReset === void 0 ? void 0 : _this$props$onReset.call(_this$props, {\n                args,\n                reason: \"imperative-api\"\n            });\n            this.setState(initialState);\n        }\n    }\n    componentDidCatch(error, info) {\n        var _this$props$onError, _this$props2;\n        (_this$props$onError = (_this$props2 = this.props).onError) === null || _this$props$onError === void 0 ? void 0 : _this$props$onError.call(_this$props2, error, info);\n    }\n    componentDidUpdate(prevProps, prevState) {\n        const { didCatch } = this.state;\n        const { resetKeys } = this.props;\n        // There's an edge case where if the thing that triggered the error happens to *also* be in the resetKeys array,\n        // we'd end up resetting the error boundary immediately.\n        // This would likely trigger a second error to be thrown.\n        // So we make sure that we don't check the resetKeys on the first call of cDU after the error is set.\n        if (didCatch && prevState.error !== null && hasArrayChanged(prevProps.resetKeys, resetKeys)) {\n            var _this$props$onReset2, _this$props3;\n            (_this$props$onReset2 = (_this$props3 = this.props).onReset) === null || _this$props$onReset2 === void 0 ? void 0 : _this$props$onReset2.call(_this$props3, {\n                next: resetKeys,\n                prev: prevProps.resetKeys,\n                reason: \"keys\"\n            });\n            this.setState(initialState);\n        }\n    }\n    render() {\n        const { children, fallbackRender, FallbackComponent, fallback } = this.props;\n        const { didCatch, error } = this.state;\n        let childToRender = children;\n        if (didCatch) {\n            const props = {\n                error,\n                resetErrorBoundary: this.resetErrorBoundary\n            };\n            if (typeof fallbackRender === \"function\") {\n                childToRender = fallbackRender(props);\n            } else if (FallbackComponent) {\n                childToRender = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(FallbackComponent, props);\n            } else if (fallback !== undefined) {\n                childToRender = fallback;\n            } else {\n                {\n                    console.error(\"react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop\");\n                }\n                throw error;\n            }\n        }\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ErrorBoundaryContext.Provider, {\n            value: {\n                didCatch,\n                error,\n                resetErrorBoundary: this.resetErrorBoundary\n            }\n        }, childToRender);\n    }\n}\nfunction hasArrayChanged() {\n    let a = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    let b = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    return a.length !== b.length || a.some((item, index)=>!Object.is(item, b[index]));\n}\nfunction assertErrorBoundaryContext(value) {\n    if (value == null || typeof value.didCatch !== \"boolean\" || typeof value.resetErrorBoundary !== \"function\") {\n        throw new Error(\"ErrorBoundaryContext not found\");\n    }\n}\nfunction useErrorBoundary() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ErrorBoundaryContext);\n    assertErrorBoundaryContext(context);\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        error: null,\n        hasError: false\n    });\n    const memoized = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useErrorBoundary.useMemo[memoized]\": ()=>({\n                resetBoundary: ({\n                    \"useErrorBoundary.useMemo[memoized]\": ()=>{\n                        context.resetErrorBoundary();\n                        setState({\n                            error: null,\n                            hasError: false\n                        });\n                    }\n                })[\"useErrorBoundary.useMemo[memoized]\"],\n                showBoundary: ({\n                    \"useErrorBoundary.useMemo[memoized]\": (error)=>setState({\n                            error,\n                            hasError: true\n                        })\n                })[\"useErrorBoundary.useMemo[memoized]\"]\n            })\n    }[\"useErrorBoundary.useMemo[memoized]\"], [\n        context.resetErrorBoundary\n    ]);\n    if (state.hasError) {\n        throw state.error;\n    }\n    return memoized;\n}\nfunction withErrorBoundary(component, errorBoundaryProps) {\n    const Wrapped = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref)=>/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ErrorBoundary, errorBoundaryProps, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(component, {\n            ...props,\n            ref\n        })));\n    // Format for display in DevTools\n    const name = component.displayName || component.name || \"Unknown\";\n    Wrapped.displayName = \"withErrorBoundary(\".concat(name, \")\");\n    return Wrapped;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/react-error-boundary@4.1.2_react@19.1.0/node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js\n");

/***/ })

};
;