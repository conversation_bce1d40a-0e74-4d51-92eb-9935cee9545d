# Phase 10: Institute Management

Comprehensive institute management system for Super Admin panel with institute creation, admin assignment, and dashboard activities.

## 📋 **Overview**

Phase 10 implements complete institute management functionality including:
- Institute CRUD operations with auto-slug generation
- Institute admin creation and assignment
- Custom domain management with verification
- Dashboard activities for both Super Admin and Institute Admin
- Soft delete functionality
- Address management with location integration

## 🗄️ **Database Schema**

### **Database Table Structure**

```sql
-- Institutes Table
CREATE TABLE institutes (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(255) UNIQUE NOT NULL,
  email VARCHAR(255),
  phone VARCHAR(50),
  website VARCHAR(255),
  tagline TEXT,
  logo_id UUID REFERENCES media(id),
  address_street TEXT,
  city_id VARCHAR(255), -- Reference to location management
  state_id VARCHAR(255), -- Reference to location management
  country_id VARCHAR(255), -- Reference to location management
  district_id VARCHAR(255), -- Reference to location management
  zip_code VARCHAR(20),
  custom_domain VARCHAR(255),
  domain_verified BOOLEAN DEFAULT FALSE,
  is_active BOOLEAN DEFAULT TRUE,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW(),
  deleted_at TIMESTAMP NULL -- For soft delete
);

-- Indexes for performance
CREATE INDEX idx_institutes_slug ON institutes(slug);
CREATE INDEX idx_institutes_active ON institutes(is_active);
CREATE INDEX idx_institutes_domain_verified ON institutes(domain_verified);
CREATE INDEX idx_institutes_deleted_at ON institutes(deleted_at);
CREATE INDEX idx_institutes_country_id ON institutes(country_id);
CREATE INDEX idx_institutes_state_id ON institutes(state_id);
CREATE INDEX idx_institutes_city_id ON institutes(city_id);
```

### **Institutes Collection**

```typescript
// apps/api/src/collections/Institutes.ts
import { CollectionConfig } from 'payload/types'

const Institutes: CollectionConfig = {
  slug: 'institutes',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'email', 'isActive', 'domainVerified', 'createdAt'],
  },
  access: {
    read: () => true,
    create: ({ req: { user } }) => user?.legacyRole === 'super_admin',
    update: ({ req: { user } }) => user?.legacyRole === 'super_admin' || user?.legacyRole === 'institute_admin',
    delete: ({ req: { user } }) => user?.legacyRole === 'super_admin',
  },
  fields: [
    // UUID Primary Key
    {
      name: 'id',
      type: 'text',
      defaultValue: () => {
        return require('crypto').randomUUID()
      },
      admin: {
        hidden: true,
      },
    },

    // Basic Information
    {
      name: 'name',
      type: 'text',
      required: true,
      label: 'Institute Name',
    },
    {
      name: 'slug',
      type: 'text',
      required: true,
      unique: true,
      label: 'Slug',
      admin: {
        readOnly: true,
      },
      hooks: {
        beforeValidate: [
          ({ data }) => {
            if (data?.name && !data?.slug) {
              data.slug = data.name
                .toLowerCase()
                .replace(/[^a-z0-9]+/g, '-')
                .replace(/(^-|-$)/g, '')
            }
          },
        ],
      },
    },
    {
      name: 'email',
      type: 'email',
      label: 'Contact Email',
    },
    {
      name: 'phone',
      type: 'text',
      label: 'Contact Phone',
    },
    {
      name: 'website',
      type: 'text',
      label: 'Website URL',
    },
    {
      name: 'tagline',
      type: 'text',
      label: 'Tagline',
    },
    {
      name: 'logo',
      type: 'upload',
      relationTo: 'media',
      label: 'Institute Logo',
    },

    // Address Information with Location References
    {
      name: 'addressStreet',
      type: 'text',
      label: 'Street Address',
    },
    {
      name: 'cityId',
      type: 'text',
      label: 'City ID',
      admin: {
        description: 'Reference to city in location management system',
      },
    },
    {
      name: 'stateId',
      type: 'text',
      label: 'State ID',
      admin: {
        description: 'Reference to state in location management system',
      },
    },
    {
      name: 'countryId',
      type: 'text',
      label: 'Country ID',
      admin: {
        description: 'Reference to country in location management system',
      },
    },
    {
      name: 'districtId',
      type: 'text',
      label: 'District ID',
      admin: {
        description: 'Reference to district in location management system',
      },
    },
    {
      name: 'zipCode',
      type: 'text',
      label: 'ZIP/Postal Code',
    },

    // Domain Management
    {
      name: 'customDomain',
      type: 'text',
      label: 'Custom Domain',
    },
    {
      name: 'domainVerified',
      type: 'checkbox',
      label: 'Domain Verified',
      defaultValue: false,
    },

    // Status
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Active',
      defaultValue: true,
    },

    // Soft Delete
    {
      name: 'deletedAt',
      type: 'date',
      admin: {
        hidden: true,
      },
    },
  ],
  timestamps: true,
}

export default Institutes
```

## 🔌 **API Endpoints**

### **Institute Management Endpoints**

```typescript
// apps/api/src/endpoints/institute-management.ts
import { Endpoint } from 'payload/config'
import { requireAuth, requireSuperAdmin } from '../middleware/auth'

// Helper function for authenticated endpoints
const createAuthenticatedEndpoint = (
  path: string,
  method: 'get' | 'post' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>,
  allowedRoles?: string[]
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      const authMiddleware = allowedRoles ? requireAuth(allowedRoles) : requireAuth()
      const authResult = await authMiddleware(req)
      
      if (authResult) {
        return authResult
      }
      
      return handler(req)
    }
  }
}

// Get institutes with filtering and pagination
export const getInstitutesEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes',
  'get',
  async (req: any) => {
    try {
      const { searchParams } = new URL(req.url!)
      const search = searchParams.get('search') || ''
      const isActive = searchParams.get('isActive') || 'true'
      const domainVerified = searchParams.get('domainVerified')
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')

      const where: any = {
        deletedAt: { exists: false } // Exclude soft deleted
      }

      // Active filter
      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      // Domain verified filter
      if (domainVerified !== null && domainVerified !== undefined) {
        where.domainVerified = { equals: domainVerified === 'true' }
      }

      // Search filter
      if (search) {
        where.or = [
          { name: { contains: search } },
          { email: { contains: search } },
          { slug: { contains: search } },
          { website: { contains: search } }
        ]
      }

      const institutes = await req.payload.find({
        collection: 'institutes',
        where,
        page,
        limit,
        sort: '-createdAt',
        populate: ['logo']
      })

      return Response.json({
        success: true,
        docs: institutes.docs,
        page: institutes.page,
        limit: institutes.limit,
        totalPages: institutes.totalPages,
        totalDocs: institutes.totalDocs,
        hasNextPage: institutes.hasNextPage,
        hasPrevPage: institutes.hasPrevPage
      })

    } catch (error) {
      console.error('Institutes fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
  ['super_admin'] // Only super admin can view all institutes
)

// Create institute with admin user
export const createInstituteEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes',
  'post',
  async (req: any) => {
    try {
      const {
        // Institute data
        name,
        email,
        phone,
        website,
        tagline,
        logo,
        addressStreet,
        cityId,
        stateId,
        countryId,
        districtId,
        zipCode,
        customDomain,
        // Admin user data
        adminFirstName,
        adminLastName,
        adminEmail,
        adminPassword
      } = await req.json()

      // Create institute
      const institute = await req.payload.create({
        collection: 'institutes',
        data: {
          name,
          email,
          phone,
          website,
          tagline,
          logo,
          addressStreet,
          cityId,
          stateId,
          countryId,
          districtId,
          zipCode,
          customDomain,
          domainVerified: false,
          isActive: true
        }
      })

      // Create institute admin user
      const adminUser = await req.payload.create({
        collection: 'users',
        data: {
          firstName: adminFirstName,
          lastName: adminLastName,
          email: adminEmail,
          password: adminPassword,
          legacyRole: 'institute_admin',
          institute: institute.id,
          isActive: true
        }
      })

      return Response.json({
        success: true,
        institute,
        adminUser: {
          id: adminUser.id,
          email: adminUser.email,
          firstName: adminUser.firstName,
          lastName: adminUser.lastName
        },
        message: 'Institute and admin user created successfully'
      })

    } catch (error) {
      console.error('Create institute error:', error)
      return Response.json(
        { success: false, error: error.message || 'Failed to create institute' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)

// Update institute
export const updateInstituteEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes/:id',
  'put',
  async (req: any) => {
    try {
      const { id } = req.params
      const updateData = await req.json()

      // Remove fields that shouldn't be updated directly
      delete updateData.slug // Slug is auto-generated
      delete updateData.createdAt
      delete updateData.deletedAt

      const institute = await req.payload.update({
        collection: 'institutes',
        id,
        data: updateData
      })

      return Response.json({
        success: true,
        institute,
        message: 'Institute updated successfully'
      })

    } catch (error) {
      console.error('Update institute error:', error)
      return Response.json(
        { success: false, error: error.message || 'Failed to update institute' },
        { status: 500 }
      )
    }
  },
  ['super_admin', 'institute_admin']
)

// Soft delete institute
export const deleteInstituteEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes/:id',
  'delete',
  async (req: any) => {
    try {
      const { id } = req.params

      // Soft delete by setting deletedAt timestamp
      const institute = await req.payload.update({
        collection: 'institutes',
        id,
        data: {
          deletedAt: new Date(),
          isActive: false
        }
      })

      return Response.json({
        success: true,
        message: 'Institute deleted successfully'
      })

    } catch (error) {
      console.error('Delete institute error:', error)
      return Response.json(
        { success: false, error: error.message || 'Failed to delete institute' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)

// Get institute statistics
export const getInstituteStatisticsEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/statistics',
  'get',
  async (req: any) => {
    try {
      // Total institutes
      const totalInstitutes = await req.payload.find({
        collection: 'institutes',
        where: { deletedAt: { exists: false } },
        limit: 0
      })

      // Active institutes
      const activeInstitutes = await req.payload.find({
        collection: 'institutes',
        where: {
          and: [
            { deletedAt: { exists: false } },
            { isActive: { equals: true } }
          ]
        },
        limit: 0
      })

      // Verified domains
      const verifiedDomains = await req.payload.find({
        collection: 'institutes',
        where: {
          and: [
            { deletedAt: { exists: false } },
            { domainVerified: { equals: true } }
          ]
        },
        limit: 0
      })

      // Recent institutes (last 30 days)
      const thirtyDaysAgo = new Date()
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

      const recentInstitutes = await req.payload.find({
        collection: 'institutes',
        where: {
          and: [
            { deletedAt: { exists: false } },
            { createdAt: { greater_than: thirtyDaysAgo } }
          ]
        },
        limit: 0
      })

      return Response.json({
        success: true,
        data: {
          total: totalInstitutes.totalDocs,
          active: activeInstitutes.totalDocs,
          inactive: totalInstitutes.totalDocs - activeInstitutes.totalDocs,
          verifiedDomains: verifiedDomains.totalDocs,
          recentlyCreated: recentInstitutes.totalDocs
        }
      })

    } catch (error) {
      console.error('Get institute statistics error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)

// Verify custom domain
export const verifyDomainEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/institute-management/institutes/:id/verify-domain',
  'post',
  async (req: any) => {
    try {
      const { id } = req.params

      // Get institute
      const institute = await req.payload.findByID({
        collection: 'institutes',
        id
      })

      if (!institute || !institute.customDomain) {
        return Response.json({
          success: false,
          error: 'Institute or custom domain not found'
        }, { status: 404 })
      }

      // TODO: Implement actual domain verification logic
      // For now, just mark as verified
      const updatedInstitute = await req.payload.update({
        collection: 'institutes',
        id,
        data: {
          domainVerified: true
        }
      })

      return Response.json({
        success: true,
        institute: updatedInstitute,
        message: 'Domain verified successfully'
      })

    } catch (error) {
      console.error('Verify domain error:', error)
      return Response.json(
        { success: false, error: 'Failed to verify domain' },
        { status: 500 }
      )
    }
  },
  ['super_admin']
)
```

## 🎨 **Frontend Implementation**

### **Zustand Store**

```typescript
// apps/frontend/src/stores/super-admin/useInstituteManagementStore.ts
import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

// Types
export interface Institute {
  id: string // UUID
  name: string
  slug: string
  email?: string
  phone?: string
  website?: string
  tagline?: string
  logo?: any
  addressStreet?: string
  cityId?: string
  stateId?: string
  countryId?: string
  districtId?: string
  zipCode?: string
  customDomain?: string
  domainVerified: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  deletedAt?: string
}

interface InstituteFormData {
  // Institute data
  name: string
  email?: string
  phone?: string
  website?: string
  tagline?: string
  logo?: string
  addressStreet?: string
  cityId?: string
  stateId?: string
  countryId?: string
  districtId?: string
  zipCode?: string
  customDomain?: string
  // Admin user data
  adminFirstName: string
  adminLastName: string
  adminEmail: string
  adminPassword: string
}

interface Pagination {
  page: number
  limit: number
  totalDocs: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface Filters {
  search: string
  isActive?: boolean
  domainVerified?: boolean
}

interface Statistics {
  total: number
  active: number
  inactive: number
  verifiedDomains: number
  recentlyCreated: number
}

interface InstituteManagementStore {
  // State
  institutes: Institute[]
  selectedInstitute: Institute | null
  isLoading: boolean
  error: string | null
  pagination: Pagination
  filters: Filters
  statistics: Statistics | null

  // UI State
  showCreateForm: boolean
  showEditForm: boolean
  viewMode: 'list' | 'cards'

  // Actions
  setSelectedInstitute: (institute: Institute | null) => void
  setFilters: (filters: Partial<Filters>) => void
  setViewMode: (mode: 'list' | 'cards') => void
  setShowCreateForm: (show: boolean) => void
  setShowEditForm: (show: boolean) => void
  clearError: () => void

  // Data Actions
  fetchInstitutes: (page?: number, filters?: Partial<Filters>) => Promise<void>
  fetchStatistics: () => Promise<void>
  createInstitute: (data: InstituteFormData) => Promise<boolean>
  updateInstitute: (id: string, data: Partial<Institute>) => Promise<boolean>
  deleteInstitute: (id: string) => Promise<boolean>
  verifyDomain: (id: string) => Promise<boolean>
}

export const useInstituteManagementStore = create<InstituteManagementStore>()(
  devtools(
    (set, get) => ({
      // Initial State
      institutes: [],
      selectedInstitute: null,
      isLoading: false,
      error: null,
      pagination: {
        page: 1,
        limit: 20,
        totalDocs: 0,
        totalPages: 0,
        hasNextPage: false,
        hasPrevPage: false
      },
      filters: {
        search: '',
        isActive: true,
        domainVerified: undefined
      },
      statistics: null,
      showCreateForm: false,
      showEditForm: false,
      viewMode: 'list',

      // UI Actions
      setSelectedInstitute: (institute) => set({ selectedInstitute: institute }),
      setFilters: (newFilters) => set(state => ({
        filters: { ...state.filters, ...newFilters }
      })),
      setViewMode: (mode) => set({ viewMode: mode }),
      setShowCreateForm: (show) => set({ showCreateForm: show }),
      setShowEditForm: (show) => set({ showEditForm: show }),
      clearError: () => set({ error: null }),

      // Fetch Institutes
      fetchInstitutes: async (page = 1, filters) => {
        set({ isLoading: true, error: null })
        try {
          const currentFilters = filters || get().filters
          const queryParams = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            ...(currentFilters.search && { search: currentFilters.search }),
            ...(currentFilters.isActive !== undefined && {
              isActive: currentFilters.isActive.toString()
            }),
            ...(currentFilters.domainVerified !== undefined && {
              domainVerified: currentFilters.domainVerified.toString()
            }),
          })

          const response = await api.get(`/api/institute-management/institutes?${queryParams}`)
          const data = response.data

          if (data.success) {
            set({
              institutes: data.docs,
              pagination: {
                page: data.page,
                limit: data.limit,
                totalDocs: data.totalDocs,
                totalPages: data.totalPages,
                hasNextPage: data.hasNextPage,
                hasPrevPage: data.hasPrevPage,
              },
              isLoading: false,
            })
          } else {
            throw new Error(data.message || 'Failed to fetch institutes')
          }
        } catch (error: any) {
          console.error('Error fetching institutes:', error)
          set({
            error: error.message || 'Failed to fetch institutes',
            isLoading: false
          })
          toast.error('Failed to fetch institutes')
        }
      },

      // Fetch Statistics
      fetchStatistics: async () => {
        try {
          const response = await api.get('/api/institute-management/statistics')
          const data = response.data

          if (data.success) {
            set({ statistics: data.data })
          }
        } catch (error: any) {
          console.error('Error fetching statistics:', error)
        }
      },

      // Create Institute
      createInstitute: async (instituteData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.post('/api/institute-management/institutes', instituteData)
          const data = response.data

          if (data.success) {
            await get().fetchInstitutes()
            await get().fetchStatistics()
            set({ showCreateForm: false })
            toast.success('Institute created successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to create institute')
          }
        } catch (error: any) {
          console.error('Error creating institute:', error)
          set({
            error: error.message || 'Failed to create institute',
            isLoading: false
          })
          toast.error('Failed to create institute')
          return false
        }
      },

      // Update Institute
      updateInstitute: async (id, updateData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.put(`/api/institute-management/institutes/${id}`, updateData)
          const data = response.data

          if (data.success) {
            await get().fetchInstitutes()
            await get().fetchStatistics()
            set({ showEditForm: false, selectedInstitute: null })
            toast.success('Institute updated successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to update institute')
          }
        } catch (error: any) {
          console.error('Error updating institute:', error)
          set({
            error: error.message || 'Failed to update institute',
            isLoading: false
          })
          toast.error('Failed to update institute')
          return false
        }
      },

      // Delete Institute
      deleteInstitute: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.delete(`/api/institute-management/institutes/${id}`)
          const data = response.data

          if (data.success) {
            await get().fetchInstitutes()
            await get().fetchStatistics()
            toast.success('Institute deleted successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to delete institute')
          }
        } catch (error: any) {
          console.error('Error deleting institute:', error)
          set({
            error: error.message || 'Failed to delete institute',
            isLoading: false
          })
          toast.error('Failed to delete institute')
          return false
        }
      },

      // Verify Domain
      verifyDomain: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.post(`/api/institute-management/institutes/${id}/verify-domain`)
          const data = response.data

          if (data.success) {
            await get().fetchInstitutes()
            toast.success('Domain verified successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to verify domain')
          }
        } catch (error: any) {
          console.error('Error verifying domain:', error)
          set({
            error: error.message || 'Failed to verify domain',
            isLoading: false
          })
          toast.error('Failed to verify domain')
          return false
        }
      },
    }),
    {
      name: 'institute-management-store',
    }
  )
)
```

### **React Components**

#### **Institute Management Page**

```typescript
// apps/frontend/src/app/super-admin/institute-management/page.tsx
'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useInstituteManagementStore } from '@/stores/super-admin/useInstituteManagementStore'
import { InstituteFilters } from '@/components/institute-management/InstituteFilters'
import { InstitutesList } from '@/components/institute-management/InstitutesList'
import { InstituteCards } from '@/components/institute-management/InstituteCards'
import { InstituteForm } from '@/components/institute-management/InstituteForm'
import { InstituteStatistics } from '@/components/institute-management/InstituteStatistics'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import {
  Building2,
  Plus,
  List,
  Grid3X3,
  Download,
  Upload,
  Settings,
  AlertTriangle,
  Loader2
} from 'lucide-react'

export default function InstituteManagementPage() {
  const router = useRouter()
  const [authChecked, setAuthChecked] = useState(false)

  const { user, isAuthenticated, isLoading, initialize } = useAuthStore()
  const {
    institutes,
    isLoading: storeLoading,
    error,
    viewMode,
    showCreateForm,
    statistics,
    fetchInstitutes,
    fetchStatistics,
    setViewMode,
    setShowCreateForm,
    clearError
  } = useInstituteManagementStore()

  // Initialize auth
  useEffect(() => {
    const timer = setTimeout(() => {
      initialize()
    }, 100)
    return () => clearTimeout(timer)
  }, [initialize])

  // Check authentication
  useEffect(() => {
    if (!isLoading) {
      setAuthChecked(true)

      if (!isAuthenticated) {
        router.push('/auth/admin/login')
        return
      }

      if (!user || user.legacyRole !== 'super_admin') {
        router.push('/auth/admin/login')
        return
      }
    }
  }, [user, isAuthenticated, isLoading, router])

  // Load data
  useEffect(() => {
    if (authChecked && isAuthenticated && user) {
      fetchInstitutes()
      fetchStatistics()
    }
  }, [authChecked, isAuthenticated, user])

  if (isLoading || !authChecked) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!isAuthenticated || !user || user.legacyRole !== 'super_admin') {
    return null
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight flex items-center gap-2">
            <Building2 className="h-8 w-8" />
            Institute Management
          </h1>
          <p className="text-muted-foreground">
            Manage institutes, create admin accounts, and monitor activities
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setViewMode(viewMode === 'list' ? 'cards' : 'list')}
          >
            {viewMode === 'list' ? <Grid3X3 className="h-4 w-4" /> : <List className="h-4 w-4" />}
            {viewMode === 'list' ? 'Card View' : 'List View'}
          </Button>
          <Button onClick={() => setShowCreateForm(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Create Institute
          </Button>
        </div>
      </div>

      {/* Statistics */}
      {statistics && <InstituteStatistics statistics={statistics} />}

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="outline" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Filters */}
      <InstituteFilters />

      {/* Content */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Institutes ({institutes.length})</span>
            <div className="flex items-center gap-2">
              <Button variant="outline" size="sm">
                <Download className="h-4 w-4 mr-2" />
                Export
              </Button>
              <Button variant="outline" size="sm">
                <Upload className="h-4 w-4 mr-2" />
                Import
              </Button>
            </div>
          </CardTitle>
          <CardDescription>
            Manage institute accounts and their administrators
          </CardDescription>
        </CardHeader>
        <CardContent>
          {storeLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin mr-2" />
              Loading institutes...
            </div>
          ) : institutes.length === 0 ? (
            <div className="text-center py-8">
              <Building2 className="h-12 w-12 mx-auto text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">No institutes found</h3>
              <p className="text-gray-500 mb-4">Get started by creating your first institute.</p>
              <Button onClick={() => setShowCreateForm(true)}>
                <Plus className="h-4 w-4 mr-2" />
                Create Institute
              </Button>
            </div>
          ) : viewMode === 'list' ? (
            <InstitutesList institutes={institutes} />
          ) : (
            <InstituteCards institutes={institutes} />
          )}
        </CardContent>
      </Card>

      {/* Create Form Modal */}
      {showCreateForm && (
        <InstituteForm
          isOpen={showCreateForm}
          onClose={() => setShowCreateForm(false)}
          mode="create"
        />
      )}
    </div>
  )
}
```

#### **Institute Form Component**

```typescript
// apps/frontend/src/components/institute-management/InstituteForm.tsx
'use client'

import { useEffect } from 'react'
import { useFormik } from 'formik'
import * as Yup from 'yup'
import { useInstituteManagementStore, Institute } from '@/stores/super-admin/useInstituteManagementStore'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Separator } from '@/components/ui/separator'
import { Loader2, Building2, User, Mail, Lock, Globe, MapPin } from 'lucide-react'

interface InstituteFormProps {
  isOpen: boolean
  onClose: () => void
  mode: 'create' | 'edit'
  institute?: Institute
}

const validationSchema = Yup.object({
  // Institute fields
  name: Yup.string().required('Institute name is required'),
  email: Yup.string().email('Invalid email format'),
  phone: Yup.string(),
  website: Yup.string().url('Invalid URL format'),
  tagline: Yup.string(),
  customDomain: Yup.string(),

  // Address fields
  addressStreet: Yup.string(),
  cityId: Yup.string(),
  stateId: Yup.string(),
  countryId: Yup.string(),
  districtId: Yup.string(),
  zipCode: Yup.string(),

  // Admin fields (only for create mode)
  adminFirstName: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.required('Admin first name is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminLastName: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.required('Admin last name is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminEmail: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.email('Invalid email').required('Admin email is required'),
    otherwise: (schema) => schema.notRequired()
  }),
  adminPassword: Yup.string().when('$mode', {
    is: 'create',
    then: (schema) => schema.min(6, 'Password must be at least 6 characters').required('Admin password is required'),
    otherwise: (schema) => schema.notRequired()
  }),
})

export function InstituteForm({ isOpen, onClose, mode, institute }: InstituteFormProps) {
  const { createInstitute, updateInstitute, isLoading } = useInstituteManagementStore()

  const formik = useFormik({
    initialValues: {
      // Institute fields
      name: institute?.name || '',
      email: institute?.email || '',
      phone: institute?.phone || '',
      website: institute?.website || '',
      tagline: institute?.tagline || '',
      customDomain: institute?.customDomain || '',

      // Address fields
      addressStreet: institute?.addressStreet || '',
      cityId: institute?.cityId || '',
      stateId: institute?.stateId || '',
      countryId: institute?.countryId || '',
      districtId: institute?.districtId || '',
      zipCode: institute?.zipCode || '',

      // Admin fields (only for create)
      adminFirstName: '',
      adminLastName: '',
      adminEmail: '',
      adminPassword: '',
    },
    validationSchema,
    validationContext: { mode },
    onSubmit: async (values) => {
      let success = false

      if (mode === 'create') {
        success = await createInstitute(values)
      } else if (mode === 'edit' && institute) {
        const { adminFirstName, adminLastName, adminEmail, adminPassword, ...updateData } = values
        success = await updateInstitute(institute.id, updateData)
      }

      if (success) {
        onClose()
      }
    },
  })

  // Reset form when institute changes
  useEffect(() => {
    if (institute && mode === 'edit') {
      formik.setValues({
        name: institute.name || '',
        email: institute.email || '',
        phone: institute.phone || '',
        website: institute.website || '',
        tagline: institute.tagline || '',
        customDomain: institute.customDomain || '',
        addressStreet: institute.addressStreet || '',
        cityId: institute.cityId || '',
        stateId: institute.stateId || '',
        countryId: institute.countryId || '',
        districtId: institute.districtId || '',
        zipCode: institute.zipCode || '',
        adminFirstName: '',
        adminLastName: '',
        adminEmail: '',
        adminPassword: '',
      })
    }
  }, [institute, mode])

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            {mode === 'create' ? 'Create New Institute' : 'Edit Institute'}
          </DialogTitle>
          <DialogDescription>
            {mode === 'create'
              ? 'Create a new institute and assign an admin user'
              : 'Update institute information'
            }
          </DialogDescription>
        </DialogHeader>

        <form onSubmit={formik.handleSubmit} className="space-y-6">
          {/* Institute Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              <h3 className="text-lg font-medium">Institute Information</h3>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="name">Institute Name *</Label>
                <Input
                  id="name"
                  name="name"
                  value={formik.values.name}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.name && formik.errors.name}
                />
              </div>

              <div>
                <Label htmlFor="email">Contact Email</Label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formik.values.email}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.email && formik.errors.email}
                />
              </div>

              <div>
                <Label htmlFor="phone">Contact Phone</Label>
                <Input
                  id="phone"
                  name="phone"
                  value={formik.values.phone}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.phone && formik.errors.phone}
                />
              </div>

              <div>
                <Label htmlFor="website">Website URL</Label>
                <Input
                  id="website"
                  name="website"
                  value={formik.values.website}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.website && formik.errors.website}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="tagline">Tagline</Label>
              <Input
                id="tagline"
                name="tagline"
                value={formik.values.tagline}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.tagline && formik.errors.tagline}
              />
            </div>

            <div>
              <Label htmlFor="customDomain">Custom Domain</Label>
              <Input
                id="customDomain"
                name="customDomain"
                placeholder="example.com"
                value={formik.values.customDomain}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.customDomain && formik.errors.customDomain}
              />
            </div>
          </div>

          <Separator />

          {/* Address Information */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <MapPin className="h-4 w-4" />
              <h3 className="text-lg font-medium">Address Information</h3>
            </div>

            <div>
              <Label htmlFor="addressStreet">Street Address</Label>
              <Input
                id="addressStreet"
                name="addressStreet"
                value={formik.values.addressStreet}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.addressStreet && formik.errors.addressStreet}
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label htmlFor="cityId">City ID</Label>
                <Input
                  id="cityId"
                  name="cityId"
                  placeholder="City reference ID"
                  value={formik.values.cityId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.cityId && formik.errors.cityId}
                />
              </div>

              <div>
                <Label htmlFor="stateId">State ID</Label>
                <Input
                  id="stateId"
                  name="stateId"
                  placeholder="State reference ID"
                  value={formik.values.stateId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.stateId && formik.errors.stateId}
                />
              </div>

              <div>
                <Label htmlFor="countryId">Country ID</Label>
                <Input
                  id="countryId"
                  name="countryId"
                  placeholder="Country reference ID"
                  value={formik.values.countryId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.countryId && formik.errors.countryId}
                />
              </div>

              <div>
                <Label htmlFor="districtId">District ID</Label>
                <Input
                  id="districtId"
                  name="districtId"
                  placeholder="District reference ID"
                  value={formik.values.districtId}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.districtId && formik.errors.districtId}
                />
              </div>

              <div>
                <Label htmlFor="zipCode">ZIP/Postal Code</Label>
                <Input
                  id="zipCode"
                  name="zipCode"
                  value={formik.values.zipCode}
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                  error={formik.touched.zipCode && formik.errors.zipCode}
                />
              </div>
            </div>
          </div>

          {/* Admin User Information (Create mode only) */}
          {mode === 'create' && (
            <>
              <Separator />
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4" />
                  <h3 className="text-lg font-medium">Institute Admin User</h3>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="adminFirstName">First Name *</Label>
                    <Input
                      id="adminFirstName"
                      name="adminFirstName"
                      value={formik.values.adminFirstName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.adminFirstName && formik.errors.adminFirstName}
                    />
                  </div>

                  <div>
                    <Label htmlFor="adminLastName">Last Name *</Label>
                    <Input
                      id="adminLastName"
                      name="adminLastName"
                      value={formik.values.adminLastName}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.adminLastName && formik.errors.adminLastName}
                    />
                  </div>

                  <div>
                    <Label htmlFor="adminEmail">Email *</Label>
                    <Input
                      id="adminEmail"
                      name="adminEmail"
                      type="email"
                      value={formik.values.adminEmail}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.adminEmail && formik.errors.adminEmail}
                    />
                  </div>

                  <div>
                    <Label htmlFor="adminPassword">Password *</Label>
                    <Input
                      id="adminPassword"
                      name="adminPassword"
                      type="password"
                      value={formik.values.adminPassword}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.adminPassword && formik.errors.adminPassword}
                    />
                  </div>
                </div>
              </div>
            </>
          )}

          {/* Form Actions */}
          <div className="flex justify-end gap-2 pt-4">
            <Button type="button" variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
              {mode === 'create' ? 'Create Institute' : 'Update Institute'}
            </Button>
          </div>
        </form>
      </DialogContent>
    </Dialog>
  )
}
```

#### **Institute Statistics Component**

```typescript
// apps/frontend/src/components/institute-management/InstituteStatistics.tsx
'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Building2, CheckCircle, XCircle, Globe, TrendingUp } from 'lucide-react'

interface Statistics {
  total: number
  active: number
  inactive: number
  verifiedDomains: number
  recentlyCreated: number
}

interface InstituteStatisticsProps {
  statistics: Statistics
}

export function InstituteStatistics({ statistics }: InstituteStatisticsProps) {
  const stats = [
    {
      title: 'Total Institutes',
      value: statistics.total,
      icon: Building2,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100',
    },
    {
      title: 'Active Institutes',
      value: statistics.active,
      icon: CheckCircle,
      color: 'text-green-600',
      bgColor: 'bg-green-100',
    },
    {
      title: 'Inactive Institutes',
      value: statistics.inactive,
      icon: XCircle,
      color: 'text-red-600',
      bgColor: 'bg-red-100',
    },
    {
      title: 'Verified Domains',
      value: statistics.verifiedDomains,
      icon: Globe,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100',
    },
    {
      title: 'Recently Created',
      value: statistics.recentlyCreated,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100',
      subtitle: 'Last 30 days',
    },
  ]

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
      {stats.map((stat) => {
        const Icon = stat.icon
        return (
          <Card key={stat.title}>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-muted-foreground">
                    {stat.title}
                  </p>
                  <p className="text-2xl font-bold">{stat.value}</p>
                  {stat.subtitle && (
                    <p className="text-xs text-muted-foreground mt-1">
                      {stat.subtitle}
                    </p>
                  )}
                </div>
                <div className={`p-2 rounded-full ${stat.bgColor}`}>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </div>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
```

## 📊 **Dashboard Activities**

### **Super Admin Dashboard Activities**

```typescript
// apps/frontend/src/components/dashboard/super-admin/RecentActivities.tsx
'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Avatar, AvatarFallback } from '@/components/ui/avatar'
import {
  Building2,
  UserPlus,
  Settings,
  Globe,
  CheckCircle,
  Clock,
  MoreHorizontal
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface Activity {
  id: string
  type: 'institute_created' | 'admin_assigned' | 'domain_verified' | 'institute_updated'
  title: string
  description: string
  timestamp: string
  user?: {
    name: string
    email: string
  }
  institute?: {
    name: string
    slug: string
  }
  metadata?: Record<string, any>
}

export function RecentActivities() {
  const [activities, setActivities] = useState<Activity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // TODO: Fetch actual activities from API
    // For now, using mock data
    const mockActivities: Activity[] = [
      {
        id: '1',
        type: 'institute_created',
        title: 'New Institute Created',
        description: 'Tech Academy was created with admin user assigned',
        timestamp: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
        user: {
          name: 'Super Admin',
          email: '<EMAIL>'
        },
        institute: {
          name: 'Tech Academy',
          slug: 'tech-academy'
        }
      },
      {
        id: '2',
        type: 'domain_verified',
        title: 'Domain Verified',
        description: 'Custom domain techacademy.com was verified',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
        institute: {
          name: 'Tech Academy',
          slug: 'tech-academy'
        }
      },
      {
        id: '3',
        type: 'admin_assigned',
        title: 'Admin User Assigned',
        description: 'John Doe was assigned as institute admin',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 4).toISOString(), // 4 hours ago
        user: {
          name: 'John Doe',
          email: '<EMAIL>'
        },
        institute: {
          name: 'Tech Academy',
          slug: 'tech-academy'
        }
      },
      {
        id: '4',
        type: 'institute_updated',
        title: 'Institute Updated',
        description: 'Business School profile information was updated',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6).toISOString(), // 6 hours ago
        institute: {
          name: 'Business School',
          slug: 'business-school'
        }
      }
    ]

    setTimeout(() => {
      setActivities(mockActivities)
      setIsLoading(false)
    }, 1000)
  }, [])

  const getActivityIcon = (type: Activity['type']) => {
    switch (type) {
      case 'institute_created':
        return <Building2 className="h-4 w-4" />
      case 'admin_assigned':
        return <UserPlus className="h-4 w-4" />
      case 'domain_verified':
        return <Globe className="h-4 w-4" />
      case 'institute_updated':
        return <Settings className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getActivityColor = (type: Activity['type']) => {
    switch (type) {
      case 'institute_created':
        return 'text-blue-600 bg-blue-100'
      case 'admin_assigned':
        return 'text-green-600 bg-green-100'
      case 'domain_verified':
        return 'text-purple-600 bg-purple-100'
      case 'institute_updated':
        return 'text-orange-600 bg-orange-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Recent Activities</CardTitle>
        <Button variant="outline" size="sm">
          View All
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-4">
              <div className={`p-2 rounded-full ${getActivityColor(activity.type)}`}>
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                  </p>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {activity.description}
                </p>
                <div className="flex items-center gap-2 mt-2">
                  {activity.institute && (
                    <Badge variant="outline" className="text-xs">
                      {activity.institute.name}
                    </Badge>
                  )}
                  {activity.user && (
                    <Badge variant="secondary" className="text-xs">
                      {activity.user.name}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
```

### **Institute Admin Dashboard Activities**

```typescript
// apps/frontend/src/components/dashboard/institute-admin/RecentActivities.tsx
'use client'

import { useEffect, useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import {
  BookOpen,
  Users,
  GraduationCap,
  Settings,
  UserCheck,
  Clock
} from 'lucide-react'
import { formatDistanceToNow } from 'date-fns'

interface InstituteActivity {
  id: string
  type: 'course_created' | 'student_enrolled' | 'course_completed' | 'profile_updated' | 'user_registered'
  title: string
  description: string
  timestamp: string
  user?: {
    name: string
    email: string
  }
  course?: {
    name: string
    slug: string
  }
  metadata?: Record<string, any>
}

export function InstituteRecentActivities() {
  const [activities, setActivities] = useState<InstituteActivity[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // TODO: Fetch actual activities from API
    // For now, using mock data
    const mockActivities: InstituteActivity[] = [
      {
        id: '1',
        type: 'student_enrolled',
        title: 'New Student Enrollment',
        description: 'Sarah Johnson enrolled in React Development Course',
        timestamp: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
        user: {
          name: 'Sarah Johnson',
          email: '<EMAIL>'
        },
        course: {
          name: 'React Development Course',
          slug: 'react-development'
        }
      },
      {
        id: '2',
        type: 'course_created',
        title: 'New Course Created',
        description: 'Advanced JavaScript course was published',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 1).toISOString(), // 1 hour ago
        course: {
          name: 'Advanced JavaScript',
          slug: 'advanced-javascript'
        }
      },
      {
        id: '3',
        type: 'course_completed',
        title: 'Course Completed',
        description: 'Mike Chen completed HTML & CSS Fundamentals',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
        user: {
          name: 'Mike Chen',
          email: '<EMAIL>'
        },
        course: {
          name: 'HTML & CSS Fundamentals',
          slug: 'html-css-fundamentals'
        }
      },
      {
        id: '4',
        type: 'user_registered',
        title: 'New User Registration',
        description: 'Alex Smith registered as a student',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 5).toISOString(), // 5 hours ago
        user: {
          name: 'Alex Smith',
          email: '<EMAIL>'
        }
      }
    ]

    setTimeout(() => {
      setActivities(mockActivities)
      setIsLoading(false)
    }, 1000)
  }, [])

  const getActivityIcon = (type: InstituteActivity['type']) => {
    switch (type) {
      case 'course_created':
        return <BookOpen className="h-4 w-4" />
      case 'student_enrolled':
        return <UserCheck className="h-4 w-4" />
      case 'course_completed':
        return <GraduationCap className="h-4 w-4" />
      case 'profile_updated':
        return <Settings className="h-4 w-4" />
      case 'user_registered':
        return <Users className="h-4 w-4" />
      default:
        return <Clock className="h-4 w-4" />
    }
  }

  const getActivityColor = (type: InstituteActivity['type']) => {
    switch (type) {
      case 'course_created':
        return 'text-blue-600 bg-blue-100'
      case 'student_enrolled':
        return 'text-green-600 bg-green-100'
      case 'course_completed':
        return 'text-purple-600 bg-purple-100'
      case 'profile_updated':
        return 'text-orange-600 bg-orange-100'
      case 'user_registered':
        return 'text-indigo-600 bg-indigo-100'
      default:
        return 'text-gray-600 bg-gray-100'
    }
  }

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Recent Activities</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <CardTitle>Recent Activities</CardTitle>
        <Button variant="outline" size="sm">
          View All
        </Button>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {activities.map((activity) => (
            <div key={activity.id} className="flex items-start space-x-4">
              <div className={`p-2 rounded-full ${getActivityColor(activity.type)}`}>
                {getActivityIcon(activity.type)}
              </div>
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <p className="text-sm font-medium text-gray-900">
                    {activity.title}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatDistanceToNow(new Date(activity.timestamp), { addSuffix: true })}
                  </p>
                </div>
                <p className="text-sm text-gray-500 mt-1">
                  {activity.description}
                </p>
                <div className="flex items-center gap-2 mt-2">
                  {activity.course && (
                    <Badge variant="outline" className="text-xs">
                      {activity.course.name}
                    </Badge>
                  )}
                  {activity.user && (
                    <Badge variant="secondary" className="text-xs">
                      {activity.user.name}
                    </Badge>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
```

## 📁 **Folder Structure**

```
apps/
├── api/
│   ├── src/
│   │   ├── collections/
│   │   │   └── Institutes.ts
│   │   ├── endpoints/
│   │   │   ├── institute-management.ts
│   │   │   └── institute-management-index.ts
│   │   └── middleware/
│   │       └── auth.ts
└── frontend/
    └── src/
        ├── app/
        │   └── super-admin/
        │       └── institute-management/
        │           └── page.tsx
        ├── components/
        │   ├── institute-management/
        │   │   ├── InstituteForm.tsx
        │   │   ├── InstitutesList.tsx
        │   │   ├── InstituteCards.tsx
        │   │   ├── InstituteFilters.tsx
        │   │   ├── InstituteStatistics.tsx
        │   │   └── InstituteActions.tsx
        │   └── dashboard/
        │       ├── super-admin/
        │       │   └── RecentActivities.tsx
        │       └── institute-admin/
        │           └── RecentActivities.tsx
        └── stores/
            └── super-admin/
                └── useInstituteManagementStore.ts
```

## 🚀 **Implementation Steps**

### **Phase 10.1: Database & API Setup**
1. **Create Institutes Collection**
   - Define collection schema with all required fields
   - Implement slug auto-generation hook
   - Add soft delete functionality
   - Set up proper access controls

2. **Create API Endpoints**
   - Implement CRUD operations for institutes
   - Add filtering, pagination, and search
   - Create statistics endpoint
   - Add domain verification endpoint

3. **Register Endpoints**
   - Add endpoints to Payload config
   - Test API functionality
   - Verify authentication middleware

### **Phase 10.2: Frontend Store & Components**
1. **Create Zustand Store**
   - Implement state management for institutes
   - Add CRUD operations
   - Include filtering and pagination
   - Add error handling and loading states

2. **Build Core Components**
   - Institute management page
   - Institute form (create/edit)
   - Institute list and card views
   - Filters and statistics components

3. **Add Navigation**
   - Update super admin sidebar
   - Add institute management menu item
   - Ensure proper routing

### **Phase 10.3: Dashboard Activities**
1. **Super Admin Dashboard**
   - Create recent activities component
   - Show institute-related activities
   - Include admin assignments and domain verifications

2. **Institute Admin Dashboard**
   - Create institute-specific activities
   - Show course and student activities
   - Filter activities by institute

### **Phase 10.4: Testing & Validation**
1. **API Testing**
   - Test all CRUD operations
   - Verify filtering and pagination
   - Test authentication and authorization

2. **Frontend Testing**
   - Test form validation
   - Verify state management
   - Test user interactions

3. **Integration Testing**
   - Test complete workflows
   - Verify data consistency
   - Test error scenarios

## 🔧 **Technical Implementation Notes**

### **Auto-Slug Generation**
```typescript
hooks: {
  beforeValidate: [
    ({ data }) => {
      if (data?.name && !data?.slug) {
        data.slug = data.name
          .toLowerCase()
          .replace(/[^a-z0-9]+/g, '-')
          .replace(/(^-|-$)/g, '')
      }
    },
  ],
},
```

### **Soft Delete Implementation**
```typescript
// In queries, always exclude soft deleted
where: {
  deletedAt: { exists: false }
}

// To soft delete
await payload.update({
  collection: 'institutes',
  id,
  data: {
    deletedAt: new Date(),
    isActive: false
  }
})
```

### **Location Integration**
```typescript
// Location fields reference existing location management system
// These should be populated from location dropdowns/selectors

// Example: Fetching location data for dropdowns
const fetchCountries = async () => {
  const response = await api.get('/api/location-management/countries')
  return response.data.docs
}

const fetchStates = async (countryId: string) => {
  const response = await api.get(`/api/location-management/states?countryId=${countryId}`)
  return response.data.docs
}

const fetchCities = async (stateId: string) => {
  const response = await api.get(`/api/location-management/cities?stateId=${stateId}`)
  return response.data.docs
}

const fetchDistricts = async (stateId: string) => {
  const response = await api.get(`/api/location-management/districts?stateId=${stateId}`)
  return response.data.docs
}

// In the form, these would be dropdown selectors instead of text inputs
// The form would store the selected IDs (countryId, stateId, cityId, districtId)
```

### **UUID Implementation**
```typescript
// Institute ID is auto-generated UUID
// Uses crypto.randomUUID() for unique identification
// Ensures global uniqueness across distributed systems

// Example UUID generation in collection
{
  name: 'id',
  type: 'text',
  defaultValue: () => {
    return require('crypto').randomUUID()
  },
  admin: {
    hidden: true,
  },
}
```

### **Domain Verification**
```typescript
// TODO: Implement actual domain verification
// - DNS TXT record verification
// - SSL certificate validation
// - Domain ownership confirmation
```

## 📋 **User Workflows**

### **Super Admin Workflow**
1. **Create Institute**
   - Fill institute information form
   - Add address details
   - Create admin user account
   - Set custom domain (optional)

2. **Manage Institutes**
   - View all institutes in list/card view
   - Filter by status, domain verification
   - Search by name, email, domain
   - Edit institute information
   - Verify custom domains
   - Soft delete institutes

3. **Monitor Activities**
   - View recent institute activities
   - Track admin assignments
   - Monitor domain verifications
   - Review institute updates

### **Institute Admin Workflow**
1. **Dashboard Overview**
   - View institute-specific statistics
   - Monitor recent activities
   - Track course and student activities

2. **Profile Management**
   - Update institute information
   - Manage contact details
   - Request domain verification

## 🎯 **Success Criteria**

### **Functional Requirements**
- ✅ Super Admin can create institutes with admin users
- ✅ Auto-slug generation from institute name
- ✅ Comprehensive filtering and search functionality
- ✅ Soft delete with proper data integrity
- ✅ Custom domain management with verification
- ✅ Dashboard activities for both user types
- ✅ Responsive design for all screen sizes

### **Technical Requirements**
- ✅ Follows existing project architectural patterns
- ✅ Uses Zustand for state management
- ✅ Implements Formik + Yup validation
- ✅ Includes proper error handling
- ✅ Toast notifications for user feedback
- ✅ Proper TypeScript typing
- ✅ Consistent UI/UX with existing components

### **Performance Requirements**
- ✅ Efficient pagination for large datasets
- ✅ Optimized API queries with proper indexing
- ✅ Lazy loading for components
- ✅ Debounced search functionality

## 🔄 **Integration Points**

### **With Existing Systems**
- **Location Management**: Address fields integration
- **Role Management**: Institute admin role assignment
- **User Management**: Admin user creation
- **Dashboard**: Activities integration

### **Future Phases**
- **Branch Management**: Institute-branch relationships
- **Course Management**: Institute-course associations
- **Student Management**: Institute-student enrollments
- **Billing System**: Institute-based billing

## 📝 **Notes**

- **Institute ID**: Uses UUID for global uniqueness and distributed system compatibility
- **Institute slugs**: Auto-generated from name and read-only to ensure consistency
- **Location Integration**: Uses reference IDs (countryId, stateId, cityId, districtId) to integrate with existing location management system
- **Address Fields**: Separated into individual fields for better data structure and location referencing
- **Soft delete**: Preserves data integrity by using deletedAt timestamp instead of hard deletion
- **Domain verification**: Placeholder implementation - requires DNS/SSL verification in production
- **Activities**: Currently mock data - implement real activity tracking system
- **Form validation**: Uses consistent Yup validation patterns across all forms
- **Error handling**: Follows project standards with proper error messages and toast notifications
- **Database indexes**: Added for performance on frequently queried fields (slug, active status, location IDs)
- **Location dropdowns**: Form should use dropdown selectors populated from location management API instead of text inputs
