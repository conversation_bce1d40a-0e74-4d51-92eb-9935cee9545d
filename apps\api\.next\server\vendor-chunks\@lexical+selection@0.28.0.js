"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+selection@0.28.0";
exports.ids = ["vendor-chunks/@lexical+selection@0.28.0"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs":
/*!*******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $addNodeStyle: () => (/* binding */ $addNodeStyle),\n/* harmony export */   $cloneWithProperties: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.$cloneWithProperties),\n/* harmony export */   $copyBlockFormatIndent: () => (/* binding */ $copyBlockFormatIndent),\n/* harmony export */   $ensureForwardRangeSelection: () => (/* binding */ $ensureForwardRangeSelection),\n/* harmony export */   $forEachSelectedTextNode: () => (/* binding */ $forEachSelectedTextNode),\n/* harmony export */   $getSelectionStyleValueForProperty: () => (/* binding */ $getSelectionStyleValueForProperty),\n/* harmony export */   $isAtNodeEnd: () => (/* binding */ $isAtNodeEnd),\n/* harmony export */   $isParentElementRTL: () => (/* binding */ $isParentElementRTL),\n/* harmony export */   $moveCaretSelection: () => (/* binding */ $moveCaretSelection),\n/* harmony export */   $moveCharacter: () => (/* binding */ $moveCharacter),\n/* harmony export */   $patchStyleText: () => (/* binding */ $patchStyleText),\n/* harmony export */   $selectAll: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.$selectAll),\n/* harmony export */   $setBlocksType: () => (/* binding */ $setBlocksType),\n/* harmony export */   $shouldOverrideDefaultCharacterSelection: () => (/* binding */ $shouldOverrideDefaultCharacterSelection),\n/* harmony export */   $sliceSelectedTextNodeContent: () => (/* binding */ $sliceSelectedTextNodeContent),\n/* harmony export */   $trimTextContentFromAnchor: () => (/* binding */ $trimTextContentFromAnchor),\n/* harmony export */   $wrapNodes: () => (/* binding */ $wrapNodes),\n/* harmony export */   createDOMRange: () => (/* binding */ createDOMRange),\n/* harmony export */   createRectsFromDOMRange: () => (/* binding */ createRectsFromDOMRange),\n/* harmony export */   getCSSFromStyleObject: () => (/* binding */ getCSSFromStyleObject),\n/* harmony export */   getStyleObjectFromCSS: () => (/* binding */ getStyleObjectFromCSS),\n/* harmony export */   trimTextContentFromAnchor: () => (/* binding */ trimTextContentFromAnchor)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nconst CSS_TO_STYLES = new Map();\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction getDOMTextNode(element) {\n  let node = element;\n  while (node != null) {\n    if (node.nodeType === Node.TEXT_NODE) {\n      return node;\n    }\n    node = node.firstChild;\n  }\n  return null;\n}\nfunction getDOMIndexWithinParent(node) {\n  const parent = node.parentNode;\n  if (parent == null) {\n    throw new Error('Should never happen');\n  }\n  return [parent, Array.from(parent.childNodes).indexOf(node)];\n}\n\n/**\n * Creates a selection range for the DOM.\n * @param editor - The lexical editor.\n * @param anchorNode - The anchor node of a selection.\n * @param _anchorOffset - The amount of space offset from the anchor to the focus.\n * @param focusNode - The current focus.\n * @param _focusOffset - The amount of space offset from the focus to the anchor.\n * @returns The range of selection for the DOM that was created.\n */\nfunction createDOMRange(editor, anchorNode, _anchorOffset, focusNode, _focusOffset) {\n  const anchorKey = anchorNode.getKey();\n  const focusKey = focusNode.getKey();\n  const range = document.createRange();\n  let anchorDOM = editor.getElementByKey(anchorKey);\n  let focusDOM = editor.getElementByKey(focusKey);\n  let anchorOffset = _anchorOffset;\n  let focusOffset = _focusOffset;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(anchorNode)) {\n    anchorDOM = getDOMTextNode(anchorDOM);\n  }\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(focusNode)) {\n    focusDOM = getDOMTextNode(focusDOM);\n  }\n  if (anchorNode === undefined || focusNode === undefined || anchorDOM === null || focusDOM === null) {\n    return null;\n  }\n  if (anchorDOM.nodeName === 'BR') {\n    [anchorDOM, anchorOffset] = getDOMIndexWithinParent(anchorDOM);\n  }\n  if (focusDOM.nodeName === 'BR') {\n    [focusDOM, focusOffset] = getDOMIndexWithinParent(focusDOM);\n  }\n  const firstChild = anchorDOM.firstChild;\n  if (anchorDOM === focusDOM && firstChild != null && firstChild.nodeName === 'BR' && anchorOffset === 0 && focusOffset === 0) {\n    focusOffset = 1;\n  }\n  try {\n    range.setStart(anchorDOM, anchorOffset);\n    range.setEnd(focusDOM, focusOffset);\n  } catch (e) {\n    return null;\n  }\n  if (range.collapsed && (anchorOffset !== focusOffset || anchorKey !== focusKey)) {\n    // Range is backwards, we need to reverse it\n    range.setStart(focusDOM, focusOffset);\n    range.setEnd(anchorDOM, anchorOffset);\n  }\n  return range;\n}\n\n/**\n * Creates DOMRects, generally used to help the editor find a specific location on the screen.\n * @param editor - The lexical editor\n * @param range - A fragment of a document that can contain nodes and parts of text nodes.\n * @returns The selectionRects as an array.\n */\nfunction createRectsFromDOMRange(editor, range) {\n  const rootElement = editor.getRootElement();\n  if (rootElement === null) {\n    return [];\n  }\n  const rootRect = rootElement.getBoundingClientRect();\n  const computedStyle = getComputedStyle(rootElement);\n  const rootPadding = parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight);\n  const selectionRects = Array.from(range.getClientRects());\n  let selectionRectsLength = selectionRects.length;\n  //sort rects from top left to bottom right.\n  selectionRects.sort((a, b) => {\n    const top = a.top - b.top;\n    // Some rects match position closely, but not perfectly,\n    // so we give a 3px tolerance.\n    if (Math.abs(top) <= 3) {\n      return a.left - b.left;\n    }\n    return top;\n  });\n  let prevRect;\n  for (let i = 0; i < selectionRectsLength; i++) {\n    const selectionRect = selectionRects[i];\n    // Exclude rects that overlap preceding Rects in the sorted list.\n    const isOverlappingRect = prevRect && prevRect.top <= selectionRect.top && prevRect.top + prevRect.height > selectionRect.top && prevRect.left + prevRect.width > selectionRect.left;\n    // Exclude selections that span the entire element\n    const selectionSpansElement = selectionRect.width + rootPadding === rootRect.width;\n    if (isOverlappingRect || selectionSpansElement) {\n      selectionRects.splice(i--, 1);\n      selectionRectsLength--;\n      continue;\n    }\n    prevRect = selectionRect;\n  }\n  return selectionRects;\n}\n\n/**\n * Creates an object containing all the styles and their values provided in the CSS string.\n * @param css - The CSS string of styles and their values.\n * @returns The styleObject containing all the styles and their values.\n */\nfunction getStyleObjectFromRawCSS(css) {\n  const styleObject = {};\n  if (!css) {\n    return styleObject;\n  }\n  const styles = css.split(';');\n  for (const style of styles) {\n    if (style !== '') {\n      const [key, value] = style.split(/:([^]+)/); // split on first colon\n      if (key && value) {\n        styleObject[key.trim()] = value.trim();\n      }\n    }\n  }\n  return styleObject;\n}\n\n/**\n * Given a CSS string, returns an object from the style cache.\n * @param css - The CSS property as a string.\n * @returns The value of the given CSS property.\n */\nfunction getStyleObjectFromCSS(css) {\n  let value = CSS_TO_STYLES.get(css);\n  if (value === undefined) {\n    value = getStyleObjectFromRawCSS(css);\n    CSS_TO_STYLES.set(css, value);\n  }\n  {\n    // Freeze the value in DEV to prevent accidental mutations\n    Object.freeze(value);\n  }\n  return value;\n}\n\n/**\n * Gets the CSS styles from the style object.\n * @param styles - The style object containing the styles to get.\n * @returns A string containing the CSS styles and their values.\n */\nfunction getCSSFromStyleObject(styles) {\n  let css = '';\n  for (const style in styles) {\n    if (style) {\n      css += `${style}: ${styles[style]};`;\n    }\n  }\n  return css;\n}\n\n/**\n * Generally used to append text content to HTML and JSON. Grabs the text content and \"slices\"\n * it to be generated into the new TextNode.\n * @param selection - The selection containing the node whose TextNode is to be edited.\n * @param textNode - The TextNode to be edited.\n * @returns The updated TextNode.\n */\nfunction $sliceSelectedTextNodeContent(selection, textNode) {\n  const anchorAndFocus = selection.getStartEndPoints();\n  if (textNode.isSelected(selection) && !textNode.isSegmented() && !textNode.isToken() && anchorAndFocus !== null) {\n    const [anchor, focus] = anchorAndFocus;\n    const isBackward = selection.isBackward();\n    const anchorNode = anchor.getNode();\n    const focusNode = focus.getNode();\n    const isAnchor = textNode.is(anchorNode);\n    const isFocus = textNode.is(focusNode);\n    if (isAnchor || isFocus) {\n      const [anchorOffset, focusOffset] = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCharacterOffsets)(selection);\n      const isSame = anchorNode.is(focusNode);\n      const isFirst = textNode.is(isBackward ? focusNode : anchorNode);\n      const isLast = textNode.is(isBackward ? anchorNode : focusNode);\n      let startOffset = 0;\n      let endOffset = undefined;\n      if (isSame) {\n        startOffset = anchorOffset > focusOffset ? focusOffset : anchorOffset;\n        endOffset = anchorOffset > focusOffset ? anchorOffset : focusOffset;\n      } else if (isFirst) {\n        const offset = isBackward ? focusOffset : anchorOffset;\n        startOffset = offset;\n        endOffset = undefined;\n      } else if (isLast) {\n        const offset = isBackward ? anchorOffset : focusOffset;\n        startOffset = 0;\n        endOffset = offset;\n      }\n      textNode.__text = textNode.__text.slice(startOffset, endOffset);\n      return textNode;\n    }\n  }\n  return textNode;\n}\n\n/**\n * Determines if the current selection is at the end of the node.\n * @param point - The point of the selection to test.\n * @returns true if the provided point offset is in the last possible position, false otherwise.\n */\nfunction $isAtNodeEnd(point) {\n  if (point.type === 'text') {\n    return point.offset === point.getNode().getTextContentSize();\n  }\n  const node = point.getNode();\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n    formatDevErrorMessage(`isAtNodeEnd: node must be a TextNode or ElementNode`);\n  }\n  return point.offset === node.getChildrenSize();\n}\n\n/**\n * Trims text from a node in order to shorten it, eg. to enforce a text's max length. If it deletes text\n * that is an ancestor of the anchor then it will leave 2 indents, otherwise, if no text content exists, it deletes\n * the TextNode. It will move the focus to either the end of any left over text or beginning of a new TextNode.\n * @param editor - The lexical editor.\n * @param anchor - The anchor of the current selection, where the selection should be pointing.\n * @param delCount - The amount of characters to delete. Useful as a dynamic variable eg. textContentSize - maxLength;\n */\nfunction $trimTextContentFromAnchor(editor, anchor, delCount) {\n  // Work from the current selection anchor point\n  let currentNode = anchor.getNode();\n  let remaining = delCount;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode)) {\n    const descendantNode = currentNode.getDescendantByIndex(anchor.offset);\n    if (descendantNode !== null) {\n      currentNode = descendantNode;\n    }\n  }\n  while (remaining > 0 && currentNode !== null) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode)) {\n      const lastDescendant = currentNode.getLastDescendant();\n      if (lastDescendant !== null) {\n        currentNode = lastDescendant;\n      }\n    }\n    let nextNode = currentNode.getPreviousSibling();\n    let additionalElementWhitespace = 0;\n    if (nextNode === null) {\n      let parent = currentNode.getParentOrThrow();\n      let parentSibling = parent.getPreviousSibling();\n      while (parentSibling === null) {\n        parent = parent.getParent();\n        if (parent === null) {\n          nextNode = null;\n          break;\n        }\n        parentSibling = parent.getPreviousSibling();\n      }\n      if (parent !== null) {\n        additionalElementWhitespace = parent.isInline() ? 0 : 2;\n        nextNode = parentSibling;\n      }\n    }\n    let text = currentNode.getTextContent();\n    // If the text is empty, we need to consider adding in two line breaks to match\n    // the content if we were to get it from its parent.\n    if (text === '' && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && !currentNode.isInline()) {\n      // TODO: should this be handled in core?\n      text = '\\n\\n';\n    }\n    const currentNodeSize = text.length;\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(currentNode) || remaining >= currentNodeSize) {\n      const parent = currentNode.getParent();\n      currentNode.remove();\n      if (parent != null && parent.getChildrenSize() === 0 && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(parent)) {\n        parent.remove();\n      }\n      remaining -= currentNodeSize + additionalElementWhitespace;\n      currentNode = nextNode;\n    } else {\n      const key = currentNode.getKey();\n      // See if we can just revert it to what was in the last editor state\n      const prevTextContent = editor.getEditorState().read(() => {\n        const prevNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(key);\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevNode) && prevNode.isSimpleText()) {\n          return prevNode.getTextContent();\n        }\n        return null;\n      });\n      const offset = currentNodeSize - remaining;\n      const slicedText = text.slice(0, offset);\n      if (prevTextContent !== null && prevTextContent !== text) {\n        const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n        let target = currentNode;\n        if (!currentNode.isSimpleText()) {\n          const textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(prevTextContent);\n          currentNode.replace(textNode);\n          target = textNode;\n        } else {\n          currentNode.setTextContent(prevTextContent);\n        }\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && prevSelection.isCollapsed()) {\n          const prevOffset = prevSelection.anchor.offset;\n          target.select(prevOffset, prevOffset);\n        }\n      } else if (currentNode.isSimpleText()) {\n        // Split text\n        const isSelected = anchor.key === key;\n        let anchorOffset = anchor.offset;\n        // Move offset to end if it's less than the remaining number, otherwise\n        // we'll have a negative splitStart.\n        if (anchorOffset < remaining) {\n          anchorOffset = currentNodeSize;\n        }\n        const splitStart = isSelected ? anchorOffset - remaining : 0;\n        const splitEnd = isSelected ? anchorOffset : offset;\n        if (isSelected && splitStart === 0) {\n          const [excessNode] = currentNode.splitText(splitStart, splitEnd);\n          excessNode.remove();\n        } else {\n          const [, excessNode] = currentNode.splitText(splitStart, splitEnd);\n          excessNode.remove();\n        }\n      } else {\n        const textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(slicedText);\n        currentNode.replace(textNode);\n      }\n      remaining = 0;\n    }\n  }\n}\n\n/**\n * Gets the TextNode's style object and adds the styles to the CSS.\n * @param node - The TextNode to add styles to.\n */\nfunction $addNodeStyle(node) {\n  const CSSText = node.getStyle();\n  const styles = getStyleObjectFromRawCSS(CSSText);\n  CSS_TO_STYLES.set(CSSText, styles);\n}\n\n/**\n * Applies the provided styles to the given TextNode, ElementNode, or\n * collapsed RangeSelection.\n *\n * @param target - The TextNode, ElementNode, or collapsed RangeSelection to apply the styles to\n * @param patch - The patch to apply, which can include multiple styles. \\\\{CSSProperty: value\\\\} . Can also accept a function that returns the new property value.\n */\nfunction $patchStyle(target, patch) {\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(target) ? target.isCollapsed() : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(target))) {\n    formatDevErrorMessage(`$patchStyle must only be called with a TextNode, ElementNode, or collapsed RangeSelection`);\n  }\n  const prevStyles = getStyleObjectFromCSS((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(target) ? target.style : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target) ? target.getStyle() : target.getTextStyle());\n  const newStyles = Object.entries(patch).reduce((styles, [key, value]) => {\n    if (typeof value === 'function') {\n      styles[key] = value(prevStyles[key], target);\n    } else if (value === null) {\n      delete styles[key];\n    } else {\n      styles[key] = value;\n    }\n    return styles;\n  }, {\n    ...prevStyles\n  });\n  const newCSSText = getCSSFromStyleObject(newStyles);\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(target) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target)) {\n    target.setStyle(newCSSText);\n  } else {\n    target.setTextStyle(newCSSText);\n  }\n  CSS_TO_STYLES.set(newCSSText, newStyles);\n}\n\n/**\n * Applies the provided styles to the TextNodes in the provided Selection.\n * Will update partially selected TextNodes by splitting the TextNode and applying\n * the styles to the appropriate one.\n * @param selection - The selected node(s) to update.\n * @param patch - The patch to apply, which can include multiple styles. \\\\{CSSProperty: value\\\\} . Can also accept a function that returns the new property value.\n */\nfunction $patchStyleText(selection, patch) {\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed()) {\n    $patchStyle(selection, patch);\n    const emptyNode = selection.anchor.getNode();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(emptyNode) && emptyNode.isEmpty()) {\n      $patchStyle(emptyNode, patch);\n    }\n  }\n  $forEachSelectedTextNode(textNode => {\n    $patchStyle(textNode, patch);\n  });\n}\nfunction $forEachSelectedTextNode(fn) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!selection) {\n    return;\n  }\n  const slicedTextNodes = new Map();\n  const getSliceIndices = node => slicedTextNodes.get(node.getKey()) || [0, node.getTextContentSize()];\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    for (const slice of (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretRangeFromSelection)(selection).getTextSlices()) {\n      if (slice) {\n        slicedTextNodes.set(slice.caret.origin.getKey(), slice.getSliceIndices());\n      }\n    }\n  }\n  const selectedNodes = selection.getNodes();\n  for (const selectedNode of selectedNodes) {\n    if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(selectedNode) && selectedNode.canHaveFormat())) {\n      continue;\n    }\n    const [startOffset, endOffset] = getSliceIndices(selectedNode);\n    // No actual text is selected, so do nothing.\n    if (endOffset === startOffset) {\n      continue;\n    }\n\n    // The entire node is selected or a token/segment, so just format it\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTokenOrSegmented)(selectedNode) || startOffset === 0 && endOffset === selectedNode.getTextContentSize()) {\n      fn(selectedNode);\n    } else {\n      // The node is partially selected, so split it into two or three nodes\n      // and style the selected one.\n      const splitNodes = selectedNode.splitText(startOffset, endOffset);\n      const replacement = splitNodes[startOffset === 0 ? 0 : 1];\n      fn(replacement);\n    }\n  }\n  // Prior to NodeCaret #7046 this would have been a side-effect\n  // so we do this for test compatibility.\n  // TODO: we may want to consider simplifying by removing this\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.anchor.type === 'text' && selection.focus.type === 'text' && selection.anchor.key === selection.focus.key) {\n    $ensureForwardRangeSelection(selection);\n  }\n}\n\n/**\n * Ensure that the given RangeSelection is not backwards. If it\n * is backwards, then the anchor and focus points will be swapped\n * in-place. Ensuring that the selection is a writable RangeSelection\n * is the responsibility of the caller (e.g. in a read-only context\n * you will want to clone $getSelection() before using this).\n *\n * @param selection a writable RangeSelection\n */\nfunction $ensureForwardRangeSelection(selection) {\n  if (selection.isBackward()) {\n    const {\n      anchor,\n      focus\n    } = selection;\n    // stash for the in-place swap\n    const {\n      key,\n      offset,\n      type\n    } = anchor;\n    anchor.set(focus.key, focus.offset, focus.type);\n    focus.set(key, offset, type);\n  }\n}\n\nfunction $copyBlockFormatIndent(srcNode, destNode) {\n  const format = srcNode.getFormatType();\n  const indent = srcNode.getIndent();\n  if (format !== destNode.getFormatType()) {\n    destNode.setFormat(format);\n  }\n  if (indent !== destNode.getIndent()) {\n    destNode.setIndent(indent);\n  }\n}\n\n/**\n * Converts all nodes in the selection that are of one block type to another.\n * @param selection - The selected blocks to be converted.\n * @param $createElement - The function that creates the node. eg. $createParagraphNode.\n * @param $afterCreateElement - The function that updates the new node based on the previous one ($copyBlockFormatIndent by default)\n */\nfunction $setBlocksType(selection, $createElement, $afterCreateElement = $copyBlockFormatIndent) {\n  if (selection === null) {\n    return;\n  }\n  // Selections tend to not include their containing blocks so we effectively\n  // expand it here\n  const anchorAndFocus = selection.getStartEndPoints();\n  const blockMap = new Map();\n  let newSelection = null;\n  if (anchorAndFocus) {\n    const [anchor, focus] = anchorAndFocus;\n    newSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelection)();\n    newSelection.anchor.set(anchor.key, anchor.offset, anchor.type);\n    newSelection.focus.set(focus.key, focus.offset, focus.type);\n    const anchorBlock = $getAncestor(anchor.getNode(), lexical__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_$isBlock);\n    const focusBlock = $getAncestor(focus.getNode(), lexical__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_$isBlock);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(anchorBlock)) {\n      blockMap.set(anchorBlock.getKey(), anchorBlock);\n    }\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(focusBlock)) {\n      blockMap.set(focusBlock.getKey(), focusBlock);\n    }\n  }\n  for (const node of selection.getNodes()) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_$isBlock)(node)) {\n      blockMap.set(node.getKey(), node);\n    }\n  }\n  for (const [key, prevNode] of blockMap) {\n    const element = $createElement();\n    $afterCreateElement(prevNode, element);\n    prevNode.replace(element, true);\n    if (newSelection) {\n      if (key === newSelection.anchor.key) {\n        newSelection.anchor.set(element.getKey(), newSelection.anchor.offset, newSelection.anchor.type);\n      }\n      if (key === newSelection.focus.key) {\n        newSelection.focus.set(element.getKey(), newSelection.focus.offset, newSelection.focus.type);\n      }\n    }\n  }\n  if (newSelection && selection.is((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)())) {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n  }\n}\nfunction isPointAttached(point) {\n  return point.getNode().isAttached();\n}\nfunction $removeParentEmptyElements(startingNode) {\n  let node = startingNode;\n  while (node !== null && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(node)) {\n    const latest = node.getLatest();\n    const parentNode = node.getParent();\n    if (latest.getChildrenSize() === 0) {\n      node.remove(true);\n    }\n    node = parentNode;\n  }\n}\n\n/**\n * @deprecated\n * Wraps all nodes in the selection into another node of the type returned by createElement.\n * @param selection - The selection of nodes to be wrapped.\n * @param createElement - A function that creates the wrapping ElementNode. eg. $createParagraphNode.\n * @param wrappingElement - An element to append the wrapped selection and its children to.\n */\nfunction $wrapNodes(selection, createElement, wrappingElement = null) {\n  const anchorAndFocus = selection.getStartEndPoints();\n  const anchor = anchorAndFocus ? anchorAndFocus[0] : null;\n  const nodes = selection.getNodes();\n  const nodesLength = nodes.length;\n  if (anchor !== null && (nodesLength === 0 || nodesLength === 1 && anchor.type === 'element' && anchor.getNode().getChildrenSize() === 0)) {\n    const target = anchor.type === 'text' ? anchor.getNode().getParentOrThrow() : anchor.getNode();\n    const children = target.getChildren();\n    let element = createElement();\n    element.setFormat(target.getFormatType());\n    element.setIndent(target.getIndent());\n    children.forEach(child => element.append(child));\n    if (wrappingElement) {\n      element = wrappingElement.append(element);\n    }\n    target.replace(element);\n    return;\n  }\n  let topLevelNode = null;\n  let descendants = [];\n  for (let i = 0; i < nodesLength; i++) {\n    const node = nodes[i];\n    // Determine whether wrapping has to be broken down into multiple chunks. This can happen if the\n    // user selected multiple Root-like nodes that have to be treated separately as if they are\n    // their own branch. I.e. you don't want to wrap a whole table, but rather the contents of each\n    // of each of the cell nodes.\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(node)) {\n      $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);\n      descendants = [];\n      topLevelNode = node;\n    } else if (topLevelNode === null || topLevelNode !== null && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$hasAncestor)(node, topLevelNode)) {\n      descendants.push(node);\n    } else {\n      $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);\n      descendants = [node];\n    }\n  }\n  $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);\n}\n\n/**\n * Wraps each node into a new ElementNode.\n * @param selection - The selection of nodes to wrap.\n * @param nodes - An array of nodes, generally the descendants of the selection.\n * @param nodesLength - The length of nodes.\n * @param createElement - A function that creates the wrapping ElementNode. eg. $createParagraphNode.\n * @param wrappingElement - An element to wrap all the nodes into.\n * @returns\n */\nfunction $wrapNodesImpl(selection, nodes, nodesLength, createElement, wrappingElement = null) {\n  if (nodes.length === 0) {\n    return;\n  }\n  const firstNode = nodes[0];\n  const elementMapping = new Map();\n  const elements = [];\n  // The below logic is to find the right target for us to\n  // either insertAfter/insertBefore/append the corresponding\n  // elements to. This is made more complicated due to nested\n  // structures.\n  let target = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(firstNode) ? firstNode : firstNode.getParentOrThrow();\n  if (target.isInline()) {\n    target = target.getParentOrThrow();\n  }\n  let targetIsPrevSibling = false;\n  while (target !== null) {\n    const prevSibling = target.getPreviousSibling();\n    if (prevSibling !== null) {\n      target = prevSibling;\n      targetIsPrevSibling = true;\n      break;\n    }\n    target = target.getParentOrThrow();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(target)) {\n      break;\n    }\n  }\n  const emptyElements = new Set();\n\n  // Find any top level empty elements\n  for (let i = 0; i < nodesLength; i++) {\n    const node = nodes[i];\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && node.getChildrenSize() === 0) {\n      emptyElements.add(node.getKey());\n    }\n  }\n  const movedNodes = new Set();\n\n  // Move out all leaf nodes into our elements array.\n  // If we find a top level empty element, also move make\n  // an element for that.\n  for (let i = 0; i < nodesLength; i++) {\n    const node = nodes[i];\n    let parent = node.getParent();\n    if (parent !== null && parent.isInline()) {\n      parent = parent.getParent();\n    }\n    if (parent !== null && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLeafNode)(node) && !movedNodes.has(node.getKey())) {\n      const parentKey = parent.getKey();\n      if (elementMapping.get(parentKey) === undefined) {\n        const targetElement = createElement();\n        targetElement.setFormat(parent.getFormatType());\n        targetElement.setIndent(parent.getIndent());\n        elements.push(targetElement);\n        elementMapping.set(parentKey, targetElement);\n        // Move node and its siblings to the new\n        // element.\n        parent.getChildren().forEach(child => {\n          targetElement.append(child);\n          movedNodes.add(child.getKey());\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(child)) {\n            // Skip nested leaf nodes if the parent has already been moved\n            child.getChildrenKeys().forEach(key => movedNodes.add(key));\n          }\n        });\n        $removeParentEmptyElements(parent);\n      }\n    } else if (emptyElements.has(node.getKey())) {\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n        formatDevErrorMessage(`Expected node in emptyElements to be an ElementNode`);\n      }\n      const targetElement = createElement();\n      targetElement.setFormat(node.getFormatType());\n      targetElement.setIndent(node.getIndent());\n      elements.push(targetElement);\n      node.remove(true);\n    }\n  }\n  if (wrappingElement !== null) {\n    for (let i = 0; i < elements.length; i++) {\n      const element = elements[i];\n      wrappingElement.append(element);\n    }\n  }\n  let lastElement = null;\n\n  // If our target is Root-like, let's see if we can re-adjust\n  // so that the target is the first child instead.\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(target)) {\n    if (targetIsPrevSibling) {\n      if (wrappingElement !== null) {\n        target.insertAfter(wrappingElement);\n      } else {\n        for (let i = elements.length - 1; i >= 0; i--) {\n          const element = elements[i];\n          target.insertAfter(element);\n        }\n      }\n    } else {\n      const firstChild = target.getFirstChild();\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(firstChild)) {\n        target = firstChild;\n      }\n      if (firstChild === null) {\n        if (wrappingElement) {\n          target.append(wrappingElement);\n        } else {\n          for (let i = 0; i < elements.length; i++) {\n            const element = elements[i];\n            target.append(element);\n            lastElement = element;\n          }\n        }\n      } else {\n        if (wrappingElement !== null) {\n          firstChild.insertBefore(wrappingElement);\n        } else {\n          for (let i = 0; i < elements.length; i++) {\n            const element = elements[i];\n            firstChild.insertBefore(element);\n            lastElement = element;\n          }\n        }\n      }\n    }\n  } else {\n    if (wrappingElement) {\n      target.insertAfter(wrappingElement);\n    } else {\n      for (let i = elements.length - 1; i >= 0; i--) {\n        const element = elements[i];\n        target.insertAfter(element);\n        lastElement = element;\n      }\n    }\n  }\n  const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && isPointAttached(prevSelection.anchor) && isPointAttached(prevSelection.focus)) {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(prevSelection.clone());\n  } else if (lastElement !== null) {\n    lastElement.selectEnd();\n  } else {\n    selection.dirty = true;\n  }\n}\n\n/**\n * Determines if the default character selection should be overridden. Used with DecoratorNodes\n * @param selection - The selection whose default character selection may need to be overridden.\n * @param isBackward - Is the selection backwards (the focus comes before the anchor)?\n * @returns true if it should be overridden, false if not.\n */\nfunction $shouldOverrideDefaultCharacterSelection(selection, isBackward) {\n  const focusCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretFromPoint)(selection.focus, isBackward ? 'previous' : 'next');\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isExtendableTextPointCaret)(focusCaret)) {\n    return false;\n  }\n  for (const nextCaret of (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$extendCaretToRange)(focusCaret)) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(nextCaret)) {\n      return !nextCaret.origin.isInline();\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(nextCaret.origin)) {\n      continue;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(nextCaret.origin)) {\n      return true;\n    }\n    break;\n  }\n  return false;\n}\n\n/**\n * Moves the selection according to the arguments.\n * @param selection - The selected text or nodes.\n * @param isHoldingShift - Is the shift key being held down during the operation.\n * @param isBackward - Is the selection selected backwards (the focus comes before the anchor)?\n * @param granularity - The distance to adjust the current selection.\n */\nfunction $moveCaretSelection(selection, isHoldingShift, isBackward, granularity) {\n  selection.modify(isHoldingShift ? 'extend' : 'move', isBackward, granularity);\n}\n\n/**\n * Tests a parent element for right to left direction.\n * @param selection - The selection whose parent is to be tested.\n * @returns true if the selections' parent element has a direction of 'rtl' (right to left), false otherwise.\n */\nfunction $isParentElementRTL(selection) {\n  const anchorNode = selection.anchor.getNode();\n  const parent = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(anchorNode) ? anchorNode : anchorNode.getParentOrThrow();\n  return parent.getDirection() === 'rtl';\n}\n\n/**\n * Moves selection by character according to arguments.\n * @param selection - The selection of the characters to move.\n * @param isHoldingShift - Is the shift key being held down during the operation.\n * @param isBackward - Is the selection backward (the focus comes before the anchor)?\n */\nfunction $moveCharacter(selection, isHoldingShift, isBackward) {\n  const isRTL = $isParentElementRTL(selection);\n  $moveCaretSelection(selection, isHoldingShift, isBackward ? !isRTL : isRTL, 'character');\n}\n\n/**\n * Returns the current value of a CSS property for Nodes, if set. If not set, it returns the defaultValue.\n * @param node - The node whose style value to get.\n * @param styleProperty - The CSS style property.\n * @param defaultValue - The default value for the property.\n * @returns The value of the property for node.\n */\nfunction $getNodeStyleValueForProperty(node, styleProperty, defaultValue) {\n  const css = node.getStyle();\n  const styleObject = getStyleObjectFromCSS(css);\n  if (styleObject !== null) {\n    return styleObject[styleProperty] || defaultValue;\n  }\n  return defaultValue;\n}\n\n/**\n * Returns the current value of a CSS property for TextNodes in the Selection, if set. If not set, it returns the defaultValue.\n * If all TextNodes do not have the same value, it returns an empty string.\n * @param selection - The selection of TextNodes whose value to find.\n * @param styleProperty - The CSS style property.\n * @param defaultValue - The default value for the property, defaults to an empty string.\n * @returns The value of the property for the selected TextNodes.\n */\nfunction $getSelectionStyleValueForProperty(selection, styleProperty, defaultValue = '') {\n  let styleValue = null;\n  const nodes = selection.getNodes();\n  const anchor = selection.anchor;\n  const focus = selection.focus;\n  const isBackward = selection.isBackward();\n  const endOffset = isBackward ? focus.offset : anchor.offset;\n  const endNode = isBackward ? focus.getNode() : anchor.getNode();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed() && selection.style !== '') {\n    const css = selection.style;\n    const styleObject = getStyleObjectFromCSS(css);\n    if (styleObject !== null && styleProperty in styleObject) {\n      return styleObject[styleProperty];\n    }\n  }\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n\n    // if no actual characters in the end node are selected, we don't\n    // include it in the selection for purposes of determining style\n    // value\n    if (i !== 0 && endOffset === 0 && node.is(endNode)) {\n      continue;\n    }\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n      const nodeStyleValue = $getNodeStyleValueForProperty(node, styleProperty, defaultValue);\n      if (styleValue === null) {\n        styleValue = nodeStyleValue;\n      } else if (styleValue !== nodeStyleValue) {\n        // multiple text nodes are in the selection and they don't all\n        // have the same style.\n        styleValue = '';\n        break;\n      }\n    }\n  }\n  return styleValue === null ? defaultValue : styleValue;\n}\nfunction $getAncestor(node, predicate) {\n  let parent = node;\n  while (parent !== null && parent.getParent() !== null && !predicate(parent)) {\n    parent = parent.getParentOrThrow();\n  }\n  return predicate(parent) ? parent : null;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/** @deprecated renamed to {@link $trimTextContentFromAnchor} by @lexical/eslint-plugin rules-of-lexical */\nconst trimTextContentFromAnchor = $trimTextContentFromAnchor;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs":
/*!*******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs ***!
  \*******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $addNodeStyle: () => (/* binding */ $addNodeStyle),\n/* harmony export */   $cloneWithProperties: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.$cloneWithProperties),\n/* harmony export */   $copyBlockFormatIndent: () => (/* binding */ $copyBlockFormatIndent),\n/* harmony export */   $ensureForwardRangeSelection: () => (/* binding */ $ensureForwardRangeSelection),\n/* harmony export */   $forEachSelectedTextNode: () => (/* binding */ $forEachSelectedTextNode),\n/* harmony export */   $getSelectionStyleValueForProperty: () => (/* binding */ $getSelectionStyleValueForProperty),\n/* harmony export */   $isAtNodeEnd: () => (/* binding */ $isAtNodeEnd),\n/* harmony export */   $isParentElementRTL: () => (/* binding */ $isParentElementRTL),\n/* harmony export */   $moveCaretSelection: () => (/* binding */ $moveCaretSelection),\n/* harmony export */   $moveCharacter: () => (/* binding */ $moveCharacter),\n/* harmony export */   $patchStyleText: () => (/* binding */ $patchStyleText),\n/* harmony export */   $selectAll: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.$selectAll),\n/* harmony export */   $setBlocksType: () => (/* binding */ $setBlocksType),\n/* harmony export */   $shouldOverrideDefaultCharacterSelection: () => (/* binding */ $shouldOverrideDefaultCharacterSelection),\n/* harmony export */   $sliceSelectedTextNodeContent: () => (/* binding */ $sliceSelectedTextNodeContent),\n/* harmony export */   $trimTextContentFromAnchor: () => (/* binding */ $trimTextContentFromAnchor),\n/* harmony export */   $wrapNodes: () => (/* binding */ $wrapNodes),\n/* harmony export */   createDOMRange: () => (/* binding */ createDOMRange),\n/* harmony export */   createRectsFromDOMRange: () => (/* binding */ createRectsFromDOMRange),\n/* harmony export */   getCSSFromStyleObject: () => (/* binding */ getCSSFromStyleObject),\n/* harmony export */   getStyleObjectFromCSS: () => (/* binding */ getStyleObjectFromCSS),\n/* harmony export */   trimTextContentFromAnchor: () => (/* binding */ trimTextContentFromAnchor)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\nconst CSS_TO_STYLES = new Map();\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction getDOMTextNode(element) {\n  let node = element;\n  while (node != null) {\n    if (node.nodeType === Node.TEXT_NODE) {\n      return node;\n    }\n    node = node.firstChild;\n  }\n  return null;\n}\nfunction getDOMIndexWithinParent(node) {\n  const parent = node.parentNode;\n  if (parent == null) {\n    throw new Error('Should never happen');\n  }\n  return [parent, Array.from(parent.childNodes).indexOf(node)];\n}\n\n/**\n * Creates a selection range for the DOM.\n * @param editor - The lexical editor.\n * @param anchorNode - The anchor node of a selection.\n * @param _anchorOffset - The amount of space offset from the anchor to the focus.\n * @param focusNode - The current focus.\n * @param _focusOffset - The amount of space offset from the focus to the anchor.\n * @returns The range of selection for the DOM that was created.\n */\nfunction createDOMRange(editor, anchorNode, _anchorOffset, focusNode, _focusOffset) {\n  const anchorKey = anchorNode.getKey();\n  const focusKey = focusNode.getKey();\n  const range = document.createRange();\n  let anchorDOM = editor.getElementByKey(anchorKey);\n  let focusDOM = editor.getElementByKey(focusKey);\n  let anchorOffset = _anchorOffset;\n  let focusOffset = _focusOffset;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(anchorNode)) {\n    anchorDOM = getDOMTextNode(anchorDOM);\n  }\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(focusNode)) {\n    focusDOM = getDOMTextNode(focusDOM);\n  }\n  if (anchorNode === undefined || focusNode === undefined || anchorDOM === null || focusDOM === null) {\n    return null;\n  }\n  if (anchorDOM.nodeName === 'BR') {\n    [anchorDOM, anchorOffset] = getDOMIndexWithinParent(anchorDOM);\n  }\n  if (focusDOM.nodeName === 'BR') {\n    [focusDOM, focusOffset] = getDOMIndexWithinParent(focusDOM);\n  }\n  const firstChild = anchorDOM.firstChild;\n  if (anchorDOM === focusDOM && firstChild != null && firstChild.nodeName === 'BR' && anchorOffset === 0 && focusOffset === 0) {\n    focusOffset = 1;\n  }\n  try {\n    range.setStart(anchorDOM, anchorOffset);\n    range.setEnd(focusDOM, focusOffset);\n  } catch (e) {\n    return null;\n  }\n  if (range.collapsed && (anchorOffset !== focusOffset || anchorKey !== focusKey)) {\n    // Range is backwards, we need to reverse it\n    range.setStart(focusDOM, focusOffset);\n    range.setEnd(anchorDOM, anchorOffset);\n  }\n  return range;\n}\n\n/**\n * Creates DOMRects, generally used to help the editor find a specific location on the screen.\n * @param editor - The lexical editor\n * @param range - A fragment of a document that can contain nodes and parts of text nodes.\n * @returns The selectionRects as an array.\n */\nfunction createRectsFromDOMRange(editor, range) {\n  const rootElement = editor.getRootElement();\n  if (rootElement === null) {\n    return [];\n  }\n  const rootRect = rootElement.getBoundingClientRect();\n  const computedStyle = getComputedStyle(rootElement);\n  const rootPadding = parseFloat(computedStyle.paddingLeft) + parseFloat(computedStyle.paddingRight);\n  const selectionRects = Array.from(range.getClientRects());\n  let selectionRectsLength = selectionRects.length;\n  //sort rects from top left to bottom right.\n  selectionRects.sort((a, b) => {\n    const top = a.top - b.top;\n    // Some rects match position closely, but not perfectly,\n    // so we give a 3px tolerance.\n    if (Math.abs(top) <= 3) {\n      return a.left - b.left;\n    }\n    return top;\n  });\n  let prevRect;\n  for (let i = 0; i < selectionRectsLength; i++) {\n    const selectionRect = selectionRects[i];\n    // Exclude rects that overlap preceding Rects in the sorted list.\n    const isOverlappingRect = prevRect && prevRect.top <= selectionRect.top && prevRect.top + prevRect.height > selectionRect.top && prevRect.left + prevRect.width > selectionRect.left;\n    // Exclude selections that span the entire element\n    const selectionSpansElement = selectionRect.width + rootPadding === rootRect.width;\n    if (isOverlappingRect || selectionSpansElement) {\n      selectionRects.splice(i--, 1);\n      selectionRectsLength--;\n      continue;\n    }\n    prevRect = selectionRect;\n  }\n  return selectionRects;\n}\n\n/**\n * Creates an object containing all the styles and their values provided in the CSS string.\n * @param css - The CSS string of styles and their values.\n * @returns The styleObject containing all the styles and their values.\n */\nfunction getStyleObjectFromRawCSS(css) {\n  const styleObject = {};\n  if (!css) {\n    return styleObject;\n  }\n  const styles = css.split(';');\n  for (const style of styles) {\n    if (style !== '') {\n      const [key, value] = style.split(/:([^]+)/); // split on first colon\n      if (key && value) {\n        styleObject[key.trim()] = value.trim();\n      }\n    }\n  }\n  return styleObject;\n}\n\n/**\n * Given a CSS string, returns an object from the style cache.\n * @param css - The CSS property as a string.\n * @returns The value of the given CSS property.\n */\nfunction getStyleObjectFromCSS(css) {\n  let value = CSS_TO_STYLES.get(css);\n  if (value === undefined) {\n    value = getStyleObjectFromRawCSS(css);\n    CSS_TO_STYLES.set(css, value);\n  }\n  {\n    // Freeze the value in DEV to prevent accidental mutations\n    Object.freeze(value);\n  }\n  return value;\n}\n\n/**\n * Gets the CSS styles from the style object.\n * @param styles - The style object containing the styles to get.\n * @returns A string containing the CSS styles and their values.\n */\nfunction getCSSFromStyleObject(styles) {\n  let css = '';\n  for (const style in styles) {\n    if (style) {\n      css += `${style}: ${styles[style]};`;\n    }\n  }\n  return css;\n}\n\n/**\n * Generally used to append text content to HTML and JSON. Grabs the text content and \"slices\"\n * it to be generated into the new TextNode.\n * @param selection - The selection containing the node whose TextNode is to be edited.\n * @param textNode - The TextNode to be edited.\n * @returns The updated TextNode.\n */\nfunction $sliceSelectedTextNodeContent(selection, textNode) {\n  const anchorAndFocus = selection.getStartEndPoints();\n  if (textNode.isSelected(selection) && !textNode.isSegmented() && !textNode.isToken() && anchorAndFocus !== null) {\n    const [anchor, focus] = anchorAndFocus;\n    const isBackward = selection.isBackward();\n    const anchorNode = anchor.getNode();\n    const focusNode = focus.getNode();\n    const isAnchor = textNode.is(anchorNode);\n    const isFocus = textNode.is(focusNode);\n    if (isAnchor || isFocus) {\n      const [anchorOffset, focusOffset] = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCharacterOffsets)(selection);\n      const isSame = anchorNode.is(focusNode);\n      const isFirst = textNode.is(isBackward ? focusNode : anchorNode);\n      const isLast = textNode.is(isBackward ? anchorNode : focusNode);\n      let startOffset = 0;\n      let endOffset = undefined;\n      if (isSame) {\n        startOffset = anchorOffset > focusOffset ? focusOffset : anchorOffset;\n        endOffset = anchorOffset > focusOffset ? anchorOffset : focusOffset;\n      } else if (isFirst) {\n        const offset = isBackward ? focusOffset : anchorOffset;\n        startOffset = offset;\n        endOffset = undefined;\n      } else if (isLast) {\n        const offset = isBackward ? anchorOffset : focusOffset;\n        startOffset = 0;\n        endOffset = offset;\n      }\n      textNode.__text = textNode.__text.slice(startOffset, endOffset);\n      return textNode;\n    }\n  }\n  return textNode;\n}\n\n/**\n * Determines if the current selection is at the end of the node.\n * @param point - The point of the selection to test.\n * @returns true if the provided point offset is in the last possible position, false otherwise.\n */\nfunction $isAtNodeEnd(point) {\n  if (point.type === 'text') {\n    return point.offset === point.getNode().getTextContentSize();\n  }\n  const node = point.getNode();\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n    formatDevErrorMessage(`isAtNodeEnd: node must be a TextNode or ElementNode`);\n  }\n  return point.offset === node.getChildrenSize();\n}\n\n/**\n * Trims text from a node in order to shorten it, eg. to enforce a text's max length. If it deletes text\n * that is an ancestor of the anchor then it will leave 2 indents, otherwise, if no text content exists, it deletes\n * the TextNode. It will move the focus to either the end of any left over text or beginning of a new TextNode.\n * @param editor - The lexical editor.\n * @param anchor - The anchor of the current selection, where the selection should be pointing.\n * @param delCount - The amount of characters to delete. Useful as a dynamic variable eg. textContentSize - maxLength;\n */\nfunction $trimTextContentFromAnchor(editor, anchor, delCount) {\n  // Work from the current selection anchor point\n  let currentNode = anchor.getNode();\n  let remaining = delCount;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode)) {\n    const descendantNode = currentNode.getDescendantByIndex(anchor.offset);\n    if (descendantNode !== null) {\n      currentNode = descendantNode;\n    }\n  }\n  while (remaining > 0 && currentNode !== null) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode)) {\n      const lastDescendant = currentNode.getLastDescendant();\n      if (lastDescendant !== null) {\n        currentNode = lastDescendant;\n      }\n    }\n    let nextNode = currentNode.getPreviousSibling();\n    let additionalElementWhitespace = 0;\n    if (nextNode === null) {\n      let parent = currentNode.getParentOrThrow();\n      let parentSibling = parent.getPreviousSibling();\n      while (parentSibling === null) {\n        parent = parent.getParent();\n        if (parent === null) {\n          nextNode = null;\n          break;\n        }\n        parentSibling = parent.getPreviousSibling();\n      }\n      if (parent !== null) {\n        additionalElementWhitespace = parent.isInline() ? 0 : 2;\n        nextNode = parentSibling;\n      }\n    }\n    let text = currentNode.getTextContent();\n    // If the text is empty, we need to consider adding in two line breaks to match\n    // the content if we were to get it from its parent.\n    if (text === '' && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(currentNode) && !currentNode.isInline()) {\n      // TODO: should this be handled in core?\n      text = '\\n\\n';\n    }\n    const currentNodeSize = text.length;\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(currentNode) || remaining >= currentNodeSize) {\n      const parent = currentNode.getParent();\n      currentNode.remove();\n      if (parent != null && parent.getChildrenSize() === 0 && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(parent)) {\n        parent.remove();\n      }\n      remaining -= currentNodeSize + additionalElementWhitespace;\n      currentNode = nextNode;\n    } else {\n      const key = currentNode.getKey();\n      // See if we can just revert it to what was in the last editor state\n      const prevTextContent = editor.getEditorState().read(() => {\n        const prevNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNodeByKey)(key);\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevNode) && prevNode.isSimpleText()) {\n          return prevNode.getTextContent();\n        }\n        return null;\n      });\n      const offset = currentNodeSize - remaining;\n      const slicedText = text.slice(0, offset);\n      if (prevTextContent !== null && prevTextContent !== text) {\n        const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n        let target = currentNode;\n        if (!currentNode.isSimpleText()) {\n          const textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(prevTextContent);\n          currentNode.replace(textNode);\n          target = textNode;\n        } else {\n          currentNode.setTextContent(prevTextContent);\n        }\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && prevSelection.isCollapsed()) {\n          const prevOffset = prevSelection.anchor.offset;\n          target.select(prevOffset, prevOffset);\n        }\n      } else if (currentNode.isSimpleText()) {\n        // Split text\n        const isSelected = anchor.key === key;\n        let anchorOffset = anchor.offset;\n        // Move offset to end if it's less than the remaining number, otherwise\n        // we'll have a negative splitStart.\n        if (anchorOffset < remaining) {\n          anchorOffset = currentNodeSize;\n        }\n        const splitStart = isSelected ? anchorOffset - remaining : 0;\n        const splitEnd = isSelected ? anchorOffset : offset;\n        if (isSelected && splitStart === 0) {\n          const [excessNode] = currentNode.splitText(splitStart, splitEnd);\n          excessNode.remove();\n        } else {\n          const [, excessNode] = currentNode.splitText(splitStart, splitEnd);\n          excessNode.remove();\n        }\n      } else {\n        const textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(slicedText);\n        currentNode.replace(textNode);\n      }\n      remaining = 0;\n    }\n  }\n}\n\n/**\n * Gets the TextNode's style object and adds the styles to the CSS.\n * @param node - The TextNode to add styles to.\n */\nfunction $addNodeStyle(node) {\n  const CSSText = node.getStyle();\n  const styles = getStyleObjectFromRawCSS(CSSText);\n  CSS_TO_STYLES.set(CSSText, styles);\n}\n\n/**\n * Applies the provided styles to the given TextNode, ElementNode, or\n * collapsed RangeSelection.\n *\n * @param target - The TextNode, ElementNode, or collapsed RangeSelection to apply the styles to\n * @param patch - The patch to apply, which can include multiple styles. \\\\{CSSProperty: value\\\\} . Can also accept a function that returns the new property value.\n */\nfunction $patchStyle(target, patch) {\n  if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(target) ? target.isCollapsed() : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(target))) {\n    formatDevErrorMessage(`$patchStyle must only be called with a TextNode, ElementNode, or collapsed RangeSelection`);\n  }\n  const prevStyles = getStyleObjectFromCSS((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(target) ? target.style : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target) ? target.getStyle() : target.getTextStyle());\n  const newStyles = Object.entries(patch).reduce((styles, [key, value]) => {\n    if (typeof value === 'function') {\n      styles[key] = value(prevStyles[key], target);\n    } else if (value === null) {\n      delete styles[key];\n    } else {\n      styles[key] = value;\n    }\n    return styles;\n  }, {\n    ...prevStyles\n  });\n  const newCSSText = getCSSFromStyleObject(newStyles);\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(target) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(target)) {\n    target.setStyle(newCSSText);\n  } else {\n    target.setTextStyle(newCSSText);\n  }\n  CSS_TO_STYLES.set(newCSSText, newStyles);\n}\n\n/**\n * Applies the provided styles to the TextNodes in the provided Selection.\n * Will update partially selected TextNodes by splitting the TextNode and applying\n * the styles to the appropriate one.\n * @param selection - The selected node(s) to update.\n * @param patch - The patch to apply, which can include multiple styles. \\\\{CSSProperty: value\\\\} . Can also accept a function that returns the new property value.\n */\nfunction $patchStyleText(selection, patch) {\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed()) {\n    $patchStyle(selection, patch);\n    const emptyNode = selection.anchor.getNode();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(emptyNode) && emptyNode.isEmpty()) {\n      $patchStyle(emptyNode, patch);\n    }\n  }\n  $forEachSelectedTextNode(textNode => {\n    $patchStyle(textNode, patch);\n  });\n}\nfunction $forEachSelectedTextNode(fn) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!selection) {\n    return;\n  }\n  const slicedTextNodes = new Map();\n  const getSliceIndices = node => slicedTextNodes.get(node.getKey()) || [0, node.getTextContentSize()];\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    for (const slice of (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretRangeFromSelection)(selection).getTextSlices()) {\n      if (slice) {\n        slicedTextNodes.set(slice.caret.origin.getKey(), slice.getSliceIndices());\n      }\n    }\n  }\n  const selectedNodes = selection.getNodes();\n  for (const selectedNode of selectedNodes) {\n    if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(selectedNode) && selectedNode.canHaveFormat())) {\n      continue;\n    }\n    const [startOffset, endOffset] = getSliceIndices(selectedNode);\n    // No actual text is selected, so do nothing.\n    if (endOffset === startOffset) {\n      continue;\n    }\n\n    // The entire node is selected or a token/segment, so just format it\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTokenOrSegmented)(selectedNode) || startOffset === 0 && endOffset === selectedNode.getTextContentSize()) {\n      fn(selectedNode);\n    } else {\n      // The node is partially selected, so split it into two or three nodes\n      // and style the selected one.\n      const splitNodes = selectedNode.splitText(startOffset, endOffset);\n      const replacement = splitNodes[startOffset === 0 ? 0 : 1];\n      fn(replacement);\n    }\n  }\n  // Prior to NodeCaret #7046 this would have been a side-effect\n  // so we do this for test compatibility.\n  // TODO: we may want to consider simplifying by removing this\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.anchor.type === 'text' && selection.focus.type === 'text' && selection.anchor.key === selection.focus.key) {\n    $ensureForwardRangeSelection(selection);\n  }\n}\n\n/**\n * Ensure that the given RangeSelection is not backwards. If it\n * is backwards, then the anchor and focus points will be swapped\n * in-place. Ensuring that the selection is a writable RangeSelection\n * is the responsibility of the caller (e.g. in a read-only context\n * you will want to clone $getSelection() before using this).\n *\n * @param selection a writable RangeSelection\n */\nfunction $ensureForwardRangeSelection(selection) {\n  if (selection.isBackward()) {\n    const {\n      anchor,\n      focus\n    } = selection;\n    // stash for the in-place swap\n    const {\n      key,\n      offset,\n      type\n    } = anchor;\n    anchor.set(focus.key, focus.offset, focus.type);\n    focus.set(key, offset, type);\n  }\n}\n\nfunction $copyBlockFormatIndent(srcNode, destNode) {\n  const format = srcNode.getFormatType();\n  const indent = srcNode.getIndent();\n  if (format !== destNode.getFormatType()) {\n    destNode.setFormat(format);\n  }\n  if (indent !== destNode.getIndent()) {\n    destNode.setIndent(indent);\n  }\n}\n\n/**\n * Converts all nodes in the selection that are of one block type to another.\n * @param selection - The selected blocks to be converted.\n * @param $createElement - The function that creates the node. eg. $createParagraphNode.\n * @param $afterCreateElement - The function that updates the new node based on the previous one ($copyBlockFormatIndent by default)\n */\nfunction $setBlocksType(selection, $createElement, $afterCreateElement = $copyBlockFormatIndent) {\n  if (selection === null) {\n    return;\n  }\n  // Selections tend to not include their containing blocks so we effectively\n  // expand it here\n  const anchorAndFocus = selection.getStartEndPoints();\n  const blockMap = new Map();\n  let newSelection = null;\n  if (anchorAndFocus) {\n    const [anchor, focus] = anchorAndFocus;\n    newSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelection)();\n    newSelection.anchor.set(anchor.key, anchor.offset, anchor.type);\n    newSelection.focus.set(focus.key, focus.offset, focus.type);\n    const anchorBlock = $getAncestor(anchor.getNode(), lexical__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_$isBlock);\n    const focusBlock = $getAncestor(focus.getNode(), lexical__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_$isBlock);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(anchorBlock)) {\n      blockMap.set(anchorBlock.getKey(), anchorBlock);\n    }\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(focusBlock)) {\n      blockMap.set(focusBlock.getKey(), focusBlock);\n    }\n  }\n  for (const node of selection.getNodes()) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.INTERNAL_$isBlock)(node)) {\n      blockMap.set(node.getKey(), node);\n    }\n  }\n  for (const [key, prevNode] of blockMap) {\n    const element = $createElement();\n    $afterCreateElement(prevNode, element);\n    prevNode.replace(element, true);\n    if (newSelection) {\n      if (key === newSelection.anchor.key) {\n        newSelection.anchor.set(element.getKey(), newSelection.anchor.offset, newSelection.anchor.type);\n      }\n      if (key === newSelection.focus.key) {\n        newSelection.focus.set(element.getKey(), newSelection.focus.offset, newSelection.focus.type);\n      }\n    }\n  }\n  if (newSelection && selection.is((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)())) {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(newSelection);\n  }\n}\nfunction isPointAttached(point) {\n  return point.getNode().isAttached();\n}\nfunction $removeParentEmptyElements(startingNode) {\n  let node = startingNode;\n  while (node !== null && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(node)) {\n    const latest = node.getLatest();\n    const parentNode = node.getParent();\n    if (latest.getChildrenSize() === 0) {\n      node.remove(true);\n    }\n    node = parentNode;\n  }\n}\n\n/**\n * @deprecated\n * Wraps all nodes in the selection into another node of the type returned by createElement.\n * @param selection - The selection of nodes to be wrapped.\n * @param createElement - A function that creates the wrapping ElementNode. eg. $createParagraphNode.\n * @param wrappingElement - An element to append the wrapped selection and its children to.\n */\nfunction $wrapNodes(selection, createElement, wrappingElement = null) {\n  const anchorAndFocus = selection.getStartEndPoints();\n  const anchor = anchorAndFocus ? anchorAndFocus[0] : null;\n  const nodes = selection.getNodes();\n  const nodesLength = nodes.length;\n  if (anchor !== null && (nodesLength === 0 || nodesLength === 1 && anchor.type === 'element' && anchor.getNode().getChildrenSize() === 0)) {\n    const target = anchor.type === 'text' ? anchor.getNode().getParentOrThrow() : anchor.getNode();\n    const children = target.getChildren();\n    let element = createElement();\n    element.setFormat(target.getFormatType());\n    element.setIndent(target.getIndent());\n    children.forEach(child => element.append(child));\n    if (wrappingElement) {\n      element = wrappingElement.append(element);\n    }\n    target.replace(element);\n    return;\n  }\n  let topLevelNode = null;\n  let descendants = [];\n  for (let i = 0; i < nodesLength; i++) {\n    const node = nodes[i];\n    // Determine whether wrapping has to be broken down into multiple chunks. This can happen if the\n    // user selected multiple Root-like nodes that have to be treated separately as if they are\n    // their own branch. I.e. you don't want to wrap a whole table, but rather the contents of each\n    // of each of the cell nodes.\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(node)) {\n      $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);\n      descendants = [];\n      topLevelNode = node;\n    } else if (topLevelNode === null || topLevelNode !== null && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$hasAncestor)(node, topLevelNode)) {\n      descendants.push(node);\n    } else {\n      $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);\n      descendants = [node];\n    }\n  }\n  $wrapNodesImpl(selection, descendants, descendants.length, createElement, wrappingElement);\n}\n\n/**\n * Wraps each node into a new ElementNode.\n * @param selection - The selection of nodes to wrap.\n * @param nodes - An array of nodes, generally the descendants of the selection.\n * @param nodesLength - The length of nodes.\n * @param createElement - A function that creates the wrapping ElementNode. eg. $createParagraphNode.\n * @param wrappingElement - An element to wrap all the nodes into.\n * @returns\n */\nfunction $wrapNodesImpl(selection, nodes, nodesLength, createElement, wrappingElement = null) {\n  if (nodes.length === 0) {\n    return;\n  }\n  const firstNode = nodes[0];\n  const elementMapping = new Map();\n  const elements = [];\n  // The below logic is to find the right target for us to\n  // either insertAfter/insertBefore/append the corresponding\n  // elements to. This is made more complicated due to nested\n  // structures.\n  let target = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(firstNode) ? firstNode : firstNode.getParentOrThrow();\n  if (target.isInline()) {\n    target = target.getParentOrThrow();\n  }\n  let targetIsPrevSibling = false;\n  while (target !== null) {\n    const prevSibling = target.getPreviousSibling();\n    if (prevSibling !== null) {\n      target = prevSibling;\n      targetIsPrevSibling = true;\n      break;\n    }\n    target = target.getParentOrThrow();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(target)) {\n      break;\n    }\n  }\n  const emptyElements = new Set();\n\n  // Find any top level empty elements\n  for (let i = 0; i < nodesLength; i++) {\n    const node = nodes[i];\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && node.getChildrenSize() === 0) {\n      emptyElements.add(node.getKey());\n    }\n  }\n  const movedNodes = new Set();\n\n  // Move out all leaf nodes into our elements array.\n  // If we find a top level empty element, also move make\n  // an element for that.\n  for (let i = 0; i < nodesLength; i++) {\n    const node = nodes[i];\n    let parent = node.getParent();\n    if (parent !== null && parent.isInline()) {\n      parent = parent.getParent();\n    }\n    if (parent !== null && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isLeafNode)(node) && !movedNodes.has(node.getKey())) {\n      const parentKey = parent.getKey();\n      if (elementMapping.get(parentKey) === undefined) {\n        const targetElement = createElement();\n        targetElement.setFormat(parent.getFormatType());\n        targetElement.setIndent(parent.getIndent());\n        elements.push(targetElement);\n        elementMapping.set(parentKey, targetElement);\n        // Move node and its siblings to the new\n        // element.\n        parent.getChildren().forEach(child => {\n          targetElement.append(child);\n          movedNodes.add(child.getKey());\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(child)) {\n            // Skip nested leaf nodes if the parent has already been moved\n            child.getChildrenKeys().forEach(key => movedNodes.add(key));\n          }\n        });\n        $removeParentEmptyElements(parent);\n      }\n    } else if (emptyElements.has(node.getKey())) {\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n        formatDevErrorMessage(`Expected node in emptyElements to be an ElementNode`);\n      }\n      const targetElement = createElement();\n      targetElement.setFormat(node.getFormatType());\n      targetElement.setIndent(node.getIndent());\n      elements.push(targetElement);\n      node.remove(true);\n    }\n  }\n  if (wrappingElement !== null) {\n    for (let i = 0; i < elements.length; i++) {\n      const element = elements[i];\n      wrappingElement.append(element);\n    }\n  }\n  let lastElement = null;\n\n  // If our target is Root-like, let's see if we can re-adjust\n  // so that the target is the first child instead.\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootOrShadowRoot)(target)) {\n    if (targetIsPrevSibling) {\n      if (wrappingElement !== null) {\n        target.insertAfter(wrappingElement);\n      } else {\n        for (let i = elements.length - 1; i >= 0; i--) {\n          const element = elements[i];\n          target.insertAfter(element);\n        }\n      }\n    } else {\n      const firstChild = target.getFirstChild();\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(firstChild)) {\n        target = firstChild;\n      }\n      if (firstChild === null) {\n        if (wrappingElement) {\n          target.append(wrappingElement);\n        } else {\n          for (let i = 0; i < elements.length; i++) {\n            const element = elements[i];\n            target.append(element);\n            lastElement = element;\n          }\n        }\n      } else {\n        if (wrappingElement !== null) {\n          firstChild.insertBefore(wrappingElement);\n        } else {\n          for (let i = 0; i < elements.length; i++) {\n            const element = elements[i];\n            firstChild.insertBefore(element);\n            lastElement = element;\n          }\n        }\n      }\n    }\n  } else {\n    if (wrappingElement) {\n      target.insertAfter(wrappingElement);\n    } else {\n      for (let i = elements.length - 1; i >= 0; i--) {\n        const element = elements[i];\n        target.insertAfter(element);\n        lastElement = element;\n      }\n    }\n  }\n  const prevSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && isPointAttached(prevSelection.anchor) && isPointAttached(prevSelection.focus)) {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(prevSelection.clone());\n  } else if (lastElement !== null) {\n    lastElement.selectEnd();\n  } else {\n    selection.dirty = true;\n  }\n}\n\n/**\n * Determines if the default character selection should be overridden. Used with DecoratorNodes\n * @param selection - The selection whose default character selection may need to be overridden.\n * @param isBackward - Is the selection backwards (the focus comes before the anchor)?\n * @returns true if it should be overridden, false if not.\n */\nfunction $shouldOverrideDefaultCharacterSelection(selection, isBackward) {\n  const focusCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretFromPoint)(selection.focus, isBackward ? 'previous' : 'next');\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isExtendableTextPointCaret)(focusCaret)) {\n    return false;\n  }\n  for (const nextCaret of (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$extendCaretToRange)(focusCaret)) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(nextCaret)) {\n      return !nextCaret.origin.isInline();\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(nextCaret.origin)) {\n      continue;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(nextCaret.origin)) {\n      return true;\n    }\n    break;\n  }\n  return false;\n}\n\n/**\n * Moves the selection according to the arguments.\n * @param selection - The selected text or nodes.\n * @param isHoldingShift - Is the shift key being held down during the operation.\n * @param isBackward - Is the selection selected backwards (the focus comes before the anchor)?\n * @param granularity - The distance to adjust the current selection.\n */\nfunction $moveCaretSelection(selection, isHoldingShift, isBackward, granularity) {\n  selection.modify(isHoldingShift ? 'extend' : 'move', isBackward, granularity);\n}\n\n/**\n * Tests a parent element for right to left direction.\n * @param selection - The selection whose parent is to be tested.\n * @returns true if the selections' parent element has a direction of 'rtl' (right to left), false otherwise.\n */\nfunction $isParentElementRTL(selection) {\n  const anchorNode = selection.anchor.getNode();\n  const parent = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(anchorNode) ? anchorNode : anchorNode.getParentOrThrow();\n  return parent.getDirection() === 'rtl';\n}\n\n/**\n * Moves selection by character according to arguments.\n * @param selection - The selection of the characters to move.\n * @param isHoldingShift - Is the shift key being held down during the operation.\n * @param isBackward - Is the selection backward (the focus comes before the anchor)?\n */\nfunction $moveCharacter(selection, isHoldingShift, isBackward) {\n  const isRTL = $isParentElementRTL(selection);\n  $moveCaretSelection(selection, isHoldingShift, isBackward ? !isRTL : isRTL, 'character');\n}\n\n/**\n * Returns the current value of a CSS property for Nodes, if set. If not set, it returns the defaultValue.\n * @param node - The node whose style value to get.\n * @param styleProperty - The CSS style property.\n * @param defaultValue - The default value for the property.\n * @returns The value of the property for node.\n */\nfunction $getNodeStyleValueForProperty(node, styleProperty, defaultValue) {\n  const css = node.getStyle();\n  const styleObject = getStyleObjectFromCSS(css);\n  if (styleObject !== null) {\n    return styleObject[styleProperty] || defaultValue;\n  }\n  return defaultValue;\n}\n\n/**\n * Returns the current value of a CSS property for TextNodes in the Selection, if set. If not set, it returns the defaultValue.\n * If all TextNodes do not have the same value, it returns an empty string.\n * @param selection - The selection of TextNodes whose value to find.\n * @param styleProperty - The CSS style property.\n * @param defaultValue - The default value for the property, defaults to an empty string.\n * @returns The value of the property for the selected TextNodes.\n */\nfunction $getSelectionStyleValueForProperty(selection, styleProperty, defaultValue = '') {\n  let styleValue = null;\n  const nodes = selection.getNodes();\n  const anchor = selection.anchor;\n  const focus = selection.focus;\n  const isBackward = selection.isBackward();\n  const endOffset = isBackward ? focus.offset : anchor.offset;\n  const endNode = isBackward ? focus.getNode() : anchor.getNode();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && selection.isCollapsed() && selection.style !== '') {\n    const css = selection.style;\n    const styleObject = getStyleObjectFromCSS(css);\n    if (styleObject !== null && styleProperty in styleObject) {\n      return styleObject[styleProperty];\n    }\n  }\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n\n    // if no actual characters in the end node are selected, we don't\n    // include it in the selection for purposes of determining style\n    // value\n    if (i !== 0 && endOffset === 0 && node.is(endNode)) {\n      continue;\n    }\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n      const nodeStyleValue = $getNodeStyleValueForProperty(node, styleProperty, defaultValue);\n      if (styleValue === null) {\n        styleValue = nodeStyleValue;\n      } else if (styleValue !== nodeStyleValue) {\n        // multiple text nodes are in the selection and they don't all\n        // have the same style.\n        styleValue = '';\n        break;\n      }\n    }\n  }\n  return styleValue === null ? defaultValue : styleValue;\n}\nfunction $getAncestor(node, predicate) {\n  let parent = node;\n  while (parent !== null && parent.getParent() !== null && !predicate(parent)) {\n    parent = parent.getParentOrThrow();\n  }\n  return predicate(parent) ? parent : null;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/** @deprecated renamed to {@link $trimTextContentFromAnchor} by @lexical/eslint-plugin rules-of-lexical */\nconst trimTextContentFromAnchor = $trimTextContentFromAnchor;\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\n");

/***/ })

};
;