self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"40a3b2965ab6d0d25c84b6d79c0cc2b4d0062fa01b\": {\n      \"workers\": {\n        \"app/(payload)/admin/[[...segments]]/page\": {\n          \"moduleId\": \"(rsc)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5Cprojects%5C%5Clms%5C%5Capps%5C%5Capi%5C%5Csrc%5C%5Capp%5C%5C(payload)%5C%5Clayout.tsx%22%2C%5B%7B%22id%22%3A%2240a3b2965ab6d0d25c84b6d79c0cc2b4d0062fa01b%22%2C%22exportedName%22%3A%22%24%24RSC_SERVER_ACTION_0%22%7D%5D%5D%2C%5B%22C%3A%5C%5Cprojects%5C%5Clms%5C%5Cnode_modules%5C%5C.pnpm%5C%5C%40payloadcms%2Bnext%403.43.0_%40types%2Breact%4019.1.0_graphql%4016.11.0_monaco-editor%400.52.2_next%4015.3.0__5kipoy5xwbqm355o7gona4yn6a%5C%5Cnode_modules%5C%5C%40payloadcms%5C%5Cnext%5C%5Cdist%5C%5Clayouts%5C%5CRoot%5C%5Cindex.js%22%2C%5B%7B%22id%22%3A%226050b2e3be40ac352bc35e6f155c1093fc860afb73%22%2C%22exportedName%22%3A%22%24%24RSC_SERVER_ACTION_0%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(payload)/admin/[[...segments]]/page\": \"rsc\"\n      }\n    },\n    \"6050b2e3be40ac352bc35e6f155c1093fc860afb73\": {\n      \"workers\": {\n        \"app/(payload)/admin/[[...segments]]/page\": {\n          \"moduleId\": \"(rsc)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/build/webpack/loaders/next-flight-action-entry-loader.js?actions=%5B%5B%22C%3A%5C%5Cprojects%5C%5Clms%5C%5Capps%5C%5Capi%5C%5Csrc%5C%5Capp%5C%5C(payload)%5C%5Clayout.tsx%22%2C%5B%7B%22id%22%3A%2240a3b2965ab6d0d25c84b6d79c0cc2b4d0062fa01b%22%2C%22exportedName%22%3A%22%24%24RSC_SERVER_ACTION_0%22%7D%5D%5D%2C%5B%22C%3A%5C%5Cprojects%5C%5Clms%5C%5Cnode_modules%5C%5C.pnpm%5C%5C%40payloadcms%2Bnext%403.43.0_%40types%2Breact%4019.1.0_graphql%4016.11.0_monaco-editor%400.52.2_next%4015.3.0__5kipoy5xwbqm355o7gona4yn6a%5C%5Cnode_modules%5C%5C%40payloadcms%5C%5Cnext%5C%5Cdist%5C%5Clayouts%5C%5CRoot%5C%5Cindex.js%22%2C%5B%7B%22id%22%3A%226050b2e3be40ac352bc35e6f155c1093fc860afb73%22%2C%22exportedName%22%3A%22%24%24RSC_SERVER_ACTION_0%22%7D%5D%5D%5D&__client_imported__=!\",\n          \"async\": true\n        }\n      },\n      \"layer\": {\n        \"app/(payload)/admin/[[...segments]]/page\": \"rsc\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"process.env.NEXT_SERVER_ACTIONS_ENCRYPTION_KEY\"\n}"