import { CollectionConfig } from 'payload/types'
import { isAdmin, isInstituteAdmin } from '../access/index'

const Roles: CollectionConfig = {
  slug: 'roles',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'level', 'isActive', 'createdAt'],
    group: 'Access Control',
  },
  access: {
    read: ({ req: { user } }) => {
      // Allow reading roles if no user (during first user creation)
      if (!user) return true

      // Use legacyRole field for access control (consistent with Users collection)
      if (user.legacyRole === 'super_admin') return true

      if (user.legacyRole === 'institute_admin') {
        return {
          or: [
            { level: { equals: '2' } },
            { level: { equals: '3' } },
            { 'scope.institute': { equals: user.institute } }
          ]
        }
      }

      return false
    },
    create: ({ req: { user } }) => {
      if (!user) return false
      // Use legacyRole field for access control
      return user.legacyRole === 'super_admin' || user.legacyRole === 'institute_admin'
    },
    update: ({ req: { user } }) => {
      if (!user) return false
      // Use legacyRole field for access control
      return user.legacyRole === 'super_admin' || user.legacyRole === 'institute_admin'
    },
    delete: ({ req: { user } }) => {
      if (!user) return false
      // Use legacyRole field for access control
      return user.legacyRole === 'super_admin'
    }
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      required: true,
      unique: true,
      validate: (val) => {
        if (!val || val.length < 2) return 'Role name must be at least 2 characters'
        if (val.length > 50) return 'Role name must be less than 50 characters'
        return true
      }
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      validate: (val) => {
        if (!val) return 'Role code is required'
        if (!/^[a-z_]+$/.test(val)) return 'Role code must contain only lowercase letters and underscores'
        return true
      }
    },
    {
      name: 'description',
      type: 'textarea',
      maxLength: 500
    },
    {
      name: 'level',
      type: 'select',
      required: true,
      options: [
        { label: 'Level 1 - Super Admin', value: '1' },
        { label: 'Level 2 - Institute Admin', value: '2' },
        { label: 'Level 3 - Branch/User Level', value: '3' }
      ],
      defaultValue: '3'
    },
    {
      name: 'permissions',
      type: 'array',
      fields: [
        {
          name: 'permission',
          type: 'relationship',
          relationTo: 'permissions',
          required: true
        },
        {
          name: 'scope',
          type: 'select',
          options: [
            { label: 'Platform', value: 'platform' },
            { label: 'Institute', value: 'institute' },
            { label: 'Branch', value: 'branch' },
            { label: 'Self', value: 'self' }
          ],
          defaultValue: 'self'
        }
      ]
    },
    {
      name: 'scope',
      type: 'group',
      fields: [
        {
          name: 'institute',
          type: 'relationship',
          relationTo: 'institutes',
          admin: {
            condition: (data) => data.level >= 2
          }
        },
        {
          name: 'branch',
          type: 'relationship',
          relationTo: 'branches',
          admin: {
            condition: (data) => data.level === 3
          }
        }
      ]
    },
    {
      name: 'isSystemRole',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        readOnly: true,
        description: 'System roles cannot be deleted'
      }
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true
    },
    {
      name: 'metadata',
      type: 'group',
      fields: [
        {
          name: 'maxUsers',
          type: 'number',
          min: 0,
          admin: {
            description: 'Maximum number of users that can have this role (0 = unlimited)'
          }
        },
        {
          name: 'autoAssign',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Automatically assign this role to new users'
          }
        },
        {
          name: 'requiresApproval',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Role assignment requires approval'
          }
        }
      ]
    }
  ],
  hooks: {
    beforeChange: [
      ({ data, req }) => {
        // Set scope based on user creating the role
        if (req.user?.role === 'institute_admin' && !data.scope?.institute) {
          data.scope = {
            ...data.scope,
            institute: req.user.institute
          }
        }
        
        // Ensure level restrictions
        if (req.user?.role === 'institute_admin' && data.level === 1) {
          throw new Error('Institute admins cannot create Level 1 roles')
        }
        
        return data
      }
    ]
  },
  timestamps: true,
}

export default Roles
