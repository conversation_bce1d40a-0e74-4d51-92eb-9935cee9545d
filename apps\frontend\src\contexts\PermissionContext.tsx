'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { 
  getUserPermissions, 
  hasPermission, 
  canAccessNavigation,
  canPerformAction,
  getAllowedActions,
  filterNavigationByPermissions,
  UserPermissions,
  Permission
} from '@/utils/permissions'

interface PermissionContextType {
  userPermissions: UserPermissions
  hasPermission: (permission: string, resource?: string, scope?: 'global' | 'institute' | 'department' | 'own') => boolean
  canAccessNavigation: (navigationId: string) => boolean
  canPerformAction: (action: 'create' | 'read' | 'update' | 'delete', resource: string, scope?: 'global' | 'institute' | 'department' | 'own') => boolean
  getAllowedActions: (resource: string) => string[]
  filterNavigationByPermissions: (navigationItems: any[]) => any[]
  isLoading: boolean
  refreshPermissions: () => Promise<void>
}

const PermissionContext = createContext<PermissionContextType | undefined>(undefined)

interface PermissionProviderProps {
  children: React.ReactNode
}

export function PermissionProvider({ children }: PermissionProviderProps) {
  const { user, isAuthenticated } = useAuthStore()
  const [userPermissions, setUserPermissions] = useState<UserPermissions>({
    role: 'guest',
    level: 5,
    permissions: [],
    department: undefined,
    instituteId: undefined
  })
  const [isLoading, setIsLoading] = useState(true)

  // Update permissions when user changes
  useEffect(() => {
    if (isAuthenticated && user) {
      const permissions = getUserPermissions(user)
      setUserPermissions(permissions)
      setIsLoading(false)
    } else {
      setUserPermissions({
        role: 'guest',
        level: 5,
        permissions: [],
        department: undefined,
        instituteId: undefined
      })
      setIsLoading(false)
    }
  }, [user, isAuthenticated])

  // Refresh permissions from server
  const refreshPermissions = async () => {
    if (!user) return

    setIsLoading(true)
    try {
      // Fetch updated user data with permissions
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/auth/verify`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,
          'Content-Type': 'application/json'
        },
        credentials: 'include'
      })

      const data = await response.json()

      if (data.success && data.user) {
        const permissions = getUserPermissions(data.user)
        setUserPermissions(permissions)
      }
    } catch (error) {
      console.error('Error refreshing permissions:', error)
    } finally {
      setIsLoading(false)
    }
  }

  // Permission checking functions
  const checkPermission = (
    permission: string, 
    resource?: string, 
    scope?: 'global' | 'institute' | 'department' | 'own'
  ) => {
    return hasPermission(userPermissions, permission, resource, scope)
  }

  const checkNavigationAccess = (navigationId: string) => {
    return canAccessNavigation(userPermissions, navigationId)
  }

  const checkActionPermission = (
    action: 'create' | 'read' | 'update' | 'delete',
    resource: string,
    scope?: 'global' | 'institute' | 'department' | 'own'
  ) => {
    return canPerformAction(userPermissions, action, resource, scope)
  }

  const getResourceActions = (resource: string) => {
    return getAllowedActions(userPermissions, resource)
  }

  const filterNavigation = (navigationItems: any[]) => {
    return filterNavigationByPermissions(navigationItems, userPermissions)
  }

  const contextValue: PermissionContextType = {
    userPermissions,
    hasPermission: checkPermission,
    canAccessNavigation: checkNavigationAccess,
    canPerformAction: checkActionPermission,
    getAllowedActions: getResourceActions,
    filterNavigationByPermissions: filterNavigation,
    isLoading,
    refreshPermissions
  }

  return (
    <PermissionContext.Provider value={contextValue}>
      {children}
    </PermissionContext.Provider>
  )
}

// Hook to use permission context
export function usePermissions() {
  const context = useContext(PermissionContext)
  if (context === undefined) {
    throw new Error('usePermissions must be used within a PermissionProvider')
  }
  return context
}

// Higher-order component for permission-based rendering
interface WithPermissionProps {
  permission: string
  resource?: string
  scope?: 'global' | 'institute' | 'department' | 'own'
  fallback?: React.ReactNode
  children: React.ReactNode
}

export function WithPermission({ 
  permission, 
  resource, 
  scope, 
  fallback = null, 
  children 
}: WithPermissionProps) {
  const { hasPermission } = usePermissions()
  
  if (hasPermission(permission, resource, scope)) {
    return <>{children}</>
  }
  
  return <>{fallback}</>
}

// Component for action-based permission checking
interface WithActionPermissionProps {
  action: 'create' | 'read' | 'update' | 'delete'
  resource: string
  scope?: 'global' | 'institute' | 'department' | 'own'
  fallback?: React.ReactNode
  children: React.ReactNode
}

export function WithActionPermission({ 
  action, 
  resource, 
  scope, 
  fallback = null, 
  children 
}: WithActionPermissionProps) {
  const { canPerformAction } = usePermissions()
  
  if (canPerformAction(action, resource, scope)) {
    return <>{children}</>
  }
  
  return <>{fallback}</>
}

// Component for navigation access checking
interface WithNavigationAccessProps {
  navigationId: string
  fallback?: React.ReactNode
  children: React.ReactNode
}

export function WithNavigationAccess({ 
  navigationId, 
  fallback = null, 
  children 
}: WithNavigationAccessProps) {
  const { canAccessNavigation } = usePermissions()
  
  if (canAccessNavigation(navigationId)) {
    return <>{children}</>
  }
  
  return <>{fallback}</>
}

// Hook for checking multiple permissions
export function useMultiplePermissions(permissions: Array<{
  permission: string
  resource?: string
  scope?: 'global' | 'institute' | 'department' | 'own'
}>) {
  const { hasPermission } = usePermissions()
  
  return permissions.map(({ permission, resource, scope }) => ({
    permission,
    resource,
    scope,
    hasAccess: hasPermission(permission, resource, scope)
  }))
}

// Hook for checking role-based access
export function useRoleAccess() {
  const { userPermissions } = usePermissions()
  
  return {
    isSuperAdmin: userPermissions.role === 'super_admin',
    isInstituteAdmin: userPermissions.role === 'institute_admin',
    isStudent: userPermissions.role === 'student',
    role: userPermissions.role,
    level: userPermissions.level,
    department: userPermissions.department,
    instituteId: userPermissions.instituteId
  }
}

export default PermissionProvider
