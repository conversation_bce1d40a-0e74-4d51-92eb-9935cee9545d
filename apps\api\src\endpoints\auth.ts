import type { Endpoint } from 'payload'
import jwt from 'jsonwebtoken'
import bcrypt from 'bcrypt'
import { createCorsResponse, createCorsErrorResponse } from '../utils/cors'

// Login endpoint for all user types
export const loginEndpoint: Endpoint = {
  path: '/auth/login',
  method: 'post',
  handler: async (req) => {
    const body = req.json ? await req.json() : req.body
    const { email, password, userType } = body || {}

    if (!email || !password) {
      return createCorsErrorResponse('Email and password are required', 400)
    }

   
      // Use Payload's built-in login method
      const result = await req.payload.login({
        collection: 'users',
        data: {
          email: email.toLowerCase(),
          password,
        },
      })

      if (!result.user) {
        return createCorsErrorResponse('Invalid email or password', 401)
      }

      const user = result.user

      // Check if user is active
      if (!user.isActive) {
        return createCorsErrorResponse('Account is deactivated. Please contact support.', 401)
      }

      // Check user type/role authorization using legacyRole field
      const allowedRoles: Record<string, string[]> = {
        super_admin: ['super_admin', 'platform_staff'],
        institute_admin: ['institute_admin', 'branch_manager', 'trainer', 'institute_staff'],
        student: ['student']
      }

      // Use legacyRole field for authorization (role field is now a relationship)
      if (userType && allowedRoles[userType] && !allowedRoles[userType].includes(user.legacyRole)) {
        return Response.json(
          { message: 'Unauthorized access for this user type' },
          { status: 403 }
        )
      }

      // Generate JWT token
      const jwtSecret = process.env.JWT_SECRET || process.env.PAYLOAD_SECRET
      if (!jwtSecret) {
        throw new Error('JWT secret not configured')
      }

      const token = jwt.sign(
        {
          id: user.id,
          email: user.email,
          role: user.legacyRole, // Use legacyRole for JWT token
          legacyRole: user.legacyRole // Include both for compatibility
        },
        jwtSecret,
        { expiresIn: '24h' }
      )

      // Update last login
      await req.payload.update({
        collection: 'users',
        id: user.id,
        data: {
          lastLogin: new Date().toISOString(),
        },
      })

      // Return user data (excluding password) and token
      const { password: _, ...userWithoutPassword } = user

      return createCorsResponse({
        message: 'Login successful',
        user: userWithoutPassword,
        token,
      })

    
  },
}

// Institute registration endpoint
export const registerInstituteEndpoint: Endpoint = {
  path: '/auth/register',
  method: 'post',
  handler: async (req) => {
    const body = req.json ? await req.json() : req.body
    const {
      instituteName,
      slug,
      email,
      phone,
      website,
      description,
      adminFirstName,
      adminLastName,
      adminEmail,
      adminPhone,
      password,
      city,
      state,
      country
    } = body || {}

    // Validation
    if (!instituteName || !slug || !email || !adminFirstName || !adminLastName || !adminEmail || !password) {
      return Response.json(
        { message: 'Required fields are missing' },
        { status: 400 }
      )
    }

    try {
      // Check if institute slug already exists
      const existingInstitutes = await req.payload.find({
        collection: 'institutes',
        where: {
          slug: {
            equals: slug,
          },
        },
      })

      if (existingInstitutes.docs.length > 0) {
        return Response.json(
          { message: 'Institute slug already exists' },
          { status: 400 }
        )
      }

      // Check if admin email already exists
      const existingUsers = await req.payload.find({
        collection: 'users',
        where: {
          email: {
            equals: adminEmail.toLowerCase(),
          },
        },
      })

      if (existingUsers.docs.length > 0) {
        return Response.json(
          { message: 'Admin email already exists' },
          { status: 400 }
        )
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10)

      // Create institute
      const institute = await req.payload.create({
        collection: 'institutes',
        data: {
          name: instituteName,
          slug,
          email,
          phone,
          website,
          description,
          address: {
            city,
            state,
            country: country || 'India',
          },
          subscriptionPlan: 'free_trial',
          subscriptionStatus: 'active',
          subscriptionExpiry: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days
          maxStudents: 100,
          maxCourses: 10,
          maxBranches: 1,
          features: {
            marketplace: false,
            liveClasses: false,
            exams: true,
            blogs: false,
            analytics: true,
          },
          isActive: true,
        },
      })

      // Create admin user
      const adminUser = await req.payload.create({
        collection: 'users',
        data: {
          email: adminEmail.toLowerCase(),
          password: hashedPassword,
          firstName: adminFirstName,
          lastName: adminLastName,
          phone: adminPhone,
          legacyRole: 'institute_admin',
          institute: institute.id,
          isActive: true,
          emailVerified: false,
        },
      })

      // Update institute with created by
      await req.payload.update({
        collection: 'institutes',
        id: institute.id,
        data: {
          createdBy: adminUser.id,
        },
      })

      return Response.json({
        message: 'Institute registered successfully',
        institute: {
          id: institute.id,
          name: institute.name,
          slug: institute.slug,
        },
        adminUser: {
          id: adminUser.id,
          email: adminUser.email,
          firstName: adminUser.firstName,
          lastName: adminUser.lastName,
        },
      })

    } catch (error) {
      console.error('Registration error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Student registration endpoint
export const registerStudentEndpoint: Endpoint = {
  path: '/auth/user-register',
  method: 'post',
  handler: async (req) => {
    const body = req.json ? await req.json() : req.body
    const {
      firstName,
      lastName,
      email,
      phone,
      password,
      role = 'student'
    } = body || {}

    // Validation
    if (!firstName || !lastName || !email || !password) {
      return Response.json(
        { message: 'Required fields are missing' },
        { status: 400 }
      )
    }

    try {
      // Check if email already exists
      const existingUsers = await req.payload.find({
        collection: 'users',
        where: {
          email: {
            equals: email.toLowerCase(),
          },
        },
      })

      if (existingUsers.docs.length > 0) {
        return Response.json(
          { message: 'Email already exists' },
          { status: 400 }
        )
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10)

      // Create student user
      const student = await req.payload.create({
        collection: 'users',
        data: {
          email: email.toLowerCase(),
          password: hashedPassword,
          firstName,
          lastName,
          phone,
          legacyRole: 'student',
          isActive: true,
          emailVerified: false,
        },
      })

      // Remove password from response
      const { password: _, ...studentWithoutPassword } = student

      return Response.json({
        message: 'Student registered successfully',
        user: studentWithoutPassword,
      })

    } catch (error) {
      console.error('Student registration error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Token refresh endpoint
export const refreshTokenEndpoint: Endpoint = {
  path: '/auth/refresh-token',
  method: 'post',
  handler: async (req) => {
    const authHeader = req.headers.get('authorization')
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return Response.json(
        { message: 'Authorization token required' },
        { status: 401 }
      )
    }

    const token = authHeader.substring(7)

    try {
      // Verify current token
      const jwtSecret = process.env.JWT_SECRET || process.env.PAYLOAD_SECRET
      if (!jwtSecret) {
        throw new Error('JWT secret not configured')
      }

      const decoded = jwt.verify(token, jwtSecret) as any

      // Get fresh user data
      const user = await req.payload.findByID({
        collection: 'users',
        id: decoded.id,
      })

      if (!user || !user.isActive) {
        return Response.json(
          { message: 'User not found or inactive' },
          { status: 401 }
        )
      }

      // Generate new token
      const newToken = jwt.sign(
        {
          id: user.id,
          email: user.email,
          role: user.legacyRole, // Use legacyRole for JWT token
          legacyRole: user.legacyRole // Include both for compatibility
        },
        jwtSecret,
        { expiresIn: '24h' }
      )

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user

      return Response.json({
        message: 'Token refreshed successfully',
        user: userWithoutPassword,
        token: newToken,
      })

    } catch (error) {
      console.error('Token refresh error:', error)
      return Response.json(
        { message: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  },
}

// Verify token endpoint
export const verifyTokenEndpoint: Endpoint = {
  path: '/auth/verify',
  method: 'post',
  handler: async (req) => {
    try {
      const authHeader = req.headers.get('authorization')
      const token = authHeader?.replace('Bearer ', '')

      if (!token) {
        return Response.json(
          { success: false, message: 'No token provided' },
          { status: 401 }
        )
      }

      // Verify the JWT token using the same secret as login
      const jwtSecret = process.env.JWT_SECRET || process.env.PAYLOAD_SECRET
      if (!jwtSecret) {
        throw new Error('JWT secret not configured')
      }

      const decoded = jwt.verify(token, jwtSecret) as any

      // Find the user
      const user = await req.payload.findByID({
        collection: 'users',
        id: decoded.id
      })

      if (!user) {
        return Response.json(
          { success: false, message: 'User not found' },
          { status: 401 }
        )
      }

      // Remove password from response
      const { password, ...userWithoutPassword } = user as any

      return Response.json({
        success: true,
        user: userWithoutPassword,
        message: 'Token is valid'
      })

    } catch (error) {
      console.error('Token verification error:', error)
      return Response.json(
        { success: false, message: 'Invalid or expired token' },
        { status: 401 }
      )
    }
  }
}
