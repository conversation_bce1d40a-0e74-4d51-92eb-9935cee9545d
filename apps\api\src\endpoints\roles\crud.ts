import { Endpoint } from 'payload/config'

const rolesCrudEndpoints: Endpoint[] = [
  // Create Role
  {
    path: '/roles',
    method: 'post',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser || (currentUser.role !== 'super_admin' && currentUser.role !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can create roles'
          })
        }

        const { name, code, description, level, permissions, scope, metadata, isActive } = req.body

        // Validate level restrictions
        if (currentUser.role === 'institute_admin' && level === 1) {
          return res.status(403).json({
            error: 'Institute admins cannot create Level 1 roles'
          })
        }

        // Set scope for institute admins
        let roleScope = scope
        if (currentUser.role === 'institute_admin') {
          roleScope = {
            ...roleScope,
            institute: currentUser.institute
          }
        }

        const roleData = {
          name,
          code,
          description,
          level,
          permissions: permissions || [],
          scope: roleScope,
          metadata: metadata || {
            maxUsers: 0,
            autoAssign: false,
            requiresApproval: false
          },
          isActive: isActive !== undefined ? isActive : true,
          isSystemRole: false
        }

        const role = await req.payload.create({
          collection: 'roles',
          data: roleData
        })

        res.json({
          success: true,
          role,
          message: 'Role created successfully'
        })

      } catch (error) {
        console.error('Create role error:', error)
        res.status(500).json({
          error: error.message || 'Failed to create role'
        })
      }
    }
  },

  // Get Roles (with pagination and filters)
  {
    path: '/roles',
    method: 'get',
    handler: async (req, res) => {
      try {
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const {
          page = 1,
          limit = 20,
          search = '',
          level = '',
          isActive = '',
          category = ''
        } = req.query

        // Build where clause
        const where: any = {}

        // Search filter
        if (search) {
          where.or = [
            { name: { contains: search } },
            { code: { contains: search } },
            { description: { contains: search } }
          ]
        }

        // Level filter
        if (level) {
          where.level = { equals: parseInt(level as string) }
        }

        // Active filter
        if (isActive !== '') {
          where.isActive = { equals: isActive === 'true' }
        }

        // Category filter (system vs custom)
        if (category === 'system') {
          where.isSystemRole = { equals: true }
        } else if (category === 'custom') {
          where.isSystemRole = { equals: false }
        }

        // Scope restrictions for institute admins
        if (currentUser.role === 'institute_admin') {
          where.and = [
            {
              or: [
                { level: { greater_than_equal: 2 } },
                { 'scope.institute': { equals: currentUser.institute } },
                { 'scope.institute': { exists: false } }
              ]
            }
          ]
        }

        const roles = await req.payload.find({
          collection: 'roles',
          where,
          page: parseInt(page as string),
          limit: parseInt(limit as string),
          sort: '-createdAt',
          populate: [
            'permissions.permission',
            'scope.institute',
            'scope.branch'
          ]
        })

        res.json({
          success: true,
          ...roles
        })

      } catch (error) {
        console.error('Get roles error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch roles'
        })
      }
    }
  },

  // Get Single Role
  {
    path: '/roles/:id',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const role = await req.payload.findByID({
          collection: 'roles',
          id,
          populate: [
            'permissions.permission',
            'scope.institute',
            'scope.branch'
          ]
        })

        if (!role) {
          return res.status(404).json({
            error: 'Role not found'
          })
        }

        // Check access permissions
        if (currentUser.role === 'institute_admin') {
          if (role.level === 1 || (role.scope?.institute && role.scope.institute !== currentUser.institute)) {
            return res.status(403).json({
              error: 'Access denied'
            })
          }
        }

        res.json({
          success: true,
          role
        })

      } catch (error) {
        console.error('Get role error:', error)
        res.status(500).json({
          error: error.message || 'Failed to fetch role'
        })
      }
    }
  },

  // Update Role
  {
    path: '/roles/:id',
    method: 'patch',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser || (currentUser.role !== 'super_admin' && currentUser.role !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can update roles'
          })
        }

        // Get existing role
        const existingRole = await req.payload.findByID({
          collection: 'roles',
          id
        })

        if (!existingRole) {
          return res.status(404).json({
            error: 'Role not found'
          })
        }

        // Check permissions
        if (currentUser.role === 'institute_admin') {
          if (existingRole.level === 1 || existingRole.isSystemRole) {
            return res.status(403).json({
              error: 'Cannot modify system roles or Level 1 roles'
            })
          }
        }

        const updateData = { ...req.body }

        // Prevent level escalation for institute admins
        if (currentUser.role === 'institute_admin' && updateData.level === 1) {
          return res.status(403).json({
            error: 'Institute admins cannot create Level 1 roles'
          })
        }

        // Prevent system role modification
        if (existingRole.isSystemRole) {
          delete updateData.isSystemRole
          delete updateData.code // Prevent code changes for system roles
        }

        const role = await req.payload.update({
          collection: 'roles',
          id,
          data: updateData
        })

        res.json({
          success: true,
          role,
          message: 'Role updated successfully'
        })

      } catch (error) {
        console.error('Update role error:', error)
        res.status(500).json({
          error: error.message || 'Failed to update role'
        })
      }
    }
  },

  // Delete Role
  {
    path: '/roles/:id',
    method: 'delete',
    handler: async (req, res) => {
      try {
        const { id } = req.params
        const currentUser = req.user

        if (!currentUser || currentUser.role !== 'super_admin') {
          return res.status(403).json({
            error: 'Only super admins can delete roles'
          })
        }

        // Get existing role
        const existingRole = await req.payload.findByID({
          collection: 'roles',
          id
        })

        if (!existingRole) {
          return res.status(404).json({
            error: 'Role not found'
          })
        }

        // Prevent system role deletion
        if (existingRole.isSystemRole) {
          return res.status(403).json({
            error: 'Cannot delete system roles'
          })
        }

        // Check if role is in use
        const usersWithRole = await req.payload.find({
          collection: 'users',
          where: { role: { equals: id } },
          limit: 1
        })

        if (usersWithRole.totalDocs > 0) {
          return res.status(400).json({
            error: 'Cannot delete role that is assigned to users'
          })
        }

        await req.payload.delete({
          collection: 'roles',
          id
        })

        res.json({
          success: true,
          message: 'Role deleted successfully'
        })

      } catch (error) {
        console.error('Delete role error:', error)
        res.status(500).json({
          error: error.message || 'Failed to delete role'
        })
      }
    }
  }
]

export default rolesCrudEndpoints
