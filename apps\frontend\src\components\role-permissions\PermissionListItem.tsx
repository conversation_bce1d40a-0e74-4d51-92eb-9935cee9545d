'use client'

import { Permission } from '@/stores/useRolePermissionsStore'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { PermissionForm } from './PermissionForm'
import { 
  Key, 
  Edit, 
  Trash2, 
  Eye,
  MoreVertical
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'

interface PermissionListItemProps {
  permission: Permission
  onSelect: (permission: Permission) => void
}

export function PermissionListItem({ permission, onSelect }: PermissionListItemProps) {
  const { deletePermission, fetchPermissions } = useRolePermissionsStore()

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete the permission "${permission.name}"?`)) {
      const success = await deletePermission(permission.id)
      if (success) {
        fetchPermissions()
      }
    }
  }

  const getResourceColor = (resource: string) => {
    const colors: Record<string, string> = {
      'users': 'bg-blue-100 text-blue-800',
      'roles': 'bg-purple-100 text-purple-800',
      'permissions': 'bg-green-100 text-green-800',
      'institutes': 'bg-orange-100 text-orange-800',
      'courses': 'bg-yellow-100 text-yellow-800',
      'settings': 'bg-gray-100 text-gray-800',
      'analytics': 'bg-pink-100 text-pink-800',
      'billing': 'bg-red-100 text-red-800',
    }
    return colors[resource.toLowerCase()] || 'bg-gray-100 text-gray-800'
  }

  const getActionColor = (action: string) => {
    const colors: Record<string, string> = {
      'create': 'bg-green-100 text-green-800',
      'read': 'bg-blue-100 text-blue-800',
      'update': 'bg-yellow-100 text-yellow-800',
      'delete': 'bg-red-100 text-red-800',
      'manage': 'bg-purple-100 text-purple-800',
      'view': 'bg-gray-100 text-gray-800',
    }
    return colors[action.toLowerCase()] || 'bg-gray-100 text-gray-800'
  }

  return (
    <Card className="hover:shadow-sm transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Left side - Permission info */}
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-green-100 rounded-lg">
              <Key className="h-4 w-4 text-green-600" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-1">
                <h3 className="font-semibold text-gray-900 truncate">
                  {permission.name}
                </h3>
                <Badge className={getResourceColor(permission.resource)}>
                  {permission.resource}
                </Badge>
                <Badge className={getActionColor(permission.action)}>
                  {permission.action}
                </Badge>
                <Badge variant={permission.isActive ? "default" : "secondary"}>
                  {permission.isActive ? "Active" : "Inactive"}
                </Badge>
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>Code: {permission.code}</span>
                <span>Priority: {permission.priority}</span>
                {permission.scope && <span>Scope: {permission.scope}</span>}
              </div>
              
              {permission.description && (
                <p className="text-sm text-gray-600 mt-1 line-clamp-1">
                  {permission.description}
                </p>
              )}
            </div>
          </div>
          
          {/* Right side - Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSelect(permission)}
              className="gap-2"
            >
              <Eye className="h-4 w-4" />
              View
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <PermissionForm 
                  mode="edit" 
                  permission={permission}
                  trigger={
                    <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Permission
                    </DropdownMenuItem>
                  }
                  onSuccess={() => fetchPermissions()}
                />
                <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Permission
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
