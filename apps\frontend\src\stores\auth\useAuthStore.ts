import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface Role {
  id: number
  name: string
  code: string
  description?: string
  level: string
  permissions: any[]
  scope: {
    institute: number | null
    branch: number | null
  }
  isActive: boolean
  isSystemRole: boolean
}

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: Role | null // New role relationship object
  legacyRole: 'super_admin' | 'platform_staff' | 'institute_admin' | 'branch_manager' | 'trainer' | 'institute_staff' | 'student' // Legacy string role
  avatar?: string
  institute?: string
  isActive: boolean
  lastLogin?: string
}

interface AuthState {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean

  // Actions
  login: (email: string, password: string, userType?: string) => Promise<void>
  register: (userData: any, userType?: string) => Promise<void>
  logout: () => void
  setUser: (user: User | null) => void
  setToken: (token: string | null) => void
  setLoading: (loading: boolean) => void
  initialize: () => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isLoading: false, // Start with false to prevent infinite loading
      isAuthenticated: false,

      login: async (email: string, password: string, userType = 'student') => {
        set({ isLoading: true })

        try {
          // Use the auth login endpoint
          const endpoint = '/api/auth/login'

          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // Include cookies for cross-origin requests
            body: JSON.stringify({
              email,
              password,
              userType
            }),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.message || errorData.error || 'Login failed')
          }

          const data = await response.json()

          // Validate that the user has the expected role using legacyRole
          if (userType === 'super_admin' && !['super_admin', 'platform_staff'].includes(data.user.legacyRole)) {
            throw new Error('Access denied. Super admin privileges required.')
          }
          if (userType === 'institute_admin' && !['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(data.user.legacyRole)) {
            throw new Error('Access denied. Institute admin privileges required.')
          }
          if (userType === 'student' && data.user.legacyRole !== 'student') {
            throw new Error('Access denied. Student account required.')
          }

          // Store token in localStorage first
          if (data.token) {
            localStorage.setItem('auth_token', data.token)
          }

          // Then set the auth state
          set({
            user: data.user,
            token: data.token,
            isAuthenticated: true,
            isLoading: false
          })

          // Force persist to localStorage immediately
          setTimeout(() => {
            console.log('Auth state set after login:', {
              user: data.user.email,
              isAuthenticated: true
            })
          }, 0)

        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      register: async (userData: any, userType = 'student') => {
        set({ isLoading: true })

        try {
          // Determine the correct API endpoint based on user type
          let endpoint = '/api/auth/register'
          if (userType === 'institute') {
            endpoint = '/api/auth/register' // Institute registration endpoint
          }

          const requestData = {
            ...userData,
            role: userType === 'institute' ? 'institute_admin' : 'student'
          }

          // Debug: Log the data being sent
          console.log('Registration data being sent:', requestData)
          console.log('Endpoint:', `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`)

          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify(requestData),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.message || errorData.error || 'Registration failed')
          }

          const data = await response.json()

          // For institute registration, don't auto-login (needs approval)
          if (userType === 'institute') {
            set({ isLoading: false })
            return
          }

          // For student registration, auto-login
          set({
            user: data.user,
            token: data.token,
            isAuthenticated: true,
            isLoading: false
          })

          if (data.token) {
            localStorage.setItem('auth_token', data.token)
          }

        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false
        })
        
        // Clear localStorage
        localStorage.removeItem('auth_token')
        
        // Redirect based on user role using legacyRole
        const { user } = get()
        if (user?.legacyRole === 'super_admin' || user?.legacyRole === 'platform_staff') {
          window.location.href = '/auth/admin/login'
        } else if (user?.legacyRole === 'student') {
          window.location.href = '/auth/user-login'
        } else {
          window.location.href = '/auth/login'
        }
      },

      setUser: (user: User | null) => {
        set({ user, isAuthenticated: !!user })
      },

      setToken: (token: string | null) => {
        set({ token })
        if (token) {
          localStorage.setItem('auth_token', token)
        } else {
          localStorage.removeItem('auth_token')
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // Initialize auth state - check persisted state and localStorage
      initialize: () => {
        const currentState = get()
        console.log('🔄 Initialize called, current state:', {
          hasUser: !!currentState.user,
          hasToken: !!currentState.token,
          isAuthenticated: currentState.isAuthenticated,
          isLoading: currentState.isLoading
        })

        // Always set loading to false first
        set({ isLoading: false })

        // If we already have valid auth state, we're good
        if (currentState.user && currentState.token && currentState.isAuthenticated) {
          console.log('✅ Auth already initialized from persisted state:', currentState.user.email)
          return
        }

        // Check Zustand persistence storage
        const authStorage = localStorage.getItem('auth-storage')

        console.log('🔍 Checking Zustand auth-storage:', {
          hasAuthStorage: !!authStorage
        })

        if (authStorage) {
          try {
            const parsedStorage = JSON.parse(authStorage)
            const { state } = parsedStorage

            if (state && state.user && state.token && state.isAuthenticated) {
              console.log('📦 Restoring auth state from Zustand storage:', {
                email: state.user.email,
                legacyRole: state.user.legacyRole,
                hasToken: !!state.token
              })

              set({
                user: state.user,
                token: state.token,
                isAuthenticated: state.isAuthenticated,
                isLoading: false
              })
              return
            }
          } catch (error) {
            console.error('❌ Failed to parse Zustand auth storage:', error)
            localStorage.removeItem('auth-storage')
          }
        }

        // If no valid persisted state, set to not authenticated
        console.log('❌ No valid auth state found, setting to not authenticated')
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false
        })
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
