/**
 * API utility functions for making requests to the backend
 */

// Get the API base URL from environment variables
const getApiBaseUrl = (): string => {
  // Always use the full backend URL for API calls
  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'
}

/**
 * Create a full API URL from a relative path
 */
export const createApiUrl = (path: string): string => {
  const baseUrl = getApiBaseUrl()

  // Remove leading slash from path if present
  const cleanPath = path.startsWith('/') ? path.slice(1) : path

  // Combine base URL with path
  return `${baseUrl}/${cleanPath}`
}

/**
 * Get the auth token from localStorage or Zustand storage
 */
const getAuthToken = (): string | null => {
  if (typeof window !== 'undefined') {
    // First try direct auth_token
    let token = localStorage.getItem('auth_token')

    // If not found, try Zustand auth storage
    if (!token) {
      try {
        const authStorage = localStorage.getItem('auth-storage')
        if (authStorage) {
          const parsed = JSON.parse(authStorage)
          token = parsed?.state?.token || null
        }
      } catch (error) {
        console.error('Failed to parse auth storage:', error)
      }
    }

    console.log('🔍 getAuthToken:', { hasToken: !!token, tokenLength: token?.length })
    return token
  }
  return null
}

/**
 * Make an API request with proper error handling
 */
export const apiRequest = async (
  path: string,
  options: RequestInit = {}
): Promise<Response> => {
  const url = createApiUrl(path)
  const token = getAuthToken()

  const defaultOptions: RequestInit = {
    credentials: 'include',
    headers: {
      'Content-Type': 'application/json',
      ...(token && { 'Authorization': `Bearer ${token}` }),
      ...options.headers,
    },
  }



  const finalOptions = {
    ...defaultOptions,
    ...options,
    headers: {
      ...defaultOptions.headers,
      ...options.headers,
    },
  }

  console.log('🔍 API Request:', {
    url,
    method: finalOptions.method || 'GET',
    hasAuth: !!(finalOptions.headers as any)?.['Authorization']
  })

  try {
    const response = await fetch(url, finalOptions)
    console.log('✅ API Response:', {
      url,
      status: response.status,
      ok: response.ok
    })
    return response
  } catch (error) {
    console.error('❌ API request failed:', { url, error })
    throw error
  }
}

/**
 * Make a GET request to the API
 */
export const apiGet = async (path: string, params?: Record<string, string>): Promise<Response> => {
  let url = path
  
  if (params) {
    const searchParams = new URLSearchParams(params)
    url = `${path}?${searchParams.toString()}`
  }
  
  return apiRequest(url, { method: 'GET' })
}

/**
 * Make a POST request to the API
 */
export const apiPost = async (path: string, data?: any): Promise<Response> => {
  return apiRequest(path, {
    method: 'POST',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * Make a PUT request to the API
 */
export const apiPut = async (path: string, data?: any): Promise<Response> => {
  return apiRequest(path, {
    method: 'PUT',
    body: data ? JSON.stringify(data) : undefined,
  })
}

/**
 * Make a DELETE request to the API
 */
export const apiDelete = async (path: string): Promise<Response> => {
  return apiRequest(path, { method: 'DELETE' })
}

/**
 * Handle API response and extract JSON data
 */
export const handleApiResponse = async <T = any>(response: Response): Promise<T> => {
  if (!response.ok) {
    const errorData = await response.json().catch(() => ({}))
    throw new Error(errorData.message || errorData.error || `HTTP ${response.status}`)
  }
  
  return response.json()
}

/**
 * Make a complete API call with error handling and JSON parsing
 */
export const apiCall = async <T = any>(
  path: string,
  options: RequestInit = {}
): Promise<T> => {
  const response = await apiRequest(path, options)
  return handleApiResponse<T>(response)
}

/**
 * Convenience methods for common API operations
 */
export const api = {
  get: async <T = any>(path: string, params?: Record<string, string>): Promise<T> => {
    const response = await apiGet(path, params)
    return handleApiResponse<T>(response)
  },
  
  post: async <T = any>(path: string, data?: any): Promise<T> => {
    const response = await apiPost(path, data)
    return handleApiResponse<T>(response)
  },
  
  put: async <T = any>(path: string, data?: any): Promise<T> => {
    const response = await apiPut(path, data)
    return handleApiResponse<T>(response)
  },
  
  delete: async <T = any>(path: string): Promise<T> => {
    const response = await apiDelete(path)
    return handleApiResponse<T>(response)
  },
}
