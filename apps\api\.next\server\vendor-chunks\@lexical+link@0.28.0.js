"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+link@0.28.0";
exports.ids = ["vendor-chunks/@lexical+link@0.28.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+link@0.28.0/node_modules/@lexical/link/LexicalLink.dev.mjs":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+link@0.28.0/node_modules/@lexical/link/LexicalLink.dev.mjs ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $createAutoLinkNode: () => (/* binding */ $createAutoLinkNode),\n/* harmony export */   $createLinkNode: () => (/* binding */ $createLinkNode),\n/* harmony export */   $isAutoLinkNode: () => (/* binding */ $isAutoLinkNode),\n/* harmony export */   $isLinkNode: () => (/* binding */ $isLinkNode),\n/* harmony export */   $toggleLink: () => (/* binding */ $toggleLink),\n/* harmony export */   AutoLinkNode: () => (/* binding */ AutoLinkNode),\n/* harmony export */   LinkNode: () => (/* binding */ LinkNode),\n/* harmony export */   TOGGLE_LINK_COMMAND: () => (/* binding */ TOGGLE_LINK_COMMAND),\n/* harmony export */   toggleLink: () => (/* binding */ toggleLink)\n/* harmony export */ });\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\nconst SUPPORTED_URL_PROTOCOLS = new Set(['http:', 'https:', 'mailto:', 'sms:', 'tel:']);\n\n/** @noInheritDoc */\nclass LinkNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  /** @internal */\n\n  static getType() {\n    return 'link';\n  }\n  static clone(node) {\n    return new LinkNode(node.__url, {\n      rel: node.__rel,\n      target: node.__target,\n      title: node.__title\n    }, node.__key);\n  }\n  constructor(url = '', attributes = {}, key) {\n    super(key);\n    const {\n      target = null,\n      rel = null,\n      title = null\n    } = attributes;\n    this.__url = url;\n    this.__target = target;\n    this.__rel = rel;\n    this.__title = title;\n  }\n  createDOM(config) {\n    const element = document.createElement('a');\n    element.href = this.sanitizeUrl(this.__url);\n    if (this.__target !== null) {\n      element.target = this.__target;\n    }\n    if (this.__rel !== null) {\n      element.rel = this.__rel;\n    }\n    if (this.__title !== null) {\n      element.title = this.__title;\n    }\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, config.theme.link);\n    return element;\n  }\n  updateDOM(prevNode, anchor, config) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLAnchorElement)(anchor)) {\n      const url = this.__url;\n      const target = this.__target;\n      const rel = this.__rel;\n      const title = this.__title;\n      if (url !== prevNode.__url) {\n        anchor.href = url;\n      }\n      if (target !== prevNode.__target) {\n        if (target) {\n          anchor.target = target;\n        } else {\n          anchor.removeAttribute('target');\n        }\n      }\n      if (rel !== prevNode.__rel) {\n        if (rel) {\n          anchor.rel = rel;\n        } else {\n          anchor.removeAttribute('rel');\n        }\n      }\n      if (title !== prevNode.__title) {\n        if (title) {\n          anchor.title = title;\n        } else {\n          anchor.removeAttribute('title');\n        }\n      }\n    }\n    return false;\n  }\n  static importDOM() {\n    return {\n      a: node => ({\n        conversion: $convertAnchorElement,\n        priority: 1\n      })\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createLinkNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setURL(serializedNode.url).setRel(serializedNode.rel || null).setTarget(serializedNode.target || null).setTitle(serializedNode.title || null);\n  }\n  sanitizeUrl(url) {\n    try {\n      const parsedUrl = new URL(url);\n      // eslint-disable-next-line no-script-url\n      if (!SUPPORTED_URL_PROTOCOLS.has(parsedUrl.protocol)) {\n        return 'about:blank';\n      }\n    } catch (_unused) {\n      return url;\n    }\n    return url;\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      rel: this.getRel(),\n      target: this.getTarget(),\n      title: this.getTitle(),\n      url: this.getURL()\n    };\n  }\n  getURL() {\n    return this.getLatest().__url;\n  }\n  setURL(url) {\n    const writable = this.getWritable();\n    writable.__url = url;\n    return writable;\n  }\n  getTarget() {\n    return this.getLatest().__target;\n  }\n  setTarget(target) {\n    const writable = this.getWritable();\n    writable.__target = target;\n    return writable;\n  }\n  getRel() {\n    return this.getLatest().__rel;\n  }\n  setRel(rel) {\n    const writable = this.getWritable();\n    writable.__rel = rel;\n    return writable;\n  }\n  getTitle() {\n    return this.getLatest().__title;\n  }\n  setTitle(title) {\n    const writable = this.getWritable();\n    writable.__title = title;\n    return writable;\n  }\n  insertNewAfter(_, restoreSelection = true) {\n    const linkNode = $createLinkNode(this.__url, {\n      rel: this.__rel,\n      target: this.__target,\n      title: this.__title\n    });\n    this.insertAfter(linkNode, restoreSelection);\n    return linkNode;\n  }\n  canInsertTextBefore() {\n    return false;\n  }\n  canInsertTextAfter() {\n    return false;\n  }\n  canBeEmpty() {\n    return false;\n  }\n  isInline() {\n    return true;\n  }\n  extractWithChild(child, selection, destination) {\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const anchorNode = selection.anchor.getNode();\n    const focusNode = selection.focus.getNode();\n    return this.isParentOf(anchorNode) && this.isParentOf(focusNode) && selection.getTextContent().length > 0;\n  }\n  isEmailURI() {\n    return this.__url.startsWith('mailto:');\n  }\n  isWebSiteURI() {\n    return this.__url.startsWith('https://') || this.__url.startsWith('http://');\n  }\n}\nfunction $convertAnchorElement(domNode) {\n  let node = null;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLAnchorElement)(domNode)) {\n    const content = domNode.textContent;\n    if (content !== null && content !== '' || domNode.children.length > 0) {\n      node = $createLinkNode(domNode.getAttribute('href') || '', {\n        rel: domNode.getAttribute('rel'),\n        target: domNode.getAttribute('target'),\n        title: domNode.getAttribute('title')\n      });\n    }\n  }\n  return {\n    node\n  };\n}\n\n/**\n * Takes a URL and creates a LinkNode.\n * @param url - The URL the LinkNode should direct to.\n * @param attributes - Optional HTML a tag attributes \\\\{ target, rel, title \\\\}\n * @returns The LinkNode.\n */\nfunction $createLinkNode(url = '', attributes) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new LinkNode(url, attributes));\n}\n\n/**\n * Determines if node is a LinkNode.\n * @param node - The node to be checked.\n * @returns true if node is a LinkNode, false otherwise.\n */\nfunction $isLinkNode(node) {\n  return node instanceof LinkNode;\n}\n// Custom node type to override `canInsertTextAfter` that will\n// allow typing within the link\nclass AutoLinkNode extends LinkNode {\n  /** @internal */\n  /** Indicates whether the autolink was ever unlinked. **/\n\n  constructor(url = '', attributes = {}, key) {\n    super(url, attributes, key);\n    this.__isUnlinked = attributes.isUnlinked !== undefined && attributes.isUnlinked !== null ? attributes.isUnlinked : false;\n  }\n  static getType() {\n    return 'autolink';\n  }\n  static clone(node) {\n    return new AutoLinkNode(node.__url, {\n      isUnlinked: node.__isUnlinked,\n      rel: node.__rel,\n      target: node.__target,\n      title: node.__title\n    }, node.__key);\n  }\n  getIsUnlinked() {\n    return this.__isUnlinked;\n  }\n  setIsUnlinked(value) {\n    const self = this.getWritable();\n    self.__isUnlinked = value;\n    return self;\n  }\n  createDOM(config) {\n    if (this.__isUnlinked) {\n      return document.createElement('span');\n    } else {\n      return super.createDOM(config);\n    }\n  }\n  updateDOM(prevNode, anchor, config) {\n    return super.updateDOM(prevNode, anchor, config) || prevNode.__isUnlinked !== this.__isUnlinked;\n  }\n  static importJSON(serializedNode) {\n    return $createAutoLinkNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setIsUnlinked(serializedNode.isUnlinked || false);\n  }\n  static importDOM() {\n    // TODO: Should link node should handle the import over autolink?\n    return null;\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      isUnlinked: this.__isUnlinked\n    };\n  }\n  insertNewAfter(selection, restoreSelection = true) {\n    const element = this.getParentOrThrow().insertNewAfter(selection, restoreSelection);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(element)) {\n      const linkNode = $createAutoLinkNode(this.__url, {\n        isUnlinked: this.__isUnlinked,\n        rel: this.__rel,\n        target: this.__target,\n        title: this.__title\n      });\n      element.append(linkNode);\n      return linkNode;\n    }\n    return null;\n  }\n}\n\n/**\n * Takes a URL and creates an AutoLinkNode. AutoLinkNodes are generally automatically generated\n * during typing, which is especially useful when a button to generate a LinkNode is not practical.\n * @param url - The URL the LinkNode should direct to.\n * @param attributes - Optional HTML a tag attributes. \\\\{ target, rel, title \\\\}\n * @returns The LinkNode.\n */\nfunction $createAutoLinkNode(url = '', attributes) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new AutoLinkNode(url, attributes));\n}\n\n/**\n * Determines if node is an AutoLinkNode.\n * @param node - The node to be checked.\n * @returns true if node is an AutoLinkNode, false otherwise.\n */\nfunction $isAutoLinkNode(node) {\n  return node instanceof AutoLinkNode;\n}\nconst TOGGLE_LINK_COMMAND = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('TOGGLE_LINK_COMMAND');\nfunction $getPointNode(point, offset) {\n  if (point.type === 'element') {\n    const node = point.getNode();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n      formatDevErrorMessage(`$getPointNode: element point is not an ElementNode`);\n    }\n    const childNode = node.getChildren()[point.offset + offset];\n    return childNode || null;\n  }\n  return null;\n}\n\n/**\n * Preserve the logical start/end of a RangeSelection in situations where\n * the point is an element that may be reparented in the callback.\n *\n * @param $fn The function to run\n * @returns The result of the callback\n */\nfunction $withSelectedNodes($fn) {\n  const initialSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(initialSelection)) {\n    return $fn();\n  }\n  const normalized = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeSelection__EXPERIMENTAL)(initialSelection);\n  const isBackwards = normalized.isBackward();\n  const anchorNode = $getPointNode(normalized.anchor, isBackwards ? -1 : 0);\n  const focusNode = $getPointNode(normalized.focus, isBackwards ? 0 : -1);\n  const rval = $fn();\n  if (anchorNode || focusNode) {\n    const updatedSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(updatedSelection)) {\n      const finalSelection = updatedSelection.clone();\n      if (anchorNode) {\n        const anchorParent = anchorNode.getParent();\n        if (anchorParent) {\n          finalSelection.anchor.set(anchorParent.getKey(), anchorNode.getIndexWithinParent() + (isBackwards ? 1 : 0), 'element');\n        }\n      }\n      if (focusNode) {\n        const focusParent = focusNode.getParent();\n        if (focusParent) {\n          finalSelection.focus.set(focusParent.getKey(), focusNode.getIndexWithinParent() + (isBackwards ? 0 : 1), 'element');\n        }\n      }\n      (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeSelection__EXPERIMENTAL)(finalSelection));\n    }\n  }\n  return rval;\n}\n\n/**\n * Generates or updates a LinkNode. It can also delete a LinkNode if the URL is null,\n * but saves any children and brings them up to the parent node.\n * @param url - The URL the link directs to.\n * @param attributes - Optional HTML a tag attributes. \\\\{ target, rel, title \\\\}\n */\nfunction $toggleLink(url, attributes = {}) {\n  const {\n    target,\n    title\n  } = attributes;\n  const rel = attributes.rel === undefined ? 'noreferrer' : attributes.rel;\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    return;\n  }\n  const nodes = selection.extract();\n  if (url === null) {\n    // Remove LinkNodes\n    nodes.forEach(node => {\n      const parentLink = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, parent => !$isAutoLinkNode(parent) && $isLinkNode(parent));\n      if (parentLink) {\n        const children = parentLink.getChildren();\n        for (let i = 0; i < children.length; i++) {\n          parentLink.insertBefore(children[i]);\n        }\n        parentLink.remove();\n      }\n    });\n    return;\n  }\n  const updatedNodes = new Set();\n  const updateLinkNode = linkNode => {\n    if (updatedNodes.has(linkNode.getKey())) {\n      return;\n    }\n    updatedNodes.add(linkNode.getKey());\n    linkNode.setURL(url);\n    if (target !== undefined) {\n      linkNode.setTarget(target);\n    }\n    if (rel !== undefined) {\n      linkNode.setRel(rel);\n    }\n    if (title !== undefined) {\n      linkNode.setTitle(title);\n    }\n  };\n  // Add or merge LinkNodes\n  if (nodes.length === 1) {\n    const firstNode = nodes[0];\n    // if the first node is a LinkNode or if its\n    // parent is a LinkNode, we update the URL, target and rel.\n    const linkNode = $getAncestor(firstNode, $isLinkNode);\n    if (linkNode !== null) {\n      return updateLinkNode(linkNode);\n    }\n  }\n  $withSelectedNodes(() => {\n    let linkNode = null;\n    for (const node of nodes) {\n      if (!node.isAttached()) {\n        continue;\n      }\n      const parentLinkNode = $getAncestor(node, $isLinkNode);\n      if (parentLinkNode) {\n        updateLinkNode(parentLinkNode);\n        continue;\n      }\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n        if (!node.isInline()) {\n          // Ignore block nodes, if there are any children we will see them\n          // later and wrap in a new LinkNode\n          continue;\n        }\n        if ($isLinkNode(node)) {\n          // If it's not an autolink node and we don't already have a LinkNode\n          // in this block then we can update it and re-use it\n          if (!$isAutoLinkNode(node) && (linkNode === null || !linkNode.getParentOrThrow().isParentOf(node))) {\n            updateLinkNode(node);\n            linkNode = node;\n            continue;\n          }\n          // Unwrap LinkNode, we already have one or it's an AutoLinkNode\n          for (const child of node.getChildren()) {\n            node.insertBefore(child);\n          }\n          node.remove();\n          continue;\n        }\n      }\n      const prevLinkNode = node.getPreviousSibling();\n      if ($isLinkNode(prevLinkNode) && prevLinkNode.is(linkNode)) {\n        prevLinkNode.append(node);\n        continue;\n      }\n      linkNode = $createLinkNode(url, {\n        rel,\n        target,\n        title\n      });\n      node.insertAfter(linkNode);\n      linkNode.append(node);\n    }\n  });\n}\n/** @deprecated renamed to {@link $toggleLink} by @lexical/eslint-plugin rules-of-lexical */\nconst toggleLink = $toggleLink;\nfunction $getAncestor(node, predicate) {\n  let parent = node;\n  while (parent !== null && parent.getParent() !== null && !predicate(parent)) {\n    parent = parent.getParentOrThrow();\n  }\n  return predicate(parent) ? parent : null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BsZXhpY2FsK2xpbmtAMC4yOC4wL25vZGVfbW9kdWxlcy9AbGV4aWNhbC9saW5rL0xleGljYWxMaW5rLmRldi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFa0c7QUFDOEU7O0FBRWhMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBLHVCQUF1QixnREFBVztBQUNsQzs7QUFFQTs7QUFFQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0EsdUNBQXVDO0FBQ3ZDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksc0VBQXNCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLFFBQVEsNERBQW1CO0FBQzNCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVTtBQUNWO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFVBQVU7QUFDVjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsMERBQWlCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sNERBQW1CO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsMERBQTBEO0FBQzFEO0FBQ0E7QUFDQTtBQUNBLFNBQVMsOERBQXFCO0FBQzlCOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLHVDQUF1QztBQUN2QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsdURBQWM7QUFDdEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQ7QUFDM0Q7QUFDQTtBQUNBO0FBQ0EsU0FBUyw4REFBcUI7QUFDOUI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDRCQUE0QixzREFBYTtBQUN6QztBQUNBO0FBQ0E7QUFDQSxTQUFTLHVEQUFjO0FBQ3ZCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyQkFBMkIsc0RBQWE7QUFDeEMsT0FBTywwREFBaUI7QUFDeEI7QUFDQTtBQUNBLHFCQUFxQiwwRUFBaUM7QUFDdEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixzREFBYTtBQUMxQyxRQUFRLDBEQUFpQjtBQUN6QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sc0RBQWEsQ0FBQywwRUFBaUM7QUFDckQ7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSwyREFBMkQ7QUFDM0Q7QUFDQSx5Q0FBeUM7QUFDekM7QUFDQTtBQUNBO0FBQ0EsSUFBSTtBQUNKO0FBQ0Esb0JBQW9CLHNEQUFhO0FBQ2pDLE9BQU8sMERBQWlCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHlCQUF5QixtRUFBbUI7QUFDNUM7QUFDQTtBQUNBLHdCQUF3QixxQkFBcUI7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVLHVEQUFjO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7QUFDQSw0QkFBNEIsbUJBQW1CO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRW9KIiwic291cmNlcyI6WyJDOlxccHJvamVjdHNcXGxtc1xcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGxleGljYWwrbGlua0AwLjI4LjBcXG5vZGVfbW9kdWxlc1xcQGxleGljYWxcXGxpbmtcXExleGljYWxMaW5rLmRldi5tanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqL1xuXG5pbXBvcnQgeyBhZGRDbGFzc05hbWVzVG9FbGVtZW50LCBpc0hUTUxBbmNob3JFbGVtZW50LCAkZmluZE1hdGNoaW5nUGFyZW50IH0gZnJvbSAnQGxleGljYWwvdXRpbHMnO1xuaW1wb3J0IHsgY3JlYXRlQ29tbWFuZCwgRWxlbWVudE5vZGUsICRpc1JhbmdlU2VsZWN0aW9uLCAkYXBwbHlOb2RlUmVwbGFjZW1lbnQsICRpc0VsZW1lbnROb2RlLCAkZ2V0U2VsZWN0aW9uLCAkbm9ybWFsaXplU2VsZWN0aW9uX19FWFBFUklNRU5UQUwsICRzZXRTZWxlY3Rpb24gfSBmcm9tICdsZXhpY2FsJztcblxuLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqL1xuXG4vLyBEbyBub3QgcmVxdWlyZSB0aGlzIG1vZHVsZSBkaXJlY3RseSEgVXNlIG5vcm1hbCBgaW52YXJpYW50YCBjYWxscy5cblxuZnVuY3Rpb24gZm9ybWF0RGV2RXJyb3JNZXNzYWdlKG1lc3NhZ2UpIHtcbiAgdGhyb3cgbmV3IEVycm9yKG1lc3NhZ2UpO1xufVxuXG5jb25zdCBTVVBQT1JURURfVVJMX1BST1RPQ09MUyA9IG5ldyBTZXQoWydodHRwOicsICdodHRwczonLCAnbWFpbHRvOicsICdzbXM6JywgJ3RlbDonXSk7XG5cbi8qKiBAbm9Jbmhlcml0RG9jICovXG5jbGFzcyBMaW5rTm9kZSBleHRlbmRzIEVsZW1lbnROb2RlIHtcbiAgLyoqIEBpbnRlcm5hbCAqL1xuXG4gIC8qKiBAaW50ZXJuYWwgKi9cblxuICAvKiogQGludGVybmFsICovXG5cbiAgLyoqIEBpbnRlcm5hbCAqL1xuXG4gIHN0YXRpYyBnZXRUeXBlKCkge1xuICAgIHJldHVybiAnbGluayc7XG4gIH1cbiAgc3RhdGljIGNsb25lKG5vZGUpIHtcbiAgICByZXR1cm4gbmV3IExpbmtOb2RlKG5vZGUuX191cmwsIHtcbiAgICAgIHJlbDogbm9kZS5fX3JlbCxcbiAgICAgIHRhcmdldDogbm9kZS5fX3RhcmdldCxcbiAgICAgIHRpdGxlOiBub2RlLl9fdGl0bGVcbiAgICB9LCBub2RlLl9fa2V5KTtcbiAgfVxuICBjb25zdHJ1Y3Rvcih1cmwgPSAnJywgYXR0cmlidXRlcyA9IHt9LCBrZXkpIHtcbiAgICBzdXBlcihrZXkpO1xuICAgIGNvbnN0IHtcbiAgICAgIHRhcmdldCA9IG51bGwsXG4gICAgICByZWwgPSBudWxsLFxuICAgICAgdGl0bGUgPSBudWxsXG4gICAgfSA9IGF0dHJpYnV0ZXM7XG4gICAgdGhpcy5fX3VybCA9IHVybDtcbiAgICB0aGlzLl9fdGFyZ2V0ID0gdGFyZ2V0O1xuICAgIHRoaXMuX19yZWwgPSByZWw7XG4gICAgdGhpcy5fX3RpdGxlID0gdGl0bGU7XG4gIH1cbiAgY3JlYXRlRE9NKGNvbmZpZykge1xuICAgIGNvbnN0IGVsZW1lbnQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdhJyk7XG4gICAgZWxlbWVudC5ocmVmID0gdGhpcy5zYW5pdGl6ZVVybCh0aGlzLl9fdXJsKTtcbiAgICBpZiAodGhpcy5fX3RhcmdldCAhPT0gbnVsbCkge1xuICAgICAgZWxlbWVudC50YXJnZXQgPSB0aGlzLl9fdGFyZ2V0O1xuICAgIH1cbiAgICBpZiAodGhpcy5fX3JlbCAhPT0gbnVsbCkge1xuICAgICAgZWxlbWVudC5yZWwgPSB0aGlzLl9fcmVsO1xuICAgIH1cbiAgICBpZiAodGhpcy5fX3RpdGxlICE9PSBudWxsKSB7XG4gICAgICBlbGVtZW50LnRpdGxlID0gdGhpcy5fX3RpdGxlO1xuICAgIH1cbiAgICBhZGRDbGFzc05hbWVzVG9FbGVtZW50KGVsZW1lbnQsIGNvbmZpZy50aGVtZS5saW5rKTtcbiAgICByZXR1cm4gZWxlbWVudDtcbiAgfVxuICB1cGRhdGVET00ocHJldk5vZGUsIGFuY2hvciwgY29uZmlnKSB7XG4gICAgaWYgKGlzSFRNTEFuY2hvckVsZW1lbnQoYW5jaG9yKSkge1xuICAgICAgY29uc3QgdXJsID0gdGhpcy5fX3VybDtcbiAgICAgIGNvbnN0IHRhcmdldCA9IHRoaXMuX190YXJnZXQ7XG4gICAgICBjb25zdCByZWwgPSB0aGlzLl9fcmVsO1xuICAgICAgY29uc3QgdGl0bGUgPSB0aGlzLl9fdGl0bGU7XG4gICAgICBpZiAodXJsICE9PSBwcmV2Tm9kZS5fX3VybCkge1xuICAgICAgICBhbmNob3IuaHJlZiA9IHVybDtcbiAgICAgIH1cbiAgICAgIGlmICh0YXJnZXQgIT09IHByZXZOb2RlLl9fdGFyZ2V0KSB7XG4gICAgICAgIGlmICh0YXJnZXQpIHtcbiAgICAgICAgICBhbmNob3IudGFyZ2V0ID0gdGFyZ2V0O1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGFuY2hvci5yZW1vdmVBdHRyaWJ1dGUoJ3RhcmdldCcpO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBpZiAocmVsICE9PSBwcmV2Tm9kZS5fX3JlbCkge1xuICAgICAgICBpZiAocmVsKSB7XG4gICAgICAgICAgYW5jaG9yLnJlbCA9IHJlbDtcbiAgICAgICAgfSBlbHNlIHtcbiAgICAgICAgICBhbmNob3IucmVtb3ZlQXR0cmlidXRlKCdyZWwnKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgaWYgKHRpdGxlICE9PSBwcmV2Tm9kZS5fX3RpdGxlKSB7XG4gICAgICAgIGlmICh0aXRsZSkge1xuICAgICAgICAgIGFuY2hvci50aXRsZSA9IHRpdGxlO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIGFuY2hvci5yZW1vdmVBdHRyaWJ1dGUoJ3RpdGxlJyk7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIHN0YXRpYyBpbXBvcnRET00oKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGE6IG5vZGUgPT4gKHtcbiAgICAgICAgY29udmVyc2lvbjogJGNvbnZlcnRBbmNob3JFbGVtZW50LFxuICAgICAgICBwcmlvcml0eTogMVxuICAgICAgfSlcbiAgICB9O1xuICB9XG4gIHN0YXRpYyBpbXBvcnRKU09OKHNlcmlhbGl6ZWROb2RlKSB7XG4gICAgcmV0dXJuICRjcmVhdGVMaW5rTm9kZSgpLnVwZGF0ZUZyb21KU09OKHNlcmlhbGl6ZWROb2RlKTtcbiAgfVxuICB1cGRhdGVGcm9tSlNPTihzZXJpYWxpemVkTm9kZSkge1xuICAgIHJldHVybiBzdXBlci51cGRhdGVGcm9tSlNPTihzZXJpYWxpemVkTm9kZSkuc2V0VVJMKHNlcmlhbGl6ZWROb2RlLnVybCkuc2V0UmVsKHNlcmlhbGl6ZWROb2RlLnJlbCB8fCBudWxsKS5zZXRUYXJnZXQoc2VyaWFsaXplZE5vZGUudGFyZ2V0IHx8IG51bGwpLnNldFRpdGxlKHNlcmlhbGl6ZWROb2RlLnRpdGxlIHx8IG51bGwpO1xuICB9XG4gIHNhbml0aXplVXJsKHVybCkge1xuICAgIHRyeSB7XG4gICAgICBjb25zdCBwYXJzZWRVcmwgPSBuZXcgVVJMKHVybCk7XG4gICAgICAvLyBlc2xpbnQtZGlzYWJsZS1uZXh0LWxpbmUgbm8tc2NyaXB0LXVybFxuICAgICAgaWYgKCFTVVBQT1JURURfVVJMX1BST1RPQ09MUy5oYXMocGFyc2VkVXJsLnByb3RvY29sKSkge1xuICAgICAgICByZXR1cm4gJ2Fib3V0OmJsYW5rJztcbiAgICAgIH1cbiAgICB9IGNhdGNoIChfdW51c2VkKSB7XG4gICAgICByZXR1cm4gdXJsO1xuICAgIH1cbiAgICByZXR1cm4gdXJsO1xuICB9XG4gIGV4cG9ydEpTT04oKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIC4uLnN1cGVyLmV4cG9ydEpTT04oKSxcbiAgICAgIHJlbDogdGhpcy5nZXRSZWwoKSxcbiAgICAgIHRhcmdldDogdGhpcy5nZXRUYXJnZXQoKSxcbiAgICAgIHRpdGxlOiB0aGlzLmdldFRpdGxlKCksXG4gICAgICB1cmw6IHRoaXMuZ2V0VVJMKClcbiAgICB9O1xuICB9XG4gIGdldFVSTCgpIHtcbiAgICByZXR1cm4gdGhpcy5nZXRMYXRlc3QoKS5fX3VybDtcbiAgfVxuICBzZXRVUkwodXJsKSB7XG4gICAgY29uc3Qgd3JpdGFibGUgPSB0aGlzLmdldFdyaXRhYmxlKCk7XG4gICAgd3JpdGFibGUuX191cmwgPSB1cmw7XG4gICAgcmV0dXJuIHdyaXRhYmxlO1xuICB9XG4gIGdldFRhcmdldCgpIHtcbiAgICByZXR1cm4gdGhpcy5nZXRMYXRlc3QoKS5fX3RhcmdldDtcbiAgfVxuICBzZXRUYXJnZXQodGFyZ2V0KSB7XG4gICAgY29uc3Qgd3JpdGFibGUgPSB0aGlzLmdldFdyaXRhYmxlKCk7XG4gICAgd3JpdGFibGUuX190YXJnZXQgPSB0YXJnZXQ7XG4gICAgcmV0dXJuIHdyaXRhYmxlO1xuICB9XG4gIGdldFJlbCgpIHtcbiAgICByZXR1cm4gdGhpcy5nZXRMYXRlc3QoKS5fX3JlbDtcbiAgfVxuICBzZXRSZWwocmVsKSB7XG4gICAgY29uc3Qgd3JpdGFibGUgPSB0aGlzLmdldFdyaXRhYmxlKCk7XG4gICAgd3JpdGFibGUuX19yZWwgPSByZWw7XG4gICAgcmV0dXJuIHdyaXRhYmxlO1xuICB9XG4gIGdldFRpdGxlKCkge1xuICAgIHJldHVybiB0aGlzLmdldExhdGVzdCgpLl9fdGl0bGU7XG4gIH1cbiAgc2V0VGl0bGUodGl0bGUpIHtcbiAgICBjb25zdCB3cml0YWJsZSA9IHRoaXMuZ2V0V3JpdGFibGUoKTtcbiAgICB3cml0YWJsZS5fX3RpdGxlID0gdGl0bGU7XG4gICAgcmV0dXJuIHdyaXRhYmxlO1xuICB9XG4gIGluc2VydE5ld0FmdGVyKF8sIHJlc3RvcmVTZWxlY3Rpb24gPSB0cnVlKSB7XG4gICAgY29uc3QgbGlua05vZGUgPSAkY3JlYXRlTGlua05vZGUodGhpcy5fX3VybCwge1xuICAgICAgcmVsOiB0aGlzLl9fcmVsLFxuICAgICAgdGFyZ2V0OiB0aGlzLl9fdGFyZ2V0LFxuICAgICAgdGl0bGU6IHRoaXMuX190aXRsZVxuICAgIH0pO1xuICAgIHRoaXMuaW5zZXJ0QWZ0ZXIobGlua05vZGUsIHJlc3RvcmVTZWxlY3Rpb24pO1xuICAgIHJldHVybiBsaW5rTm9kZTtcbiAgfVxuICBjYW5JbnNlcnRUZXh0QmVmb3JlKCkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBjYW5JbnNlcnRUZXh0QWZ0ZXIoKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGNhbkJlRW1wdHkoKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGlzSW5saW5lKCkge1xuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIGV4dHJhY3RXaXRoQ2hpbGQoY2hpbGQsIHNlbGVjdGlvbiwgZGVzdGluYXRpb24pIHtcbiAgICBpZiAoISRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgY29uc3QgYW5jaG9yTm9kZSA9IHNlbGVjdGlvbi5hbmNob3IuZ2V0Tm9kZSgpO1xuICAgIGNvbnN0IGZvY3VzTm9kZSA9IHNlbGVjdGlvbi5mb2N1cy5nZXROb2RlKCk7XG4gICAgcmV0dXJuIHRoaXMuaXNQYXJlbnRPZihhbmNob3JOb2RlKSAmJiB0aGlzLmlzUGFyZW50T2YoZm9jdXNOb2RlKSAmJiBzZWxlY3Rpb24uZ2V0VGV4dENvbnRlbnQoKS5sZW5ndGggPiAwO1xuICB9XG4gIGlzRW1haWxVUkkoKSB7XG4gICAgcmV0dXJuIHRoaXMuX191cmwuc3RhcnRzV2l0aCgnbWFpbHRvOicpO1xuICB9XG4gIGlzV2ViU2l0ZVVSSSgpIHtcbiAgICByZXR1cm4gdGhpcy5fX3VybC5zdGFydHNXaXRoKCdodHRwczovLycpIHx8IHRoaXMuX191cmwuc3RhcnRzV2l0aCgnaHR0cDovLycpO1xuICB9XG59XG5mdW5jdGlvbiAkY29udmVydEFuY2hvckVsZW1lbnQoZG9tTm9kZSkge1xuICBsZXQgbm9kZSA9IG51bGw7XG4gIGlmIChpc0hUTUxBbmNob3JFbGVtZW50KGRvbU5vZGUpKSB7XG4gICAgY29uc3QgY29udGVudCA9IGRvbU5vZGUudGV4dENvbnRlbnQ7XG4gICAgaWYgKGNvbnRlbnQgIT09IG51bGwgJiYgY29udGVudCAhPT0gJycgfHwgZG9tTm9kZS5jaGlsZHJlbi5sZW5ndGggPiAwKSB7XG4gICAgICBub2RlID0gJGNyZWF0ZUxpbmtOb2RlKGRvbU5vZGUuZ2V0QXR0cmlidXRlKCdocmVmJykgfHwgJycsIHtcbiAgICAgICAgcmVsOiBkb21Ob2RlLmdldEF0dHJpYnV0ZSgncmVsJyksXG4gICAgICAgIHRhcmdldDogZG9tTm9kZS5nZXRBdHRyaWJ1dGUoJ3RhcmdldCcpLFxuICAgICAgICB0aXRsZTogZG9tTm9kZS5nZXRBdHRyaWJ1dGUoJ3RpdGxlJylcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxuICByZXR1cm4ge1xuICAgIG5vZGVcbiAgfTtcbn1cblxuLyoqXG4gKiBUYWtlcyBhIFVSTCBhbmQgY3JlYXRlcyBhIExpbmtOb2RlLlxuICogQHBhcmFtIHVybCAtIFRoZSBVUkwgdGhlIExpbmtOb2RlIHNob3VsZCBkaXJlY3QgdG8uXG4gKiBAcGFyYW0gYXR0cmlidXRlcyAtIE9wdGlvbmFsIEhUTUwgYSB0YWcgYXR0cmlidXRlcyBcXFxceyB0YXJnZXQsIHJlbCwgdGl0bGUgXFxcXH1cbiAqIEByZXR1cm5zIFRoZSBMaW5rTm9kZS5cbiAqL1xuZnVuY3Rpb24gJGNyZWF0ZUxpbmtOb2RlKHVybCA9ICcnLCBhdHRyaWJ1dGVzKSB7XG4gIHJldHVybiAkYXBwbHlOb2RlUmVwbGFjZW1lbnQobmV3IExpbmtOb2RlKHVybCwgYXR0cmlidXRlcykpO1xufVxuXG4vKipcbiAqIERldGVybWluZXMgaWYgbm9kZSBpcyBhIExpbmtOb2RlLlxuICogQHBhcmFtIG5vZGUgLSBUaGUgbm9kZSB0byBiZSBjaGVja2VkLlxuICogQHJldHVybnMgdHJ1ZSBpZiBub2RlIGlzIGEgTGlua05vZGUsIGZhbHNlIG90aGVyd2lzZS5cbiAqL1xuZnVuY3Rpb24gJGlzTGlua05vZGUobm9kZSkge1xuICByZXR1cm4gbm9kZSBpbnN0YW5jZW9mIExpbmtOb2RlO1xufVxuLy8gQ3VzdG9tIG5vZGUgdHlwZSB0byBvdmVycmlkZSBgY2FuSW5zZXJ0VGV4dEFmdGVyYCB0aGF0IHdpbGxcbi8vIGFsbG93IHR5cGluZyB3aXRoaW4gdGhlIGxpbmtcbmNsYXNzIEF1dG9MaW5rTm9kZSBleHRlbmRzIExpbmtOb2RlIHtcbiAgLyoqIEBpbnRlcm5hbCAqL1xuICAvKiogSW5kaWNhdGVzIHdoZXRoZXIgdGhlIGF1dG9saW5rIHdhcyBldmVyIHVubGlua2VkLiAqKi9cblxuICBjb25zdHJ1Y3Rvcih1cmwgPSAnJywgYXR0cmlidXRlcyA9IHt9LCBrZXkpIHtcbiAgICBzdXBlcih1cmwsIGF0dHJpYnV0ZXMsIGtleSk7XG4gICAgdGhpcy5fX2lzVW5saW5rZWQgPSBhdHRyaWJ1dGVzLmlzVW5saW5rZWQgIT09IHVuZGVmaW5lZCAmJiBhdHRyaWJ1dGVzLmlzVW5saW5rZWQgIT09IG51bGwgPyBhdHRyaWJ1dGVzLmlzVW5saW5rZWQgOiBmYWxzZTtcbiAgfVxuICBzdGF0aWMgZ2V0VHlwZSgpIHtcbiAgICByZXR1cm4gJ2F1dG9saW5rJztcbiAgfVxuICBzdGF0aWMgY2xvbmUobm9kZSkge1xuICAgIHJldHVybiBuZXcgQXV0b0xpbmtOb2RlKG5vZGUuX191cmwsIHtcbiAgICAgIGlzVW5saW5rZWQ6IG5vZGUuX19pc1VubGlua2VkLFxuICAgICAgcmVsOiBub2RlLl9fcmVsLFxuICAgICAgdGFyZ2V0OiBub2RlLl9fdGFyZ2V0LFxuICAgICAgdGl0bGU6IG5vZGUuX190aXRsZVxuICAgIH0sIG5vZGUuX19rZXkpO1xuICB9XG4gIGdldElzVW5saW5rZWQoKSB7XG4gICAgcmV0dXJuIHRoaXMuX19pc1VubGlua2VkO1xuICB9XG4gIHNldElzVW5saW5rZWQodmFsdWUpIHtcbiAgICBjb25zdCBzZWxmID0gdGhpcy5nZXRXcml0YWJsZSgpO1xuICAgIHNlbGYuX19pc1VubGlua2VkID0gdmFsdWU7XG4gICAgcmV0dXJuIHNlbGY7XG4gIH1cbiAgY3JlYXRlRE9NKGNvbmZpZykge1xuICAgIGlmICh0aGlzLl9faXNVbmxpbmtlZCkge1xuICAgICAgcmV0dXJuIGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ3NwYW4nKTtcbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIHN1cGVyLmNyZWF0ZURPTShjb25maWcpO1xuICAgIH1cbiAgfVxuICB1cGRhdGVET00ocHJldk5vZGUsIGFuY2hvciwgY29uZmlnKSB7XG4gICAgcmV0dXJuIHN1cGVyLnVwZGF0ZURPTShwcmV2Tm9kZSwgYW5jaG9yLCBjb25maWcpIHx8IHByZXZOb2RlLl9faXNVbmxpbmtlZCAhPT0gdGhpcy5fX2lzVW5saW5rZWQ7XG4gIH1cbiAgc3RhdGljIGltcG9ydEpTT04oc2VyaWFsaXplZE5vZGUpIHtcbiAgICByZXR1cm4gJGNyZWF0ZUF1dG9MaW5rTm9kZSgpLnVwZGF0ZUZyb21KU09OKHNlcmlhbGl6ZWROb2RlKTtcbiAgfVxuICB1cGRhdGVGcm9tSlNPTihzZXJpYWxpemVkTm9kZSkge1xuICAgIHJldHVybiBzdXBlci51cGRhdGVGcm9tSlNPTihzZXJpYWxpemVkTm9kZSkuc2V0SXNVbmxpbmtlZChzZXJpYWxpemVkTm9kZS5pc1VubGlua2VkIHx8IGZhbHNlKTtcbiAgfVxuICBzdGF0aWMgaW1wb3J0RE9NKCkge1xuICAgIC8vIFRPRE86IFNob3VsZCBsaW5rIG5vZGUgc2hvdWxkIGhhbmRsZSB0aGUgaW1wb3J0IG92ZXIgYXV0b2xpbms/XG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbiAgZXhwb3J0SlNPTigpIHtcbiAgICByZXR1cm4ge1xuICAgICAgLi4uc3VwZXIuZXhwb3J0SlNPTigpLFxuICAgICAgaXNVbmxpbmtlZDogdGhpcy5fX2lzVW5saW5rZWRcbiAgICB9O1xuICB9XG4gIGluc2VydE5ld0FmdGVyKHNlbGVjdGlvbiwgcmVzdG9yZVNlbGVjdGlvbiA9IHRydWUpIHtcbiAgICBjb25zdCBlbGVtZW50ID0gdGhpcy5nZXRQYXJlbnRPclRocm93KCkuaW5zZXJ0TmV3QWZ0ZXIoc2VsZWN0aW9uLCByZXN0b3JlU2VsZWN0aW9uKTtcbiAgICBpZiAoJGlzRWxlbWVudE5vZGUoZWxlbWVudCkpIHtcbiAgICAgIGNvbnN0IGxpbmtOb2RlID0gJGNyZWF0ZUF1dG9MaW5rTm9kZSh0aGlzLl9fdXJsLCB7XG4gICAgICAgIGlzVW5saW5rZWQ6IHRoaXMuX19pc1VubGlua2VkLFxuICAgICAgICByZWw6IHRoaXMuX19yZWwsXG4gICAgICAgIHRhcmdldDogdGhpcy5fX3RhcmdldCxcbiAgICAgICAgdGl0bGU6IHRoaXMuX190aXRsZVxuICAgICAgfSk7XG4gICAgICBlbGVtZW50LmFwcGVuZChsaW5rTm9kZSk7XG4gICAgICByZXR1cm4gbGlua05vZGU7XG4gICAgfVxuICAgIHJldHVybiBudWxsO1xuICB9XG59XG5cbi8qKlxuICogVGFrZXMgYSBVUkwgYW5kIGNyZWF0ZXMgYW4gQXV0b0xpbmtOb2RlLiBBdXRvTGlua05vZGVzIGFyZSBnZW5lcmFsbHkgYXV0b21hdGljYWxseSBnZW5lcmF0ZWRcbiAqIGR1cmluZyB0eXBpbmcsIHdoaWNoIGlzIGVzcGVjaWFsbHkgdXNlZnVsIHdoZW4gYSBidXR0b24gdG8gZ2VuZXJhdGUgYSBMaW5rTm9kZSBpcyBub3QgcHJhY3RpY2FsLlxuICogQHBhcmFtIHVybCAtIFRoZSBVUkwgdGhlIExpbmtOb2RlIHNob3VsZCBkaXJlY3QgdG8uXG4gKiBAcGFyYW0gYXR0cmlidXRlcyAtIE9wdGlvbmFsIEhUTUwgYSB0YWcgYXR0cmlidXRlcy4gXFxcXHsgdGFyZ2V0LCByZWwsIHRpdGxlIFxcXFx9XG4gKiBAcmV0dXJucyBUaGUgTGlua05vZGUuXG4gKi9cbmZ1bmN0aW9uICRjcmVhdGVBdXRvTGlua05vZGUodXJsID0gJycsIGF0dHJpYnV0ZXMpIHtcbiAgcmV0dXJuICRhcHBseU5vZGVSZXBsYWNlbWVudChuZXcgQXV0b0xpbmtOb2RlKHVybCwgYXR0cmlidXRlcykpO1xufVxuXG4vKipcbiAqIERldGVybWluZXMgaWYgbm9kZSBpcyBhbiBBdXRvTGlua05vZGUuXG4gKiBAcGFyYW0gbm9kZSAtIFRoZSBub2RlIHRvIGJlIGNoZWNrZWQuXG4gKiBAcmV0dXJucyB0cnVlIGlmIG5vZGUgaXMgYW4gQXV0b0xpbmtOb2RlLCBmYWxzZSBvdGhlcndpc2UuXG4gKi9cbmZ1bmN0aW9uICRpc0F1dG9MaW5rTm9kZShub2RlKSB7XG4gIHJldHVybiBub2RlIGluc3RhbmNlb2YgQXV0b0xpbmtOb2RlO1xufVxuY29uc3QgVE9HR0xFX0xJTktfQ09NTUFORCA9IGNyZWF0ZUNvbW1hbmQoJ1RPR0dMRV9MSU5LX0NPTU1BTkQnKTtcbmZ1bmN0aW9uICRnZXRQb2ludE5vZGUocG9pbnQsIG9mZnNldCkge1xuICBpZiAocG9pbnQudHlwZSA9PT0gJ2VsZW1lbnQnKSB7XG4gICAgY29uc3Qgbm9kZSA9IHBvaW50LmdldE5vZGUoKTtcbiAgICBpZiAoISRpc0VsZW1lbnROb2RlKG5vZGUpKSB7XG4gICAgICBmb3JtYXREZXZFcnJvck1lc3NhZ2UoYCRnZXRQb2ludE5vZGU6IGVsZW1lbnQgcG9pbnQgaXMgbm90IGFuIEVsZW1lbnROb2RlYCk7XG4gICAgfVxuICAgIGNvbnN0IGNoaWxkTm9kZSA9IG5vZGUuZ2V0Q2hpbGRyZW4oKVtwb2ludC5vZmZzZXQgKyBvZmZzZXRdO1xuICAgIHJldHVybiBjaGlsZE5vZGUgfHwgbnVsbDtcbiAgfVxuICByZXR1cm4gbnVsbDtcbn1cblxuLyoqXG4gKiBQcmVzZXJ2ZSB0aGUgbG9naWNhbCBzdGFydC9lbmQgb2YgYSBSYW5nZVNlbGVjdGlvbiBpbiBzaXR1YXRpb25zIHdoZXJlXG4gKiB0aGUgcG9pbnQgaXMgYW4gZWxlbWVudCB0aGF0IG1heSBiZSByZXBhcmVudGVkIGluIHRoZSBjYWxsYmFjay5cbiAqXG4gKiBAcGFyYW0gJGZuIFRoZSBmdW5jdGlvbiB0byBydW5cbiAqIEByZXR1cm5zIFRoZSByZXN1bHQgb2YgdGhlIGNhbGxiYWNrXG4gKi9cbmZ1bmN0aW9uICR3aXRoU2VsZWN0ZWROb2RlcygkZm4pIHtcbiAgY29uc3QgaW5pdGlhbFNlbGVjdGlvbiA9ICRnZXRTZWxlY3Rpb24oKTtcbiAgaWYgKCEkaXNSYW5nZVNlbGVjdGlvbihpbml0aWFsU2VsZWN0aW9uKSkge1xuICAgIHJldHVybiAkZm4oKTtcbiAgfVxuICBjb25zdCBub3JtYWxpemVkID0gJG5vcm1hbGl6ZVNlbGVjdGlvbl9fRVhQRVJJTUVOVEFMKGluaXRpYWxTZWxlY3Rpb24pO1xuICBjb25zdCBpc0JhY2t3YXJkcyA9IG5vcm1hbGl6ZWQuaXNCYWNrd2FyZCgpO1xuICBjb25zdCBhbmNob3JOb2RlID0gJGdldFBvaW50Tm9kZShub3JtYWxpemVkLmFuY2hvciwgaXNCYWNrd2FyZHMgPyAtMSA6IDApO1xuICBjb25zdCBmb2N1c05vZGUgPSAkZ2V0UG9pbnROb2RlKG5vcm1hbGl6ZWQuZm9jdXMsIGlzQmFja3dhcmRzID8gMCA6IC0xKTtcbiAgY29uc3QgcnZhbCA9ICRmbigpO1xuICBpZiAoYW5jaG9yTm9kZSB8fCBmb2N1c05vZGUpIHtcbiAgICBjb25zdCB1cGRhdGVkU2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICgkaXNSYW5nZVNlbGVjdGlvbih1cGRhdGVkU2VsZWN0aW9uKSkge1xuICAgICAgY29uc3QgZmluYWxTZWxlY3Rpb24gPSB1cGRhdGVkU2VsZWN0aW9uLmNsb25lKCk7XG4gICAgICBpZiAoYW5jaG9yTm9kZSkge1xuICAgICAgICBjb25zdCBhbmNob3JQYXJlbnQgPSBhbmNob3JOb2RlLmdldFBhcmVudCgpO1xuICAgICAgICBpZiAoYW5jaG9yUGFyZW50KSB7XG4gICAgICAgICAgZmluYWxTZWxlY3Rpb24uYW5jaG9yLnNldChhbmNob3JQYXJlbnQuZ2V0S2V5KCksIGFuY2hvck5vZGUuZ2V0SW5kZXhXaXRoaW5QYXJlbnQoKSArIChpc0JhY2t3YXJkcyA/IDEgOiAwKSwgJ2VsZW1lbnQnKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgaWYgKGZvY3VzTm9kZSkge1xuICAgICAgICBjb25zdCBmb2N1c1BhcmVudCA9IGZvY3VzTm9kZS5nZXRQYXJlbnQoKTtcbiAgICAgICAgaWYgKGZvY3VzUGFyZW50KSB7XG4gICAgICAgICAgZmluYWxTZWxlY3Rpb24uZm9jdXMuc2V0KGZvY3VzUGFyZW50LmdldEtleSgpLCBmb2N1c05vZGUuZ2V0SW5kZXhXaXRoaW5QYXJlbnQoKSArIChpc0JhY2t3YXJkcyA/IDAgOiAxKSwgJ2VsZW1lbnQnKTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgJHNldFNlbGVjdGlvbigkbm9ybWFsaXplU2VsZWN0aW9uX19FWFBFUklNRU5UQUwoZmluYWxTZWxlY3Rpb24pKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIHJ2YWw7XG59XG5cbi8qKlxuICogR2VuZXJhdGVzIG9yIHVwZGF0ZXMgYSBMaW5rTm9kZS4gSXQgY2FuIGFsc28gZGVsZXRlIGEgTGlua05vZGUgaWYgdGhlIFVSTCBpcyBudWxsLFxuICogYnV0IHNhdmVzIGFueSBjaGlsZHJlbiBhbmQgYnJpbmdzIHRoZW0gdXAgdG8gdGhlIHBhcmVudCBub2RlLlxuICogQHBhcmFtIHVybCAtIFRoZSBVUkwgdGhlIGxpbmsgZGlyZWN0cyB0by5cbiAqIEBwYXJhbSBhdHRyaWJ1dGVzIC0gT3B0aW9uYWwgSFRNTCBhIHRhZyBhdHRyaWJ1dGVzLiBcXFxceyB0YXJnZXQsIHJlbCwgdGl0bGUgXFxcXH1cbiAqL1xuZnVuY3Rpb24gJHRvZ2dsZUxpbmsodXJsLCBhdHRyaWJ1dGVzID0ge30pIHtcbiAgY29uc3Qge1xuICAgIHRhcmdldCxcbiAgICB0aXRsZVxuICB9ID0gYXR0cmlidXRlcztcbiAgY29uc3QgcmVsID0gYXR0cmlidXRlcy5yZWwgPT09IHVuZGVmaW5lZCA/ICdub3JlZmVycmVyJyA6IGF0dHJpYnV0ZXMucmVsO1xuICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gIGlmICghJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgIHJldHVybjtcbiAgfVxuICBjb25zdCBub2RlcyA9IHNlbGVjdGlvbi5leHRyYWN0KCk7XG4gIGlmICh1cmwgPT09IG51bGwpIHtcbiAgICAvLyBSZW1vdmUgTGlua05vZGVzXG4gICAgbm9kZXMuZm9yRWFjaChub2RlID0+IHtcbiAgICAgIGNvbnN0IHBhcmVudExpbmsgPSAkZmluZE1hdGNoaW5nUGFyZW50KG5vZGUsIHBhcmVudCA9PiAhJGlzQXV0b0xpbmtOb2RlKHBhcmVudCkgJiYgJGlzTGlua05vZGUocGFyZW50KSk7XG4gICAgICBpZiAocGFyZW50TGluaykge1xuICAgICAgICBjb25zdCBjaGlsZHJlbiA9IHBhcmVudExpbmsuZ2V0Q2hpbGRyZW4oKTtcbiAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBjaGlsZHJlbi5sZW5ndGg7IGkrKykge1xuICAgICAgICAgIHBhcmVudExpbmsuaW5zZXJ0QmVmb3JlKGNoaWxkcmVuW2ldKTtcbiAgICAgICAgfVxuICAgICAgICBwYXJlbnRMaW5rLnJlbW92ZSgpO1xuICAgICAgfVxuICAgIH0pO1xuICAgIHJldHVybjtcbiAgfVxuICBjb25zdCB1cGRhdGVkTm9kZXMgPSBuZXcgU2V0KCk7XG4gIGNvbnN0IHVwZGF0ZUxpbmtOb2RlID0gbGlua05vZGUgPT4ge1xuICAgIGlmICh1cGRhdGVkTm9kZXMuaGFzKGxpbmtOb2RlLmdldEtleSgpKSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICB1cGRhdGVkTm9kZXMuYWRkKGxpbmtOb2RlLmdldEtleSgpKTtcbiAgICBsaW5rTm9kZS5zZXRVUkwodXJsKTtcbiAgICBpZiAodGFyZ2V0ICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGxpbmtOb2RlLnNldFRhcmdldCh0YXJnZXQpO1xuICAgIH1cbiAgICBpZiAocmVsICE9PSB1bmRlZmluZWQpIHtcbiAgICAgIGxpbmtOb2RlLnNldFJlbChyZWwpO1xuICAgIH1cbiAgICBpZiAodGl0bGUgIT09IHVuZGVmaW5lZCkge1xuICAgICAgbGlua05vZGUuc2V0VGl0bGUodGl0bGUpO1xuICAgIH1cbiAgfTtcbiAgLy8gQWRkIG9yIG1lcmdlIExpbmtOb2Rlc1xuICBpZiAobm9kZXMubGVuZ3RoID09PSAxKSB7XG4gICAgY29uc3QgZmlyc3ROb2RlID0gbm9kZXNbMF07XG4gICAgLy8gaWYgdGhlIGZpcnN0IG5vZGUgaXMgYSBMaW5rTm9kZSBvciBpZiBpdHNcbiAgICAvLyBwYXJlbnQgaXMgYSBMaW5rTm9kZSwgd2UgdXBkYXRlIHRoZSBVUkwsIHRhcmdldCBhbmQgcmVsLlxuICAgIGNvbnN0IGxpbmtOb2RlID0gJGdldEFuY2VzdG9yKGZpcnN0Tm9kZSwgJGlzTGlua05vZGUpO1xuICAgIGlmIChsaW5rTm9kZSAhPT0gbnVsbCkge1xuICAgICAgcmV0dXJuIHVwZGF0ZUxpbmtOb2RlKGxpbmtOb2RlKTtcbiAgICB9XG4gIH1cbiAgJHdpdGhTZWxlY3RlZE5vZGVzKCgpID0+IHtcbiAgICBsZXQgbGlua05vZGUgPSBudWxsO1xuICAgIGZvciAoY29uc3Qgbm9kZSBvZiBub2Rlcykge1xuICAgICAgaWYgKCFub2RlLmlzQXR0YWNoZWQoKSkge1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IHBhcmVudExpbmtOb2RlID0gJGdldEFuY2VzdG9yKG5vZGUsICRpc0xpbmtOb2RlKTtcbiAgICAgIGlmIChwYXJlbnRMaW5rTm9kZSkge1xuICAgICAgICB1cGRhdGVMaW5rTm9kZShwYXJlbnRMaW5rTm9kZSk7XG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuICAgICAgaWYgKCRpc0VsZW1lbnROb2RlKG5vZGUpKSB7XG4gICAgICAgIGlmICghbm9kZS5pc0lubGluZSgpKSB7XG4gICAgICAgICAgLy8gSWdub3JlIGJsb2NrIG5vZGVzLCBpZiB0aGVyZSBhcmUgYW55IGNoaWxkcmVuIHdlIHdpbGwgc2VlIHRoZW1cbiAgICAgICAgICAvLyBsYXRlciBhbmQgd3JhcCBpbiBhIG5ldyBMaW5rTm9kZVxuICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmICgkaXNMaW5rTm9kZShub2RlKSkge1xuICAgICAgICAgIC8vIElmIGl0J3Mgbm90IGFuIGF1dG9saW5rIG5vZGUgYW5kIHdlIGRvbid0IGFscmVhZHkgaGF2ZSBhIExpbmtOb2RlXG4gICAgICAgICAgLy8gaW4gdGhpcyBibG9jayB0aGVuIHdlIGNhbiB1cGRhdGUgaXQgYW5kIHJlLXVzZSBpdFxuICAgICAgICAgIGlmICghJGlzQXV0b0xpbmtOb2RlKG5vZGUpICYmIChsaW5rTm9kZSA9PT0gbnVsbCB8fCAhbGlua05vZGUuZ2V0UGFyZW50T3JUaHJvdygpLmlzUGFyZW50T2Yobm9kZSkpKSB7XG4gICAgICAgICAgICB1cGRhdGVMaW5rTm9kZShub2RlKTtcbiAgICAgICAgICAgIGxpbmtOb2RlID0gbm9kZTtcbiAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgIH1cbiAgICAgICAgICAvLyBVbndyYXAgTGlua05vZGUsIHdlIGFscmVhZHkgaGF2ZSBvbmUgb3IgaXQncyBhbiBBdXRvTGlua05vZGVcbiAgICAgICAgICBmb3IgKGNvbnN0IGNoaWxkIG9mIG5vZGUuZ2V0Q2hpbGRyZW4oKSkge1xuICAgICAgICAgICAgbm9kZS5pbnNlcnRCZWZvcmUoY2hpbGQpO1xuICAgICAgICAgIH1cbiAgICAgICAgICBub2RlLnJlbW92ZSgpO1xuICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICB9XG4gICAgICB9XG4gICAgICBjb25zdCBwcmV2TGlua05vZGUgPSBub2RlLmdldFByZXZpb3VzU2libGluZygpO1xuICAgICAgaWYgKCRpc0xpbmtOb2RlKHByZXZMaW5rTm9kZSkgJiYgcHJldkxpbmtOb2RlLmlzKGxpbmtOb2RlKSkge1xuICAgICAgICBwcmV2TGlua05vZGUuYXBwZW5kKG5vZGUpO1xuICAgICAgICBjb250aW51ZTtcbiAgICAgIH1cbiAgICAgIGxpbmtOb2RlID0gJGNyZWF0ZUxpbmtOb2RlKHVybCwge1xuICAgICAgICByZWwsXG4gICAgICAgIHRhcmdldCxcbiAgICAgICAgdGl0bGVcbiAgICAgIH0pO1xuICAgICAgbm9kZS5pbnNlcnRBZnRlcihsaW5rTm9kZSk7XG4gICAgICBsaW5rTm9kZS5hcHBlbmQobm9kZSk7XG4gICAgfVxuICB9KTtcbn1cbi8qKiBAZGVwcmVjYXRlZCByZW5hbWVkIHRvIHtAbGluayAkdG9nZ2xlTGlua30gYnkgQGxleGljYWwvZXNsaW50LXBsdWdpbiBydWxlcy1vZi1sZXhpY2FsICovXG5jb25zdCB0b2dnbGVMaW5rID0gJHRvZ2dsZUxpbms7XG5mdW5jdGlvbiAkZ2V0QW5jZXN0b3Iobm9kZSwgcHJlZGljYXRlKSB7XG4gIGxldCBwYXJlbnQgPSBub2RlO1xuICB3aGlsZSAocGFyZW50ICE9PSBudWxsICYmIHBhcmVudC5nZXRQYXJlbnQoKSAhPT0gbnVsbCAmJiAhcHJlZGljYXRlKHBhcmVudCkpIHtcbiAgICBwYXJlbnQgPSBwYXJlbnQuZ2V0UGFyZW50T3JUaHJvdygpO1xuICB9XG4gIHJldHVybiBwcmVkaWNhdGUocGFyZW50KSA/IHBhcmVudCA6IG51bGw7XG59XG5cbmV4cG9ydCB7ICRjcmVhdGVBdXRvTGlua05vZGUsICRjcmVhdGVMaW5rTm9kZSwgJGlzQXV0b0xpbmtOb2RlLCAkaXNMaW5rTm9kZSwgJHRvZ2dsZUxpbmssIEF1dG9MaW5rTm9kZSwgTGlua05vZGUsIFRPR0dMRV9MSU5LX0NPTU1BTkQsIHRvZ2dsZUxpbmsgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+link@0.28.0/node_modules/@lexical/link/LexicalLink.dev.mjs\n");

/***/ })

};
;