import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'
import { api } from '@/lib/api'

// Types - Following Location Management patterns exactly
export interface Permission {
  id: string
  name: string
  code: string
  description?: string
  resource: string
  action: string
  scope?: string
  isActive: boolean
  priority: number
  createdAt: string
  updatedAt: string
}

export interface Role {
  id: string
  name: string
  code: string
  description?: string
  level: string
  scope?: string
  isActive: boolean
  isSystemRole: boolean
  priority: number
  permissions?: Permission[]
  createdAt: string
  updatedAt: string
}

// Following Location Management pagination structure exactly
interface Pagination {
  page: number
  limit: number
  totalDocs: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

// Following Location Management filters structure exactly
interface Filters {
  search: string
  isActive?: boolean
  level?: string
  scope?: string
  resource?: string
  action?: string
}

// Following Location Management state structure exactly
interface RolePermissionsState {
  // Data
  roles: Role[]
  permissions: Permission[]
  
  // UI State
  viewMode: 'list' | 'card'
  isLoading: boolean
  error: string | null
  
  // Pagination - separate for each entity like Location Management
  rolesPagination: Pagination
  permissionsPagination: Pagination
  
  // Filters - separate for each entity like Location Management
  rolesFilters: Filters
  permissionsFilters: Filters
  
  // Selected items
  selectedRole: Role | null
  selectedPermission: Permission | null
  selectedPermissions: string[]
  
  // UI Actions - Following Location Management patterns exactly
  setViewMode: (mode: 'list' | 'card') => void
  setRolesFilters: (filters: Partial<Filters>) => void
  setPermissionsFilters: (filters: Partial<Filters>) => void
  setSelectedRole: (role: Role | null) => void
  setSelectedPermission: (permission: Permission | null) => void
  setSelectedPermissions: (permissions: string[]) => void
  clearError: () => void
  
  // Data Actions - Following Location Management patterns exactly
  fetchRoles: (page?: number, filters?: Partial<Filters>) => Promise<void>
  fetchPermissions: (page?: number, filters?: Partial<Filters>) => Promise<void>
  fetchRolesWithPermissions: () => Promise<void>
  fetchRolePermissions: (roleId: string) => Promise<void>
  
  // CRUD Actions - Roles
  createRole: (roleData: Partial<Role>) => Promise<boolean>
  updateRole: (id: string, roleData: Partial<Role>) => Promise<boolean>
  deleteRole: (id: string) => Promise<boolean>
  
  // CRUD Actions - Permissions
  createPermission: (permissionData: Partial<Permission>) => Promise<boolean>
  updatePermission: (id: string, permissionData: Partial<Permission>) => Promise<boolean>
  deletePermission: (id: string) => Promise<boolean>
  
  // Role-Permission Assignment
  assignPermissionsToRole: (roleId: string, permissionIds: string[]) => Promise<boolean>
  removePermissionFromRole: (roleId: string, permissionId: string) => Promise<boolean>
}

// Initial values following Location Management patterns exactly
const initialPagination: Pagination = {
  page: 1,
  limit: 10,
  totalDocs: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false,
}

const initialFilters: Filters = {
  search: '',
  isActive: undefined,
  level: '',
  scope: '',
  resource: '',
  action: '',
}

export const useRolePermissionsStore = create<RolePermissionsState>()(
  devtools(
    (set, get) => ({
      // Initial State - Following Location Management structure exactly
      roles: [],
      permissions: [],
      viewMode: 'list',
      isLoading: false,
      error: null,
      rolesPagination: initialPagination,
      permissionsPagination: initialPagination,
      rolesFilters: initialFilters,
      permissionsFilters: initialFilters,
      selectedRole: null,
      selectedPermission: null,
      selectedPermissions: [],
      
      // UI Actions - Following Location Management patterns exactly
      setViewMode: (mode) => set({ viewMode: mode }),
      
      setRolesFilters: (newFilters) => set((state) => ({
        rolesFilters: { ...state.rolesFilters, ...newFilters }
      })),
      
      setPermissionsFilters: (newFilters) => set((state) => ({
        permissionsFilters: { ...state.permissionsFilters, ...newFilters }
      })),
      
      setSelectedRole: (role) => set({ 
        selectedRole: role,
        selectedPermission: null,
        selectedPermissions: []
      }),
      
      setSelectedPermission: (permission) => set({ selectedPermission: permission }),
      setSelectedPermissions: (permissions) => set({ selectedPermissions: permissions }),
      clearError: () => set({ error: null }),
      
      // Fetch Roles - Following Location Management patterns exactly
      fetchRoles: async (page = 1, filters) => {
        set({ isLoading: true, error: null })
        try {
          const currentFilters = filters || get().rolesFilters
          const queryParams = new URLSearchParams({
            page: page.toString(),
            limit: '10',
            ...(currentFilters.search && { search: currentFilters.search }),
            ...(currentFilters.level && { level: currentFilters.level }),
            ...(currentFilters.isActive !== undefined && { isActive: currentFilters.isActive.toString() }),
            ...(currentFilters.scope && { scope: currentFilters.scope }),
          })
          
          const response = await api.get(`/roles?${queryParams}`)
          const data = response.data
          
          if (data.success) {
            set({
              roles: data.docs,
              rolesPagination: {
                page: data.page,
                limit: data.limit,
                totalDocs: data.totalDocs,
                totalPages: data.totalPages,
                hasNextPage: data.hasNextPage,
                hasPrevPage: data.hasPrevPage,
              },
              isLoading: false,
            })
          } else {
            throw new Error(data.message || 'Failed to fetch roles')
          }
        } catch (error: any) {
          console.error('Error fetching roles:', error)
          set({ 
            error: error.message || 'Failed to fetch roles',
            isLoading: false 
          })
          toast.error('Failed to fetch roles')
        }
      },
      
      // Fetch Permissions - Following Location Management patterns exactly
      fetchPermissions: async (page = 1, filters) => {
        set({ isLoading: true, error: null })
        try {
          const currentFilters = filters || get().permissionsFilters
          const queryParams = new URLSearchParams({
            page: page.toString(),
            limit: '10',
            ...(currentFilters.search && { search: currentFilters.search }),
            ...(currentFilters.resource && { resource: currentFilters.resource }),
            ...(currentFilters.action && { action: currentFilters.action }),
            ...(currentFilters.isActive !== undefined && { isActive: currentFilters.isActive.toString() }),
          })
          
          const response = await api.get(`/permissions?${queryParams}`)
          const data = response.data
          
          if (data.success) {
            set({
              permissions: data.docs,
              permissionsPagination: {
                page: data.page,
                limit: data.limit,
                totalDocs: data.totalDocs,
                totalPages: data.totalPages,
                hasNextPage: data.hasNextPage,
                hasPrevPage: data.hasPrevPage,
              },
              isLoading: false,
            })
          } else {
            throw new Error(data.message || 'Failed to fetch permissions')
          }
        } catch (error: any) {
          console.error('Error fetching permissions:', error)
          set({ 
            error: error.message || 'Failed to fetch permissions',
            isLoading: false 
          })
          toast.error('Failed to fetch permissions')
        }
      },
      
      // Fetch Roles with Permissions - Custom endpoint
      fetchRolesWithPermissions: async () => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.get('/roles-with-permissions')
          const data = response.data
          
          if (data.success) {
            set({
              roles: data.data,
              isLoading: false,
            })
          } else {
            throw new Error(data.message || 'Failed to fetch roles with permissions')
          }
        } catch (error: any) {
          console.error('Error fetching roles with permissions:', error)
          set({ 
            error: error.message || 'Failed to fetch roles with permissions',
            isLoading: false 
          })
          toast.error('Failed to fetch roles with permissions')
        }
      },
      
      // Fetch Role Permissions
      fetchRolePermissions: async (roleId: string) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.get(`/roles/${roleId}/permissions`)
          const data = response.data
          
          if (data.success) {
            // Update the selected role with its permissions
            const { selectedRole } = get()
            if (selectedRole && selectedRole.id === roleId) {
              set({
                selectedRole: {
                  ...selectedRole,
                  permissions: data.data
                },
                isLoading: false,
              })
            }
          } else {
            throw new Error(data.message || 'Failed to fetch role permissions')
          }
        } catch (error: any) {
          console.error('Error fetching role permissions:', error)
          set({ 
            error: error.message || 'Failed to fetch role permissions',
            isLoading: false 
          })
          toast.error('Failed to fetch role permissions')
        }
      },

      // CRUD Operations - Roles
      createRole: async (roleData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.post('/roles', roleData)
          const data = response.data

          if (data.success) {
            // Refresh roles list
            await get().fetchRoles()
            toast.success('Role created successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to create role')
          }
        } catch (error: any) {
          console.error('Error creating role:', error)
          set({
            error: error.message || 'Failed to create role',
            isLoading: false
          })
          toast.error('Failed to create role')
          return false
        }
      },

      updateRole: async (id, roleData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.put(`/roles/${id}`, roleData)
          const data = response.data

          if (data.success) {
            // Refresh roles list
            await get().fetchRoles()
            toast.success('Role updated successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to update role')
          }
        } catch (error: any) {
          console.error('Error updating role:', error)
          set({
            error: error.message || 'Failed to update role',
            isLoading: false
          })
          toast.error('Failed to update role')
          return false
        }
      },

      deleteRole: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.delete(`/roles/${id}`)
          const data = response.data

          if (data.success) {
            // Refresh roles list
            await get().fetchRoles()
            toast.success('Role deleted successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to delete role')
          }
        } catch (error: any) {
          console.error('Error deleting role:', error)
          set({
            error: error.message || 'Failed to delete role',
            isLoading: false
          })
          toast.error('Failed to delete role')
          return false
        }
      },

      // CRUD Operations - Permissions
      createPermission: async (permissionData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.post('/permissions', permissionData)
          const data = response.data

          if (data.success) {
            // Refresh permissions list
            await get().fetchPermissions()
            toast.success('Permission created successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to create permission')
          }
        } catch (error: any) {
          console.error('Error creating permission:', error)
          set({
            error: error.message || 'Failed to create permission',
            isLoading: false
          })
          toast.error('Failed to create permission')
          return false
        }
      },

      updatePermission: async (id, permissionData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.put(`/permissions/${id}`, permissionData)
          const data = response.data

          if (data.success) {
            // Refresh permissions list
            await get().fetchPermissions()
            toast.success('Permission updated successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to update permission')
          }
        } catch (error: any) {
          console.error('Error updating permission:', error)
          set({
            error: error.message || 'Failed to update permission',
            isLoading: false
          })
          toast.error('Failed to update permission')
          return false
        }
      },

      deletePermission: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.delete(`/permissions/${id}`)
          const data = response.data

          if (data.success) {
            // Refresh permissions list
            await get().fetchPermissions()
            toast.success('Permission deleted successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to delete permission')
          }
        } catch (error: any) {
          console.error('Error deleting permission:', error)
          set({
            error: error.message || 'Failed to delete permission',
            isLoading: false
          })
          toast.error('Failed to delete permission')
          return false
        }
      },

      // Role-Permission Assignment
      assignPermissionsToRole: async (roleId, permissionIds) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.post(`/roles/${roleId}/permissions`, { permissionIds })
          const data = response.data

          if (data.success) {
            // Refresh roles with permissions
            await get().fetchRolesWithPermissions()
            toast.success('Permissions assigned successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to assign permissions')
          }
        } catch (error: any) {
          console.error('Error assigning permissions:', error)
          set({
            error: error.message || 'Failed to assign permissions',
            isLoading: false
          })
          toast.error('Failed to assign permissions')
          return false
        }
      },

      removePermissionFromRole: async (roleId, permissionId) => {
        set({ isLoading: true, error: null })
        try {
          const response = await api.delete(`/roles/${roleId}/permissions/${permissionId}`)
          const data = response.data

          if (data.success) {
            // Refresh roles with permissions
            await get().fetchRolesWithPermissions()
            toast.success('Permission removed successfully')
            return true
          } else {
            throw new Error(data.message || 'Failed to remove permission')
          }
        } catch (error: any) {
          console.error('Error removing permission:', error)
          set({
            error: error.message || 'Failed to remove permission',
            isLoading: false
          })
          toast.error('Failed to remove permission')
          return false
        }
      },
    }),
    {
      name: 'role-permissions-storage',
    }
  )
)
