'use client'

import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'

export default function SuperAdminDashboard() {
  const { user, isAuthenticated, isLoading, logout, initialize } = useAuthStore()
  const router = useRouter()

  useEffect(() => {
    // Initialize auth state on component mount
    initialize()
  }, [initialize])

  useEffect(() => {
    // Only redirect if we're done loading AND definitely not authenticated
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/admin/login')
      return
    }

    // If authenticated but wrong role, redirect to login
    if (!isLoading && isAuthenticated && user && user.legacyRole !== 'super_admin' && user.legacyRole !== 'platform_staff') {
      router.push('/auth/admin/login')
    }
  }, [user, isAuthenticated, isLoading, router])

  // Show loading while checking authentication
  if (isLoading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <div className="bg-white shadow rounded-lg p-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Super Admin Dashboard</h1>
            <p className="text-gray-600">Welcome back, {user.firstName} {user.lastName}</p>
          </div>
          <div className="text-sm text-gray-500">
            Role: <span className="font-medium capitalize">
              {typeof user.role === 'string' ? user.role.replace('_', ' ') : (user.legacyRole || 'Unknown').replace('_', ' ')}
            </span>
          </div>
        </div>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Platform Overview */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">📊</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Institutes</dt>
                  <dd className="text-lg font-medium text-gray-900">24</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Active Users */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">👥</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Active Users</dt>
                  <dd className="text-lg font-medium text-gray-900">1,247</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* Revenue */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">💰</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Monthly Revenue</dt>
                  <dd className="text-lg font-medium text-gray-900">₹2,45,000</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        {/* System Health */}
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <div className="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                  <span className="text-white text-sm font-medium">⚡</span>
                </div>
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">System Health</dt>
                  <dd className="text-lg font-medium text-green-600">Excellent</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link href="/super-admin/institutes" className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
            <div className="text-center">
              <div className="text-2xl mb-2">🏢</div>
              <div className="text-sm font-medium text-gray-900">Manage Institutes</div>
            </div>
          </Link>

          <Link href="/super-admin/themes" className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
            <div className="text-center">
              <div className="text-2xl mb-2">🎨</div>
              <div className="text-sm font-medium text-gray-900">Theme Management</div>
            </div>
          </Link>
          <Link href="/super-admin/settings" className="bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors">
            <div className="text-center">
              <div className="text-2xl mb-2">⚙️</div>
              <div className="text-sm font-medium text-gray-900">System Settings</div>
            </div>
          </Link>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white shadow rounded-lg p-6">
        <h2 className="text-lg font-medium text-gray-900 mb-4">Recent Activity</h2>
        <div className="space-y-3">
          <div className="flex items-center space-x-3 text-sm">
            <div className="w-2 h-2 bg-green-400 rounded-full"></div>
            <span className="text-gray-600">New institute registration: Tech Academy</span>
            <span className="text-gray-400">2 hours ago</span>
          </div>
          <div className="flex items-center space-x-3 text-sm">
            <div className="w-2 h-2 bg-blue-400 rounded-full"></div>
            <span className="text-gray-600">System backup completed successfully</span>
            <span className="text-gray-400">4 hours ago</span>
          </div>
          <div className="flex items-center space-x-3 text-sm">
            <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
            <span className="text-gray-600">Domain verification pending: artschool.edu</span>
            <span className="text-gray-400">6 hours ago</span>
          </div>
        </div>
      </div>
    </div>
  )
}
