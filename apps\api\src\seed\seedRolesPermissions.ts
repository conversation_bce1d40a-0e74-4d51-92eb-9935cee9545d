import type { Payload } from 'payload'

// Helper function to generate permissions
const createPermission = (name: string, code: string, description: string, resource: string, action: string, category: string, level: string = '1') => ({
  name,
  code,
  description,
  resource,
  action,
  category,
  level,
  isActive: true
})

// Define essential permissions for the LMS system
const PERMISSIONS = [
  // User Management
  createPermission('View Users', 'view_users', 'View user list and details', 'users', 'view', 'users', '1'),
  createPermission('Create Users', 'create_users', 'Create new users', 'users', 'create', 'users', '1'),
  createPermission('Update Users', 'update_users', 'Edit user information', 'users', 'update', 'users', '1'),
  createPermission('Delete Users', 'delete_users', 'Delete users from system', 'users', 'delete', 'users', '1'),
  createPermission('Manage Users', 'manage_users', 'Full user management access', 'users', 'manage', 'users', '1'),

  // Role & Permission Management
  createPermission('View Roles', 'view_roles', 'View roles and their details', 'roles', 'view', 'users', '1'),
  createPermission('Create Roles', 'create_roles', 'Create new roles', 'roles', 'create', 'users', '1'),
  createPermission('Update Roles', 'update_roles', 'Edit role information', 'roles', 'update', 'users', '1'),
  createPermission('Delete Roles', 'delete_roles', 'Delete roles from system', 'roles', 'delete', 'users', '1'),
  createPermission('Manage Roles', 'manage_roles', 'Full role management access', 'roles', 'manage', 'users', '1'),

  createPermission('View Permissions', 'view_permissions', 'View permissions list', 'permissions', 'view', 'users', '1'),
  createPermission('Create Permissions', 'create_permissions', 'Create new permissions', 'permissions', 'create', 'users', '1'),
  createPermission('Update Permissions', 'update_permissions', 'Edit permission details', 'permissions', 'update', 'users', '1'),
  createPermission('Delete Permissions', 'delete_permissions', 'Delete permissions', 'permissions', 'delete', 'users', '1'),
  createPermission('Manage Permissions', 'manage_permissions', 'Full permission management', 'permissions', 'manage', 'users', '1'),

  // Institute Management
  createPermission('View Institutes', 'view_institutes', 'View institute list and details', 'institutes', 'view', 'institute', '1'),
  createPermission('Create Institutes', 'create_institutes', 'Create new institutes', 'institutes', 'create', 'institute', '1'),
  createPermission('Update Institutes', 'update_institutes', 'Edit institute information', 'institutes', 'update', 'institute', '1'),
  createPermission('Delete Institutes', 'delete_institutes', 'Delete institutes', 'institutes', 'delete', 'institute', '1'),
  createPermission('Manage Institutes', 'manage_institutes', 'Full institute management', 'institutes', 'manage', 'institute', '1'),

  // Course Management
  createPermission('View Courses', 'view_courses', 'View course list and details', 'courses', 'view', 'courses', '2'),
  createPermission('Create Courses', 'create_courses', 'Create new courses', 'courses', 'create', 'courses', '2'),
  createPermission('Update Courses', 'update_courses', 'Edit course information', 'courses', 'update', 'courses', '2'),
  createPermission('Delete Courses', 'delete_courses', 'Delete courses', 'courses', 'delete', 'courses', '2'),
  createPermission('Manage Courses', 'manage_courses', 'Full course management', 'courses', 'manage', 'courses', '2'),

  // Student Management
  createPermission('View Students', 'view_students', 'View student list and details', 'students', 'view', 'students', '2'),
  createPermission('Create Students', 'create_students', 'Create new student accounts', 'students', 'create', 'students', '2'),
  createPermission('Update Students', 'update_students', 'Edit student information', 'students', 'update', 'students', '2'),
  createPermission('Delete Students', 'delete_students', 'Delete student accounts', 'students', 'delete', 'students', '2'),
  createPermission('Manage Students', 'manage_students', 'Full student management', 'students', 'manage', 'students', '2'),

  // Branch Management
  createPermission('View Branches', 'view_branches', 'View branch list and details', 'branches', 'view', 'branch', '2'),
  createPermission('Create Branches', 'create_branches', 'Create new branches', 'branches', 'create', 'branch', '2'),
  createPermission('Update Branches', 'update_branches', 'Edit branch information', 'branches', 'update', 'branch', '2'),
  createPermission('Delete Branches', 'delete_branches', 'Delete branches', 'branches', 'delete', 'branch', '2'),
  createPermission('Manage Branches', 'manage_branches', 'Full branch management', 'branches', 'manage', 'branch', '2'),

  // System Settings
  createPermission('View Settings', 'view_settings', 'View system settings', 'settings', 'view', 'settings', '1'),
  createPermission('Update Settings', 'update_settings', 'Modify system settings', 'settings', 'update', 'settings', '1'),
  createPermission('Manage Settings', 'manage_settings', 'Full settings management', 'settings', 'manage', 'settings', '1'),

  // Analytics & Reports
  createPermission('View Analytics', 'view_analytics', 'View system analytics', 'analytics', 'view', 'reports', '1'),
  createPermission('View Reports', 'view_reports', 'View system reports', 'reports', 'view', 'reports', '1'),
  createPermission('Export Reports', 'export_reports', 'Export reports and data', 'reports', 'export', 'reports', '1'),

  // Billing & Finance
  createPermission('View Billing', 'view_billing', 'View billing information', 'billing', 'view', 'billing', '1'),
  createPermission('Manage Billing', 'manage_billing', 'Manage billing and payments', 'billing', 'manage', 'billing', '1'),

  // System Administration
  createPermission('System Admin', 'system_admin', 'Full system administration access', 'system', 'manage', 'platform', '1'),
  createPermission('Access All', 'access_all', 'Access to all system features', 'all', 'manage', 'platform', '1'),


]

// Define roles with their basic information
const ROLES = [
  {
    name: 'Super Admin',
    code: 'super_admin',
    description: 'Full system access with all permissions',
    level: '1',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 1,
    metadata: {
      maxUsers: null,
      autoAssign: false,
      requiresApproval: false
    }
  },
  {
    name: 'Platform Staff',
    code: 'platform_staff',
    description: 'Platform staff with administrative access',
    level: '1',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 2,
    metadata: {
      maxUsers: null,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Institute Admin',
    code: 'institute_admin',
    description: 'Institute administrator with full institute access',
    level: '2',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 3,
    metadata: {
      maxUsers: null,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Branch Manager',
    code: 'branch_manager',
    description: 'Branch manager with branch-level access',
    level: '3',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 4,
    metadata: {
      maxUsers: 50,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Trainer',
    code: 'trainer',
    description: 'Course trainer with teaching permissions',
    level: '3',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 5,
    metadata: {
      maxUsers: 100,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Institute Staff',
    code: 'institute_staff',
    description: 'Institute staff with limited administrative access',
    level: '3',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 6,
    metadata: {
      maxUsers: 20,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Student',
    code: 'student',
    description: 'Student with learning access',
    level: '3',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 7,
    metadata: {
      maxUsers: null,
      autoAssign: true,
      requiresApproval: false
    }
  }
]

// Define which permissions each role should have
const ROLE_PERMISSIONS = {
  super_admin: [
    // Super Admin gets ALL permissions
    ...PERMISSIONS.map(p => p.code)
  ],
  platform_staff: [
    'view_users', 'view_institutes', 'view_analytics', 'view_reports', 'view_settings',
    'manage_institutes', 'view_billing', 'export_reports'
  ],
  institute_admin: [
    'view_users', 'create_users', 'update_users', 'manage_users',
    'view_courses', 'create_courses', 'update_courses', 'delete_courses', 'manage_courses',
    'view_students', 'create_students', 'update_students', 'delete_students', 'manage_students',
    'view_branches', 'create_branches', 'update_branches', 'delete_branches', 'manage_branches',
    'view_analytics', 'view_reports', 'export_reports', 'view_billing'
  ],
  branch_manager: [
    'view_users', 'create_users', 'update_users',
    'view_courses', 'create_courses', 'update_courses', 'manage_courses',
    'view_students', 'create_students', 'update_students', 'manage_students',
    'view_branches', 'update_branches',
    'view_analytics', 'view_reports'
  ],
  trainer: [
    'view_courses', 'update_courses',
    'view_students', 'update_students'
  ],
  institute_staff: [
    'view_users', 'view_courses', 'view_students', 'update_students'
  ],
  student: [
    'view_courses', 'view_students'
  ]
}

export async function seedRolesAndPermissions(payload: Payload) {
  console.log('🌱 Starting roles and permissions seeding...')

  try {
    // 1. Create Permissions
    console.log('📝 Creating permissions...')
    const createdPermissions: any[] = []
    
    for (const permission of PERMISSIONS) {
      try {
        // Check if permission already exists
        const existing = await payload.find({
          collection: 'permissions',
          where: { code: { equals: permission.code } }
        })

        if (existing.docs.length === 0) {
          const created = await payload.create({
            collection: 'permissions',
            data: permission as any
          })
          createdPermissions.push(created)
          console.log(`  ✅ Created permission: ${permission.name}`)
        } else {
          createdPermissions.push(existing.docs[0])
          console.log(`  ⏭️  Permission already exists: ${permission.name}`)
        }
      } catch (error) {
        console.error(`  ❌ Error creating permission ${permission.name}:`, error)
      }
    }

    // 2. Create Roles
    console.log('👥 Creating roles...')
    const createdRoles: any[] = []
    
    for (const role of ROLES) {
      try {
        // Check if role already exists
        const existing = await payload.find({
          collection: 'roles',
          where: { code: { equals: role.code } }
        })

        if (existing.docs.length === 0) {
          const created = await payload.create({
            collection: 'roles',
            data: role as any
          })
          createdRoles.push(created)
          console.log(`  ✅ Created role: ${role.name}`)
        } else {
          createdRoles.push(existing.docs[0])
          console.log(`  ⏭️  Role already exists: ${role.name}`)
        }
      } catch (error) {
        console.error(`  ❌ Error creating role ${role.name}:`, error)
      }
    }

    // 3. Create Role-Permission relationships
    console.log('🔗 Creating role-permission relationships...')
    
    for (const [roleCode, permissionCodes] of Object.entries(ROLE_PERMISSIONS)) {
      const role = createdRoles.find(r => r.code === roleCode)
      if (!role) {
        console.log(`  ⚠️  Role not found: ${roleCode}`)
        continue
      }

      console.log(`  🔗 Assigning permissions to ${role.name}...`)
      
      for (const permissionCode of permissionCodes) {
        const permission = createdPermissions.find(p => p.code === permissionCode)
        if (!permission) {
          console.log(`    ⚠️  Permission not found: ${permissionCode}`)
          continue
        }

        try {
          // Check if relationship already exists
          const existing = await payload.find({
            collection: 'role-permissions',
            where: {
              and: [
                { role: { equals: role.id } },
                { permission: { equals: permission.id } }
              ]
            }
          })

          if (existing.docs.length === 0) {
            await payload.create({
              collection: 'role-permissions',
              data: {
                role: role.id,
                permission: permission.id,
                scope: 'global',
                isActive: true,
                assignedAt: new Date().toISOString()
              } as any
            })
            console.log(`    ✅ Assigned ${permission.name} to ${role.name}`)
          } else {
            console.log(`    ⏭️  Permission already assigned: ${permission.name} to ${role.name}`)
          }
        } catch (error) {
          console.error(`    ❌ Error assigning ${permission.name} to ${role.name}:`, error)
        }
      }
    }

    console.log('🎉 Roles and permissions seeding completed successfully!')
    
    // Summary
    console.log('\n📊 Seeding Summary:')
    console.log(`  Permissions: ${createdPermissions.length}`)
    console.log(`  Roles: ${createdRoles.length}`)
    console.log(`  Super Admin permissions: ${ROLE_PERMISSIONS.super_admin.length}`)
    
    return {
      permissions: createdPermissions,
      roles: createdRoles,
      success: true
    }

  } catch (error) {
    console.error('❌ Error during roles and permissions seeding:', error)
    throw error
  }
}

export default seedRolesAndPermissions
