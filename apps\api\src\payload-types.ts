/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    institutes: Institute;
    courses: Course;
    themes: Theme;
    'course-categories': CourseCategory;
    permissions: Permission;
    roles: Role;
    'user-permissions': UserPermission;
    'role-permissions': RolePermission;
    sessions: Session;
    settings: Setting;
    'domain-requests': DomainRequest;
    countries: Country;
    states: State;
    districts: District;
    'tax-components': TaxComponent;
    'tax-groups': TaxGroup;
    'tax-rules': TaxRule;
    branches: Branch;
    bills: Bill;
    'course-purchases': CoursePurchase;
    'payment-gateways': PaymentGateway;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    institutes: InstitutesSelect<false> | InstitutesSelect<true>;
    courses: CoursesSelect<false> | CoursesSelect<true>;
    themes: ThemesSelect<false> | ThemesSelect<true>;
    'course-categories': CourseCategoriesSelect<false> | CourseCategoriesSelect<true>;
    permissions: PermissionsSelect<false> | PermissionsSelect<true>;
    roles: RolesSelect<false> | RolesSelect<true>;
    'user-permissions': UserPermissionsSelect<false> | UserPermissionsSelect<true>;
    'role-permissions': RolePermissionsSelect<false> | RolePermissionsSelect<true>;
    sessions: SessionsSelect<false> | SessionsSelect<true>;
    settings: SettingsSelect<false> | SettingsSelect<true>;
    'domain-requests': DomainRequestsSelect<false> | DomainRequestsSelect<true>;
    countries: CountriesSelect<false> | CountriesSelect<true>;
    states: StatesSelect<false> | StatesSelect<true>;
    districts: DistrictsSelect<false> | DistrictsSelect<true>;
    'tax-components': TaxComponentsSelect<false> | TaxComponentsSelect<true>;
    'tax-groups': TaxGroupsSelect<false> | TaxGroupsSelect<true>;
    'tax-rules': TaxRulesSelect<false> | TaxRulesSelect<true>;
    branches: BranchesSelect<false> | BranchesSelect<true>;
    bills: BillsSelect<false> | BillsSelect<true>;
    'course-purchases': CoursePurchasesSelect<false> | CoursePurchasesSelect<true>;
    'payment-gateways': PaymentGatewaysSelect<false> | PaymentGatewaysSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: number;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: number;
  /**
   * Select a role for this user
   */
  role?: (number | null) | Role;
  /**
   * Legacy role field - currently used for access control
   */
  legacyRole:
    | 'super_admin'
    | 'platform_staff'
    | 'institute_admin'
    | 'branch_manager'
    | 'trainer'
    | 'institute_staff'
    | 'student';
  firstName: string;
  lastName: string;
  phone?: string | null;
  avatar?: (number | null) | Media;
  institute?: (number | null) | Institute;
  branch?: string | null;
  isActive?: boolean | null;
  emailVerified?: boolean | null;
  lastLogin?: string | null;
  lockedUntil?: string | null;
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "roles".
 */
export interface Role {
  id: number;
  name: string;
  code: string;
  description?: string | null;
  level: '1' | '2' | '3';
  permissions?:
    | {
        permission: number | Permission;
        scope?: ('platform' | 'institute' | 'branch' | 'self') | null;
        id?: string | null;
      }[]
    | null;
  scope?: {
    institute?: (number | null) | Institute;
    branch?: (number | null) | Branch;
  };
  /**
   * System roles cannot be deleted
   */
  isSystemRole?: boolean | null;
  isActive?: boolean | null;
  metadata?: {
    /**
     * Maximum number of users that can have this role (0 = unlimited)
     */
    maxUsers?: number | null;
    /**
     * Automatically assign this role to new users
     */
    autoAssign?: boolean | null;
    /**
     * Role assignment requires approval
     */
    requiresApproval?: boolean | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "permissions".
 */
export interface Permission {
  id: number;
  name: string;
  description?: string | null;
  code: string;
  category: 'platform' | 'institute' | 'branch' | 'users' | 'courses' | 'students' | 'billing' | 'reports' | 'settings';
  level: '1' | '2' | '3';
  /**
   * The resource this permission applies to (e.g., users, courses, institutes)
   */
  resource: string;
  action: 'create' | 'read' | 'update' | 'delete' | 'manage' | 'view' | 'execute' | 'approve' | 'export' | 'import';
  /**
   * System permissions cannot be deleted and are required for core functionality
   */
  isSystemPermission?: boolean | null;
  isActive?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "institutes".
 */
export interface Institute {
  id: number;
  name: string;
  /**
   * URL-friendly version of the institute name (e.g., abc-academy)
   */
  slug: string;
  email: string;
  phone: string;
  website?: string | null;
  logo?: (number | null) | Media;
  description?: string | null;
  address: {
    street?: string | null;
    city: string;
    state: string;
    country: string;
    zipCode?: string | null;
  };
  /**
   * Custom domain for the institute (e.g., abc-academy.com)
   */
  customDomain?: string | null;
  domainVerified?: boolean | null;
  subscriptionPlan: 'free_trial' | 'basic' | 'professional' | 'enterprise';
  subscriptionStatus: 'active' | 'inactive' | 'suspended' | 'cancelled';
  subscriptionExpiry?: string | null;
  maxStudents?: number | null;
  maxCourses?: number | null;
  maxBranches?: number | null;
  features?: {
    marketplace?: boolean | null;
    liveClasses?: boolean | null;
    exams?: boolean | null;
    blogs?: boolean | null;
    analytics?: boolean | null;
  };
  paymentGateways?:
    | {
        gateway?: ('razorpay' | 'payu' | 'stripe' | 'paypal') | null;
        isActive?: boolean | null;
        /**
         * API Key (encrypted)
         */
        apiKey?: string | null;
        /**
         * Secret Key (encrypted)
         */
        secretKey?: string | null;
        id?: string | null;
      }[]
    | null;
  isActive?: boolean | null;
  createdBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: number;
  /**
   * Alternative text for accessibility and SEO
   */
  alt: string;
  /**
   * Caption or description for the media
   */
  caption?: string | null;
  /**
   * Type of media for organization and filtering
   */
  mediaType?:
    | (
        | 'course_thumbnail'
        | 'course_content'
        | 'theme_asset'
        | 'institute_logo'
        | 'user_avatar'
        | 'blog_image'
        | 'marketing'
        | 'document'
        | 'other'
      )
    | null;
  /**
   * User who uploaded this media
   */
  uploadedBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
  sizes?: {
    thumbnail?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    card?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
    hero?: {
      url?: string | null;
      width?: number | null;
      height?: number | null;
      mimeType?: string | null;
      filesize?: number | null;
      filename?: string | null;
    };
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "branches".
 */
export interface Branch {
  id: number;
  name: string;
  code: string;
  institute: number | Institute;
  location: {
    address: string;
    country: number | Country;
    state: number | State;
    district: number | District;
    pincode?: string | null;
    coordinates?: {
      latitude?: number | null;
      longitude?: number | null;
    };
  };
  contact?: {
    phone?: string | null;
    email?: string | null;
    website?: string | null;
  };
  taxInformation?: {
    gstNumber?: string | null;
    panNumber?: string | null;
    taxRegistrationNumber?: string | null;
    isGstRegistered?: boolean | null;
  };
  billingSettings?: {
    billingCycle?: ('monthly' | 'quarterly' | 'yearly') | null;
    /**
     * Day of month for billing (1-28)
     */
    billingDay?: number | null;
    currency?: string | null;
    /**
     * Platform commission percentage
     */
    commissionRate?: number | null;
  };
  isActive?: boolean | null;
  /**
   * Mark as head office/main branch
   */
  isHeadOffice?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "countries".
 */
export interface Country {
  id: number;
  name: string;
  code: string;
  flag?: (number | null) | Media;
  details?: {
    capital?: string | null;
    currency?: string | null;
    currencyCode?: string | null;
    language?: string | null;
    timezone?: string | null;
    population?: number | null;
    /**
     * Area in square kilometers
     */
    area?: number | null;
  };
  coordinates?: {
    latitude?: number | null;
    longitude?: number | null;
  };
  isActive?: boolean | null;
  /**
   * Higher priority countries appear first
   */
  priority?: number | null;
  /**
   * Additional country metadata
   */
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "states".
 */
export interface State {
  id: number;
  name: string;
  code?: string | null;
  country: number | Country;
  details?: {
    capital?: string | null;
    population?: number | null;
    /**
     * Area in square kilometers
     */
    area?: number | null;
    type?: ('state' | 'province' | 'territory' | 'region') | null;
  };
  coordinates?: {
    latitude?: number | null;
    longitude?: number | null;
  };
  isActive?: boolean | null;
  priority?: number | null;
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "districts".
 */
export interface District {
  id: number;
  name: string;
  code?: string | null;
  state: number | State;
  details: {
    type: 'district' | 'city' | 'municipality' | 'town' | 'village';
    population?: number | null;
    area?: number | null;
    pincode?: string | null;
  };
  coordinates?: {
    latitude?: number | null;
    longitude?: number | null;
  };
  isActive?: boolean | null;
  priority?: number | null;
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "courses".
 */
export interface Course {
  id: number;
  /**
   * Course title that will be displayed to students
   */
  title: string;
  /**
   * URL-friendly version of the title
   */
  slug: string;
  /**
   * Detailed course description with formatting
   */
  description: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * Brief description for course cards and previews
   */
  shortDescription: string;
  /**
   * Course thumbnail image (recommended: 1280x720px)
   */
  thumbnail: number | Media;
  /**
   * Institute offering this course
   */
  institute: number | Institute;
  /**
   * Primary instructor for this course
   */
  instructor: number | User;
  /**
   * Course category for filtering and organization
   */
  category:
    | 'upsc'
    | 'banking'
    | 'ssc'
    | 'it_software'
    | 'languages'
    | 'business'
    | 'design'
    | 'marketing'
    | 'engineering'
    | 'medical'
    | 'law'
    | 'other';
  /**
   * Difficulty level of the course
   */
  level: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  /**
   * Primary language of instruction
   */
  language:
    | 'english'
    | 'hindi'
    | 'tamil'
    | 'telugu'
    | 'bengali'
    | 'marathi'
    | 'gujarati'
    | 'kannada'
    | 'malayalam'
    | 'punjabi';
  pricing: {
    /**
     * Current selling price
     */
    price: number;
    /**
     * Original price (for showing discounts)
     */
    originalPrice?: number | null;
    currency?: ('INR' | 'USD' | 'EUR') | null;
    /**
     * Mark as free course
     */
    isFree?: boolean | null;
  };
  duration: {
    /**
     * Total course duration in hours
     */
    totalHours: number;
    /**
     * Total number of lessons
     */
    totalLessons: number;
    /**
     * Estimated time to complete (e.g., "4 weeks", "2 months")
     */
    estimatedCompletion?: string | null;
  };
  /**
   * Course publication status
   */
  status: 'draft' | 'review' | 'published' | 'archived';
  /**
   * Feature this course on homepage and promotions
   */
  featured?: boolean | null;
  /**
   * Total number of enrolled students
   */
  enrollmentCount?: number | null;
  rating?: {
    /**
     * Average rating from student reviews
     */
    average?: number | null;
    /**
     * Total number of ratings
     */
    count?: number | null;
  };
  /**
   * Tags for better searchability
   */
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "themes".
 */
export interface Theme {
  id: number;
  /**
   * Display name of the theme
   */
  name: string;
  /**
   * URL-friendly identifier for the theme
   */
  slug: string;
  /**
   * Type of theme - Platform for main site, Institute for institute websites
   */
  type: 'platform' | 'institute';
  /**
   * Theme category for organization and filtering
   */
  category:
    | 'saas_modern'
    | 'saas_corporate'
    | 'saas_startup'
    | 'saas_minimal'
    | 'education_modern'
    | 'education_classic'
    | 'coaching_professional'
    | 'university_classic'
    | 'online_academy'
    | 'training_center';
  /**
   * Brief description of the theme and its features
   */
  description: string;
  /**
   * Theme version number
   */
  version: string;
  /**
   * Preview thumbnail (400x300px recommended)
   */
  previewImage: number | Media;
  /**
   * Full demo screenshot (1200x800px recommended)
   */
  demoImage: number | Media;
  /**
   * Additional screenshots showcasing theme features
   */
  screenshots?:
    | {
        image: number | Media;
        title: string;
        description?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Default color scheme for the theme
   */
  colors: {
    /**
     * Primary brand color (hex code)
     */
    primary: string;
    /**
     * Secondary color (hex code)
     */
    secondary: string;
    /**
     * Accent color for highlights (hex code)
     */
    accent: string;
    /**
     * Background color (hex code)
     */
    background: string;
    /**
     * Primary text color (hex code)
     */
    text: string;
    /**
     * Muted text color (hex code)
     */
    muted?: string | null;
    /**
     * Border color (hex code)
     */
    border?: string | null;
  };
  /**
   * Typography settings for the theme
   */
  fonts: {
    /**
     * Font family for headings
     */
    heading: string;
    /**
     * Font family for body text
     */
    body: string;
    /**
     * Monospace font for code
     */
    mono?: string | null;
  };
  /**
   * List of theme features and capabilities
   */
  features?:
    | {
        feature: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Types of institutes this theme is suitable for
   */
  suitableFor?:
    | {
        type?:
          | (
              | 'coaching_centers'
              | 'online_academies'
              | 'universities'
              | 'training_institutes'
              | 'skill_development'
              | 'professional_courses'
              | 'exam_preparation'
              | 'corporate_training'
            )
          | null;
        id?: string | null;
      }[]
    | null;
  /**
   * JSON configuration for customizable theme elements
   */
  customizableElements?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  /**
   * Number of institutes using this theme
   */
  usageCount?: number | null;
  rating?: {
    /**
     * Average rating from users
     */
    average?: number | null;
    /**
     * Total number of ratings
     */
    count?: number | null;
  };
  /**
   * Whether this theme is available for selection
   */
  isActive?: boolean | null;
  /**
   * Set as default theme for new institutes
   */
  isDefault?: boolean | null;
  /**
   * Feature this theme in the theme gallery
   */
  isFeatured?: boolean | null;
  /**
   * URL to live demo of the theme
   */
  demoUrl?: string | null;
  /**
   * URL to theme documentation
   */
  documentationUrl?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "course-categories".
 */
export interface CourseCategory {
  id: number;
  /**
   * Category name (e.g., "UPSC Preparation", "Web Development")
   */
  name: string;
  /**
   * URL-friendly version of the category name
   */
  slug: string;
  /**
   * Brief description of what this category covers
   */
  description?: string | null;
  /**
   * Parent category for hierarchical organization
   */
  parent?: (number | null) | CourseCategory;
  /**
   * Icon name or class for displaying category icon
   */
  icon?: string | null;
  /**
   * Color code for category theming (hex format)
   */
  color?: string | null;
  /**
   * Category banner or representative image
   */
  image?: (number | null) | Media;
  /**
   * Whether this category is available for course assignment
   */
  isActive?: boolean | null;
  /**
   * Feature this category on homepage and promotions
   */
  isFeatured?: boolean | null;
  /**
   * Order for displaying categories (lower numbers first)
   */
  sortOrder?: number | null;
  /**
   * Number of courses in this category
   */
  courseCount?: number | null;
  /**
   * SEO title for category pages
   */
  seoTitle?: string | null;
  /**
   * SEO meta description for category pages
   */
  seoDescription?: string | null;
  /**
   * Tags for better searchability and filtering
   */
  tags?:
    | {
        tag: string;
        id?: string | null;
      }[]
    | null;
  /**
   * Institute-specific category (leave empty for global categories)
   */
  institute?: (number | null) | Institute;
  /**
   * User who created this category
   */
  createdBy?: (number | null) | User;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-permissions".
 */
export interface UserPermission {
  id: number;
  user: number | User;
  permission: number | Permission;
  type: 'grant' | 'revoke' | 'override';
  scope: 'platform' | 'institute' | 'branch' | 'self';
  scopeTarget?: {
    institute?: (number | null) | Institute;
    branch?: (number | null) | Branch;
  };
  status: 'pending' | 'approved' | 'rejected' | 'revoked';
  approvalWorkflow: {
    requestedBy: number | User;
    approvedBy?: (number | null) | User;
    rejectedBy?: (number | null) | User;
    approvalDate?: string | null;
    rejectionDate?: string | null;
    /**
     * Reason for approval/rejection
     */
    reason?: string | null;
  };
  effectivePeriod?: {
    startDate?: string | null;
    /**
     * Leave empty for permanent permission
     */
    endDate?: string | null;
  };
  isActive?: boolean | null;
  metadata?: {
    /**
     * Permission was automatically approved
     */
    autoApproved?: boolean | null;
    /**
     * Role this permission was inherited from
     */
    inheritedFromRole?: (number | null) | Role;
    notes?: string | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "role-permissions".
 */
export interface RolePermission {
  id: number;
  /**
   * The role to assign permissions to
   */
  role: number | Role;
  /**
   * The permission to assign to the role
   */
  permission: number | Permission;
  /**
   * Whether this role-permission assignment is active
   */
  isActive?: boolean | null;
  /**
   * User who assigned this permission to the role
   */
  assignedBy?: (number | null) | User;
  /**
   * When this permission was assigned to the role
   */
  assignedAt?: string | null;
  /**
   * Optional notes about this role-permission assignment
   */
  notes?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sessions".
 */
export interface Session {
  id: number;
  user: number | User;
  sessionToken: string;
  deviceInfo: {
    deviceType: 'desktop' | 'mobile' | 'tablet';
    deviceName: string;
    browser: string;
    operatingSystem: string;
    userAgent?: string | null;
  };
  location: {
    ipAddress: string;
    city?: string | null;
    region?: string | null;
    country?: string | null;
    latitude?: number | null;
    longitude?: number | null;
  };
  security?: {
    isSecure?: boolean | null;
    isCurrent?: boolean | null;
    loginMethod?: ('password' | '2fa' | 'social' | 'sso') | null;
  };
  isActive?: boolean | null;
  lastActivity: string;
  expiresAt: string;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "settings".
 */
export interface Setting {
  id: number;
  key: string;
  value:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  category:
    | 'platform'
    | 'security'
    | 'email'
    | 'storage'
    | 'billing'
    | 'theme'
    | 'institute'
    | 'course'
    | 'user'
    | 'notification';
  scope: 'global' | 'institute' | 'branch';
  dataType: 'string' | 'number' | 'boolean' | 'json' | 'array';
  description?: string | null;
  institute?: (number | null) | Institute;
  /**
   * Whether this setting can be accessed by non-authenticated users
   */
  isPublic?: boolean | null;
  /**
   * Whether this setting can be modified through the UI
   */
  isEditable?: boolean | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "domain-requests".
 */
export interface DomainRequest {
  id: number;
  institute: number | Institute;
  requestedDomain: string;
  currentDomain?: string | null;
  status: 'pending' | 'approved' | 'active' | 'rejected';
  purpose: string;
  notes?: string | null;
  requestedBy: number | User;
  requestedAt: string;
  reviewedBy?: (number | null) | User;
  reviewedAt?: string | null;
  rejectionReason?: string | null;
  sslStatus?: ('pending' | 'active' | 'failed') | null;
  dnsRecords?:
    | {
        type: 'A' | 'CNAME' | 'TXT';
        name: string;
        value: string;
        ttl: number;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-components".
 */
export interface TaxComponent {
  id: number;
  name: string;
  code: string;
  description?: string | null;
  type: 'sgst' | 'cgst' | 'igst' | 'vat' | 'sales_tax' | 'income_tax' | 'service_tax' | 'custom';
  /**
   * Tax rate as percentage (e.g., 9 for 9%)
   */
  rate: number;
  applicableRegions?:
    | {
        country: number | Country;
        states?: (number | State)[] | null;
        /**
         * Default tax for this region
         */
        isDefault?: boolean | null;
        id?: string | null;
      }[]
    | null;
  calculationMethod: 'percentage' | 'fixed' | 'tiered';
  tieredRates?:
    | {
        minAmount: number;
        maxAmount?: number | null;
        rate: number;
        id?: string | null;
      }[]
    | null;
  isActive?: boolean | null;
  effectiveFrom: string;
  effectiveTo?: string | null;
  /**
   * Higher priority taxes are applied first
   */
  priority?: number | null;
  /**
   * Additional tax metadata and configuration
   */
  metadata?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-groups".
 */
export interface TaxGroup {
  id: number;
  name: string;
  code: string;
  description?: string | null;
  taxComponents: {
    component: number | TaxComponent;
    /**
     * Override component rate (optional)
     */
    customRate?: number | null;
    isOptional?: boolean | null;
    id?: string | null;
  }[];
  /**
   * Calculated total rate of all components
   */
  totalRate?: number | null;
  applicableScenarios?:
    | {
        scenario: 'intra_state' | 'inter_state' | 'international' | 'b2b' | 'b2c' | 'export' | 'import';
        fromCountry?: (number | null) | Country;
        toCountry?: (number | null) | Country;
        fromState?: (number | null) | State;
        toState?: (number | null) | State;
        id?: string | null;
      }[]
    | null;
  isActive?: boolean | null;
  /**
   * Default tax group for applicable scenarios
   */
  isDefault?: boolean | null;
  effectiveFrom: string;
  effectiveTo?: string | null;
  priority?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-rules".
 */
export interface TaxRule {
  id: number;
  name: string;
  description?: string | null;
  taxGroup: number | TaxGroup;
  conditions?: {
    transactionType?:
      | ('course_purchase' | 'subscription' | 'certification' | 'live_class' | 'exam_fee' | 'material_purchase')
      | null;
    customerType?: ('individual' | 'business' | 'educational' | 'government') | null;
    /**
     * Minimum transaction amount for this rule
     */
    minAmount?: number | null;
    /**
     * Maximum transaction amount for this rule
     */
    maxAmount?: number | null;
    /**
     * Customer location country
     */
    customerCountry?: (number | null) | Country;
    /**
     * Customer location state
     */
    customerState?: (number | null) | State;
    /**
     * Institute location country
     */
    instituteCountry?: (number | null) | Country;
    /**
     * Institute location state
     */
    instituteState?: (number | null) | State;
    /**
     * Location-based tax scenario
     */
    locationScenario?: ('same_state' | 'different_state' | 'different_country') | null;
    /**
     * Date from which this condition is effective
     */
    conditionEffectiveFrom?: string | null;
    /**
     * Date until which this condition is effective
     */
    conditionEffectiveTo?: string | null;
    /**
     * Days of week when this rule applies
     */
    applicableDays?: ('1' | '2' | '3' | '4' | '5' | '6' | '0')[] | null;
  };
  exemptions?:
    | {
        condition:
          | 'student_discount'
          | 'educational_exemption'
          | 'government_exemption'
          | 'export_exemption'
          | 'threshold_exemption'
          | 'special_category';
        /**
         * Percentage of tax to exempt (100 = full exemption)
         */
        exemptionPercentage?: number | null;
        thresholdAmount?: number | null;
        id?: string | null;
      }[]
    | null;
  isActive?: boolean | null;
  /**
   * Higher priority rules are evaluated first
   */
  priority: number;
  effectiveFrom: string;
  effectiveTo?: string | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "bills".
 */
export interface Bill {
  id: number;
  billNumber: string;
  branch: number | Branch;
  billingPeriod: {
    startDate: string;
    endDate: string;
    month: number;
    year: number;
  };
  amounts: {
    /**
     * Monthly base subscription fee
     */
    baseFee: number;
    /**
     * Total commission from student purchases
     */
    commissionAmount: number;
    /**
     * Base fee + Commission amount
     */
    subtotal: number;
    /**
     * Tax amount based on location
     */
    taxAmount: number;
    /**
     * Final amount including tax
     */
    totalAmount: number;
    currency: 'INR' | 'USD' | 'EUR' | 'GBP';
  };
  taxDetails?: {
    taxScenario?: ('intra_state' | 'inter_state' | 'international') | null;
    taxComponents?:
      | {
          componentName: string;
          componentCode: string;
          rate: number;
          amount: number;
          id?: string | null;
        }[]
      | null;
  };
  commissionDetails?:
    | {
        studentPurchase?: (number | null) | CoursePurchase;
        courseTitle: string;
        studentName: string;
        purchaseAmount: number;
        /**
         * Commission rate as percentage
         */
        commissionRate: number;
        commissionAmount: number;
        purchaseDate: string;
        id?: string | null;
      }[]
    | null;
  status: 'pending' | 'sent' | 'viewed' | 'paid' | 'overdue' | 'cancelled';
  dates: {
    generatedDate: string;
    sentDate?: string | null;
    dueDate: string;
    paidDate?: string | null;
    viewedDate?: string | null;
  };
  paymentDetails?: {
    paymentMethod?: ('razorpay' | 'stripe' | 'paypal' | 'bank_transfer' | 'upi') | null;
    transactionId?: string | null;
    /**
     * Payment gateway response data
     */
    paymentGatewayResponse?:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
    paidBy?: (number | null) | User;
  };
  notes?: string | null;
  attachments?:
    | {
        file?: (number | null) | Media;
        description?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "course-purchases".
 */
export interface CoursePurchase {
  id: number;
  student: number | User;
  course: number | Course;
  branch: number | Branch;
  purchaseDetails: {
    originalPrice: number;
    discountAmount?: number | null;
    finalAmount: number;
    currency: 'INR' | 'USD' | 'EUR';
  };
  commissionDetails: {
    /**
     * Commission rate as percentage
     */
    commissionRate: number;
    /**
     * Calculated commission amount
     */
    commissionAmount: number;
    /**
     * Amount branch receives after commission
     */
    branchReceives: number;
  };
  paymentDetails: {
    paymentMethod: 'razorpay' | 'stripe' | 'paypal' | 'upi' | 'credit_card' | 'debit_card';
    transactionId: string;
    paymentGatewayResponse?:
      | {
          [k: string]: unknown;
        }
      | unknown[]
      | string
      | number
      | boolean
      | null;
    paymentStatus: 'pending' | 'processing' | 'completed' | 'failed' | 'refunded';
  };
  billingInfo?: {
    /**
     * Whether commission is added to monthly bill
     */
    addedToBill?: boolean | null;
    billId?: (number | null) | Bill;
    billingMonth?: number | null;
    billingYear?: number | null;
  };
  purchaseDate: string;
  accessDetails?: {
    accessGranted?: boolean | null;
    accessStartDate?: string | null;
    accessEndDate?: string | null;
    isActive?: boolean | null;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payment-gateways".
 */
export interface PaymentGateway {
  id: number;
  name: string;
  provider: 'razorpay' | 'stripe' | 'paypal' | 'paytm' | 'phonepe' | 'googlepay' | 'bank_transfer';
  configuration: {
    /**
     * API Key or Public Key
     */
    apiKey: string;
    /**
     * Secret Key (encrypted)
     */
    secretKey: string;
    /**
     * Webhook secret for payment verification
     */
    webhookSecret?: string | null;
    environment: 'sandbox' | 'production';
  };
  supportedCurrencies?:
    | {
        currency: 'INR' | 'USD' | 'EUR' | 'GBP';
        id?: string | null;
      }[]
    | null;
  supportedMethods?:
    | {
        method: 'credit_card' | 'debit_card' | 'upi' | 'net_banking' | 'wallet' | 'bank_transfer';
        id?: string | null;
      }[]
    | null;
  fees: {
    transactionFeeType: 'percentage' | 'fixed' | 'both';
    transactionFeePercentage?: number | null;
    transactionFeeFixed?: number | null;
  };
  isActive?: boolean | null;
  /**
   * Default gateway for new transactions
   */
  isDefault?: boolean | null;
  /**
   * Order in which gateway appears in selection
   */
  displayOrder?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: number;
  document?:
    | ({
        relationTo: 'users';
        value: number | User;
      } | null)
    | ({
        relationTo: 'media';
        value: number | Media;
      } | null)
    | ({
        relationTo: 'institutes';
        value: number | Institute;
      } | null)
    | ({
        relationTo: 'courses';
        value: number | Course;
      } | null)
    | ({
        relationTo: 'themes';
        value: number | Theme;
      } | null)
    | ({
        relationTo: 'course-categories';
        value: number | CourseCategory;
      } | null)
    | ({
        relationTo: 'permissions';
        value: number | Permission;
      } | null)
    | ({
        relationTo: 'roles';
        value: number | Role;
      } | null)
    | ({
        relationTo: 'user-permissions';
        value: number | UserPermission;
      } | null)
    | ({
        relationTo: 'role-permissions';
        value: number | RolePermission;
      } | null)
    | ({
        relationTo: 'sessions';
        value: number | Session;
      } | null)
    | ({
        relationTo: 'settings';
        value: number | Setting;
      } | null)
    | ({
        relationTo: 'domain-requests';
        value: number | DomainRequest;
      } | null)
    | ({
        relationTo: 'countries';
        value: number | Country;
      } | null)
    | ({
        relationTo: 'states';
        value: number | State;
      } | null)
    | ({
        relationTo: 'districts';
        value: number | District;
      } | null)
    | ({
        relationTo: 'tax-components';
        value: number | TaxComponent;
      } | null)
    | ({
        relationTo: 'tax-groups';
        value: number | TaxGroup;
      } | null)
    | ({
        relationTo: 'tax-rules';
        value: number | TaxRule;
      } | null)
    | ({
        relationTo: 'branches';
        value: number | Branch;
      } | null)
    | ({
        relationTo: 'bills';
        value: number | Bill;
      } | null)
    | ({
        relationTo: 'course-purchases';
        value: number | CoursePurchase;
      } | null)
    | ({
        relationTo: 'payment-gateways';
        value: number | PaymentGateway;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: number;
  user: {
    relationTo: 'users';
    value: number | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: number;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  role?: T;
  legacyRole?: T;
  firstName?: T;
  lastName?: T;
  phone?: T;
  avatar?: T;
  institute?: T;
  branch?: T;
  isActive?: T;
  emailVerified?: T;
  lastLogin?: T;
  lockedUntil?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  mediaType?: T;
  uploadedBy?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
  sizes?:
    | T
    | {
        thumbnail?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        card?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
        hero?:
          | T
          | {
              url?: T;
              width?: T;
              height?: T;
              mimeType?: T;
              filesize?: T;
              filename?: T;
            };
      };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "institutes_select".
 */
export interface InstitutesSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  email?: T;
  phone?: T;
  website?: T;
  logo?: T;
  description?: T;
  address?:
    | T
    | {
        street?: T;
        city?: T;
        state?: T;
        country?: T;
        zipCode?: T;
      };
  customDomain?: T;
  domainVerified?: T;
  subscriptionPlan?: T;
  subscriptionStatus?: T;
  subscriptionExpiry?: T;
  maxStudents?: T;
  maxCourses?: T;
  maxBranches?: T;
  features?:
    | T
    | {
        marketplace?: T;
        liveClasses?: T;
        exams?: T;
        blogs?: T;
        analytics?: T;
      };
  paymentGateways?:
    | T
    | {
        gateway?: T;
        isActive?: T;
        apiKey?: T;
        secretKey?: T;
        id?: T;
      };
  isActive?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "courses_select".
 */
export interface CoursesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  shortDescription?: T;
  thumbnail?: T;
  institute?: T;
  instructor?: T;
  category?: T;
  level?: T;
  language?: T;
  pricing?:
    | T
    | {
        price?: T;
        originalPrice?: T;
        currency?: T;
        isFree?: T;
      };
  duration?:
    | T
    | {
        totalHours?: T;
        totalLessons?: T;
        estimatedCompletion?: T;
      };
  status?: T;
  featured?: T;
  enrollmentCount?: T;
  rating?:
    | T
    | {
        average?: T;
        count?: T;
      };
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "themes_select".
 */
export interface ThemesSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  type?: T;
  category?: T;
  description?: T;
  version?: T;
  previewImage?: T;
  demoImage?: T;
  screenshots?:
    | T
    | {
        image?: T;
        title?: T;
        description?: T;
        id?: T;
      };
  colors?:
    | T
    | {
        primary?: T;
        secondary?: T;
        accent?: T;
        background?: T;
        text?: T;
        muted?: T;
        border?: T;
      };
  fonts?:
    | T
    | {
        heading?: T;
        body?: T;
        mono?: T;
      };
  features?:
    | T
    | {
        feature?: T;
        id?: T;
      };
  suitableFor?:
    | T
    | {
        type?: T;
        id?: T;
      };
  customizableElements?: T;
  usageCount?: T;
  rating?:
    | T
    | {
        average?: T;
        count?: T;
      };
  isActive?: T;
  isDefault?: T;
  isFeatured?: T;
  demoUrl?: T;
  documentationUrl?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "course-categories_select".
 */
export interface CourseCategoriesSelect<T extends boolean = true> {
  name?: T;
  slug?: T;
  description?: T;
  parent?: T;
  icon?: T;
  color?: T;
  image?: T;
  isActive?: T;
  isFeatured?: T;
  sortOrder?: T;
  courseCount?: T;
  seoTitle?: T;
  seoDescription?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  institute?: T;
  createdBy?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "permissions_select".
 */
export interface PermissionsSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  code?: T;
  category?: T;
  level?: T;
  resource?: T;
  action?: T;
  isSystemPermission?: T;
  isActive?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "roles_select".
 */
export interface RolesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  description?: T;
  level?: T;
  permissions?:
    | T
    | {
        permission?: T;
        scope?: T;
        id?: T;
      };
  scope?:
    | T
    | {
        institute?: T;
        branch?: T;
      };
  isSystemRole?: T;
  isActive?: T;
  metadata?:
    | T
    | {
        maxUsers?: T;
        autoAssign?: T;
        requiresApproval?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "user-permissions_select".
 */
export interface UserPermissionsSelect<T extends boolean = true> {
  user?: T;
  permission?: T;
  type?: T;
  scope?: T;
  scopeTarget?:
    | T
    | {
        institute?: T;
        branch?: T;
      };
  status?: T;
  approvalWorkflow?:
    | T
    | {
        requestedBy?: T;
        approvedBy?: T;
        rejectedBy?: T;
        approvalDate?: T;
        rejectionDate?: T;
        reason?: T;
      };
  effectivePeriod?:
    | T
    | {
        startDate?: T;
        endDate?: T;
      };
  isActive?: T;
  metadata?:
    | T
    | {
        autoApproved?: T;
        inheritedFromRole?: T;
        notes?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "role-permissions_select".
 */
export interface RolePermissionsSelect<T extends boolean = true> {
  role?: T;
  permission?: T;
  isActive?: T;
  assignedBy?: T;
  assignedAt?: T;
  notes?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "sessions_select".
 */
export interface SessionsSelect<T extends boolean = true> {
  user?: T;
  sessionToken?: T;
  deviceInfo?:
    | T
    | {
        deviceType?: T;
        deviceName?: T;
        browser?: T;
        operatingSystem?: T;
        userAgent?: T;
      };
  location?:
    | T
    | {
        ipAddress?: T;
        city?: T;
        region?: T;
        country?: T;
        latitude?: T;
        longitude?: T;
      };
  security?:
    | T
    | {
        isSecure?: T;
        isCurrent?: T;
        loginMethod?: T;
      };
  isActive?: T;
  lastActivity?: T;
  expiresAt?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "settings_select".
 */
export interface SettingsSelect<T extends boolean = true> {
  key?: T;
  value?: T;
  category?: T;
  scope?: T;
  dataType?: T;
  description?: T;
  institute?: T;
  isPublic?: T;
  isEditable?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "domain-requests_select".
 */
export interface DomainRequestsSelect<T extends boolean = true> {
  institute?: T;
  requestedDomain?: T;
  currentDomain?: T;
  status?: T;
  purpose?: T;
  notes?: T;
  requestedBy?: T;
  requestedAt?: T;
  reviewedBy?: T;
  reviewedAt?: T;
  rejectionReason?: T;
  sslStatus?: T;
  dnsRecords?:
    | T
    | {
        type?: T;
        name?: T;
        value?: T;
        ttl?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "countries_select".
 */
export interface CountriesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  flag?: T;
  details?:
    | T
    | {
        capital?: T;
        currency?: T;
        currencyCode?: T;
        language?: T;
        timezone?: T;
        population?: T;
        area?: T;
      };
  coordinates?:
    | T
    | {
        latitude?: T;
        longitude?: T;
      };
  isActive?: T;
  priority?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "states_select".
 */
export interface StatesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  country?: T;
  details?:
    | T
    | {
        capital?: T;
        population?: T;
        area?: T;
        type?: T;
      };
  coordinates?:
    | T
    | {
        latitude?: T;
        longitude?: T;
      };
  isActive?: T;
  priority?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "districts_select".
 */
export interface DistrictsSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  state?: T;
  details?:
    | T
    | {
        type?: T;
        population?: T;
        area?: T;
        pincode?: T;
      };
  coordinates?:
    | T
    | {
        latitude?: T;
        longitude?: T;
      };
  isActive?: T;
  priority?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-components_select".
 */
export interface TaxComponentsSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  description?: T;
  type?: T;
  rate?: T;
  applicableRegions?:
    | T
    | {
        country?: T;
        states?: T;
        isDefault?: T;
        id?: T;
      };
  calculationMethod?: T;
  tieredRates?:
    | T
    | {
        minAmount?: T;
        maxAmount?: T;
        rate?: T;
        id?: T;
      };
  isActive?: T;
  effectiveFrom?: T;
  effectiveTo?: T;
  priority?: T;
  metadata?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-groups_select".
 */
export interface TaxGroupsSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  description?: T;
  taxComponents?:
    | T
    | {
        component?: T;
        customRate?: T;
        isOptional?: T;
        id?: T;
      };
  totalRate?: T;
  applicableScenarios?:
    | T
    | {
        scenario?: T;
        fromCountry?: T;
        toCountry?: T;
        fromState?: T;
        toState?: T;
        id?: T;
      };
  isActive?: T;
  isDefault?: T;
  effectiveFrom?: T;
  effectiveTo?: T;
  priority?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "tax-rules_select".
 */
export interface TaxRulesSelect<T extends boolean = true> {
  name?: T;
  description?: T;
  taxGroup?: T;
  conditions?:
    | T
    | {
        transactionType?: T;
        customerType?: T;
        minAmount?: T;
        maxAmount?: T;
        customerCountry?: T;
        customerState?: T;
        instituteCountry?: T;
        instituteState?: T;
        locationScenario?: T;
        conditionEffectiveFrom?: T;
        conditionEffectiveTo?: T;
        applicableDays?: T;
      };
  exemptions?:
    | T
    | {
        condition?: T;
        exemptionPercentage?: T;
        thresholdAmount?: T;
        id?: T;
      };
  isActive?: T;
  priority?: T;
  effectiveFrom?: T;
  effectiveTo?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "branches_select".
 */
export interface BranchesSelect<T extends boolean = true> {
  name?: T;
  code?: T;
  institute?: T;
  location?:
    | T
    | {
        address?: T;
        country?: T;
        state?: T;
        district?: T;
        pincode?: T;
        coordinates?:
          | T
          | {
              latitude?: T;
              longitude?: T;
            };
      };
  contact?:
    | T
    | {
        phone?: T;
        email?: T;
        website?: T;
      };
  taxInformation?:
    | T
    | {
        gstNumber?: T;
        panNumber?: T;
        taxRegistrationNumber?: T;
        isGstRegistered?: T;
      };
  billingSettings?:
    | T
    | {
        billingCycle?: T;
        billingDay?: T;
        currency?: T;
        commissionRate?: T;
      };
  isActive?: T;
  isHeadOffice?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "bills_select".
 */
export interface BillsSelect<T extends boolean = true> {
  billNumber?: T;
  branch?: T;
  billingPeriod?:
    | T
    | {
        startDate?: T;
        endDate?: T;
        month?: T;
        year?: T;
      };
  amounts?:
    | T
    | {
        baseFee?: T;
        commissionAmount?: T;
        subtotal?: T;
        taxAmount?: T;
        totalAmount?: T;
        currency?: T;
      };
  taxDetails?:
    | T
    | {
        taxScenario?: T;
        taxComponents?:
          | T
          | {
              componentName?: T;
              componentCode?: T;
              rate?: T;
              amount?: T;
              id?: T;
            };
      };
  commissionDetails?:
    | T
    | {
        studentPurchase?: T;
        courseTitle?: T;
        studentName?: T;
        purchaseAmount?: T;
        commissionRate?: T;
        commissionAmount?: T;
        purchaseDate?: T;
        id?: T;
      };
  status?: T;
  dates?:
    | T
    | {
        generatedDate?: T;
        sentDate?: T;
        dueDate?: T;
        paidDate?: T;
        viewedDate?: T;
      };
  paymentDetails?:
    | T
    | {
        paymentMethod?: T;
        transactionId?: T;
        paymentGatewayResponse?: T;
        paidBy?: T;
      };
  notes?: T;
  attachments?:
    | T
    | {
        file?: T;
        description?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "course-purchases_select".
 */
export interface CoursePurchasesSelect<T extends boolean = true> {
  student?: T;
  course?: T;
  branch?: T;
  purchaseDetails?:
    | T
    | {
        originalPrice?: T;
        discountAmount?: T;
        finalAmount?: T;
        currency?: T;
      };
  commissionDetails?:
    | T
    | {
        commissionRate?: T;
        commissionAmount?: T;
        branchReceives?: T;
      };
  paymentDetails?:
    | T
    | {
        paymentMethod?: T;
        transactionId?: T;
        paymentGatewayResponse?: T;
        paymentStatus?: T;
      };
  billingInfo?:
    | T
    | {
        addedToBill?: T;
        billId?: T;
        billingMonth?: T;
        billingYear?: T;
      };
  purchaseDate?: T;
  accessDetails?:
    | T
    | {
        accessGranted?: T;
        accessStartDate?: T;
        accessEndDate?: T;
        isActive?: T;
      };
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payment-gateways_select".
 */
export interface PaymentGatewaysSelect<T extends boolean = true> {
  name?: T;
  provider?: T;
  configuration?:
    | T
    | {
        apiKey?: T;
        secretKey?: T;
        webhookSecret?: T;
        environment?: T;
      };
  supportedCurrencies?:
    | T
    | {
        currency?: T;
        id?: T;
      };
  supportedMethods?:
    | T
    | {
        method?: T;
        id?: T;
      };
  fees?:
    | T
    | {
        transactionFeeType?: T;
        transactionFeePercentage?: T;
        transactionFeeFixed?: T;
      };
  isActive?: T;
  isDefault?: T;
  displayOrder?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}