"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+mark@0.28.0";
exports.ids = ["vendor-chunks/@lexical+mark@0.28.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+mark@0.28.0/node_modules/@lexical/mark/LexicalMark.dev.mjs":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+mark@0.28.0/node_modules/@lexical/mark/LexicalMark.dev.mjs ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $createMarkNode: () => (/* binding */ $createMarkNode),\n/* harmony export */   $getMarkIDs: () => (/* binding */ $getMarkIDs),\n/* harmony export */   $isMarkNode: () => (/* binding */ $isMarkNode),\n/* harmony export */   $unwrapMarkNode: () => (/* binding */ $unwrapMarkNode),\n/* harmony export */   $wrapSelectionInMarkNode: () => (/* binding */ $wrapSelectionInMarkNode),\n/* harmony export */   MarkNode: () => (/* binding */ MarkNode)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst NO_IDS = [];\n\n/** @noInheritDoc */\nclass MarkNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  static getType() {\n    return 'mark';\n  }\n  static clone(node) {\n    return new MarkNode(node.__ids, node.__key);\n  }\n  static importDOM() {\n    return null;\n  }\n  static importJSON(serializedNode) {\n    return $createMarkNode().updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setIDs(serializedNode.ids);\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      ids: this.getIDs()\n    };\n  }\n  constructor(ids = NO_IDS, key) {\n    super(key);\n    this.__ids = ids;\n  }\n  createDOM(config) {\n    const element = document.createElement('mark');\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, config.theme.mark);\n    if (this.__ids.length > 1) {\n      (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, config.theme.markOverlap);\n    }\n    return element;\n  }\n  updateDOM(prevNode, element, config) {\n    const prevIDs = prevNode.__ids;\n    const nextIDs = this.__ids;\n    const prevIDsCount = prevIDs.length;\n    const nextIDsCount = nextIDs.length;\n    const overlapTheme = config.theme.markOverlap;\n    if (prevIDsCount !== nextIDsCount) {\n      if (prevIDsCount === 1) {\n        if (nextIDsCount === 2) {\n          (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, overlapTheme);\n        }\n      } else if (nextIDsCount === 1) {\n        (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.removeClassNamesFromElement)(element, overlapTheme);\n      }\n    }\n    return false;\n  }\n  hasID(id) {\n    return this.getIDs().includes(id);\n  }\n  getIDs() {\n    return Array.from(this.getLatest().__ids);\n  }\n  setIDs(ids) {\n    const self = this.getWritable();\n    self.__ids = ids;\n    return self;\n  }\n  addID(id) {\n    const self = this.getWritable();\n    return self.__ids.includes(id) ? self : self.setIDs([...self.__ids, id]);\n  }\n  deleteID(id) {\n    const self = this.getWritable();\n    const idx = self.__ids.indexOf(id);\n    if (idx === -1) {\n      return self;\n    }\n    const ids = Array.from(self.__ids);\n    ids.splice(idx, 1);\n    return self.setIDs(ids);\n  }\n  insertNewAfter(selection, restoreSelection = true) {\n    const markNode = $createMarkNode(this.__ids);\n    this.insertAfter(markNode, restoreSelection);\n    return markNode;\n  }\n  canInsertTextBefore() {\n    return false;\n  }\n  canInsertTextAfter() {\n    return false;\n  }\n  canBeEmpty() {\n    return false;\n  }\n  isInline() {\n    return true;\n  }\n  extractWithChild(child, selection, destination) {\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || destination === 'html') {\n      return false;\n    }\n    const anchor = selection.anchor;\n    const focus = selection.focus;\n    const anchorNode = anchor.getNode();\n    const focusNode = focus.getNode();\n    const isBackward = selection.isBackward();\n    const selectionLength = isBackward ? anchor.offset - focus.offset : focus.offset - anchor.offset;\n    return this.isParentOf(anchorNode) && this.isParentOf(focusNode) && this.getTextContent().length === selectionLength;\n  }\n  excludeFromCopy(destination) {\n    return destination !== 'clone';\n  }\n}\nfunction $createMarkNode(ids = NO_IDS) {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new MarkNode(ids));\n}\nfunction $isMarkNode(node) {\n  return node instanceof MarkNode;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction $unwrapMarkNode(node) {\n  const children = node.getChildren();\n  let target = null;\n  for (let i = 0; i < children.length; i++) {\n    const child = children[i];\n    if (target === null) {\n      node.insertBefore(child);\n    } else {\n      target.insertAfter(child);\n    }\n    target = child;\n  }\n  node.remove();\n}\nfunction $wrapSelectionInMarkNode(selection, isBackward, id, createNode) {\n  // Force a forwards selection since append is used, ignore the argument.\n  // A new selection is used to avoid side-effects of flipping the given\n  // selection\n  const forwardSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelection)();\n  const [startPoint, endPoint] = selection.isBackward() ? [selection.focus, selection.anchor] : [selection.anchor, selection.focus];\n  forwardSelection.anchor.set(startPoint.key, startPoint.offset, startPoint.type);\n  forwardSelection.focus.set(endPoint.key, endPoint.offset, endPoint.type);\n  let currentNodeParent;\n  let lastCreatedMarkNode;\n\n  // Note that extract will split text nodes at the boundaries\n  const nodes = forwardSelection.extract();\n  // We only want wrap adjacent text nodes, line break nodes\n  // and inline element nodes. For decorator nodes and block\n  // element nodes, we step out of their boundary and start\n  // again after, if there are more nodes.\n  for (const node of nodes) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(lastCreatedMarkNode) && lastCreatedMarkNode.isParentOf(node)) {\n      // If the current node is a child of the last created mark node, there is nothing to do here\n      continue;\n    }\n    let targetNode = null;\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n      // Case 1: The node is a text node and we can include it\n      targetNode = node;\n    } else if ($isMarkNode(node)) {\n      // Case 2: the node is a mark node and we can ignore it as a target,\n      // moving on to its children. Note that when we make a mark inside\n      // another mark, it may utlimately be unnested by a call to\n      // `registerNestedElementResolver<MarkNode>` somewhere else in the\n      // codebase.\n      continue;\n    } else if (((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(node)) && node.isInline()) {\n      // Case 3: inline element/decorator nodes can be added in their entirety\n      // to the new mark\n      targetNode = node;\n    }\n    if (targetNode !== null) {\n      // Now that we have a target node for wrapping with a mark, we can run\n      // through special cases.\n      if (targetNode && targetNode.is(currentNodeParent)) {\n        // The current node is a child of the target node to be wrapped, there\n        // is nothing to do here.\n        continue;\n      }\n      const parentNode = targetNode.getParent();\n      if (parentNode == null || !parentNode.is(currentNodeParent)) {\n        // If the parent node is not the current node's parent node, we can\n        // clear the last created mark node.\n        lastCreatedMarkNode = undefined;\n      }\n      currentNodeParent = parentNode;\n      if (lastCreatedMarkNode === undefined) {\n        // If we don't have a created mark node, we can make one\n        const createMarkNode = createNode || $createMarkNode;\n        lastCreatedMarkNode = createMarkNode([id]);\n        targetNode.insertBefore(lastCreatedMarkNode);\n      }\n\n      // Add the target node to be wrapped in the latest created mark node\n      lastCreatedMarkNode.append(targetNode);\n    } else {\n      // If we don't have a target node to wrap we can clear our state and\n      // continue on with the next node\n      currentNodeParent = undefined;\n      lastCreatedMarkNode = undefined;\n    }\n  }\n  // Make selection collapsed at the end\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(lastCreatedMarkNode)) {\n    // eslint-disable-next-line no-unused-expressions\n    isBackward ? lastCreatedMarkNode.selectStart() : lastCreatedMarkNode.selectEnd();\n  }\n}\nfunction $getMarkIDs(node, offset) {\n  let currentNode = node;\n  while (currentNode !== null) {\n    if ($isMarkNode(currentNode)) {\n      return currentNode.getIDs();\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(currentNode) && offset === currentNode.getTextContentSize()) {\n      const nextSibling = currentNode.getNextSibling();\n      if ($isMarkNode(nextSibling)) {\n        return nextSibling.getIDs();\n      }\n    }\n    currentNode = currentNode.getParent();\n  }\n  return null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BsZXhpY2FsK21hcmtAMC4yOC4wL25vZGVfbW9kdWxlcy9AbGV4aWNhbC9tYXJrL0xleGljYWxNYXJrLmRldi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFc0o7QUFDakU7O0FBRXJGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsdUJBQXVCLGdEQUFXO0FBQ2xDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUksc0VBQXNCO0FBQzFCO0FBQ0EsTUFBTSxzRUFBc0I7QUFDNUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsVUFBVSxzRUFBc0I7QUFDaEM7QUFDQSxRQUFRO0FBQ1IsUUFBUSwyRUFBMkI7QUFDbkM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsMERBQWlCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsOERBQXFCO0FBQzlCO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBLGtCQUFrQixxQkFBcUI7QUFDdkM7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMkJBQTJCLDhEQUFxQjtBQUNoRDtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUSx1REFBYztBQUN0QjtBQUNBO0FBQ0E7QUFDQTtBQUNBLFFBQVEsb0RBQVc7QUFDbkI7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLFVBQVUsdURBQWMsVUFBVSx5REFBZ0I7QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLE1BQU07QUFDTjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sdURBQWM7QUFDcEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxTQUFTLG9EQUFXO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFMEciLCJzb3VyY2VzIjpbIkM6XFxwcm9qZWN0c1xcbG1zXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAbGV4aWNhbCttYXJrQDAuMjguMFxcbm9kZV9tb2R1bGVzXFxAbGV4aWNhbFxcbWFya1xcTGV4aWNhbE1hcmsuZGV2Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICovXG5cbmltcG9ydCB7IEVsZW1lbnROb2RlLCAkaXNSYW5nZVNlbGVjdGlvbiwgJGFwcGx5Tm9kZVJlcGxhY2VtZW50LCAkY3JlYXRlUmFuZ2VTZWxlY3Rpb24sICRpc0VsZW1lbnROb2RlLCAkaXNUZXh0Tm9kZSwgJGlzRGVjb3JhdG9yTm9kZSB9IGZyb20gJ2xleGljYWwnO1xuaW1wb3J0IHsgYWRkQ2xhc3NOYW1lc1RvRWxlbWVudCwgcmVtb3ZlQ2xhc3NOYW1lc0Zyb21FbGVtZW50IH0gZnJvbSAnQGxleGljYWwvdXRpbHMnO1xuXG4vKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICovXG5cbmNvbnN0IE5PX0lEUyA9IFtdO1xuXG4vKiogQG5vSW5oZXJpdERvYyAqL1xuY2xhc3MgTWFya05vZGUgZXh0ZW5kcyBFbGVtZW50Tm9kZSB7XG4gIC8qKiBAaW50ZXJuYWwgKi9cblxuICBzdGF0aWMgZ2V0VHlwZSgpIHtcbiAgICByZXR1cm4gJ21hcmsnO1xuICB9XG4gIHN0YXRpYyBjbG9uZShub2RlKSB7XG4gICAgcmV0dXJuIG5ldyBNYXJrTm9kZShub2RlLl9faWRzLCBub2RlLl9fa2V5KTtcbiAgfVxuICBzdGF0aWMgaW1wb3J0RE9NKCkge1xuICAgIHJldHVybiBudWxsO1xuICB9XG4gIHN0YXRpYyBpbXBvcnRKU09OKHNlcmlhbGl6ZWROb2RlKSB7XG4gICAgcmV0dXJuICRjcmVhdGVNYXJrTm9kZSgpLnVwZGF0ZUZyb21KU09OKHNlcmlhbGl6ZWROb2RlKTtcbiAgfVxuICB1cGRhdGVGcm9tSlNPTihzZXJpYWxpemVkTm9kZSkge1xuICAgIHJldHVybiBzdXBlci51cGRhdGVGcm9tSlNPTihzZXJpYWxpemVkTm9kZSkuc2V0SURzKHNlcmlhbGl6ZWROb2RlLmlkcyk7XG4gIH1cbiAgZXhwb3J0SlNPTigpIHtcbiAgICByZXR1cm4ge1xuICAgICAgLi4uc3VwZXIuZXhwb3J0SlNPTigpLFxuICAgICAgaWRzOiB0aGlzLmdldElEcygpXG4gICAgfTtcbiAgfVxuICBjb25zdHJ1Y3RvcihpZHMgPSBOT19JRFMsIGtleSkge1xuICAgIHN1cGVyKGtleSk7XG4gICAgdGhpcy5fX2lkcyA9IGlkcztcbiAgfVxuICBjcmVhdGVET00oY29uZmlnKSB7XG4gICAgY29uc3QgZWxlbWVudCA9IGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ21hcmsnKTtcbiAgICBhZGRDbGFzc05hbWVzVG9FbGVtZW50KGVsZW1lbnQsIGNvbmZpZy50aGVtZS5tYXJrKTtcbiAgICBpZiAodGhpcy5fX2lkcy5sZW5ndGggPiAxKSB7XG4gICAgICBhZGRDbGFzc05hbWVzVG9FbGVtZW50KGVsZW1lbnQsIGNvbmZpZy50aGVtZS5tYXJrT3ZlcmxhcCk7XG4gICAgfVxuICAgIHJldHVybiBlbGVtZW50O1xuICB9XG4gIHVwZGF0ZURPTShwcmV2Tm9kZSwgZWxlbWVudCwgY29uZmlnKSB7XG4gICAgY29uc3QgcHJldklEcyA9IHByZXZOb2RlLl9faWRzO1xuICAgIGNvbnN0IG5leHRJRHMgPSB0aGlzLl9faWRzO1xuICAgIGNvbnN0IHByZXZJRHNDb3VudCA9IHByZXZJRHMubGVuZ3RoO1xuICAgIGNvbnN0IG5leHRJRHNDb3VudCA9IG5leHRJRHMubGVuZ3RoO1xuICAgIGNvbnN0IG92ZXJsYXBUaGVtZSA9IGNvbmZpZy50aGVtZS5tYXJrT3ZlcmxhcDtcbiAgICBpZiAocHJldklEc0NvdW50ICE9PSBuZXh0SURzQ291bnQpIHtcbiAgICAgIGlmIChwcmV2SURzQ291bnQgPT09IDEpIHtcbiAgICAgICAgaWYgKG5leHRJRHNDb3VudCA9PT0gMikge1xuICAgICAgICAgIGFkZENsYXNzTmFtZXNUb0VsZW1lbnQoZWxlbWVudCwgb3ZlcmxhcFRoZW1lKTtcbiAgICAgICAgfVxuICAgICAgfSBlbHNlIGlmIChuZXh0SURzQ291bnQgPT09IDEpIHtcbiAgICAgICAgcmVtb3ZlQ2xhc3NOYW1lc0Zyb21FbGVtZW50KGVsZW1lbnQsIG92ZXJsYXBUaGVtZSk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBoYXNJRChpZCkge1xuICAgIHJldHVybiB0aGlzLmdldElEcygpLmluY2x1ZGVzKGlkKTtcbiAgfVxuICBnZXRJRHMoKSB7XG4gICAgcmV0dXJuIEFycmF5LmZyb20odGhpcy5nZXRMYXRlc3QoKS5fX2lkcyk7XG4gIH1cbiAgc2V0SURzKGlkcykge1xuICAgIGNvbnN0IHNlbGYgPSB0aGlzLmdldFdyaXRhYmxlKCk7XG4gICAgc2VsZi5fX2lkcyA9IGlkcztcbiAgICByZXR1cm4gc2VsZjtcbiAgfVxuICBhZGRJRChpZCkge1xuICAgIGNvbnN0IHNlbGYgPSB0aGlzLmdldFdyaXRhYmxlKCk7XG4gICAgcmV0dXJuIHNlbGYuX19pZHMuaW5jbHVkZXMoaWQpID8gc2VsZiA6IHNlbGYuc2V0SURzKFsuLi5zZWxmLl9faWRzLCBpZF0pO1xuICB9XG4gIGRlbGV0ZUlEKGlkKSB7XG4gICAgY29uc3Qgc2VsZiA9IHRoaXMuZ2V0V3JpdGFibGUoKTtcbiAgICBjb25zdCBpZHggPSBzZWxmLl9faWRzLmluZGV4T2YoaWQpO1xuICAgIGlmIChpZHggPT09IC0xKSB7XG4gICAgICByZXR1cm4gc2VsZjtcbiAgICB9XG4gICAgY29uc3QgaWRzID0gQXJyYXkuZnJvbShzZWxmLl9faWRzKTtcbiAgICBpZHMuc3BsaWNlKGlkeCwgMSk7XG4gICAgcmV0dXJuIHNlbGYuc2V0SURzKGlkcyk7XG4gIH1cbiAgaW5zZXJ0TmV3QWZ0ZXIoc2VsZWN0aW9uLCByZXN0b3JlU2VsZWN0aW9uID0gdHJ1ZSkge1xuICAgIGNvbnN0IG1hcmtOb2RlID0gJGNyZWF0ZU1hcmtOb2RlKHRoaXMuX19pZHMpO1xuICAgIHRoaXMuaW5zZXJ0QWZ0ZXIobWFya05vZGUsIHJlc3RvcmVTZWxlY3Rpb24pO1xuICAgIHJldHVybiBtYXJrTm9kZTtcbiAgfVxuICBjYW5JbnNlcnRUZXh0QmVmb3JlKCkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBjYW5JbnNlcnRUZXh0QWZ0ZXIoKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGNhbkJlRW1wdHkoKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIGlzSW5saW5lKCkge1xuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIGV4dHJhY3RXaXRoQ2hpbGQoY2hpbGQsIHNlbGVjdGlvbiwgZGVzdGluYXRpb24pIHtcbiAgICBpZiAoISRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikgfHwgZGVzdGluYXRpb24gPT09ICdodG1sJykge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBjb25zdCBhbmNob3IgPSBzZWxlY3Rpb24uYW5jaG9yO1xuICAgIGNvbnN0IGZvY3VzID0gc2VsZWN0aW9uLmZvY3VzO1xuICAgIGNvbnN0IGFuY2hvck5vZGUgPSBhbmNob3IuZ2V0Tm9kZSgpO1xuICAgIGNvbnN0IGZvY3VzTm9kZSA9IGZvY3VzLmdldE5vZGUoKTtcbiAgICBjb25zdCBpc0JhY2t3YXJkID0gc2VsZWN0aW9uLmlzQmFja3dhcmQoKTtcbiAgICBjb25zdCBzZWxlY3Rpb25MZW5ndGggPSBpc0JhY2t3YXJkID8gYW5jaG9yLm9mZnNldCAtIGZvY3VzLm9mZnNldCA6IGZvY3VzLm9mZnNldCAtIGFuY2hvci5vZmZzZXQ7XG4gICAgcmV0dXJuIHRoaXMuaXNQYXJlbnRPZihhbmNob3JOb2RlKSAmJiB0aGlzLmlzUGFyZW50T2YoZm9jdXNOb2RlKSAmJiB0aGlzLmdldFRleHRDb250ZW50KCkubGVuZ3RoID09PSBzZWxlY3Rpb25MZW5ndGg7XG4gIH1cbiAgZXhjbHVkZUZyb21Db3B5KGRlc3RpbmF0aW9uKSB7XG4gICAgcmV0dXJuIGRlc3RpbmF0aW9uICE9PSAnY2xvbmUnO1xuICB9XG59XG5mdW5jdGlvbiAkY3JlYXRlTWFya05vZGUoaWRzID0gTk9fSURTKSB7XG4gIHJldHVybiAkYXBwbHlOb2RlUmVwbGFjZW1lbnQobmV3IE1hcmtOb2RlKGlkcykpO1xufVxuZnVuY3Rpb24gJGlzTWFya05vZGUobm9kZSkge1xuICByZXR1cm4gbm9kZSBpbnN0YW5jZW9mIE1hcmtOb2RlO1xufVxuXG4vKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICovXG5cbmZ1bmN0aW9uICR1bndyYXBNYXJrTm9kZShub2RlKSB7XG4gIGNvbnN0IGNoaWxkcmVuID0gbm9kZS5nZXRDaGlsZHJlbigpO1xuICBsZXQgdGFyZ2V0ID0gbnVsbDtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBjaGlsZHJlbi5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IGNoaWxkID0gY2hpbGRyZW5baV07XG4gICAgaWYgKHRhcmdldCA9PT0gbnVsbCkge1xuICAgICAgbm9kZS5pbnNlcnRCZWZvcmUoY2hpbGQpO1xuICAgIH0gZWxzZSB7XG4gICAgICB0YXJnZXQuaW5zZXJ0QWZ0ZXIoY2hpbGQpO1xuICAgIH1cbiAgICB0YXJnZXQgPSBjaGlsZDtcbiAgfVxuICBub2RlLnJlbW92ZSgpO1xufVxuZnVuY3Rpb24gJHdyYXBTZWxlY3Rpb25Jbk1hcmtOb2RlKHNlbGVjdGlvbiwgaXNCYWNrd2FyZCwgaWQsIGNyZWF0ZU5vZGUpIHtcbiAgLy8gRm9yY2UgYSBmb3J3YXJkcyBzZWxlY3Rpb24gc2luY2UgYXBwZW5kIGlzIHVzZWQsIGlnbm9yZSB0aGUgYXJndW1lbnQuXG4gIC8vIEEgbmV3IHNlbGVjdGlvbiBpcyB1c2VkIHRvIGF2b2lkIHNpZGUtZWZmZWN0cyBvZiBmbGlwcGluZyB0aGUgZ2l2ZW5cbiAgLy8gc2VsZWN0aW9uXG4gIGNvbnN0IGZvcndhcmRTZWxlY3Rpb24gPSAkY3JlYXRlUmFuZ2VTZWxlY3Rpb24oKTtcbiAgY29uc3QgW3N0YXJ0UG9pbnQsIGVuZFBvaW50XSA9IHNlbGVjdGlvbi5pc0JhY2t3YXJkKCkgPyBbc2VsZWN0aW9uLmZvY3VzLCBzZWxlY3Rpb24uYW5jaG9yXSA6IFtzZWxlY3Rpb24uYW5jaG9yLCBzZWxlY3Rpb24uZm9jdXNdO1xuICBmb3J3YXJkU2VsZWN0aW9uLmFuY2hvci5zZXQoc3RhcnRQb2ludC5rZXksIHN0YXJ0UG9pbnQub2Zmc2V0LCBzdGFydFBvaW50LnR5cGUpO1xuICBmb3J3YXJkU2VsZWN0aW9uLmZvY3VzLnNldChlbmRQb2ludC5rZXksIGVuZFBvaW50Lm9mZnNldCwgZW5kUG9pbnQudHlwZSk7XG4gIGxldCBjdXJyZW50Tm9kZVBhcmVudDtcbiAgbGV0IGxhc3RDcmVhdGVkTWFya05vZGU7XG5cbiAgLy8gTm90ZSB0aGF0IGV4dHJhY3Qgd2lsbCBzcGxpdCB0ZXh0IG5vZGVzIGF0IHRoZSBib3VuZGFyaWVzXG4gIGNvbnN0IG5vZGVzID0gZm9yd2FyZFNlbGVjdGlvbi5leHRyYWN0KCk7XG4gIC8vIFdlIG9ubHkgd2FudCB3cmFwIGFkamFjZW50IHRleHQgbm9kZXMsIGxpbmUgYnJlYWsgbm9kZXNcbiAgLy8gYW5kIGlubGluZSBlbGVtZW50IG5vZGVzLiBGb3IgZGVjb3JhdG9yIG5vZGVzIGFuZCBibG9ja1xuICAvLyBlbGVtZW50IG5vZGVzLCB3ZSBzdGVwIG91dCBvZiB0aGVpciBib3VuZGFyeSBhbmQgc3RhcnRcbiAgLy8gYWdhaW4gYWZ0ZXIsIGlmIHRoZXJlIGFyZSBtb3JlIG5vZGVzLlxuICBmb3IgKGNvbnN0IG5vZGUgb2Ygbm9kZXMpIHtcbiAgICBpZiAoJGlzRWxlbWVudE5vZGUobGFzdENyZWF0ZWRNYXJrTm9kZSkgJiYgbGFzdENyZWF0ZWRNYXJrTm9kZS5pc1BhcmVudE9mKG5vZGUpKSB7XG4gICAgICAvLyBJZiB0aGUgY3VycmVudCBub2RlIGlzIGEgY2hpbGQgb2YgdGhlIGxhc3QgY3JlYXRlZCBtYXJrIG5vZGUsIHRoZXJlIGlzIG5vdGhpbmcgdG8gZG8gaGVyZVxuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGxldCB0YXJnZXROb2RlID0gbnVsbDtcbiAgICBpZiAoJGlzVGV4dE5vZGUobm9kZSkpIHtcbiAgICAgIC8vIENhc2UgMTogVGhlIG5vZGUgaXMgYSB0ZXh0IG5vZGUgYW5kIHdlIGNhbiBpbmNsdWRlIGl0XG4gICAgICB0YXJnZXROb2RlID0gbm9kZTtcbiAgICB9IGVsc2UgaWYgKCRpc01hcmtOb2RlKG5vZGUpKSB7XG4gICAgICAvLyBDYXNlIDI6IHRoZSBub2RlIGlzIGEgbWFyayBub2RlIGFuZCB3ZSBjYW4gaWdub3JlIGl0IGFzIGEgdGFyZ2V0LFxuICAgICAgLy8gbW92aW5nIG9uIHRvIGl0cyBjaGlsZHJlbi4gTm90ZSB0aGF0IHdoZW4gd2UgbWFrZSBhIG1hcmsgaW5zaWRlXG4gICAgICAvLyBhbm90aGVyIG1hcmssIGl0IG1heSB1dGxpbWF0ZWx5IGJlIHVubmVzdGVkIGJ5IGEgY2FsbCB0b1xuICAgICAgLy8gYHJlZ2lzdGVyTmVzdGVkRWxlbWVudFJlc29sdmVyPE1hcmtOb2RlPmAgc29tZXdoZXJlIGVsc2UgaW4gdGhlXG4gICAgICAvLyBjb2RlYmFzZS5cbiAgICAgIGNvbnRpbnVlO1xuICAgIH0gZWxzZSBpZiAoKCRpc0VsZW1lbnROb2RlKG5vZGUpIHx8ICRpc0RlY29yYXRvck5vZGUobm9kZSkpICYmIG5vZGUuaXNJbmxpbmUoKSkge1xuICAgICAgLy8gQ2FzZSAzOiBpbmxpbmUgZWxlbWVudC9kZWNvcmF0b3Igbm9kZXMgY2FuIGJlIGFkZGVkIGluIHRoZWlyIGVudGlyZXR5XG4gICAgICAvLyB0byB0aGUgbmV3IG1hcmtcbiAgICAgIHRhcmdldE5vZGUgPSBub2RlO1xuICAgIH1cbiAgICBpZiAodGFyZ2V0Tm9kZSAhPT0gbnVsbCkge1xuICAgICAgLy8gTm93IHRoYXQgd2UgaGF2ZSBhIHRhcmdldCBub2RlIGZvciB3cmFwcGluZyB3aXRoIGEgbWFyaywgd2UgY2FuIHJ1blxuICAgICAgLy8gdGhyb3VnaCBzcGVjaWFsIGNhc2VzLlxuICAgICAgaWYgKHRhcmdldE5vZGUgJiYgdGFyZ2V0Tm9kZS5pcyhjdXJyZW50Tm9kZVBhcmVudCkpIHtcbiAgICAgICAgLy8gVGhlIGN1cnJlbnQgbm9kZSBpcyBhIGNoaWxkIG9mIHRoZSB0YXJnZXQgbm9kZSB0byBiZSB3cmFwcGVkLCB0aGVyZVxuICAgICAgICAvLyBpcyBub3RoaW5nIHRvIGRvIGhlcmUuXG4gICAgICAgIGNvbnRpbnVlO1xuICAgICAgfVxuICAgICAgY29uc3QgcGFyZW50Tm9kZSA9IHRhcmdldE5vZGUuZ2V0UGFyZW50KCk7XG4gICAgICBpZiAocGFyZW50Tm9kZSA9PSBudWxsIHx8ICFwYXJlbnROb2RlLmlzKGN1cnJlbnROb2RlUGFyZW50KSkge1xuICAgICAgICAvLyBJZiB0aGUgcGFyZW50IG5vZGUgaXMgbm90IHRoZSBjdXJyZW50IG5vZGUncyBwYXJlbnQgbm9kZSwgd2UgY2FuXG4gICAgICAgIC8vIGNsZWFyIHRoZSBsYXN0IGNyZWF0ZWQgbWFyayBub2RlLlxuICAgICAgICBsYXN0Q3JlYXRlZE1hcmtOb2RlID0gdW5kZWZpbmVkO1xuICAgICAgfVxuICAgICAgY3VycmVudE5vZGVQYXJlbnQgPSBwYXJlbnROb2RlO1xuICAgICAgaWYgKGxhc3RDcmVhdGVkTWFya05vZGUgPT09IHVuZGVmaW5lZCkge1xuICAgICAgICAvLyBJZiB3ZSBkb24ndCBoYXZlIGEgY3JlYXRlZCBtYXJrIG5vZGUsIHdlIGNhbiBtYWtlIG9uZVxuICAgICAgICBjb25zdCBjcmVhdGVNYXJrTm9kZSA9IGNyZWF0ZU5vZGUgfHwgJGNyZWF0ZU1hcmtOb2RlO1xuICAgICAgICBsYXN0Q3JlYXRlZE1hcmtOb2RlID0gY3JlYXRlTWFya05vZGUoW2lkXSk7XG4gICAgICAgIHRhcmdldE5vZGUuaW5zZXJ0QmVmb3JlKGxhc3RDcmVhdGVkTWFya05vZGUpO1xuICAgICAgfVxuXG4gICAgICAvLyBBZGQgdGhlIHRhcmdldCBub2RlIHRvIGJlIHdyYXBwZWQgaW4gdGhlIGxhdGVzdCBjcmVhdGVkIG1hcmsgbm9kZVxuICAgICAgbGFzdENyZWF0ZWRNYXJrTm9kZS5hcHBlbmQodGFyZ2V0Tm9kZSk7XG4gICAgfSBlbHNlIHtcbiAgICAgIC8vIElmIHdlIGRvbid0IGhhdmUgYSB0YXJnZXQgbm9kZSB0byB3cmFwIHdlIGNhbiBjbGVhciBvdXIgc3RhdGUgYW5kXG4gICAgICAvLyBjb250aW51ZSBvbiB3aXRoIHRoZSBuZXh0IG5vZGVcbiAgICAgIGN1cnJlbnROb2RlUGFyZW50ID0gdW5kZWZpbmVkO1xuICAgICAgbGFzdENyZWF0ZWRNYXJrTm9kZSA9IHVuZGVmaW5lZDtcbiAgICB9XG4gIH1cbiAgLy8gTWFrZSBzZWxlY3Rpb24gY29sbGFwc2VkIGF0IHRoZSBlbmRcbiAgaWYgKCRpc0VsZW1lbnROb2RlKGxhc3RDcmVhdGVkTWFya05vZGUpKSB7XG4gICAgLy8gZXNsaW50LWRpc2FibGUtbmV4dC1saW5lIG5vLXVudXNlZC1leHByZXNzaW9uc1xuICAgIGlzQmFja3dhcmQgPyBsYXN0Q3JlYXRlZE1hcmtOb2RlLnNlbGVjdFN0YXJ0KCkgOiBsYXN0Q3JlYXRlZE1hcmtOb2RlLnNlbGVjdEVuZCgpO1xuICB9XG59XG5mdW5jdGlvbiAkZ2V0TWFya0lEcyhub2RlLCBvZmZzZXQpIHtcbiAgbGV0IGN1cnJlbnROb2RlID0gbm9kZTtcbiAgd2hpbGUgKGN1cnJlbnROb2RlICE9PSBudWxsKSB7XG4gICAgaWYgKCRpc01hcmtOb2RlKGN1cnJlbnROb2RlKSkge1xuICAgICAgcmV0dXJuIGN1cnJlbnROb2RlLmdldElEcygpO1xuICAgIH0gZWxzZSBpZiAoJGlzVGV4dE5vZGUoY3VycmVudE5vZGUpICYmIG9mZnNldCA9PT0gY3VycmVudE5vZGUuZ2V0VGV4dENvbnRlbnRTaXplKCkpIHtcbiAgICAgIGNvbnN0IG5leHRTaWJsaW5nID0gY3VycmVudE5vZGUuZ2V0TmV4dFNpYmxpbmcoKTtcbiAgICAgIGlmICgkaXNNYXJrTm9kZShuZXh0U2libGluZykpIHtcbiAgICAgICAgcmV0dXJuIG5leHRTaWJsaW5nLmdldElEcygpO1xuICAgICAgfVxuICAgIH1cbiAgICBjdXJyZW50Tm9kZSA9IGN1cnJlbnROb2RlLmdldFBhcmVudCgpO1xuICB9XG4gIHJldHVybiBudWxsO1xufVxuXG5leHBvcnQgeyAkY3JlYXRlTWFya05vZGUsICRnZXRNYXJrSURzLCAkaXNNYXJrTm9kZSwgJHVud3JhcE1hcmtOb2RlLCAkd3JhcFNlbGVjdGlvbkluTWFya05vZGUsIE1hcmtOb2RlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+mark@0.28.0/node_modules/@lexical/mark/LexicalMark.dev.mjs\n");

/***/ })

};
;