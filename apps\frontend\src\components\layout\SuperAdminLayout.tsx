'use client'

import { ReactNode, useEffect } from 'react'
import { useSidebarStore } from '@/stores/sidebar/useSidebarStore'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useResponsive } from '@/hooks/useResponsive'
import { Sidebar } from './Sidebar'
import { Header } from './Header'
import { MobileNavigation } from '@/components/shared/navigation/MobileNavigation'
import { superAdminNavigationConfig } from '@/config/navigation/superAdminNavigation'

interface SuperAdminLayoutProps {
  children: ReactNode
}

export function SuperAdminLayout({ children }: SuperAdminLayoutProps) {
  const {
    isCollapsed,
    isMobileOpen,
    navigationItems,
    setNavigationItems,
    setMobileSidebarOpen,
    setUserType
  } = useSidebarStore()
  const { user } = useAuthStore()
  const { isMobile, isTablet } = useResponsive()

  // Initialize navigation for super admin only once
  useEffect(() => {
    // Only set navigation items if they haven't been set yet or if they're different
    if (navigationItems.length === 0 || navigationItems[0]?.id !== superAdminNavigationConfig[0]?.id) {
      setNavigationItems(superAdminNavigationConfig)
    }
    setUserType('super_admin')
  }, []) // Remove dependencies to prevent re-running

  // Auto-collapse sidebar on mobile/tablet
  useEffect(() => {
    if (isMobile && isMobileOpen) {
      setMobileSidebarOpen(false)
    }
  }, [isMobile, isMobileOpen, setMobileSidebarOpen])

  // Verify user has super admin access using legacyRole
  if (!user || (user.legacyRole !== 'super_admin' && user.legacyRole !== 'platform_staff')) {
    console.log('❌ SuperAdminLayout access denied:', {
      hasUser: !!user,
      userEmail: user?.email,
      userLegacyRole: user?.legacyRole,
      userRole: user?.role
    })

    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-red-500 text-lg font-medium mb-2">Access Denied</div>
          <div className="text-gray-600">You don't have permission to access this area.</div>
          <div className="text-xs text-gray-500 mt-2">
            Role: {user?.legacyRole || 'none'} (Expected: super_admin)
          </div>
        </div>
      </div>
    )
  }

  console.log('✅ SuperAdminLayout access granted:', {
    userEmail: user.email,
    userLegacyRole: user.legacyRole
  })

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Mobile Navigation */}
      {isMobile && <MobileNavigation />}

      {/* Desktop/Tablet Layout */}
      {!isMobile && (
        <div className="flex h-screen">
          {/* Sidebar */}
          <div className={`
            ${isCollapsed ? 'w-16' : 'w-64'} 
            transition-all duration-300 ease-in-out
            flex-shrink-0
            ${isMobile ? 'hidden' : 'block'}
          `}>
            <Sidebar userType="super_admin" />
          </div>

          {/* Main Content Area */}
          <div className="flex-1 flex flex-col overflow-hidden">
            {/* Header */}
            <Header userType="super_admin" />

            {/* Page Content */}
            <main className="flex-1 overflow-y-auto">
              <div className="p-6">
                {children}
              </div>
            </main>
          </div>
        </div>
      )}

      {/* Mobile Layout */}
      {isMobile && (
        <div className="pb-20">
          {/* Mobile Header */}
          <div className="bg-white border-b border-gray-200 sticky top-0 z-30">
            <Header userType="super_admin" />
          </div>

          {/* Mobile Content */}
          <main className="p-4">
            {children}
          </main>
        </div>
      )}

      {/* Mobile Sidebar Overlay */}
      {isMobile && isMobileOpen && (
        <div className="fixed inset-0 z-50">
          {/* Backdrop */}
          <div 
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={() => setMobileSidebarOpen(false)}
          />

          {/* Sidebar */}
          <div className="fixed inset-y-0 left-0 w-64 bg-white shadow-xl">
            <Sidebar userType="super_admin" />
          </div>
        </div>
      )}
    </div>
  )
}

export default SuperAdminLayout
