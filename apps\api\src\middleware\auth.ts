import jwt from 'jsonwebtoken'
import type { PayloadRequest } from 'payload'

export interface AuthenticatedUser {
  id: string
  email: string
  role: string
}

export const authenticateToken = async (req: PayloadRequest): Promise<AuthenticatedUser | null> => {
  const authHeader = req.headers.get('authorization')
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return null
  }

  const token = authHeader.substring(7)

  try {
    const decoded = jwt.verify(
      token, 
      process.env.JWT_SECRET || process.env.PAYLOAD_SECRET
    ) as AuthenticatedUser

    // Verify user still exists and is active
    const user = await req.payload.findByID({
      collection: 'users',
      id: decoded.id,
    })

    if (!user || !user.isActive) {
      return null
    }

    return {
      id: user.id,
      email: user.email,
      role: user.legacyRole, // Use legacyRole for authentication
      legacyRole: user.legacyRole,
    }
  } catch (error) {
    console.error('Token verification failed:', error)
    return null
  }
}

export const requireAuth = (allowedRoles?: string[]) => {
  return async (req: PayloadRequest) => {
    const user = await authenticateToken(req)

    if (!user) {
      return Response.json(
        { message: 'Authentication required' },
        { status: 401 }
      )
    }

    if (allowedRoles && !allowedRoles.includes(user.role)) {
      return Response.json(
        { message: 'Insufficient permissions' },
        { status: 403 }
      )
    }

    // Add user to request for use in handlers
    req.user = user
    return null // Continue to handler
  }
}

export const requireSuperAdmin = requireAuth(['super_admin', 'platform_staff'])
export const requireInstituteAdmin = requireAuth(['institute_admin', 'branch_manager', 'trainer', 'institute_staff'])
export const requireStudent = requireAuth(['student'])
export const requireAnyAuth = requireAuth() // Any authenticated user
