import type { CollectionConfig } from 'payload'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
  },
  auth: true,
  fields: [
    // Email and password added by default with auth: true
    {
      name: 'role',
      type: 'relationship',
      relationTo: 'roles',
      required: false, // Make it optional for now to allow user creation
      admin: {
        position: 'sidebar',
        description: 'Select a role for this user',
      },
      // Add access control for the relationship field
      access: {
        read: ({ req: { user } }) => {
          // Allow reading roles if user is super admin or if no user (first user creation)
          if (!user) return true // Allow during first user creation
          return user.legacyRole === 'super_admin'
        },
        update: ({ req: { user } }) => {
          // Allow updating role if user is super admin or if no user (first user creation)
          if (!user) return true // Allow during first user creation
          return user.legacyRole === 'super_admin'
        },
      },
    },
    // Keep legacy role field for backward compatibility during migration
    {
      name: 'legacyRole',
      type: 'select',
      required: true,
      options: [
        {
          label: 'Super Admin',
          value: 'super_admin',
        },
        {
          label: 'Platform Staff',
          value: 'platform_staff',
        },
        {
          label: 'Institute Admin',
          value: 'institute_admin',
        },
        {
          label: 'Branch Manager',
          value: 'branch_manager',
        },
        {
          label: 'Trainer',
          value: 'trainer',
        },
        {
          label: 'Institute Staff',
          value: 'institute_staff',
        },
        {
          label: 'Student',
          value: 'student',
        },
      ],
      defaultValue: 'student',
      admin: {
        position: 'sidebar',
        description: 'Legacy role field - currently used for access control',
      },
    },
    {
      name: 'firstName',
      type: 'text',
      required: true,
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
    },
    {
      name: 'phone',
      type: 'text',
      validate: (val: any) => {
        if (val && !/^[+]?[\d\s\-\(\)]+$/.test(val)) {
          return 'Please enter a valid phone number'
        }
        return true
      },
    },
    {
      name: 'avatar',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      admin: {
        condition: (data) => {
          // Use legacy role field for conditional display
          return ['institute_admin', 'branch_manager', 'trainer', 'institute_staff', 'student'].includes(data.legacyRole)
        },
      },
    },
    {
      name: 'branch',
      type: 'text',
      admin: {
        condition: (data) => {
          // Use legacy role field for conditional display
          return ['branch_manager', 'trainer', 'institute_staff', 'student'].includes(data.legacyRole)
        },
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'emailVerified',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'lastLogin',
      type: 'date',
      admin: {
        position: 'sidebar',
        readOnly: true,
      },
    },
    {
      name: 'loginAttempts',
      type: 'number',
      defaultValue: 0,
      admin: {
        position: 'sidebar',
        readOnly: true,
      },
    },
    {
      name: 'lockedUntil',
      type: 'date',
      admin: {
        position: 'sidebar',
        readOnly: true,
      },
    },
  ],
  access: {
    // Allow authentication and user management
    create: ({ req: { user } }) => {
      // Allow user creation if no user (first user) or if super admin
      if (!user) return true // Allow first user creation
      return user?.legacyRole === 'super_admin'
    },
    read: ({ req: { user } }) => {
      // Allow reading for authentication purposes and first user creation
      if (!user) return true // Allow during authentication and first user creation

      // Super admins can read all users
      if (user?.legacyRole === 'super_admin') return true

      // For now, allow all authenticated users to read (can be restricted later)
      return true
    },
    update: ({ req: { user } }) => {
      // Allow updates if no user (first user creation) or if super admin
      if (!user) return true // Allow first user creation/update

      // Super admins can update all users
      if (user?.legacyRole === 'super_admin') return true

      // For now, allow all authenticated users to update (can be restricted later)
      return true
    },
    delete: ({ req: { user } }) => {
      // Only super admins can delete users
      return user?.legacyRole === 'super_admin'
    },
  },
}
