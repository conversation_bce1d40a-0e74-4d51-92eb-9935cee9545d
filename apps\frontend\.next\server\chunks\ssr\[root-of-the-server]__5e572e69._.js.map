{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/sidebar/useSidebarStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\n\n// Types\nexport type UserType = 'super_admin' | 'institute_admin' | 'student'\n\nexport interface NavigationItem {\n  id: string\n  label: string\n  icon: string\n  href: string\n  badge?: number\n  isActive?: boolean\n  children?: NavigationItem[]\n  permissions?: string[]\n  description?: string\n}\n\nexport interface SidebarSection {\n  id: string\n  title: string\n  items: NavigationItem[]\n  isCollapsed?: boolean\n}\n\nexport interface BreadcrumbItem {\n  label: string\n  href?: string\n  isActive?: boolean\n}\n\nexport interface SidebarState {\n  // Layout State\n  isCollapsed: boolean\n  isMobileOpen: boolean\n  userType: UserType\n  \n  // Navigation State\n  activeItem: string\n  activeSection: string\n  navigationItems: NavigationItem[]\n  sections: SidebarSection[]\n  breadcrumbs: BreadcrumbItem[]\n  \n  // Search State\n  searchQuery: string\n  searchResults: NavigationItem[]\n  isSearching: boolean\n  \n  // Favorites & Recent\n  favoriteItems: string[]\n  recentItems: string[]\n  \n  // Notifications\n  notifications: Array<{\n    id: string\n    title: string\n    message: string\n    type: 'info' | 'warning' | 'error' | 'success'\n    timestamp: string\n    isRead: boolean\n  }>\n  unreadCount: number\n}\n\ninterface SidebarStore extends SidebarState {\n  // Layout Actions\n  toggleSidebar: () => void\n  setSidebarCollapsed: (collapsed: boolean) => void\n  toggleMobileSidebar: () => void\n  setMobileSidebarOpen: (open: boolean) => void\n  setUserType: (userType: UserType) => void\n  \n  // Navigation Actions\n  setActiveItem: (itemId: string) => void\n  setActiveSection: (sectionId: string) => void\n  setNavigationItems: (items: NavigationItem[]) => void\n  setSections: (sections: SidebarSection[]) => void\n  setBreadcrumbs: (breadcrumbs: BreadcrumbItem[]) => void\n  updateItemBadge: (itemId: string, badge: number) => void\n  \n  // Search Actions\n  setSearchQuery: (query: string) => void\n  performSearch: (query: string) => void\n  clearSearch: () => void\n  \n  // Favorites & Recent Actions\n  addToFavorites: (itemId: string) => void\n  removeFromFavorites: (itemId: string) => void\n  addToRecent: (itemId: string) => void\n  clearRecent: () => void\n  \n  // Notification Actions\n  addNotification: (notification: Omit<SidebarState['notifications'][0], 'id' | 'timestamp' | 'isRead'>) => void\n  markNotificationAsRead: (notificationId: string) => void\n  markAllNotificationsAsRead: () => void\n  removeNotification: (notificationId: string) => void\n  clearNotifications: () => void\n  \n  // Utility Actions\n  initializeNavigation: (userType: UserType) => void\n  resetState: () => void\n}\n\n// Default navigation items for each user type\nconst getDefaultNavigation = (userType: UserType): NavigationItem[] => {\n  switch (userType) {\n    case 'super_admin':\n      return [\n        {\n          id: 'dashboard',\n          label: 'Dashboard',\n          icon: 'LayoutDashboard',\n          href: '/super-admin',\n          description: 'Overview and analytics'\n        },\n        {\n          id: 'institutes',\n          label: 'Institute Management',\n          icon: 'Building2',\n          href: '/super-admin/institutes',\n          description: 'Manage institutes and verification'\n        },\n        {\n          id: 'users',\n          label: 'User Management',\n          icon: 'Users',\n          href: '/super-admin/users',\n          description: 'Manage all platform users'\n        },\n        {\n          id: 'billing',\n          label: 'Billing & Finance',\n          icon: 'CreditCard',\n          href: '/super-admin/billing',\n          description: 'Financial management and billing'\n        },\n        {\n          id: 'themes',\n          label: 'Theme Management',\n          icon: 'Palette',\n          href: '/super-admin/themes',\n          description: 'Platform and institute themes'\n        },\n        {\n          id: 'analytics',\n          label: 'Analytics & Reports',\n          icon: 'BarChart3',\n          href: '/super-admin/analytics',\n          description: 'Platform analytics and reports'\n        },\n        {\n          id: 'locations',\n          label: 'Location Management',\n          icon: 'MapPin',\n          href: '/super-admin/locations',\n          description: 'Manage countries, states, and districts'\n        },\n       \n        {\n          id: 'tax-management',\n          label: 'Tax Management',\n          icon: 'Calculator',\n          href: '/super-admin/tax-management',\n          description: 'Manage tax components, groups, and rules'\n        },\n        {\n          id: 'role-permissions',\n          label: 'Roles & Permissions',\n          icon: 'Shield',\n          href: '/super-admin/role-permissions',\n          description: 'Manage user roles and permissions'\n        },\n        {\n          id: 'settings',\n          label: 'System Settings',\n          icon: 'Settings',\n          href: '/super-admin/settings',\n          description: 'System configuration and settings'\n        }\n      ]\n    \n    case 'institute_admin':\n      return [\n        {\n          id: 'dashboard',\n          label: 'Dashboard',\n          icon: 'LayoutDashboard',\n          href: '/institute-admin',\n          description: 'Institute overview and analytics'\n        },\n        {\n          id: 'courses',\n          label: 'Course Management',\n          icon: 'BookOpen',\n          href: '/institute-admin/courses',\n          description: 'Manage courses and curriculum'\n        },\n        {\n          id: 'students',\n          label: 'Student Management',\n          icon: 'GraduationCap',\n          href: '/institute-admin/students',\n          description: 'Manage student enrollments and progress'\n        },\n        {\n          id: 'branches',\n          label: 'Branch Management',\n          icon: 'MapPin',\n          href: '/institute-admin/branches',\n          description: 'Manage institute branches and locations'\n        },\n        {\n          id: 'billing',\n          label: 'Billing & Payments',\n          icon: 'CreditCard',\n          href: '/institute-admin/billing',\n          description: 'Student billing and payment management'\n        },\n        {\n          id: 'analytics',\n          label: 'Analytics & Reports',\n          icon: 'BarChart3',\n          href: '/institute-admin/analytics',\n          description: 'Institute analytics and performance reports'\n        },\n        {\n          id: 'website',\n          label: 'Website & Themes',\n          icon: 'Globe',\n          href: '/institute-admin/website',\n          description: 'Manage institute website and themes'\n        },\n        {\n          id: 'settings',\n          label: 'Institute Settings',\n          icon: 'Settings',\n          href: '/institute-admin/settings',\n          description: 'Institute configuration and preferences'\n        }\n      ]\n    \n    case 'student':\n      return [\n        {\n          id: 'dashboard',\n          label: 'Dashboard',\n          icon: 'LayoutDashboard',\n          href: '/student',\n          description: 'Your learning dashboard'\n        },\n        {\n          id: 'my-courses',\n          label: 'My Courses',\n          icon: 'BookOpen',\n          href: '/student/courses',\n          description: 'Your enrolled courses and progress'\n        },\n        {\n          id: 'marketplace',\n          label: 'Course Marketplace',\n          icon: 'ShoppingCart',\n          href: '/student/marketplace',\n          description: 'Browse and purchase new courses'\n        },\n        {\n          id: 'assignments',\n          label: 'Assignments & Exams',\n          icon: 'FileText',\n          href: '/student/assignments',\n          description: 'View and submit assignments'\n        },\n        {\n          id: 'live-classes',\n          label: 'Live Classes',\n          icon: 'Video',\n          href: '/student/live-classes',\n          description: 'Join live classes and webinars'\n        },\n        {\n          id: 'progress',\n          label: 'Progress & Analytics',\n          icon: 'TrendingUp',\n          href: '/student/progress',\n          description: 'Track your learning progress'\n        },\n        {\n          id: 'community',\n          label: 'Community',\n          icon: 'MessageCircle',\n          href: '/student/community',\n          description: 'Connect with peers and instructors'\n        },\n        {\n          id: 'payments',\n          label: 'Payments & Billing',\n          icon: 'CreditCard',\n          href: '/student/payments',\n          description: 'Manage payments and billing history'\n        },\n        {\n          id: 'account',\n          label: 'Account Settings',\n          icon: 'User',\n          href: '/student/account',\n          description: 'Manage your account and preferences'\n        },\n        {\n          id: 'support',\n          label: 'Support & Help',\n          icon: 'HelpCircle',\n          href: '/student/support',\n          description: 'Get help and contact support'\n        }\n      ]\n    \n    default:\n      return []\n  }\n}\n\nconst initialState: SidebarState = {\n  isCollapsed: false,\n  isMobileOpen: false,\n  userType: 'super_admin',\n  activeItem: '',\n  activeSection: '',\n  navigationItems: [],\n  sections: [],\n  breadcrumbs: [],\n  searchQuery: '',\n  searchResults: [],\n  isSearching: false,\n  favoriteItems: [],\n  recentItems: [],\n  notifications: [],\n  unreadCount: 0\n}\n\nexport const useSidebarStore = create<SidebarStore>()(\n  persist(\n    (set, get) => ({\n      ...initialState,\n\n      // Layout Actions\n      toggleSidebar: () => set((state) => ({ isCollapsed: !state.isCollapsed })),\n      setSidebarCollapsed: (collapsed) => set({ isCollapsed: collapsed }),\n      toggleMobileSidebar: () => set((state) => ({ isMobileOpen: !state.isMobileOpen })),\n      setMobileSidebarOpen: (open) => set({ isMobileOpen: open }),\n      setUserType: (userType) => {\n        set({ userType })\n        get().initializeNavigation(userType)\n      },\n\n      // Navigation Actions\n      setActiveItem: (itemId) => {\n        set({ activeItem: itemId })\n        get().addToRecent(itemId)\n      },\n      setActiveSection: (sectionId) => set({ activeSection: sectionId }),\n      setNavigationItems: (items) => set({ navigationItems: items }),\n      setSections: (sections) => set({ sections }),\n      setBreadcrumbs: (breadcrumbs) => set({ breadcrumbs }),\n      updateItemBadge: (itemId, badge) => {\n        set((state) => ({\n          navigationItems: state.navigationItems.map(item =>\n            item.id === itemId ? { ...item, badge } : item\n          )\n        }))\n      },\n\n      // Search Actions\n      setSearchQuery: (query) => set({ searchQuery: query }),\n      performSearch: (query) => {\n        set({ isSearching: true, searchQuery: query })\n        \n        const { navigationItems } = get()\n        const results = navigationItems.filter(item =>\n          item.label.toLowerCase().includes(query.toLowerCase()) ||\n          item.description?.toLowerCase().includes(query.toLowerCase())\n        )\n        \n        set({ searchResults: results, isSearching: false })\n      },\n      clearSearch: () => set({ searchQuery: '', searchResults: [], isSearching: false }),\n\n      // Favorites & Recent Actions\n      addToFavorites: (itemId) => {\n        set((state) => ({\n          favoriteItems: state.favoriteItems.includes(itemId)\n            ? state.favoriteItems\n            : [...state.favoriteItems, itemId]\n        }))\n      },\n      removeFromFavorites: (itemId) => {\n        set((state) => ({\n          favoriteItems: state.favoriteItems.filter(id => id !== itemId)\n        }))\n      },\n      addToRecent: (itemId) => {\n        set((state) => {\n          const filtered = state.recentItems.filter(id => id !== itemId)\n          return {\n            recentItems: [itemId, ...filtered].slice(0, 10) // Keep only last 10\n          }\n        })\n      },\n      clearRecent: () => set({ recentItems: [] }),\n\n      // Notification Actions\n      addNotification: (notification) => {\n        const newNotification = {\n          ...notification,\n          id: Date.now().toString(),\n          timestamp: new Date().toISOString(),\n          isRead: false\n        }\n        \n        set((state) => ({\n          notifications: [newNotification, ...state.notifications],\n          unreadCount: state.unreadCount + 1\n        }))\n      },\n      markNotificationAsRead: (notificationId) => {\n        set((state) => ({\n          notifications: state.notifications.map(notification =>\n            notification.id === notificationId\n              ? { ...notification, isRead: true }\n              : notification\n          ),\n          unreadCount: Math.max(0, state.unreadCount - 1)\n        }))\n      },\n      markAllNotificationsAsRead: () => {\n        set((state) => ({\n          notifications: state.notifications.map(notification => ({\n            ...notification,\n            isRead: true\n          })),\n          unreadCount: 0\n        }))\n      },\n      removeNotification: (notificationId) => {\n        set((state) => {\n          const notification = state.notifications.find(n => n.id === notificationId)\n          return {\n            notifications: state.notifications.filter(n => n.id !== notificationId),\n            unreadCount: notification && !notification.isRead \n              ? Math.max(0, state.unreadCount - 1)\n              : state.unreadCount\n          }\n        })\n      },\n      clearNotifications: () => set({ notifications: [], unreadCount: 0 }),\n\n      // Utility Actions\n      initializeNavigation: (userType) => {\n        const navigationItems = getDefaultNavigation(userType)\n        set({ navigationItems, userType })\n      },\n      resetState: () => set(initialState)\n    }),\n    {\n      name: 'sidebar-store',\n      partialize: (state) => ({\n        isCollapsed: state.isCollapsed,\n        userType: state.userType,\n        favoriteItems: state.favoriteItems,\n        recentItems: state.recentItems,\n        notifications: state.notifications,\n        unreadCount: state.unreadCount\n      })\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAuGA,8CAA8C;AAC9C,MAAM,uBAAuB,CAAC;IAC5B,OAAQ;QACN,KAAK;YACH,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBAEA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;aACD;QAEH,KAAK;YACH,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;aACD;QAEH,KAAK;YACH,OAAO;gBACL;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;aACD;QAEH;YACE,OAAO,EAAE;IACb;AACF;AAEA,MAAM,eAA6B;IACjC,aAAa;IACb,cAAc;IACd,UAAU;IACV,YAAY;IACZ,eAAe;IACf,iBAAiB,EAAE;IACnB,UAAU,EAAE;IACZ,aAAa,EAAE;IACf,aAAa;IACb,eAAe,EAAE;IACjB,aAAa;IACb,eAAe,EAAE;IACjB,aAAa,EAAE;IACf,eAAe,EAAE;IACjB,aAAa;AACf;AAEO,MAAM,kBAAkB,CAAA,GAAA,qPAAA,CAAA,SAAM,AAAD,IAClC,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,GAAG,YAAY;QAEf,iBAAiB;QACjB,eAAe,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,aAAa,CAAC,MAAM,WAAW;gBAAC,CAAC;QACxE,qBAAqB,CAAC,YAAc,IAAI;gBAAE,aAAa;YAAU;QACjE,qBAAqB,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,cAAc,CAAC,MAAM,YAAY;gBAAC,CAAC;QAChF,sBAAsB,CAAC,OAAS,IAAI;gBAAE,cAAc;YAAK;QACzD,aAAa,CAAC;YACZ,IAAI;gBAAE;YAAS;YACf,MAAM,oBAAoB,CAAC;QAC7B;QAEA,qBAAqB;QACrB,eAAe,CAAC;YACd,IAAI;gBAAE,YAAY;YAAO;YACzB,MAAM,WAAW,CAAC;QACpB;QACA,kBAAkB,CAAC,YAAc,IAAI;gBAAE,eAAe;YAAU;QAChE,oBAAoB,CAAC,QAAU,IAAI;gBAAE,iBAAiB;YAAM;QAC5D,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAC1C,gBAAgB,CAAC,cAAgB,IAAI;gBAAE;YAAY;QACnD,iBAAiB,CAAC,QAAQ;YACxB,IAAI,CAAC,QAAU,CAAC;oBACd,iBAAiB,MAAM,eAAe,CAAC,GAAG,CAAC,CAAA,OACzC,KAAK,EAAE,KAAK,SAAS;4BAAE,GAAG,IAAI;4BAAE;wBAAM,IAAI;gBAE9C,CAAC;QACH;QAEA,iBAAiB;QACjB,gBAAgB,CAAC,QAAU,IAAI;gBAAE,aAAa;YAAM;QACpD,eAAe,CAAC;YACd,IAAI;gBAAE,aAAa;gBAAM,aAAa;YAAM;YAE5C,MAAM,EAAE,eAAe,EAAE,GAAG;YAC5B,MAAM,UAAU,gBAAgB,MAAM,CAAC,CAAA,OACrC,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,MAAM,WAAW,OACnD,KAAK,WAAW,EAAE,cAAc,SAAS,MAAM,WAAW;YAG5D,IAAI;gBAAE,eAAe;gBAAS,aAAa;YAAM;QACnD;QACA,aAAa,IAAM,IAAI;gBAAE,aAAa;gBAAI,eAAe,EAAE;gBAAE,aAAa;YAAM;QAEhF,6BAA6B;QAC7B,gBAAgB,CAAC;YACf,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,QAAQ,CAAC,UACxC,MAAM,aAAa,GACnB;2BAAI,MAAM,aAAa;wBAAE;qBAAO;gBACtC,CAAC;QACH;QACA,qBAAqB,CAAC;YACpB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;gBACzD,CAAC;QACH;QACA,aAAa,CAAC;YACZ,IAAI,CAAC;gBACH,MAAM,WAAW,MAAM,WAAW,CAAC,MAAM,CAAC,CAAA,KAAM,OAAO;gBACvD,OAAO;oBACL,aAAa;wBAAC;2BAAW;qBAAS,CAAC,KAAK,CAAC,GAAG,IAAI,oBAAoB;gBACtE;YACF;QACF;QACA,aAAa,IAAM,IAAI;gBAAE,aAAa,EAAE;YAAC;QAEzC,uBAAuB;QACvB,iBAAiB,CAAC;YAChB,MAAM,kBAAkB;gBACtB,GAAG,YAAY;gBACf,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,WAAW,IAAI,OAAO,WAAW;gBACjC,QAAQ;YACV;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;wBAAC;2BAAoB,MAAM,aAAa;qBAAC;oBACxD,aAAa,MAAM,WAAW,GAAG;gBACnC,CAAC;QACH;QACA,wBAAwB,CAAC;YACvB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,eACrC,aAAa,EAAE,KAAK,iBAChB;4BAAE,GAAG,YAAY;4BAAE,QAAQ;wBAAK,IAChC;oBAEN,aAAa,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG;gBAC/C,CAAC;QACH;QACA,4BAA4B;YAC1B,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,GAAG,CAAC,CAAA,eAAgB,CAAC;4BACtD,GAAG,YAAY;4BACf,QAAQ;wBACV,CAAC;oBACD,aAAa;gBACf,CAAC;QACH;QACA,oBAAoB,CAAC;YACnB,IAAI,CAAC;gBACH,MAAM,eAAe,MAAM,aAAa,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;gBAC5D,OAAO;oBACL,eAAe,MAAM,aAAa,CAAC,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oBACxD,aAAa,gBAAgB,CAAC,aAAa,MAAM,GAC7C,KAAK,GAAG,CAAC,GAAG,MAAM,WAAW,GAAG,KAChC,MAAM,WAAW;gBACvB;YACF;QACF;QACA,oBAAoB,IAAM,IAAI;gBAAE,eAAe,EAAE;gBAAE,aAAa;YAAE;QAElE,kBAAkB;QAClB,sBAAsB,CAAC;YACrB,MAAM,kBAAkB,qBAAqB;YAC7C,IAAI;gBAAE;gBAAiB;YAAS;QAClC;QACA,YAAY,IAAM,IAAI;IACxB,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,aAAa,MAAM,WAAW;YAC9B,UAAU,MAAM,QAAQ;YACxB,eAAe,MAAM,aAAa;YAClC,aAAa,MAAM,WAAW;YAC9B,eAAe,MAAM,aAAa;YAClC,aAAa,MAAM,WAAW;QAChC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 413, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/auth/useAuthStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist } from 'zustand/middleware'\n\ninterface Role {\n  id: number\n  name: string\n  code: string\n  description?: string\n  level: string\n  permissions: any[]\n  scope: {\n    institute: number | null\n    branch: number | null\n  }\n  isActive: boolean\n  isSystemRole: boolean\n}\n\ninterface User {\n  id: string\n  email: string\n  firstName: string\n  lastName: string\n  role: Role | null // New role relationship object\n  legacyRole: 'super_admin' | 'platform_staff' | 'institute_admin' | 'branch_manager' | 'trainer' | 'institute_staff' | 'student' // Legacy string role\n  avatar?: string\n  institute?: string\n  isActive: boolean\n  lastLogin?: string\n}\n\ninterface AuthState {\n  user: User | null\n  token: string | null\n  isLoading: boolean\n  isAuthenticated: boolean\n\n  // Actions\n  login: (email: string, password: string, userType?: string) => Promise<void>\n  register: (userData: any, userType?: string) => Promise<void>\n  logout: () => void\n  setUser: (user: User | null) => void\n  setToken: (token: string | null) => void\n  setLoading: (loading: boolean) => void\n  initialize: () => void\n}\n\nexport const useAuthStore = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      token: null,\n      isLoading: false, // Start with false to prevent infinite loading\n      isAuthenticated: false,\n\n      login: async (email: string, password: string, userType = 'student') => {\n        set({ isLoading: true })\n\n        try {\n          // Use the auth login endpoint\n          const endpoint = '/api/auth/login'\n\n          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            credentials: 'include', // Include cookies for cross-origin requests\n            body: JSON.stringify({\n              email,\n              password,\n              userType\n            }),\n          })\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            throw new Error(errorData.message || errorData.error || 'Login failed')\n          }\n\n          const data = await response.json()\n\n          // Validate that the user has the expected role using legacyRole\n          if (userType === 'super_admin' && !['super_admin', 'platform_staff'].includes(data.user.legacyRole)) {\n            throw new Error('Access denied. Super admin privileges required.')\n          }\n          if (userType === 'institute_admin' && !['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(data.user.legacyRole)) {\n            throw new Error('Access denied. Institute admin privileges required.')\n          }\n          if (userType === 'student' && data.user.legacyRole !== 'student') {\n            throw new Error('Access denied. Student account required.')\n          }\n\n          // Store token in localStorage first\n          if (data.token) {\n            localStorage.setItem('auth_token', data.token)\n          }\n\n          // Then set the auth state\n          set({\n            user: data.user,\n            token: data.token,\n            isAuthenticated: true,\n            isLoading: false\n          })\n\n          // Force persist to localStorage immediately\n          setTimeout(() => {\n            console.log('Auth state set after login:', {\n              user: data.user.email,\n              isAuthenticated: true\n            })\n          }, 0)\n\n        } catch (error) {\n          set({ isLoading: false })\n          throw error\n        }\n      },\n\n      register: async (userData: any, userType = 'student') => {\n        set({ isLoading: true })\n\n        try {\n          // Determine the correct API endpoint based on user type\n          let endpoint = '/api/auth/register'\n          if (userType === 'institute') {\n            endpoint = '/api/auth/register' // Institute registration endpoint\n          }\n\n          const requestData = {\n            ...userData,\n            role: userType === 'institute' ? 'institute_admin' : 'student'\n          }\n\n          // Debug: Log the data being sent\n          console.log('Registration data being sent:', requestData)\n          console.log('Endpoint:', `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`)\n\n          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`, {\n            method: 'POST',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            credentials: 'include',\n            body: JSON.stringify(requestData),\n          })\n\n          if (!response.ok) {\n            const errorData = await response.json()\n            throw new Error(errorData.message || errorData.error || 'Registration failed')\n          }\n\n          const data = await response.json()\n\n          // For institute registration, don't auto-login (needs approval)\n          if (userType === 'institute') {\n            set({ isLoading: false })\n            return\n          }\n\n          // For student registration, auto-login\n          set({\n            user: data.user,\n            token: data.token,\n            isAuthenticated: true,\n            isLoading: false\n          })\n\n          if (data.token) {\n            localStorage.setItem('auth_token', data.token)\n          }\n\n        } catch (error) {\n          set({ isLoading: false })\n          throw error\n        }\n      },\n\n      logout: () => {\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false\n        })\n        \n        // Clear localStorage\n        localStorage.removeItem('auth_token')\n        \n        // Redirect based on user role using legacyRole\n        const { user } = get()\n        if (user?.legacyRole === 'super_admin' || user?.legacyRole === 'platform_staff') {\n          window.location.href = '/auth/admin/login'\n        } else if (user?.legacyRole === 'student') {\n          window.location.href = '/auth/user-login'\n        } else {\n          window.location.href = '/auth/login'\n        }\n      },\n\n      setUser: (user: User | null) => {\n        set({ user, isAuthenticated: !!user })\n      },\n\n      setToken: (token: string | null) => {\n        set({ token })\n        if (token) {\n          localStorage.setItem('auth_token', token)\n        } else {\n          localStorage.removeItem('auth_token')\n        }\n      },\n\n      setLoading: (loading: boolean) => {\n        set({ isLoading: loading })\n      },\n\n      // Initialize auth state - check persisted state first\n      initialize: () => {\n        const currentState = get()\n        console.log('Initialize called, current state:', {\n          hasUser: !!currentState.user,\n          hasToken: !!currentState.token,\n          isAuthenticated: currentState.isAuthenticated,\n          isLoading: currentState.isLoading\n        })\n\n        // If we already have valid auth state, just ensure loading is false\n        if (currentState.user && currentState.token && currentState.isAuthenticated) {\n          console.log('Auth already initialized from persisted state:', currentState.user.email)\n          set({ isLoading: false })\n          return\n        }\n\n        // If no valid persisted state, set to not authenticated\n        console.log('No valid auth state found, setting to not authenticated')\n        set({\n          user: null,\n          token: null,\n          isAuthenticated: false,\n          isLoading: false\n        })\n      }\n    }),\n    {\n      name: 'auth-storage',\n      partialize: (state) => ({\n        user: state.user,\n        token: state.token,\n        isAuthenticated: state.isAuthenticated\n      })\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AA8CO,MAAM,eAAe,CAAA,GAAA,qPAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,OAAO;QACP,WAAW;QACX,iBAAiB;QAEjB,OAAO,OAAO,OAAe,UAAkB,WAAW,SAAS;YACjE,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,8BAA8B;gBAC9B,MAAM,WAAW;gBAEjB,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,0BAA0B,UAAU,EAAE;oBACvG,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,aAAa;oBACb,MAAM,KAAK,SAAS,CAAC;wBACnB;wBACA;wBACA;oBACF;gBACF;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;gBAC1D;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,gEAAgE;gBAChE,IAAI,aAAa,iBAAiB,CAAC;oBAAC;oBAAe;iBAAiB,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,UAAU,GAAG;oBACnG,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,aAAa,qBAAqB,CAAC;oBAAC;oBAAmB;oBAAkB;oBAAW;iBAAkB,CAAC,QAAQ,CAAC,KAAK,IAAI,CAAC,UAAU,GAAG;oBACzI,MAAM,IAAI,MAAM;gBAClB;gBACA,IAAI,aAAa,aAAa,KAAK,IAAI,CAAC,UAAU,KAAK,WAAW;oBAChE,MAAM,IAAI,MAAM;gBAClB;gBAEA,oCAAoC;gBACpC,IAAI,KAAK,KAAK,EAAE;oBACd,aAAa,OAAO,CAAC,cAAc,KAAK,KAAK;gBAC/C;gBAEA,0BAA0B;gBAC1B,IAAI;oBACF,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,iBAAiB;oBACjB,WAAW;gBACb;gBAEA,4CAA4C;gBAC5C,WAAW;oBACT,QAAQ,GAAG,CAAC,+BAA+B;wBACzC,MAAM,KAAK,IAAI,CAAC,KAAK;wBACrB,iBAAiB;oBACnB;gBACF,GAAG;YAEL,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,MAAM;YACR;QACF;QAEA,UAAU,OAAO,UAAe,WAAW,SAAS;YAClD,IAAI;gBAAE,WAAW;YAAK;YAEtB,IAAI;gBACF,wDAAwD;gBACxD,IAAI,WAAW;gBACf,IAAI,aAAa,aAAa;oBAC5B,WAAW,qBAAqB,kCAAkC;;gBACpE;gBAEA,MAAM,cAAc;oBAClB,GAAG,QAAQ;oBACX,MAAM,aAAa,cAAc,oBAAoB;gBACvD;gBAEA,iCAAiC;gBACjC,QAAQ,GAAG,CAAC,iCAAiC;gBAC7C,QAAQ,GAAG,CAAC,aAAa,GAAG,6DAAmC,0BAA0B,UAAU;gBAEnG,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,0BAA0B,UAAU,EAAE;oBACvG,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,aAAa;oBACb,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;oBACrC,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI;gBAC1D;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,gEAAgE;gBAChE,IAAI,aAAa,aAAa;oBAC5B,IAAI;wBAAE,WAAW;oBAAM;oBACvB;gBACF;gBAEA,uCAAuC;gBACvC,IAAI;oBACF,MAAM,KAAK,IAAI;oBACf,OAAO,KAAK,KAAK;oBACjB,iBAAiB;oBACjB,WAAW;gBACb;gBAEA,IAAI,KAAK,KAAK,EAAE;oBACd,aAAa,OAAO,CAAC,cAAc,KAAK,KAAK;gBAC/C;YAEF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,MAAM;YACR;QACF;QAEA,QAAQ;YACN,IAAI;gBACF,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,WAAW;YACb;YAEA,qBAAqB;YACrB,aAAa,UAAU,CAAC;YAExB,+CAA+C;YAC/C,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,MAAM,eAAe,iBAAiB,MAAM,eAAe,kBAAkB;gBAC/E,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO,IAAI,MAAM,eAAe,WAAW;gBACzC,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,OAAO;gBACL,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB;QACF;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;YAAK;QACtC;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;YACZ,IAAI,OAAO;gBACT,aAAa,OAAO,CAAC,cAAc;YACrC,OAAO;gBACL,aAAa,UAAU,CAAC;YAC1B;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,sDAAsD;QACtD,YAAY;YACV,MAAM,eAAe;YACrB,QAAQ,GAAG,CAAC,qCAAqC;gBAC/C,SAAS,CAAC,CAAC,aAAa,IAAI;gBAC5B,UAAU,CAAC,CAAC,aAAa,KAAK;gBAC9B,iBAAiB,aAAa,eAAe;gBAC7C,WAAW,aAAa,SAAS;YACnC;YAEA,oEAAoE;YACpE,IAAI,aAAa,IAAI,IAAI,aAAa,KAAK,IAAI,aAAa,eAAe,EAAE;gBAC3E,QAAQ,GAAG,CAAC,kDAAkD,aAAa,IAAI,CAAC,KAAK;gBACrF,IAAI;oBAAE,WAAW;gBAAM;gBACvB;YACF;YAEA,wDAAwD;YACxD,QAAQ,GAAG,CAAC;YACZ,IAAI;gBACF,MAAM;gBACN,OAAO;gBACP,iBAAiB;gBACjB,WAAW;YACb;QACF;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,OAAO,MAAM,KAAK;YAClB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 627, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/hooks/useResponsive.ts"], "sourcesContent": ["import { useState, useEffect } from 'react'\n\n// Breakpoint definitions (matching Tailwind CSS)\nexport const breakpoints = {\n  sm: 640,\n  md: 768,\n  lg: 1024,\n  xl: 1280,\n  '2xl': 1536\n} as const\n\nexport type Breakpoint = keyof typeof breakpoints\n\n// Hook to detect current screen size\nexport function useResponsive() {\n  const [screenSize, setScreenSize] = useState<{\n    width: number\n    height: number\n    isMobile: boolean\n    isTablet: boolean\n    isDesktop: boolean\n    isLarge: boolean\n    currentBreakpoint: Breakpoint | 'xs'\n  }>({\n    width: 0,\n    height: 0,\n    isMobile: false,\n    isTablet: false,\n    isDesktop: false,\n    isLarge: false,\n    currentBreakpoint: 'xs'\n  })\n\n  useEffect(() => {\n    const updateScreenSize = () => {\n      const width = window.innerWidth\n      const height = window.innerHeight\n\n      // Determine current breakpoint\n      let currentBreakpoint: Breakpoint | 'xs' = 'xs'\n      if (width >= breakpoints['2xl']) currentBreakpoint = '2xl'\n      else if (width >= breakpoints.xl) currentBreakpoint = 'xl'\n      else if (width >= breakpoints.lg) currentBreakpoint = 'lg'\n      else if (width >= breakpoints.md) currentBreakpoint = 'md'\n      else if (width >= breakpoints.sm) currentBreakpoint = 'sm'\n\n      setScreenSize({\n        width,\n        height,\n        isMobile: width < breakpoints.md,\n        isTablet: width >= breakpoints.md && width < breakpoints.lg,\n        isDesktop: width >= breakpoints.lg,\n        isLarge: width >= breakpoints.xl,\n        currentBreakpoint\n      })\n    }\n\n    // Initial call\n    updateScreenSize()\n\n    // Add event listener\n    window.addEventListener('resize', updateScreenSize)\n\n    // Cleanup\n    return () => window.removeEventListener('resize', updateScreenSize)\n  }, [])\n\n  return screenSize\n}\n\n// Hook to check if screen is at least a certain breakpoint\nexport function useBreakpoint(breakpoint: Breakpoint) {\n  const { width } = useResponsive()\n  return width >= breakpoints[breakpoint]\n}\n\n// Hook for media queries\nexport function useMediaQuery(query: string) {\n  const [matches, setMatches] = useState(false)\n\n  useEffect(() => {\n    const mediaQuery = window.matchMedia(query)\n    setMatches(mediaQuery.matches)\n\n    const handler = (event: MediaQueryListEvent) => {\n      setMatches(event.matches)\n    }\n\n    mediaQuery.addEventListener('change', handler)\n    return () => mediaQuery.removeEventListener('change', handler)\n  }, [query])\n\n  return matches\n}\n\n// Utility functions for responsive design\nexport const responsive = {\n  // Check if current screen is mobile\n  isMobile: () => window.innerWidth < breakpoints.md,\n  \n  // Check if current screen is tablet\n  isTablet: () => window.innerWidth >= breakpoints.md && window.innerWidth < breakpoints.lg,\n  \n  // Check if current screen is desktop\n  isDesktop: () => window.innerWidth >= breakpoints.lg,\n  \n  // Get current breakpoint\n  getCurrentBreakpoint: (): Breakpoint | 'xs' => {\n    const width = window.innerWidth\n    if (width >= breakpoints['2xl']) return '2xl'\n    if (width >= breakpoints.xl) return 'xl'\n    if (width >= breakpoints.lg) return 'lg'\n    if (width >= breakpoints.md) return 'md'\n    if (width >= breakpoints.sm) return 'sm'\n    return 'xs'\n  },\n  \n  // Get responsive grid columns\n  getGridColumns: (mobile: number = 1, tablet: number = 2, desktop: number = 3, large: number = 4) => {\n    const width = window.innerWidth\n    if (width >= breakpoints.xl) return large\n    if (width >= breakpoints.lg) return desktop\n    if (width >= breakpoints.md) return tablet\n    return mobile\n  },\n  \n  // Get responsive items per page\n  getItemsPerPage: (mobile: number = 5, tablet: number = 10, desktop: number = 20) => {\n    const width = window.innerWidth\n    if (width >= breakpoints.lg) return desktop\n    if (width >= breakpoints.md) return tablet\n    return mobile\n  }\n}\n\n// Hook for responsive grid columns\nexport function useResponsiveGrid(\n  mobile: number = 1,\n  tablet: number = 2,\n  desktop: number = 3,\n  large: number = 4\n) {\n  const { isMobile, isTablet, isDesktop, isLarge } = useResponsive()\n  \n  if (isLarge) return large\n  if (isDesktop) return desktop\n  if (isTablet) return tablet\n  return mobile\n}\n\n// Hook for responsive items per page\nexport function useResponsiveItemsPerPage(\n  mobile: number = 5,\n  tablet: number = 10,\n  desktop: number = 20\n) {\n  const { isMobile, isTablet } = useResponsive()\n  \n  if (isMobile) return mobile\n  if (isTablet) return tablet\n  return desktop\n}\n\n// Hook for responsive sidebar behavior\nexport function useResponsiveSidebar() {\n  const { isMobile, isTablet } = useResponsive()\n  \n  return {\n    shouldCollapse: isMobile || isTablet,\n    shouldOverlay: isMobile,\n    defaultCollapsed: isMobile || isTablet\n  }\n}\n\n// Hook for responsive table behavior\nexport function useResponsiveTable() {\n  const { isMobile, isTablet } = useResponsive()\n  \n  return {\n    shouldUseCards: isMobile,\n    shouldHideColumns: isMobile || isTablet,\n    shouldScroll: isMobile || isTablet\n  }\n}\n\n// Hook for responsive form layout\nexport function useResponsiveForm() {\n  const { isMobile, isTablet } = useResponsive()\n  \n  return {\n    columns: isMobile ? 1 : isTablet ? 2 : 3,\n    shouldStack: isMobile,\n    shouldUseFullWidth: isMobile\n  }\n}\n\n// Hook for responsive modal behavior\nexport function useResponsiveModal() {\n  const { isMobile } = useResponsive()\n  \n  return {\n    shouldUseFullScreen: isMobile,\n    maxWidth: isMobile ? '100%' : '90%',\n    padding: isMobile ? '1rem' : '2rem'\n  }\n}\n\nexport default useResponsive\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;AAGO,MAAM,cAAc;IACzB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;AACT;AAKO,SAAS;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAQxC;QACD,OAAO;QACP,QAAQ;QACR,UAAU;QACV,UAAU;QACV,WAAW;QACX,SAAS;QACT,mBAAmB;IACrB;IAEA,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,mBAAmB;YACvB,MAAM,QAAQ,OAAO,UAAU;YAC/B,MAAM,SAAS,OAAO,WAAW;YAEjC,+BAA+B;YAC/B,IAAI,oBAAuC;YAC3C,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,oBAAoB;iBAChD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;iBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;iBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;iBACjD,IAAI,SAAS,YAAY,EAAE,EAAE,oBAAoB;YAEtD,cAAc;gBACZ;gBACA;gBACA,UAAU,QAAQ,YAAY,EAAE;gBAChC,UAAU,SAAS,YAAY,EAAE,IAAI,QAAQ,YAAY,EAAE;gBAC3D,WAAW,SAAS,YAAY,EAAE;gBAClC,SAAS,SAAS,YAAY,EAAE;gBAChC;YACF;QACF;QAEA,eAAe;QACf;QAEA,qBAAqB;QACrB,OAAO,gBAAgB,CAAC,UAAU;QAElC,UAAU;QACV,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,OAAO;AACT;AAGO,SAAS,cAAc,UAAsB;IAClD,MAAM,EAAE,KAAK,EAAE,GAAG;IAClB,OAAO,SAAS,WAAW,CAAC,WAAW;AACzC;AAGO,SAAS,cAAc,KAAa;IACzC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,aAAa,OAAO,UAAU,CAAC;QACrC,WAAW,WAAW,OAAO;QAE7B,MAAM,UAAU,CAAC;YACf,WAAW,MAAM,OAAO;QAC1B;QAEA,WAAW,gBAAgB,CAAC,UAAU;QACtC,OAAO,IAAM,WAAW,mBAAmB,CAAC,UAAU;IACxD,GAAG;QAAC;KAAM;IAEV,OAAO;AACT;AAGO,MAAM,aAAa;IACxB,oCAAoC;IACpC,UAAU,IAAM,OAAO,UAAU,GAAG,YAAY,EAAE;IAElD,oCAAoC;IACpC,UAAU,IAAM,OAAO,UAAU,IAAI,YAAY,EAAE,IAAI,OAAO,UAAU,GAAG,YAAY,EAAE;IAEzF,qCAAqC;IACrC,WAAW,IAAM,OAAO,UAAU,IAAI,YAAY,EAAE;IAEpD,yBAAyB;IACzB,sBAAsB;QACpB,MAAM,QAAQ,OAAO,UAAU;QAC/B,IAAI,SAAS,WAAW,CAAC,MAAM,EAAE,OAAO;QACxC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,OAAO;IACT;IAEA,8BAA8B;IAC9B,gBAAgB,CAAC,SAAiB,CAAC,EAAE,SAAiB,CAAC,EAAE,UAAkB,CAAC,EAAE,QAAgB,CAAC;QAC7F,MAAM,QAAQ,OAAO,UAAU;QAC/B,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,OAAO;IACT;IAEA,gCAAgC;IAChC,iBAAiB,CAAC,SAAiB,CAAC,EAAE,SAAiB,EAAE,EAAE,UAAkB,EAAE;QAC7E,MAAM,QAAQ,OAAO,UAAU;QAC/B,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,IAAI,SAAS,YAAY,EAAE,EAAE,OAAO;QACpC,OAAO;IACT;AACF;AAGO,SAAS,kBACd,SAAiB,CAAC,EAClB,SAAiB,CAAC,EAClB,UAAkB,CAAC,EACnB,QAAgB,CAAC;IAEjB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;IAEnD,IAAI,SAAS,OAAO;IACpB,IAAI,WAAW,OAAO;IACtB,IAAI,UAAU,OAAO;IACrB,OAAO;AACT;AAGO,SAAS,0BACd,SAAiB,CAAC,EAClB,SAAiB,EAAE,EACnB,UAAkB,EAAE;IAEpB,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAE/B,IAAI,UAAU,OAAO;IACrB,IAAI,UAAU,OAAO;IACrB,OAAO;AACT;AAGO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAE/B,OAAO;QACL,gBAAgB,YAAY;QAC5B,eAAe;QACf,kBAAkB,YAAY;IAChC;AACF;AAGO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAE/B,OAAO;QACL,gBAAgB;QAChB,mBAAmB,YAAY;QAC/B,cAAc,YAAY;IAC5B;AACF;AAGO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG;IAE/B,OAAO;QACL,SAAS,WAAW,IAAI,WAAW,IAAI;QACvC,aAAa;QACb,oBAAoB;IACtB;AACF;AAGO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG;IAErB,OAAO;QACL,qBAAqB;QACrB,UAAU,WAAW,SAAS;QAC9B,SAAS,WAAW,SAAS;IAC/B;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/utils/permissions.tsx"], "sourcesContent": ["import React from 'react'\nimport { UserType } from '@/stores/sidebar/useSidebarStore'\n\n// Permission types\nexport interface Permission {\n  id: string\n  name: string\n  code: string // Add code field for permission checking\n  category: string\n  resource: string\n  action: string\n  scope: 'global' | 'institute' | 'department' | 'own'\n  requiredLevel: 1 | 2 | 3 | 4 | 5\n  level?: string // Add level field from backend\n  isActive?: boolean // Add isActive field from backend\n}\n\nexport interface UserPermissions {\n  role: string\n  level: number\n  permissions: Permission[]\n  department?: string\n  instituteId?: string\n}\n\n// Default permissions for each user type\nexport const DEFAULT_PERMISSIONS = {\n  super_admin: {\n    level: 1,\n    permissions: [\n      // Dashboard\n      'view_dashboard',\n      'manage_dashboard',\n\n      // User & Role Management\n      'manage_users',\n      'manage_roles',\n      'manage_permissions',\n      'view_users',\n      'create_users',\n      'update_users',\n      'delete_users',\n      'view_roles',\n      'create_roles',\n      'update_roles',\n      'delete_roles',\n      'view_permissions',\n      'create_permissions',\n      'update_permissions',\n      'delete_permissions',\n\n      // Institute Management\n      'manage_institutes',\n      'view_institutes',\n      'create_institutes',\n      'update_institutes',\n      'delete_institutes',\n\n      // Location Management\n      'manage_locations',\n      'manage_countries',\n      'manage_states',\n      'manage_districts',\n      'view_locations',\n      'view_countries',\n      'view_states',\n      'view_districts',\n      'create_countries',\n      'create_states',\n      'create_districts',\n      'update_countries',\n      'update_states',\n      'update_districts',\n      'delete_countries',\n      'delete_states',\n      'delete_districts',\n\n      // Tax Management\n      'manage_taxes',\n      'view_taxes',\n      'create_taxes',\n      'update_taxes',\n      'delete_taxes',\n      'calculate_taxes',\n\n      // Theme Management\n      'manage_themes',\n      'view_themes',\n      'create_themes',\n      'update_themes',\n      'delete_themes',\n      'apply_themes',\n\n      // System Management\n      'manage_settings',\n      'view_settings',\n      'update_settings',\n      'manage_billing',\n      'view_billing',\n      'view_analytics',\n      'view_reports',\n      'export_reports',\n\n      // Course & Content Management\n      'manage_courses',\n      'view_courses',\n      'create_courses',\n      'update_courses',\n      'delete_courses',\n      'manage_content',\n      'view_content',\n      'create_content',\n      'update_content',\n      'delete_content',\n\n      // Student Management\n      'manage_students',\n      'view_students',\n      'create_students',\n      'update_students',\n      'delete_students',\n\n      // Branch Management\n      'manage_branches',\n      'view_branches',\n      'create_branches',\n      'update_branches',\n      'delete_branches',\n\n      // Live Classes & Assignments\n      'manage_live_classes',\n      'view_live_classes',\n      'create_live_classes',\n      'update_live_classes',\n      'delete_live_classes',\n      'join_live_classes',\n      'view_assignments',\n      'create_assignments',\n      'update_assignments',\n      'delete_assignments',\n      'grade_assignments',\n      'submit_assignments',\n\n      // Communication & Website\n      'manage_website',\n      'view_website',\n      'update_website',\n      'send_notifications',\n      'manage_communication',\n\n      // System Administration\n      'system_admin',\n      'access_all'\n    ]\n  },\n  institute_admin: {\n    level: 2,\n    permissions: [\n      'manage_courses',\n      'manage_students',\n      'manage_branches',\n      'manage_institute_billing',\n      'view_institute_analytics',\n      'manage_website',\n      'manage_institute_settings'\n    ]\n  },\n  student: {\n    level: 5,\n    permissions: [\n      'view_courses',\n      'enroll_courses',\n      'view_assignments',\n      'submit_assignments',\n      'join_live_classes',\n      'view_progress',\n      'manage_account',\n      'access_community'\n    ]\n  }\n}\n\n// Navigation permissions mapping - Updated for new permission structure\nexport const NAVIGATION_PERMISSIONS = {\n  // Super Admin Dashboard\n  'super-admin': ['view_dashboard', 'manage_dashboard'],\n  'super-admin-dashboard': ['view_dashboard', 'manage_dashboard'],\n\n  // User & Role Management\n  'super-admin-users': ['manage_users', 'view_users'],\n  'super-admin-role-permissions': ['manage_roles', 'manage_permissions', 'view_roles', 'view_permissions'],\n\n  // Institute Management\n  'super-admin-institutes': ['manage_institutes', 'view_institutes'],\n\n  // Location Management\n  'super-admin-locations': ['manage_locations', 'view_locations'],\n  'super-admin-countries': ['manage_countries', 'view_countries'],\n  'super-admin-states': ['manage_states', 'view_states'],\n  'super-admin-districts': ['manage_districts', 'view_districts'],\n\n  // Tax Management\n  'super-admin-tax': ['manage_taxes', 'view_taxes'],\n  'super-admin-tax-management': ['manage_taxes', 'view_taxes', 'calculate_taxes'],\n\n  // Theme Management\n  'super-admin-themes': ['manage_themes', 'view_themes', 'apply_themes'],\n\n  // Billing & Finance\n  'super-admin-billing': ['manage_billing', 'view_billing'],\n\n  // Analytics & Reports\n  'super-admin-analytics': ['view_analytics', 'view_reports', 'export_reports'],\n\n  // System Settings\n  'super-admin-settings': ['manage_settings', 'view_settings', 'update_settings'],\n\n  // Website Management\n  'super-admin-website': ['manage_website', 'view_website', 'update_website'],\n\n  // Institute Admin\n  'institute-admin-dashboard': [],\n  'institute-admin-courses': ['manage_courses'],\n  'institute-admin-students': ['manage_students'],\n  'institute-admin-branches': ['manage_branches'],\n  'institute-admin-billing': ['manage_institute_billing'],\n  'institute-admin-analytics': ['view_institute_analytics'],\n  'institute-admin-website': ['manage_website'],\n  'institute-admin-settings': ['manage_institute_settings'],\n\n  // Student\n  'student-dashboard': [],\n  'student-courses': ['view_courses'],\n  'student-marketplace': ['view_courses'],\n  'student-assignments': ['view_assignments'],\n  'student-live-classes': ['join_live_classes'],\n  'student-progress': ['view_progress'],\n  'student-community': ['access_community'],\n  'student-payments': ['manage_account'],\n  'student-account': ['manage_account'],\n  'student-support': []\n}\n\n/**\n * Check if user has a specific permission using the new structure\n */\nexport function hasPermission(\n  userPermissions: UserPermissions,\n  requiredPermission: string,\n  resource?: string,\n  scope?: 'global' | 'institute' | 'department' | 'own'\n): boolean {\n  console.log(`🔍 hasPermission check: ${requiredPermission}`, {\n    userRole: userPermissions.role,\n    permissionCount: userPermissions.permissions.length,\n    isSuperAdmin: userPermissions.role === 'super_admin'\n  })\n\n  // Super admin and platform staff have all permissions\n  if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {\n    console.log(`✅ Permission granted (${userPermissions.role} has all permissions)`)\n    return true\n  }\n\n  // Check if user has the specific permission by code, name, or id\n  const hasDirectPermission = userPermissions.permissions.some(\n    permission =>\n      permission.code === requiredPermission ||\n      permission.name === requiredPermission ||\n      permission.id === requiredPermission ||\n      permission.name.toLowerCase() === requiredPermission.toLowerCase() ||\n      permission.code?.toLowerCase() === requiredPermission.toLowerCase()\n  )\n\n  console.log(`🔍 Direct permission check result: ${hasDirectPermission}`)\n\n  if (!hasDirectPermission) {\n    // Log available permissions for debugging\n    const availablePermissions = userPermissions.permissions.map(p => p.code || p.name).slice(0, 10)\n    console.log(`❌ Permission \"${requiredPermission}\" not found. Available permissions (first 10):`, availablePermissions)\n    return false\n  }\n\n  // If scope is specified, check scope restrictions\n  if (scope) {\n    const permission = userPermissions.permissions.find(\n      p => p.code === requiredPermission || p.name === requiredPermission || p.id === requiredPermission\n    )\n\n    if (!permission) return false\n\n    // Check scope hierarchy: global > institute > department > own\n    const scopeHierarchy = ['global', 'institute', 'department', 'own']\n    const userScopeIndex = scopeHierarchy.indexOf(permission.scope)\n    const requiredScopeIndex = scopeHierarchy.indexOf(scope)\n\n    const scopeAllowed = userScopeIndex <= requiredScopeIndex\n    console.log(`🔍 Scope check: ${permission.scope} vs ${scope} = ${scopeAllowed}`)\n    return scopeAllowed\n  }\n\n  console.log(`✅ Permission \"${requiredPermission}\" granted`)\n  return true\n}\n\n/**\n * Check if user can access a navigation item using new permission structure\n */\nexport function canAccessNavigation(\n  userPermissions: UserPermissions,\n  navigationId: string\n): boolean {\n  console.log(`🔍 canAccessNavigation check: ${navigationId}`, {\n    userRole: userPermissions.role,\n    isSuperAdmin: userPermissions.role === 'super_admin'\n  })\n\n  // Super admin can access everything\n  if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {\n    console.log(`✅ Navigation access granted (${userPermissions.role} has all access)`)\n    return true\n  }\n\n  const requiredPermissions = NAVIGATION_PERMISSIONS[navigationId as keyof typeof NAVIGATION_PERMISSIONS]\n\n  // If no permissions required, allow access\n  if (!requiredPermissions || requiredPermissions.length === 0) {\n    console.log(`✅ Navigation access granted (no permissions required for ${navigationId})`)\n    return true\n  }\n\n  // Check if user has any of the required permissions\n  const hasAccess = requiredPermissions.some(permission =>\n    hasPermission(userPermissions, permission)\n  )\n\n  console.log(`🔍 Navigation access result for ${navigationId}: ${hasAccess}`)\n  console.log(`   Required permissions: ${requiredPermissions.join(', ')}`)\n\n  return hasAccess\n}\n\n/**\n * Filter navigation items based on user permissions\n */\nexport function filterNavigationByPermissions(\n  navigationItems: any[],\n  userPermissions: UserPermissions\n): any[] {\n  // Super admin has access to all navigation items\n  if (userPermissions.role === 'super_admin') {\n    return navigationItems\n  }\n\n  // Prevent infinite loops by checking if navigationItems is valid\n  if (!Array.isArray(navigationItems) || navigationItems.length === 0) {\n    return []\n  }\n\n  return navigationItems.filter(item => {\n    // Ensure item has required properties\n    if (!item || !item.href) {\n      return false\n    }\n\n    // Generate navigation ID from href\n    const navigationId = item.href.replace('/', '').replace(/\\//g, '-')\n\n    // Check if user can access this navigation item\n    const canAccess = canAccessNavigation(userPermissions, navigationId)\n\n    // If item has children, filter them recursively\n    if (item.children && Array.isArray(item.children) && item.children.length > 0) {\n      const filteredChildren = filterNavigationByPermissions(item.children, userPermissions)\n\n      // If no children are accessible, hide the parent too\n      if (filteredChildren.length === 0 && !canAccess) {\n        return false\n      }\n\n      // Update children with filtered list (avoid mutation)\n      item = { ...item, children: filteredChildren }\n    }\n\n    return canAccess\n  })\n}\n\n/**\n * Check if user has minimum required level\n */\nexport function hasMinimumLevel(\n  userLevel: number,\n  requiredLevel: number\n): boolean {\n  // Lower numbers = higher level (1 = highest, 5 = lowest)\n  return userLevel <= requiredLevel\n}\n\n/**\n * Get user permissions from user object with new structure\n */\nexport function getUserPermissions(user: any): UserPermissions {\n  if (!user) {\n    return {\n      role: 'guest',\n      level: 5,\n      permissions: [],\n      department: undefined,\n      instituteId: undefined\n    }\n  }\n\n  console.log('🔍 getUserPermissions - Processing user:', {\n    email: user.email,\n    legacyRole: user.legacyRole,\n    hasRole: !!user.role,\n    roleType: typeof user.role,\n    hasRolePermissions: !!(user.role && user.role.permissions),\n    rolePermissionsCount: user.role?.permissions?.length || 0\n  })\n\n  // Use legacyRole for role checking (consistent with auth system)\n  const role = user.legacyRole || 'student'\n\n  // Get level from role relationship or default\n  let level = 5\n  if (user.role && typeof user.role === 'object' && user.role.level) {\n    level = parseInt(user.role.level) || 5\n  } else {\n    level = DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS]?.level || 5\n  }\n\n  // Extract permissions from user.role.permissions array (new structure)\n  let permissions: Permission[] = []\n\n  if (user.role && typeof user.role === 'object' && Array.isArray(user.role.permissions)) {\n    console.log(`🔍 Found ${user.role.permissions.length} permissions in user.role.permissions`)\n\n    // Convert backend permission objects to frontend format\n    permissions = user.role.permissions\n      .filter((perm: any) => perm && perm.isActive !== false) // Only include active permissions\n      .map((perm: any) => ({\n        id: perm.id || perm.code,\n        name: perm.name || perm.code,\n        code: perm.code, // Include the code field for permission checking\n        category: perm.category || 'general',\n        resource: perm.resource || 'general',\n        action: perm.action || 'access',\n        scope: 'global' as const,\n        requiredLevel: level,\n        level: perm.level || '1',\n        isActive: perm.isActive !== false\n      }))\n\n    console.log(`✅ Processed ${permissions.length} active permissions`)\n\n    // Log sample permissions for debugging\n    if (permissions.length > 0) {\n      console.log('📝 Sample permissions:', permissions.slice(0, 5).map(p => ({\n        name: p.name,\n        code: p.code,\n        category: p.category,\n        resource: p.resource,\n        action: p.action\n      })))\n    }\n  } else if (user.permissions && Array.isArray(user.permissions)) {\n    // Fallback: Handle legacy simple permission strings\n    console.log('🔄 Using legacy permissions format')\n    permissions = user.permissions.map((perm: string) => ({\n      id: perm,\n      name: perm,\n      code: perm,\n      category: 'general',\n      resource: 'general',\n      action: 'access',\n      scope: 'global' as const,\n      requiredLevel: level,\n      level: '1',\n      isActive: true\n    }))\n  } else {\n    console.log('⚠️ No permissions found in user object')\n  }\n\n  const department = user.employment?.department || user.department\n  const instituteId = user.institute?.id || user.institute\n\n  const result = {\n    role,\n    level,\n    permissions,\n    department,\n    instituteId\n  }\n\n  console.log('✅ getUserPermissions result:', {\n    role: result.role,\n    level: result.level,\n    permissionCount: result.permissions.length,\n    department: result.department,\n    instituteId: result.instituteId\n  })\n\n  return result\n}\n\n/**\n * Check if user can perform CRUD operations\n */\nexport function canPerformAction(\n  userPermissions: UserPermissions,\n  action: 'create' | 'read' | 'update' | 'delete',\n  resource: string,\n  scope?: 'global' | 'institute' | 'department' | 'own'\n): boolean {\n  const permissionName = `${action}_${resource}`\n  return hasPermission(userPermissions, permissionName, resource, scope)\n}\n\n/**\n * Get allowed actions for a resource\n */\nexport function getAllowedActions(\n  userPermissions: UserPermissions,\n  resource: string\n): string[] {\n  const actions = ['create', 'read', 'update', 'delete']\n  \n  return actions.filter(action =>\n    canPerformAction(userPermissions, action as any, resource)\n  )\n}\n\n/**\n * Check if user is in same department\n */\nexport function isSameDepartment(\n  userDepartment: string | undefined,\n  targetDepartment: string | undefined\n): boolean {\n  if (!userDepartment || !targetDepartment) return false\n  return userDepartment === targetDepartment\n}\n\n/**\n * Check if user is in same institute\n */\nexport function isSameInstitute(\n  userInstituteId: string | undefined,\n  targetInstituteId: string | undefined\n): boolean {\n  if (!userInstituteId || !targetInstituteId) return false\n  return userInstituteId === targetInstituteId\n}\n\n/**\n * Permission-based component wrapper\n */\nexport function withPermission<T extends object>(\n  Component: React.ComponentType<T>,\n  requiredPermission: string,\n  fallback?: React.ComponentType<T>\n) {\n  return function PermissionWrapper(props: T) {\n    // This would be used with a permission context or hook\n    // For now, returning the component as-is\n    return <Component {...props} />\n  }\n}\n\n/**\n * Generate permission key for caching\n */\nexport function generatePermissionKey(\n  userId: string,\n  permission: string,\n  resource?: string,\n  scope?: string\n): string {\n  return `${userId}:${permission}:${resource || 'any'}:${scope || 'any'}`\n}\n\n/**\n * Validate permission structure\n */\nexport function validatePermission(permission: any): permission is Permission {\n  return (\n    permission &&\n    typeof permission.id === 'string' &&\n    typeof permission.name === 'string' &&\n    typeof permission.category === 'string' &&\n    typeof permission.resource === 'string' &&\n    typeof permission.action === 'string' &&\n    ['global', 'institute', 'department', 'own'].includes(permission.scope) &&\n    [1, 2, 3, 4, 5].includes(permission.requiredLevel)\n  )\n}\n\n/**\n * Debug function to test super admin permissions with new structure\n */\nexport function debugSuperAdminPermissions(user: any) {\n  console.log('=== SUPER ADMIN PERMISSION DEBUG ===')\n  console.log('User object:', {\n    email: user.email,\n    legacyRole: user.legacyRole,\n    hasRole: !!user.role,\n    roleType: typeof user.role,\n    roleName: user.role?.name,\n    roleCode: user.role?.code,\n    rolePermissionsCount: user.role?.permissions?.length || 0\n  })\n\n  const userPermissions = getUserPermissions(user)\n  console.log('Processed user permissions:', {\n    role: userPermissions.role,\n    level: userPermissions.level,\n    permissionCount: userPermissions.permissions.length,\n    department: userPermissions.department,\n    instituteId: userPermissions.instituteId\n  })\n\n  // Test key permissions with new codes\n  const testPermissions = [\n    'manage_users',\n    'manage_roles',\n    'manage_permissions',\n    'view_users',\n    'view_dashboard',\n    'manage_taxes',\n    'manage_locations',\n    'manage_themes',\n    'system_admin',\n    'access_all'\n  ]\n\n  console.log('Permission test results:')\n  testPermissions.forEach(permission => {\n    const hasAccess = hasPermission(userPermissions, permission)\n    console.log(`  ${permission}: ${hasAccess}`)\n  })\n\n  // Test navigation access with new navigation IDs\n  const testNavigation = [\n    'super-admin',\n    'super-admin-dashboard',\n    'super-admin-role-permissions',\n    'super-admin-users',\n    'super-admin-institutes',\n    'super-admin-tax',\n    'super-admin-locations',\n    'super-admin-themes',\n    'super-admin-settings'\n  ]\n\n  console.log('Navigation access test results:')\n  testNavigation.forEach(navId => {\n    const hasAccess = canAccessNavigation(userPermissions, navId)\n    console.log(`  ${navId}: ${hasAccess}`)\n  })\n\n  // Show sample permissions\n  if (userPermissions.permissions.length > 0) {\n    console.log('Sample permissions (first 10):')\n    userPermissions.permissions.slice(0, 10).forEach((perm, index) => {\n      console.log(`  ${index + 1}. ${perm.name} (${perm.code}) - ${perm.category}/${perm.resource}`)\n    })\n  }\n\n  console.log('=== END DEBUG ===')\n\n  return userPermissions\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AA0BO,MAAM,sBAAsB;IACjC,aAAa;QACX,OAAO;QACP,aAAa;YACX,YAAY;YACZ;YACA;YAEA,yBAAyB;YACzB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,uBAAuB;YACvB;YACA;YACA;YACA;YACA;YAEA,sBAAsB;YACtB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,iBAAiB;YACjB;YACA;YACA;YACA;YACA;YACA;YAEA,mBAAmB;YACnB;YACA;YACA;YACA;YACA;YACA;YAEA,oBAAoB;YACpB;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,8BAA8B;YAC9B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,qBAAqB;YACrB;YACA;YACA;YACA;YACA;YAEA,oBAAoB;YACpB;YACA;YACA;YACA;YACA;YAEA,6BAA6B;YAC7B;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YAEA,0BAA0B;YAC1B;YACA;YACA;YACA;YACA;YAEA,wBAAwB;YACxB;YACA;SACD;IACH;IACA,iBAAiB;QACf,OAAO;QACP,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;IACA,SAAS;QACP,OAAO;QACP,aAAa;YACX;YACA;YACA;YACA;YACA;YACA;YACA;YACA;SACD;IACH;AACF;AAGO,MAAM,yBAAyB;IACpC,wBAAwB;IACxB,eAAe;QAAC;QAAkB;KAAmB;IACrD,yBAAyB;QAAC;QAAkB;KAAmB;IAE/D,yBAAyB;IACzB,qBAAqB;QAAC;QAAgB;KAAa;IACnD,gCAAgC;QAAC;QAAgB;QAAsB;QAAc;KAAmB;IAExG,uBAAuB;IACvB,0BAA0B;QAAC;QAAqB;KAAkB;IAElE,sBAAsB;IACtB,yBAAyB;QAAC;QAAoB;KAAiB;IAC/D,yBAAyB;QAAC;QAAoB;KAAiB;IAC/D,sBAAsB;QAAC;QAAiB;KAAc;IACtD,yBAAyB;QAAC;QAAoB;KAAiB;IAE/D,iBAAiB;IACjB,mBAAmB;QAAC;QAAgB;KAAa;IACjD,8BAA8B;QAAC;QAAgB;QAAc;KAAkB;IAE/E,mBAAmB;IACnB,sBAAsB;QAAC;QAAiB;QAAe;KAAe;IAEtE,oBAAoB;IACpB,uBAAuB;QAAC;QAAkB;KAAe;IAEzD,sBAAsB;IACtB,yBAAyB;QAAC;QAAkB;QAAgB;KAAiB;IAE7E,kBAAkB;IAClB,wBAAwB;QAAC;QAAmB;QAAiB;KAAkB;IAE/E,qBAAqB;IACrB,uBAAuB;QAAC;QAAkB;QAAgB;KAAiB;IAE3E,kBAAkB;IAClB,6BAA6B,EAAE;IAC/B,2BAA2B;QAAC;KAAiB;IAC7C,4BAA4B;QAAC;KAAkB;IAC/C,4BAA4B;QAAC;KAAkB;IAC/C,2BAA2B;QAAC;KAA2B;IACvD,6BAA6B;QAAC;KAA2B;IACzD,2BAA2B;QAAC;KAAiB;IAC7C,4BAA4B;QAAC;KAA4B;IAEzD,UAAU;IACV,qBAAqB,EAAE;IACvB,mBAAmB;QAAC;KAAe;IACnC,uBAAuB;QAAC;KAAe;IACvC,uBAAuB;QAAC;KAAmB;IAC3C,wBAAwB;QAAC;KAAoB;IAC7C,oBAAoB;QAAC;KAAgB;IACrC,qBAAqB;QAAC;KAAmB;IACzC,oBAAoB;QAAC;KAAiB;IACtC,mBAAmB;QAAC;KAAiB;IACrC,mBAAmB,EAAE;AACvB;AAKO,SAAS,cACd,eAAgC,EAChC,kBAA0B,EAC1B,QAAiB,EACjB,KAAqD;IAErD,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,oBAAoB,EAAE;QAC3D,UAAU,gBAAgB,IAAI;QAC9B,iBAAiB,gBAAgB,WAAW,CAAC,MAAM;QACnD,cAAc,gBAAgB,IAAI,KAAK;IACzC;IAEA,sDAAsD;IACtD,IAAI,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK,kBAAkB;QACvF,QAAQ,GAAG,CAAC,CAAC,sBAAsB,EAAE,gBAAgB,IAAI,CAAC,qBAAqB,CAAC;QAChF,OAAO;IACT;IAEA,iEAAiE;IACjE,MAAM,sBAAsB,gBAAgB,WAAW,CAAC,IAAI,CAC1D,CAAA,aACE,WAAW,IAAI,KAAK,sBACpB,WAAW,IAAI,KAAK,sBACpB,WAAW,EAAE,KAAK,sBAClB,WAAW,IAAI,CAAC,WAAW,OAAO,mBAAmB,WAAW,MAChE,WAAW,IAAI,EAAE,kBAAkB,mBAAmB,WAAW;IAGrE,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,qBAAqB;IAEvE,IAAI,CAAC,qBAAqB;QACxB,0CAA0C;QAC1C,MAAM,uBAAuB,gBAAgB,WAAW,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI,IAAI,EAAE,IAAI,EAAE,KAAK,CAAC,GAAG;QAC7F,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,mBAAmB,8CAA8C,CAAC,EAAE;QACjG,OAAO;IACT;IAEA,kDAAkD;IAClD,IAAI,OAAO;QACT,MAAM,aAAa,gBAAgB,WAAW,CAAC,IAAI,CACjD,CAAA,IAAK,EAAE,IAAI,KAAK,sBAAsB,EAAE,IAAI,KAAK,sBAAsB,EAAE,EAAE,KAAK;QAGlF,IAAI,CAAC,YAAY,OAAO;QAExB,+DAA+D;QAC/D,MAAM,iBAAiB;YAAC;YAAU;YAAa;YAAc;SAAM;QACnE,MAAM,iBAAiB,eAAe,OAAO,CAAC,WAAW,KAAK;QAC9D,MAAM,qBAAqB,eAAe,OAAO,CAAC;QAElD,MAAM,eAAe,kBAAkB;QACvC,QAAQ,GAAG,CAAC,CAAC,gBAAgB,EAAE,WAAW,KAAK,CAAC,IAAI,EAAE,MAAM,GAAG,EAAE,cAAc;QAC/E,OAAO;IACT;IAEA,QAAQ,GAAG,CAAC,CAAC,cAAc,EAAE,mBAAmB,SAAS,CAAC;IAC1D,OAAO;AACT;AAKO,SAAS,oBACd,eAAgC,EAChC,YAAoB;IAEpB,QAAQ,GAAG,CAAC,CAAC,8BAA8B,EAAE,cAAc,EAAE;QAC3D,UAAU,gBAAgB,IAAI;QAC9B,cAAc,gBAAgB,IAAI,KAAK;IACzC;IAEA,oCAAoC;IACpC,IAAI,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK,kBAAkB;QACvF,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,gBAAgB,IAAI,CAAC,gBAAgB,CAAC;QAClF,OAAO;IACT;IAEA,MAAM,sBAAsB,sBAAsB,CAAC,aAAoD;IAEvG,2CAA2C;IAC3C,IAAI,CAAC,uBAAuB,oBAAoB,MAAM,KAAK,GAAG;QAC5D,QAAQ,GAAG,CAAC,CAAC,yDAAyD,EAAE,aAAa,CAAC,CAAC;QACvF,OAAO;IACT;IAEA,oDAAoD;IACpD,MAAM,YAAY,oBAAoB,IAAI,CAAC,CAAA,aACzC,cAAc,iBAAiB;IAGjC,QAAQ,GAAG,CAAC,CAAC,gCAAgC,EAAE,aAAa,EAAE,EAAE,WAAW;IAC3E,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,oBAAoB,IAAI,CAAC,OAAO;IAExE,OAAO;AACT;AAKO,SAAS,8BACd,eAAsB,EACtB,eAAgC;IAEhC,iDAAiD;IACjD,IAAI,gBAAgB,IAAI,KAAK,eAAe;QAC1C,OAAO;IACT;IAEA,iEAAiE;IACjE,IAAI,CAAC,MAAM,OAAO,CAAC,oBAAoB,gBAAgB,MAAM,KAAK,GAAG;QACnE,OAAO,EAAE;IACX;IAEA,OAAO,gBAAgB,MAAM,CAAC,CAAA;QAC5B,sCAAsC;QACtC,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;YACvB,OAAO;QACT;QAEA,mCAAmC;QACnC,MAAM,eAAe,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,OAAO;QAE/D,gDAAgD;QAChD,MAAM,YAAY,oBAAoB,iBAAiB;QAEvD,gDAAgD;QAChD,IAAI,KAAK,QAAQ,IAAI,MAAM,OAAO,CAAC,KAAK,QAAQ,KAAK,KAAK,QAAQ,CAAC,MAAM,GAAG,GAAG;YAC7E,MAAM,mBAAmB,8BAA8B,KAAK,QAAQ,EAAE;YAEtE,qDAAqD;YACrD,IAAI,iBAAiB,MAAM,KAAK,KAAK,CAAC,WAAW;gBAC/C,OAAO;YACT;YAEA,sDAAsD;YACtD,OAAO;gBAAE,GAAG,IAAI;gBAAE,UAAU;YAAiB;QAC/C;QAEA,OAAO;IACT;AACF;AAKO,SAAS,gBACd,SAAiB,EACjB,aAAqB;IAErB,yDAAyD;IACzD,OAAO,aAAa;AACtB;AAKO,SAAS,mBAAmB,IAAS;IAC1C,IAAI,CAAC,MAAM;QACT,OAAO;YACL,MAAM;YACN,OAAO;YACP,aAAa,EAAE;YACf,YAAY;YACZ,aAAa;QACf;IACF;IAEA,QAAQ,GAAG,CAAC,4CAA4C;QACtD,OAAO,KAAK,KAAK;QACjB,YAAY,KAAK,UAAU;QAC3B,SAAS,CAAC,CAAC,KAAK,IAAI;QACpB,UAAU,OAAO,KAAK,IAAI;QAC1B,oBAAoB,CAAC,CAAC,CAAC,KAAK,IAAI,IAAI,KAAK,IAAI,CAAC,WAAW;QACzD,sBAAsB,KAAK,IAAI,EAAE,aAAa,UAAU;IAC1D;IAEA,iEAAiE;IACjE,MAAM,OAAO,KAAK,UAAU,IAAI;IAEhC,8CAA8C;IAC9C,IAAI,QAAQ;IACZ,IAAI,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,YAAY,KAAK,IAAI,CAAC,KAAK,EAAE;QACjE,QAAQ,SAAS,KAAK,IAAI,CAAC,KAAK,KAAK;IACvC,OAAO;QACL,QAAQ,mBAAmB,CAAC,KAAyC,EAAE,SAAS;IAClF;IAEA,uEAAuE;IACvE,IAAI,cAA4B,EAAE;IAElC,IAAI,KAAK,IAAI,IAAI,OAAO,KAAK,IAAI,KAAK,YAAY,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,WAAW,GAAG;QACtF,QAAQ,GAAG,CAAC,CAAC,SAAS,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,qCAAqC,CAAC;QAE3F,wDAAwD;QACxD,cAAc,KAAK,IAAI,CAAC,WAAW,CAChC,MAAM,CAAC,CAAC,OAAc,QAAQ,KAAK,QAAQ,KAAK,OAAO,kCAAkC;SACzF,GAAG,CAAC,CAAC,OAAc,CAAC;gBACnB,IAAI,KAAK,EAAE,IAAI,KAAK,IAAI;gBACxB,MAAM,KAAK,IAAI,IAAI,KAAK,IAAI;gBAC5B,MAAM,KAAK,IAAI;gBACf,UAAU,KAAK,QAAQ,IAAI;gBAC3B,UAAU,KAAK,QAAQ,IAAI;gBAC3B,QAAQ,KAAK,MAAM,IAAI;gBACvB,OAAO;gBACP,eAAe;gBACf,OAAO,KAAK,KAAK,IAAI;gBACrB,UAAU,KAAK,QAAQ,KAAK;YAC9B,CAAC;QAEH,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,YAAY,MAAM,CAAC,mBAAmB,CAAC;QAElE,uCAAuC;QACvC,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,QAAQ,GAAG,CAAC,0BAA0B,YAAY,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,IAAK,CAAC;oBACtE,MAAM,EAAE,IAAI;oBACZ,MAAM,EAAE,IAAI;oBACZ,UAAU,EAAE,QAAQ;oBACpB,UAAU,EAAE,QAAQ;oBACpB,QAAQ,EAAE,MAAM;gBAClB,CAAC;QACH;IACF,OAAO,IAAI,KAAK,WAAW,IAAI,MAAM,OAAO,CAAC,KAAK,WAAW,GAAG;QAC9D,oDAAoD;QACpD,QAAQ,GAAG,CAAC;QACZ,cAAc,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,OAAiB,CAAC;gBACpD,IAAI;gBACJ,MAAM;gBACN,MAAM;gBACN,UAAU;gBACV,UAAU;gBACV,QAAQ;gBACR,OAAO;gBACP,eAAe;gBACf,OAAO;gBACP,UAAU;YACZ,CAAC;IACH,OAAO;QACL,QAAQ,GAAG,CAAC;IACd;IAEA,MAAM,aAAa,KAAK,UAAU,EAAE,cAAc,KAAK,UAAU;IACjE,MAAM,cAAc,KAAK,SAAS,EAAE,MAAM,KAAK,SAAS;IAExD,MAAM,SAAS;QACb;QACA;QACA;QACA;QACA;IACF;IAEA,QAAQ,GAAG,CAAC,gCAAgC;QAC1C,MAAM,OAAO,IAAI;QACjB,OAAO,OAAO,KAAK;QACnB,iBAAiB,OAAO,WAAW,CAAC,MAAM;QAC1C,YAAY,OAAO,UAAU;QAC7B,aAAa,OAAO,WAAW;IACjC;IAEA,OAAO;AACT;AAKO,SAAS,iBACd,eAAgC,EAChC,MAA+C,EAC/C,QAAgB,EAChB,KAAqD;IAErD,MAAM,iBAAiB,GAAG,OAAO,CAAC,EAAE,UAAU;IAC9C,OAAO,cAAc,iBAAiB,gBAAgB,UAAU;AAClE;AAKO,SAAS,kBACd,eAAgC,EAChC,QAAgB;IAEhB,MAAM,UAAU;QAAC;QAAU;QAAQ;QAAU;KAAS;IAEtD,OAAO,QAAQ,MAAM,CAAC,CAAA,SACpB,iBAAiB,iBAAiB,QAAe;AAErD;AAKO,SAAS,iBACd,cAAkC,EAClC,gBAAoC;IAEpC,IAAI,CAAC,kBAAkB,CAAC,kBAAkB,OAAO;IACjD,OAAO,mBAAmB;AAC5B;AAKO,SAAS,gBACd,eAAmC,EACnC,iBAAqC;IAErC,IAAI,CAAC,mBAAmB,CAAC,mBAAmB,OAAO;IACnD,OAAO,oBAAoB;AAC7B;AAKO,SAAS,eACd,SAAiC,EACjC,kBAA0B,EAC1B,QAAiC;IAEjC,OAAO,SAAS,kBAAkB,KAAQ;QACxC,uDAAuD;QACvD,yCAAyC;QACzC,qBAAO,mVAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;AACF;AAKO,SAAS,sBACd,MAAc,EACd,UAAkB,EAClB,QAAiB,EACjB,KAAc;IAEd,OAAO,GAAG,OAAO,CAAC,EAAE,WAAW,CAAC,EAAE,YAAY,MAAM,CAAC,EAAE,SAAS,OAAO;AACzE;AAKO,SAAS,mBAAmB,UAAe;IAChD,OACE,cACA,OAAO,WAAW,EAAE,KAAK,YACzB,OAAO,WAAW,IAAI,KAAK,YAC3B,OAAO,WAAW,QAAQ,KAAK,YAC/B,OAAO,WAAW,QAAQ,KAAK,YAC/B,OAAO,WAAW,MAAM,KAAK,YAC7B;QAAC;QAAU;QAAa;QAAc;KAAM,CAAC,QAAQ,CAAC,WAAW,KAAK,KACtE;QAAC;QAAG;QAAG;QAAG;QAAG;KAAE,CAAC,QAAQ,CAAC,WAAW,aAAa;AAErD;AAKO,SAAS,2BAA2B,IAAS;IAClD,QAAQ,GAAG,CAAC;IACZ,QAAQ,GAAG,CAAC,gBAAgB;QAC1B,OAAO,KAAK,KAAK;QACjB,YAAY,KAAK,UAAU;QAC3B,SAAS,CAAC,CAAC,KAAK,IAAI;QACpB,UAAU,OAAO,KAAK,IAAI;QAC1B,UAAU,KAAK,IAAI,EAAE;QACrB,UAAU,KAAK,IAAI,EAAE;QACrB,sBAAsB,KAAK,IAAI,EAAE,aAAa,UAAU;IAC1D;IAEA,MAAM,kBAAkB,mBAAmB;IAC3C,QAAQ,GAAG,CAAC,+BAA+B;QACzC,MAAM,gBAAgB,IAAI;QAC1B,OAAO,gBAAgB,KAAK;QAC5B,iBAAiB,gBAAgB,WAAW,CAAC,MAAM;QACnD,YAAY,gBAAgB,UAAU;QACtC,aAAa,gBAAgB,WAAW;IAC1C;IAEA,sCAAsC;IACtC,MAAM,kBAAkB;QACtB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,QAAQ,GAAG,CAAC;IACZ,gBAAgB,OAAO,CAAC,CAAA;QACtB,MAAM,YAAY,cAAc,iBAAiB;QACjD,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,WAAW,EAAE,EAAE,WAAW;IAC7C;IAEA,iDAAiD;IACjD,MAAM,iBAAiB;QACrB;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;KACD;IAED,QAAQ,GAAG,CAAC;IACZ,eAAe,OAAO,CAAC,CAAA;QACrB,MAAM,YAAY,oBAAoB,iBAAiB;QACvD,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,MAAM,EAAE,EAAE,WAAW;IACxC;IAEA,0BAA0B;IAC1B,IAAI,gBAAgB,WAAW,CAAC,MAAM,GAAG,GAAG;QAC1C,QAAQ,GAAG,CAAC;QACZ,gBAAgB,WAAW,CAAC,KAAK,CAAC,GAAG,IAAI,OAAO,CAAC,CAAC,MAAM;YACtD,QAAQ,GAAG,CAAC,CAAC,EAAE,EAAE,QAAQ,EAAE,EAAE,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,IAAI,CAAC,IAAI,EAAE,KAAK,QAAQ,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;QAC/F;IACF;IAEA,QAAQ,GAAG,CAAC;IAEZ,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/contexts/PermissionContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState } from 'react'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport {\n  getUserPermissions,\n  hasPermission,\n  canAccessNavigation,\n  canPerformAction,\n  getAllowedActions,\n  filterNavigationByPermissions,\n  debugSuperAdminPermissions,\n  UserPermissions,\n  Permission\n} from '@/utils/permissions'\n\ninterface PermissionContextType {\n  userPermissions: UserPermissions\n  hasPermission: (permission: string, resource?: string, scope?: 'global' | 'institute' | 'department' | 'own') => boolean\n  canAccessNavigation: (navigationId: string) => boolean\n  canPerformAction: (action: 'create' | 'read' | 'update' | 'delete', resource: string, scope?: 'global' | 'institute' | 'department' | 'own') => boolean\n  getAllowedActions: (resource: string) => string[]\n  filterNavigationByPermissions: (navigationItems: any[]) => any[]\n  isLoading: boolean\n  refreshPermissions: () => Promise<void>\n}\n\nconst PermissionContext = createContext<PermissionContextType | undefined>(undefined)\n\ninterface PermissionProviderProps {\n  children: React.ReactNode\n}\n\nexport function PermissionProvider({ children }: PermissionProviderProps) {\n  const { user, isAuthenticated } = useAuthStore()\n  const [userPermissions, setUserPermissions] = useState<UserPermissions>({\n    role: 'guest',\n    level: 5,\n    permissions: [],\n    department: undefined,\n    instituteId: undefined\n  })\n  const [isLoading, setIsLoading] = useState(true)\n\n  // Update permissions when user changes\n  useEffect(() => {\n    if (isAuthenticated && user) {\n      console.log('🔄 Processing user permissions...')\n      console.log('User object:', user)\n      console.log('User role:', user.role)\n      console.log('User legacyRole:', user.legacyRole)\n\n      // Extract permissions from the new structure\n      const permissions = getUserPermissions(user)\n\n      // Store user data with permissions in localStorage for persistence\n      try {\n        localStorage.setItem('user_data', JSON.stringify(user))\n        localStorage.setItem('user_permissions', JSON.stringify(permissions))\n        console.log('✅ User data and permissions stored in localStorage')\n      } catch (error) {\n        console.error('❌ Failed to store user data in localStorage:', error)\n      }\n\n      // Debug super admin permissions\n      if (user.legacyRole === 'super_admin') {\n        console.log('🔍 Super admin detected, running permission debug...')\n        debugSuperAdminPermissions(user)\n      }\n\n      setUserPermissions(permissions)\n      setIsLoading(false)\n\n      console.log(`✅ Processed ${permissions.permissions.length} permissions for ${permissions.role}`)\n    } else {\n      // Clear stored data when not authenticated\n      localStorage.removeItem('user_data')\n      localStorage.removeItem('user_permissions')\n\n      setUserPermissions({\n        role: 'guest',\n        level: 5,\n        permissions: [],\n        department: undefined,\n        instituteId: undefined\n      })\n      setIsLoading(false)\n      console.log('🔄 Cleared user permissions (not authenticated)')\n    }\n  }, [user, isAuthenticated])\n\n  // Initialize permissions on mount and try to restore from localStorage\n  useEffect(() => {\n    // Try to restore user data from localStorage if no current user\n    if (!isAuthenticated && !user) {\n      try {\n        const storedUserData = localStorage.getItem('user_data')\n        const storedPermissions = localStorage.getItem('user_permissions')\n\n        if (storedUserData && storedPermissions) {\n          console.log('🔄 Attempting to restore user data from localStorage')\n          const userData = JSON.parse(storedUserData)\n          const permissionsData = JSON.parse(storedPermissions)\n\n          console.log('📦 Restored user data from localStorage:', {\n            email: userData.email,\n            role: userData.legacyRole,\n            permissionCount: permissionsData.permissions.length\n          })\n\n          setUserPermissions(permissionsData)\n          setIsLoading(false)\n        } else {\n          setIsLoading(false)\n        }\n      } catch (error) {\n        console.error('❌ Failed to restore user data from localStorage:', error)\n        localStorage.removeItem('user_data')\n        localStorage.removeItem('user_permissions')\n        setIsLoading(false)\n      }\n    }\n  }, [isAuthenticated, user])\n\n  // Refresh permissions from server\n  const refreshPermissions = async () => {\n    if (!user) return\n\n    setIsLoading(true)\n    try {\n      // Fetch updated user data with permissions\n      const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/auth/verify`, {\n        method: 'POST',\n        headers: {\n          'Authorization': `Bearer ${localStorage.getItem('auth_token')}`,\n          'Content-Type': 'application/json'\n        },\n        credentials: 'include'\n      })\n\n      const data = await response.json()\n\n      if (data.success && data.user) {\n        const permissions = getUserPermissions(data.user)\n        setUserPermissions(permissions)\n      }\n    } catch (error) {\n      console.error('Error refreshing permissions:', error)\n    } finally {\n      setIsLoading(false)\n    }\n  }\n\n  // Permission checking functions\n  const checkPermission = (\n    permission: string, \n    resource?: string, \n    scope?: 'global' | 'institute' | 'department' | 'own'\n  ) => {\n    return hasPermission(userPermissions, permission, resource, scope)\n  }\n\n  const checkNavigationAccess = (navigationId: string) => {\n    return canAccessNavigation(userPermissions, navigationId)\n  }\n\n  const checkActionPermission = (\n    action: 'create' | 'read' | 'update' | 'delete',\n    resource: string,\n    scope?: 'global' | 'institute' | 'department' | 'own'\n  ) => {\n    return canPerformAction(userPermissions, action, resource, scope)\n  }\n\n  const getResourceActions = (resource: string) => {\n    return getAllowedActions(userPermissions, resource)\n  }\n\n  const filterNavigation = (navigationItems: any[]) => {\n    return filterNavigationByPermissions(navigationItems, userPermissions)\n  }\n\n  const contextValue: PermissionContextType = {\n    userPermissions,\n    hasPermission: checkPermission,\n    canAccessNavigation: checkNavigationAccess,\n    canPerformAction: checkActionPermission,\n    getAllowedActions: getResourceActions,\n    filterNavigationByPermissions: filterNavigation,\n    isLoading,\n    refreshPermissions\n  }\n\n  return (\n    <PermissionContext.Provider value={contextValue}>\n      {children}\n    </PermissionContext.Provider>\n  )\n}\n\n// Hook to use permission context\nexport function usePermissions() {\n  const context = useContext(PermissionContext)\n  if (context === undefined) {\n    throw new Error('usePermissions must be used within a PermissionProvider')\n  }\n  return context\n}\n\n// Higher-order component for permission-based rendering\ninterface WithPermissionProps {\n  permission: string\n  resource?: string\n  scope?: 'global' | 'institute' | 'department' | 'own'\n  fallback?: React.ReactNode\n  children: React.ReactNode\n}\n\nexport function WithPermission({ \n  permission, \n  resource, \n  scope, \n  fallback = null, \n  children \n}: WithPermissionProps) {\n  const { hasPermission } = usePermissions()\n  \n  if (hasPermission(permission, resource, scope)) {\n    return <>{children}</>\n  }\n  \n  return <>{fallback}</>\n}\n\n// Component for action-based permission checking\ninterface WithActionPermissionProps {\n  action: 'create' | 'read' | 'update' | 'delete'\n  resource: string\n  scope?: 'global' | 'institute' | 'department' | 'own'\n  fallback?: React.ReactNode\n  children: React.ReactNode\n}\n\nexport function WithActionPermission({ \n  action, \n  resource, \n  scope, \n  fallback = null, \n  children \n}: WithActionPermissionProps) {\n  const { canPerformAction } = usePermissions()\n  \n  if (canPerformAction(action, resource, scope)) {\n    return <>{children}</>\n  }\n  \n  return <>{fallback}</>\n}\n\n// Component for navigation access checking\ninterface WithNavigationAccessProps {\n  navigationId: string\n  fallback?: React.ReactNode\n  children: React.ReactNode\n}\n\nexport function WithNavigationAccess({ \n  navigationId, \n  fallback = null, \n  children \n}: WithNavigationAccessProps) {\n  const { canAccessNavigation } = usePermissions()\n  \n  if (canAccessNavigation(navigationId)) {\n    return <>{children}</>\n  }\n  \n  return <>{fallback}</>\n}\n\n// Hook for checking multiple permissions\nexport function useMultiplePermissions(permissions: Array<{\n  permission: string\n  resource?: string\n  scope?: 'global' | 'institute' | 'department' | 'own'\n}>) {\n  const { hasPermission } = usePermissions()\n  \n  return permissions.map(({ permission, resource, scope }) => ({\n    permission,\n    resource,\n    scope,\n    hasAccess: hasPermission(permission, resource, scope)\n  }))\n}\n\n// Hook for checking role-based access\nexport function useRoleAccess() {\n  const { userPermissions } = usePermissions()\n\n  return {\n    isSuperAdmin: userPermissions.role === 'super_admin',\n    isInstituteAdmin: userPermissions.role === 'institute_admin',\n    isStudent: userPermissions.role === 'student',\n    role: userPermissions.role,\n    level: userPermissions.level,\n    department: userPermissions.department,\n    instituteId: userPermissions.instituteId,\n    // Helper function to check if user has admin privileges\n    hasAdminAccess: () => userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff',\n    // Helper function to check if user can manage roles/permissions\n    canManageRoles: () => userPermissions.role === 'super_admin'\n  }\n}\n\nexport default PermissionProvider\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AA2BA,MAAM,kCAAoB,CAAA,GAAA,0SAAA,CAAA,gBAAa,AAAD,EAAqC;AAMpE,SAAS,mBAAmB,EAAE,QAAQ,EAA2B;IACtE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAmB;QACtE,MAAM;QACN,OAAO;QACP,aAAa,EAAE;QACf,YAAY;QACZ,aAAa;IACf;IACA,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,uCAAuC;IACvC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,mBAAmB,MAAM;YAC3B,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,gBAAgB;YAC5B,QAAQ,GAAG,CAAC,cAAc,KAAK,IAAI;YACnC,QAAQ,GAAG,CAAC,oBAAoB,KAAK,UAAU;YAE/C,6CAA6C;YAC7C,MAAM,cAAc,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE;YAEvC,mEAAmE;YACnE,IAAI;gBACF,aAAa,OAAO,CAAC,aAAa,KAAK,SAAS,CAAC;gBACjD,aAAa,OAAO,CAAC,oBAAoB,KAAK,SAAS,CAAC;gBACxD,QAAQ,GAAG,CAAC;YACd,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,gDAAgD;YAChE;YAEA,gCAAgC;YAChC,IAAI,KAAK,UAAU,KAAK,eAAe;gBACrC,QAAQ,GAAG,CAAC;gBACZ,CAAA,GAAA,gJAAA,CAAA,6BAA0B,AAAD,EAAE;YAC7B;YAEA,mBAAmB;YACnB,aAAa;YAEb,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,YAAY,WAAW,CAAC,MAAM,CAAC,iBAAiB,EAAE,YAAY,IAAI,EAAE;QACjG,OAAO;YACL,2CAA2C;YAC3C,aAAa,UAAU,CAAC;YACxB,aAAa,UAAU,CAAC;YAExB,mBAAmB;gBACjB,MAAM;gBACN,OAAO;gBACP,aAAa,EAAE;gBACf,YAAY;gBACZ,aAAa;YACf;YACA,aAAa;YACb,QAAQ,GAAG,CAAC;QACd;IACF,GAAG;QAAC;QAAM;KAAgB;IAE1B,uEAAuE;IACvE,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,gEAAgE;QAChE,IAAI,CAAC,mBAAmB,CAAC,MAAM;YAC7B,IAAI;gBACF,MAAM,iBAAiB,aAAa,OAAO,CAAC;gBAC5C,MAAM,oBAAoB,aAAa,OAAO,CAAC;gBAE/C,IAAI,kBAAkB,mBAAmB;oBACvC,QAAQ,GAAG,CAAC;oBACZ,MAAM,WAAW,KAAK,KAAK,CAAC;oBAC5B,MAAM,kBAAkB,KAAK,KAAK,CAAC;oBAEnC,QAAQ,GAAG,CAAC,4CAA4C;wBACtD,OAAO,SAAS,KAAK;wBACrB,MAAM,SAAS,UAAU;wBACzB,iBAAiB,gBAAgB,WAAW,CAAC,MAAM;oBACrD;oBAEA,mBAAmB;oBACnB,aAAa;gBACf,OAAO;oBACL,aAAa;gBACf;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,oDAAoD;gBAClE,aAAa,UAAU,CAAC;gBACxB,aAAa,UAAU,CAAC;gBACxB,aAAa;YACf;QACF;IACF,GAAG;QAAC;QAAiB;KAAK;IAE1B,kCAAkC;IAClC,MAAM,qBAAqB;QACzB,IAAI,CAAC,MAAM;QAEX,aAAa;QACb,IAAI;YACF,2CAA2C;YAC3C,MAAM,WAAW,MAAM,MAAM,GAAG,6DAAmC,wBAAwB,gBAAgB,CAAC,EAAE;gBAC5G,QAAQ;gBACR,SAAS;oBACP,iBAAiB,CAAC,OAAO,EAAE,aAAa,OAAO,CAAC,eAAe;oBAC/D,gBAAgB;gBAClB;gBACA,aAAa;YACf;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,EAAE;gBAC7B,MAAM,cAAc,CAAA,GAAA,gJAAA,CAAA,qBAAkB,AAAD,EAAE,KAAK,IAAI;gBAChD,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,aAAa;QACf;IACF;IAEA,gCAAgC;IAChC,MAAM,kBAAkB,CACtB,YACA,UACA;QAEA,OAAO,CAAA,GAAA,gJAAA,CAAA,gBAAa,AAAD,EAAE,iBAAiB,YAAY,UAAU;IAC9D;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,CAAA,GAAA,gJAAA,CAAA,sBAAmB,AAAD,EAAE,iBAAiB;IAC9C;IAEA,MAAM,wBAAwB,CAC5B,QACA,UACA;QAEA,OAAO,CAAA,GAAA,gJAAA,CAAA,mBAAgB,AAAD,EAAE,iBAAiB,QAAQ,UAAU;IAC7D;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAO,CAAA,GAAA,gJAAA,CAAA,oBAAiB,AAAD,EAAE,iBAAiB;IAC5C;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,CAAA,GAAA,gJAAA,CAAA,gCAA6B,AAAD,EAAE,iBAAiB;IACxD;IAEA,MAAM,eAAsC;QAC1C;QACA,eAAe;QACf,qBAAqB;QACrB,kBAAkB;QAClB,mBAAmB;QACnB,+BAA+B;QAC/B;QACA;IACF;IAEA,qBACE,mVAAC,kBAAkB,QAAQ;QAAC,OAAO;kBAChC;;;;;;AAGP;AAGO,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAWO,SAAS,eAAe,EAC7B,UAAU,EACV,QAAQ,EACR,KAAK,EACL,WAAW,IAAI,EACf,QAAQ,EACY;IACpB,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,IAAI,cAAc,YAAY,UAAU,QAAQ;QAC9C,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;AAWO,SAAS,qBAAqB,EACnC,MAAM,EACN,QAAQ,EACR,KAAK,EACL,WAAW,IAAI,EACf,QAAQ,EACkB;IAC1B,MAAM,EAAE,gBAAgB,EAAE,GAAG;IAE7B,IAAI,iBAAiB,QAAQ,UAAU,QAAQ;QAC7C,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;AASO,SAAS,qBAAqB,EACnC,YAAY,EACZ,WAAW,IAAI,EACf,QAAQ,EACkB;IAC1B,MAAM,EAAE,mBAAmB,EAAE,GAAG;IAEhC,IAAI,oBAAoB,eAAe;QACrC,qBAAO;sBAAG;;IACZ;IAEA,qBAAO;kBAAG;;AACZ;AAGO,SAAS,uBAAuB,WAIrC;IACA,MAAM,EAAE,aAAa,EAAE,GAAG;IAE1B,OAAO,YAAY,GAAG,CAAC,CAAC,EAAE,UAAU,EAAE,QAAQ,EAAE,KAAK,EAAE,GAAK,CAAC;YAC3D;YACA;YACA;YACA,WAAW,cAAc,YAAY,UAAU;QACjD,CAAC;AACH;AAGO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,GAAG;IAE5B,OAAO;QACL,cAAc,gBAAgB,IAAI,KAAK;QACvC,kBAAkB,gBAAgB,IAAI,KAAK;QAC3C,WAAW,gBAAgB,IAAI,KAAK;QACpC,MAAM,gBAAgB,IAAI;QAC1B,OAAO,gBAAgB,KAAK;QAC5B,YAAY,gBAAgB,UAAU;QACtC,aAAa,gBAAgB,WAAW;QACxC,wDAAwD;QACxD,gBAAgB,IAAM,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK;QACzF,gEAAgE;QAChE,gBAAgB,IAAM,gBAAgB,IAAI,KAAK;IACjD;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 1663, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/hooks/usePermissionAwareNavigation.ts"], "sourcesContent": ["import { useMemo } from 'react'\nimport { usePermissions } from '@/contexts/PermissionContext'\nimport { useSidebarStore, NavigationItem } from '@/stores/sidebar/useSidebarStore'\n\n/**\n * Hook to filter navigation items based on user permissions\n */\nexport function usePermissionAwareNavigation() {\n  const { filterNavigationByPermissions, userPermissions } = usePermissions()\n  const { navigationItems } = useSidebarStore()\n\n  // Filter navigation items based on permissions with new structure\n  const filteredNavigationItems = useMemo(() => {\n    // Prevent infinite loops by checking if navigationItems is valid\n    if (!Array.isArray(navigationItems) || navigationItems.length === 0) {\n      console.log('🔍 usePermissionAwareNavigation - No navigation items available')\n      return []\n    }\n\n    console.log('🔍 usePermissionAwareNavigation - Filtering navigation items', {\n      userRole: userPermissions.role,\n      permissionCount: userPermissions.permissions.length,\n      isSuperAdmin: userPermissions.role === 'super_admin',\n      totalNavigationItems: navigationItems.length\n    })\n\n    // For super admin, return all items without filtering to prevent loops\n    if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {\n      console.log('✅ Super admin detected - returning all navigation items')\n      return navigationItems\n    }\n\n    const filtered = filterNavigationByPermissions(navigationItems)\n    console.log(`✅ Navigation filtering complete: ${filtered.length}/${navigationItems.length} items accessible`)\n    return filtered\n  }, [navigationItems, filterNavigationByPermissions, userPermissions.role, userPermissions.permissions.length])\n\n  // Get navigation items with permission status\n  const navigationWithPermissions = useMemo(() => {\n    if (!Array.isArray(navigationItems) || navigationItems.length === 0) {\n      return []\n    }\n\n    return navigationItems.map(item => {\n      if (!item || !item.href) {\n        return { ...item, hasAccess: false, navigationId: '' }\n      }\n\n      const navigationId = item.href.replace('/', '').replace(/\\//g, '-')\n      // For super admin, always has access\n      const hasAccess = userPermissions.role === 'super_admin' || filterNavigationByPermissions([item]).length > 0\n\n      return {\n        ...item,\n        hasAccess,\n        navigationId\n      }\n    })\n  }, [navigationItems, filterNavigationByPermissions, userPermissions.role])\n\n  // Get accessible navigation count\n  const accessibleItemsCount = filteredNavigationItems.length\n  const totalItemsCount = navigationItems.length\n  const restrictedItemsCount = totalItemsCount - accessibleItemsCount\n\n  // Check if specific navigation item is accessible\n  const isNavigationAccessible = (href: string): boolean => {\n    const navigationId = href.replace('/', '').replace(/\\//g, '-')\n    return filteredNavigationItems.some(item => item.href === href)\n  }\n\n  // Get navigation items by category/section\n  const getNavigationBySection = (sectionName: string): NavigationItem[] => {\n    return filteredNavigationItems.filter(item => \n      item.href.includes(sectionName.toLowerCase())\n    )\n  }\n\n  // Get restricted navigation items (for debugging/admin purposes)\n  const getRestrictedNavigation = (): NavigationItem[] => {\n    return navigationItems.filter(item => \n      !filteredNavigationItems.some(filtered => filtered.id === item.id)\n    )\n  }\n\n  return {\n    // Filtered navigation\n    navigationItems: filteredNavigationItems,\n    navigationWithPermissions,\n    \n    // Statistics\n    accessibleItemsCount,\n    totalItemsCount,\n    restrictedItemsCount,\n    \n    // Utility functions\n    isNavigationAccessible,\n    getNavigationBySection,\n    getRestrictedNavigation,\n    \n    // User context\n    userPermissions\n  }\n}\n\n/**\n * Hook to check navigation permissions for specific routes\n */\nexport function useNavigationPermissions(routes: string[]) {\n  const { canAccessNavigation } = usePermissions()\n\n  const routePermissions = useMemo(() => {\n    return routes.reduce((acc, route) => {\n      const navigationId = route.replace('/', '').replace(/\\//g, '-')\n      acc[route] = canAccessNavigation(navigationId)\n      return acc\n    }, {} as Record<string, boolean>)\n  }, [routes, canAccessNavigation])\n\n  const accessibleRoutes = routes.filter(route => routePermissions[route])\n  const restrictedRoutes = routes.filter(route => !routePermissions[route])\n\n  return {\n    routePermissions,\n    accessibleRoutes,\n    restrictedRoutes,\n    hasAccessToAny: accessibleRoutes.length > 0,\n    hasAccessToAll: restrictedRoutes.length === 0\n  }\n}\n\n/**\n * Hook for dynamic navigation based on user role and permissions\n */\nexport function useDynamicNavigation() {\n  const { userPermissions } = usePermissions()\n  const { navigationItems, setNavigationItems } = useSidebarStore()\n\n  // Generate role-specific navigation\n  const generateRoleNavigation = useMemo(() => {\n    const baseNavigation: NavigationItem[] = []\n\n    // Add common dashboard\n    baseNavigation.push({\n      id: 'dashboard',\n      label: 'Dashboard',\n      icon: 'LayoutDashboard',\n      href: `/${userPermissions.role.replace('_', '-')}`,\n      description: 'Overview and analytics'\n    })\n\n    // Add role-specific navigation\n    switch (userPermissions.role) {\n      case 'super_admin':\n        baseNavigation.push(\n          {\n            id: 'institutes',\n            label: 'Institute Management',\n            icon: 'Building2',\n            href: '/super-admin/institutes',\n            description: 'Manage institutes and verification'\n          },\n          {\n            id: 'users',\n            label: 'User Management',\n            icon: 'Users',\n            href: '/super-admin/users',\n            description: 'Manage all platform users'\n          },\n          // {\n          //   id: 'staff',\n          //   label: 'Staff Management',\n          //   icon: 'UserCheck',\n          //   href: '/super-admin/staff',\n          //   description: 'Manage platform staff and roles'\n          // }\n        )\n        break\n\n      case 'institute_admin':\n        baseNavigation.push(\n          {\n            id: 'courses',\n            label: 'Course Management',\n            icon: 'BookOpen',\n            href: '/institute-admin/courses',\n            description: 'Manage courses and curriculum'\n          },\n          {\n            id: 'students',\n            label: 'Student Management',\n            icon: 'GraduationCap',\n            href: '/institute-admin/students',\n            description: 'Manage student enrollments'\n          }\n        )\n        break\n\n      case 'student':\n        baseNavigation.push(\n          {\n            id: 'my-courses',\n            label: 'My Courses',\n            icon: 'BookOpen',\n            href: '/student/courses',\n            description: 'Your enrolled courses'\n          },\n          {\n            id: 'marketplace',\n            label: 'Course Marketplace',\n            icon: 'ShoppingCart',\n            href: '/student/marketplace',\n            description: 'Browse and purchase courses'\n          }\n        )\n        break\n    }\n\n    return baseNavigation\n  }, [userPermissions.role])\n\n  // Update navigation when role changes\n  const updateNavigationForRole = () => {\n    setNavigationItems(generateRoleNavigation)\n  }\n\n  return {\n    generateRoleNavigation,\n    updateNavigationForRole,\n    currentRole: userPermissions.role,\n    currentLevel: userPermissions.level\n  }\n}\n\n/**\n * Hook for permission-based feature flags\n */\nexport function useFeaturePermissions() {\n  const { hasPermission, canPerformAction, userPermissions } = usePermissions()\n\n  const features = useMemo(() => {\n    return {\n      // Staff Management Features - REMOVED\n      // canCreateStaff: canPerformAction('create', 'staff'),\n      // canEditStaff: canPerformAction('update', 'staff'),\n      // canDeleteStaff: canPerformAction('delete', 'staff'),\n      // canViewStaff: canPerformAction('read', 'staff'),\n      \n      // Institute Management Features\n      canManageInstitutes: hasPermission('manage_institutes'),\n      canApproveInstitutes: hasPermission('approve_institutes'),\n      \n      // Billing Features\n      canViewBilling: hasPermission('view_billing'),\n      canManageBilling: hasPermission('manage_billing'),\n      \n      // Analytics Features\n      canViewAnalytics: hasPermission('view_analytics'),\n      canExportReports: hasPermission('export_reports'),\n      \n      // System Features\n      canManageSettings: hasPermission('manage_system_settings'),\n      canViewLogs: hasPermission('view_system_logs'),\n      \n      // Theme Features\n      canManageThemes: hasPermission('manage_themes'),\n      canCustomizeThemes: hasPermission('customize_themes'),\n      \n      // Course Features\n      canCreateCourses: canPerformAction('create', 'courses'),\n      canEditCourses: canPerformAction('update', 'courses'),\n      canDeleteCourses: canPerformAction('delete', 'courses'),\n      \n      // Student Features\n      canEnrollStudents: hasPermission('enroll_students'),\n      canViewStudentProgress: hasPermission('view_student_progress'),\n      \n      // Advanced Features\n      canAccessAdvancedFeatures: userPermissions.level <= 2,\n      canManagePermissions: userPermissions.level <= 1,\n      canViewSystemHealth: userPermissions.level <= 1\n    }\n  }, [hasPermission, canPerformAction, userPermissions])\n\n  return features\n}\n\nexport default usePermissionAwareNavigation\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;;;;AAKO,SAAS;IACd,MAAM,EAAE,6BAA6B,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAE1C,kEAAkE;IAClE,MAAM,0BAA0B,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD,EAAE;QACtC,iEAAiE;QACjE,IAAI,CAAC,MAAM,OAAO,CAAC,oBAAoB,gBAAgB,MAAM,KAAK,GAAG;YACnE,QAAQ,GAAG,CAAC;YACZ,OAAO,EAAE;QACX;QAEA,QAAQ,GAAG,CAAC,gEAAgE;YAC1E,UAAU,gBAAgB,IAAI;YAC9B,iBAAiB,gBAAgB,WAAW,CAAC,MAAM;YACnD,cAAc,gBAAgB,IAAI,KAAK;YACvC,sBAAsB,gBAAgB,MAAM;QAC9C;QAEA,uEAAuE;QACvE,IAAI,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK,kBAAkB;YACvF,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT;QAEA,MAAM,WAAW,8BAA8B;QAC/C,QAAQ,GAAG,CAAC,CAAC,iCAAiC,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,gBAAgB,MAAM,CAAC,iBAAiB,CAAC;QAC5G,OAAO;IACT,GAAG;QAAC;QAAiB;QAA+B,gBAAgB,IAAI;QAAE,gBAAgB,WAAW,CAAC,MAAM;KAAC;IAE7G,8CAA8C;IAC9C,MAAM,4BAA4B,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD,EAAE;QACxC,IAAI,CAAC,MAAM,OAAO,CAAC,oBAAoB,gBAAgB,MAAM,KAAK,GAAG;YACnE,OAAO,EAAE;QACX;QAEA,OAAO,gBAAgB,GAAG,CAAC,CAAA;YACzB,IAAI,CAAC,QAAQ,CAAC,KAAK,IAAI,EAAE;gBACvB,OAAO;oBAAE,GAAG,IAAI;oBAAE,WAAW;oBAAO,cAAc;gBAAG;YACvD;YAEA,MAAM,eAAe,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,OAAO;YAC/D,qCAAqC;YACrC,MAAM,YAAY,gBAAgB,IAAI,KAAK,iBAAiB,8BAA8B;gBAAC;aAAK,EAAE,MAAM,GAAG;YAE3G,OAAO;gBACL,GAAG,IAAI;gBACP;gBACA;YACF;QACF;IACF,GAAG;QAAC;QAAiB;QAA+B,gBAAgB,IAAI;KAAC;IAEzE,kCAAkC;IAClC,MAAM,uBAAuB,wBAAwB,MAAM;IAC3D,MAAM,kBAAkB,gBAAgB,MAAM;IAC9C,MAAM,uBAAuB,kBAAkB;IAE/C,kDAAkD;IAClD,MAAM,yBAAyB,CAAC;QAC9B,MAAM,eAAe,KAAK,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,OAAO;QAC1D,OAAO,wBAAwB,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;IAC5D;IAEA,2CAA2C;IAC3C,MAAM,yBAAyB,CAAC;QAC9B,OAAO,wBAAwB,MAAM,CAAC,CAAA,OACpC,KAAK,IAAI,CAAC,QAAQ,CAAC,YAAY,WAAW;IAE9C;IAEA,iEAAiE;IACjE,MAAM,0BAA0B;QAC9B,OAAO,gBAAgB,MAAM,CAAC,CAAA,OAC5B,CAAC,wBAAwB,IAAI,CAAC,CAAA,WAAY,SAAS,EAAE,KAAK,KAAK,EAAE;IAErE;IAEA,OAAO;QACL,sBAAsB;QACtB,iBAAiB;QACjB;QAEA,aAAa;QACb;QACA;QACA;QAEA,oBAAoB;QACpB;QACA;QACA;QAEA,eAAe;QACf;IACF;AACF;AAKO,SAAS,yBAAyB,MAAgB;IACvD,MAAM,EAAE,mBAAmB,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;IAE7C,MAAM,mBAAmB,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD,EAAE;QAC/B,OAAO,OAAO,MAAM,CAAC,CAAC,KAAK;YACzB,MAAM,eAAe,MAAM,OAAO,CAAC,KAAK,IAAI,OAAO,CAAC,OAAO;YAC3D,GAAG,CAAC,MAAM,GAAG,oBAAoB;YACjC,OAAO;QACT,GAAG,CAAC;IACN,GAAG;QAAC;QAAQ;KAAoB;IAEhC,MAAM,mBAAmB,OAAO,MAAM,CAAC,CAAA,QAAS,gBAAgB,CAAC,MAAM;IACvE,MAAM,mBAAmB,OAAO,MAAM,CAAC,CAAA,QAAS,CAAC,gBAAgB,CAAC,MAAM;IAExE,OAAO;QACL;QACA;QACA;QACA,gBAAgB,iBAAiB,MAAM,GAAG;QAC1C,gBAAgB,iBAAiB,MAAM,KAAK;IAC9C;AACF;AAKO,SAAS;IACd,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;IACzC,MAAM,EAAE,eAAe,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAE9D,oCAAoC;IACpC,MAAM,yBAAyB,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD,EAAE;QACrC,MAAM,iBAAmC,EAAE;QAE3C,uBAAuB;QACvB,eAAe,IAAI,CAAC;YAClB,IAAI;YACJ,OAAO;YACP,MAAM;YACN,MAAM,CAAC,CAAC,EAAE,gBAAgB,IAAI,CAAC,OAAO,CAAC,KAAK,MAAM;YAClD,aAAa;QACf;QAEA,+BAA+B;QAC/B,OAAQ,gBAAgB,IAAI;YAC1B,KAAK;gBACH,eAAe,IAAI,CACjB;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf,GACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBASF;YAEF,KAAK;gBACH,eAAe,IAAI,CACjB;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf,GACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBAEF;YAEF,KAAK;gBACH,eAAe,IAAI,CACjB;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf,GACA;oBACE,IAAI;oBACJ,OAAO;oBACP,MAAM;oBACN,MAAM;oBACN,aAAa;gBACf;gBAEF;QACJ;QAEA,OAAO;IACT,GAAG;QAAC,gBAAgB,IAAI;KAAC;IAEzB,sCAAsC;IACtC,MAAM,0BAA0B;QAC9B,mBAAmB;IACrB;IAEA,OAAO;QACL;QACA;QACA,aAAa,gBAAgB,IAAI;QACjC,cAAc,gBAAgB,KAAK;IACrC;AACF;AAKO,SAAS;IACd,MAAM,EAAE,aAAa,EAAE,gBAAgB,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;IAE1E,MAAM,WAAW,CAAA,GAAA,0SAAA,CAAA,UAAO,AAAD,EAAE;QACvB,OAAO;YACL,sCAAsC;YACtC,uDAAuD;YACvD,qDAAqD;YACrD,uDAAuD;YACvD,mDAAmD;YAEnD,gCAAgC;YAChC,qBAAqB,cAAc;YACnC,sBAAsB,cAAc;YAEpC,mBAAmB;YACnB,gBAAgB,cAAc;YAC9B,kBAAkB,cAAc;YAEhC,qBAAqB;YACrB,kBAAkB,cAAc;YAChC,kBAAkB,cAAc;YAEhC,kBAAkB;YAClB,mBAAmB,cAAc;YACjC,aAAa,cAAc;YAE3B,iBAAiB;YACjB,iBAAiB,cAAc;YAC/B,oBAAoB,cAAc;YAElC,kBAAkB;YAClB,kBAAkB,iBAAiB,UAAU;YAC7C,gBAAgB,iBAAiB,UAAU;YAC3C,kBAAkB,iBAAiB,UAAU;YAE7C,mBAAmB;YACnB,mBAAmB,cAAc;YACjC,wBAAwB,cAAc;YAEtC,oBAAoB;YACpB,2BAA2B,gBAAgB,KAAK,IAAI;YACpD,sBAAsB,gBAAgB,KAAK,IAAI;YAC/C,qBAAqB,gBAAgB,KAAK,IAAI;QAChD;IACF,GAAG;QAAC;QAAe;QAAkB;KAAgB;IAErD,OAAO;AACT;uCAEe", "debugId": null}}, {"offset": {"line": 1917, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/SidebarItem.tsx"], "sourcesContent": ["'use client'\n\nimport Link from 'next/link'\nimport { useState } from 'react'\nimport { useSidebarStore, NavigationItem } from '@/stores/sidebar/useSidebarStore'\nimport { \n  Star,\n  StarOff,\n  ChevronRight,\n  ChevronDown\n} from 'lucide-react'\nimport { LucideIcon } from 'lucide-react'\nimport * as Icons from 'lucide-react'\n\ninterface SidebarItemProps {\n  item: NavigationItem\n  isActive: boolean\n  isCollapsed: boolean\n  onClick: () => void\n  variant?: 'default' | 'compact'\n}\n\nexport function SidebarItem({ \n  item, \n  isActive, \n  isCollapsed, \n  onClick, \n  variant = 'default' \n}: SidebarItemProps) {\n  const { \n    favoriteItems, \n    addToFavorites, \n    removeFromFavorites \n  } = useSidebarStore()\n  \n  const [isExpanded, setIsExpanded] = useState(false)\n  const isFavorite = favoriteItems.includes(item.id)\n  const hasChildren = item.children && item.children.length > 0\n\n  // Get the icon component\n  const IconComponent = (Icons as any)[item.icon] as LucideIcon\n  \n  const handleFavoriteToggle = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    \n    if (isFavorite) {\n      removeFromFavorites(item.id)\n    } else {\n      addToFavorites(item.id)\n    }\n  }\n\n  const handleExpandToggle = (e: React.MouseEvent) => {\n    e.preventDefault()\n    e.stopPropagation()\n    setIsExpanded(!isExpanded)\n  }\n\n  const handleItemClick = () => {\n    onClick()\n    if (hasChildren) {\n      setIsExpanded(!isExpanded)\n    }\n  }\n\n  const baseClasses = `\n    group relative flex items-center w-full text-left transition-all duration-200\n    ${variant === 'compact' ? 'px-3 py-1.5' : 'px-3 py-2'}\n    ${isActive \n      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' \n      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'\n    }\n    rounded-lg mx-1\n  `\n\n  const content = (\n    <>\n      {/* Icon */}\n      <div className={`flex-shrink-0 ${isCollapsed ? 'mx-auto' : 'mr-3'}`}>\n        {IconComponent && (\n          <IconComponent \n            className={`w-5 h-5 ${\n              isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'\n            }`} \n          />\n        )}\n      </div>\n\n      {/* Label and Badge */}\n      {!isCollapsed && (\n        <>\n          <div className=\"flex-1 min-w-0\">\n            <div className=\"flex items-center justify-between\">\n              <span className={`text-sm font-medium truncate ${\n                variant === 'compact' ? 'text-xs' : ''\n              }`}>\n                {item.label}\n              </span>\n              \n              {/* Badge */}\n              {item.badge && item.badge > 0 && (\n                <span className=\"ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full\">\n                  {item.badge > 99 ? '99+' : item.badge}\n                </span>\n              )}\n            </div>\n            \n            {/* Description for non-compact variant */}\n            {variant !== 'compact' && item.description && (\n              <p className=\"text-xs text-gray-500 mt-0.5 truncate\">\n                {item.description}\n              </p>\n            )}\n          </div>\n\n          {/* Actions */}\n          <div className=\"flex items-center space-x-1 ml-2\">\n            {/* Favorite Toggle */}\n            <button\n              onClick={handleFavoriteToggle}\n              className=\"p-1 rounded opacity-0 group-hover:opacity-100 hover:bg-gray-200 transition-all\"\n              title={isFavorite ? 'Remove from favorites' : 'Add to favorites'}\n            >\n              {isFavorite ? (\n                <Star className=\"w-3 h-3 text-yellow-500 fill-current\" />\n              ) : (\n                <StarOff className=\"w-3 h-3 text-gray-400\" />\n              )}\n            </button>\n\n            {/* Expand Toggle for items with children */}\n            {hasChildren && (\n              <button\n                onClick={handleExpandToggle}\n                className=\"p-1 rounded hover:bg-gray-200 transition-colors\"\n              >\n                {isExpanded ? (\n                  <ChevronDown className=\"w-3 h-3 text-gray-400\" />\n                ) : (\n                  <ChevronRight className=\"w-3 h-3 text-gray-400\" />\n                )}\n              </button>\n            )}\n          </div>\n        </>\n      )}\n\n      {/* Tooltip for collapsed sidebar */}\n      {isCollapsed && (\n        <div className=\"absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-sm rounded opacity-0 group-hover:opacity-100 transition-opacity pointer-events-none whitespace-nowrap z-50\">\n          {item.label}\n          {item.badge && item.badge > 0 && (\n            <span className=\"ml-1 px-1.5 py-0.5 text-xs bg-red-500 rounded-full\">\n              {item.badge > 99 ? '99+' : item.badge}\n            </span>\n          )}\n        </div>\n      )}\n    </>\n  )\n\n  return (\n    <div>\n      {/* Main Item */}\n      {hasChildren ? (\n        <button\n          onClick={handleItemClick}\n          className={baseClasses}\n        >\n          {content}\n        </button>\n      ) : (\n        <Link\n          href={item.href}\n          onClick={handleItemClick}\n          className={baseClasses}\n        >\n          {content}\n        </Link>\n      )}\n\n      {/* Children Items */}\n      {hasChildren && isExpanded && !isCollapsed && (\n        <div className=\"ml-6 mt-1 space-y-1\">\n          {item.children?.map((child) => (\n            <SidebarItem\n              key={child.id}\n              item={child}\n              isActive={false} // You might want to implement nested active state\n              isCollapsed={false}\n              onClick={() => onClick()}\n              variant=\"compact\"\n            />\n          ))}\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default SidebarItem\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAOA;AAZA;;;;;;;AAsBO,SAAS,YAAY,EAC1B,IAAI,EACJ,QAAQ,EACR,WAAW,EACX,OAAO,EACP,UAAU,SAAS,EACF;IACjB,MAAM,EACJ,aAAa,EACb,cAAc,EACd,mBAAmB,EACpB,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;IACjD,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;IAE5D,yBAAyB;IACzB,MAAM,gBAAgB,AAAC,qPAAa,CAAC,KAAK,IAAI,CAAC;IAE/C,MAAM,uBAAuB,CAAC;QAC5B,EAAE,cAAc;QAChB,EAAE,eAAe;QAEjB,IAAI,YAAY;YACd,oBAAoB,KAAK,EAAE;QAC7B,OAAO;YACL,eAAe,KAAK,EAAE;QACxB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,EAAE,eAAe;QACjB,cAAc,CAAC;IACjB;IAEA,MAAM,kBAAkB;QACtB;QACA,IAAI,aAAa;YACf,cAAc,CAAC;QACjB;IACF;IAEA,MAAM,cAAc,CAAC;;IAEnB,EAAE,YAAY,YAAY,gBAAgB,YAAY;IACtD,EAAE,WACE,wDACA,qDACH;;EAEH,CAAC;IAED,MAAM,wBACJ;;0BAEE,mVAAC;gBAAI,WAAW,CAAC,cAAc,EAAE,cAAc,YAAY,QAAQ;0BAChE,+BACC,mVAAC;oBACC,WAAW,CAAC,QAAQ,EAClB,WAAW,kBAAkB,2CAC7B;;;;;;;;;;;YAMP,CAAC,6BACA;;kCACE,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAK,WAAW,CAAC,6BAA6B,EAC7C,YAAY,YAAY,YAAY,IACpC;kDACC,KAAK,KAAK;;;;;;oCAIZ,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,mVAAC;wCAAK,WAAU;kDACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;;;;;;4BAM1C,YAAY,aAAa,KAAK,WAAW,kBACxC,mVAAC;gCAAE,WAAU;0CACV,KAAK,WAAW;;;;;;;;;;;;kCAMvB,mVAAC;wBAAI,WAAU;;0CAEb,mVAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAO,aAAa,0BAA0B;0CAE7C,2BACC,mVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAEhB,mVAAC,gSAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;4BAKtB,6BACC,mVAAC;gCACC,SAAS;gCACT,WAAU;0CAET,2BACC,mVAAC,wSAAA,CAAA,cAAW;oCAAC,WAAU;;;;;yDAEvB,mVAAC,0SAAA,CAAA,eAAY;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;YASnC,6BACC,mVAAC;gBAAI,WAAU;;oBACZ,KAAK,KAAK;oBACV,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,mVAAC;wBAAK,WAAU;kCACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;;;;;;;;IAQjD,qBACE,mVAAC;;YAEE,4BACC,mVAAC;gBACC,SAAS;gBACT,WAAW;0BAEV;;;;;qCAGH,mVAAC,iQAAA,CAAA,UAAI;gBACH,MAAM,KAAK,IAAI;gBACf,SAAS;gBACT,WAAW;0BAEV;;;;;;YAKJ,eAAe,cAAc,CAAC,6BAC7B,mVAAC;gBAAI,WAAU;0BACZ,KAAK,QAAQ,EAAE,IAAI,CAAC,sBACnB,mVAAC;wBAEC,MAAM;wBACN,UAAU;wBACV,aAAa;wBACb,SAAS,IAAM;wBACf,SAAQ;uBALH,MAAM,EAAE;;;;;;;;;;;;;;;;AAY3B;uCAEe", "debugId": null}}, {"offset": {"line": 2156, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/UserProfile.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { \n  User, \n  LogOut, \n  Setting<PERSON>, \n  ChevronUp,\n  ChevronDown\n} from 'lucide-react'\n\ninterface UserProfileProps {\n  user: any // User type from auth store\n  isCollapsed: boolean\n  onLogout: () => void\n}\n\nexport function UserProfile({ user, isCollapsed, onLogout }: UserProfileProps) {\n  const [showMenu, setShowMenu] = useState(false)\n  const [showLogoutConfirm, setShowLogoutConfirm] = useState(false)\n\n  const handleLogoutClick = () => {\n    setShowLogoutConfirm(true)\n    setShowMenu(false)\n  }\n\n  const handleConfirmLogout = () => {\n    onLogout()\n    setShowLogoutConfirm(false)\n  }\n\n  const handleCancelLogout = () => {\n    setShowLogoutConfirm(false)\n  }\n\n  if (isCollapsed) {\n    return (\n      <div className=\"p-2\">\n        <div className=\"relative\">\n          <button\n            onClick={() => setShowMenu(!showMenu)}\n            className=\"w-full p-2 rounded-lg hover:bg-gray-100 transition-colors group\"\n            title={user?.personalInfo?.fullName || user?.email || 'User Menu'}\n          >\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center mx-auto\">\n              {user?.personalInfo?.avatar ? (\n                <img \n                  src={user.personalInfo.avatar} \n                  alt={user.personalInfo.fullName || user.email}\n                  className=\"w-8 h-8 rounded-full object-cover\"\n                />\n              ) : (\n                <User className=\"w-4 h-4 text-white\" />\n              )}\n            </div>\n          </button>\n\n          {/* Collapsed Menu */}\n          {showMenu && (\n            <div className=\"absolute bottom-full left-0 mb-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50\">\n              <div className=\"p-3 border-b border-gray-200\">\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n                    {user?.personalInfo?.avatar ? (\n                      <img \n                        src={user.personalInfo.avatar} \n                        alt={user.personalInfo.fullName || user.email}\n                        className=\"w-8 h-8 rounded-full object-cover\"\n                      />\n                    ) : (\n                      <User className=\"w-4 h-4 text-white\" />\n                    )}\n                  </div>\n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"text-sm font-medium text-gray-900 truncate\">\n                      {user?.personalInfo?.fullName || user?.email || 'User'}\n                    </div>\n                    <div className=\"text-xs text-gray-500 capitalize\">\n                      {user?.role?.replace('_', ' ') || 'User'}\n                    </div>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"py-1\">\n                <button\n                  onClick={() => {\n                    setShowMenu(false)\n                    // Navigate to settings\n                  }}\n                  className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n                >\n                  <Settings className=\"w-4 h-4 mr-2\" />\n                  Settings\n                </button>\n                <button\n                  onClick={handleLogoutClick}\n                  className=\"flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\"\n                >\n                  <LogOut className=\"w-4 h-4 mr-2\" />\n                  Sign Out\n                </button>\n              </div>\n            </div>\n          )}\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"p-4\">\n      {/* User Info */}\n      <div className=\"relative\">\n        <button\n          onClick={() => setShowMenu(!showMenu)}\n          className=\"w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-100 transition-colors group\"\n        >\n          <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\">\n            {user?.personalInfo?.avatar ? (\n              <img \n                src={user.personalInfo.avatar} \n                alt={user.personalInfo.fullName || user.email}\n                className=\"w-10 h-10 rounded-full object-cover\"\n              />\n            ) : (\n              <User className=\"w-5 h-5 text-white\" />\n            )}\n          </div>\n          \n          <div className=\"flex-1 min-w-0 text-left\">\n            <div className=\"text-sm font-medium text-gray-900 truncate\">\n              {user?.personalInfo?.fullName || user?.email || 'User'}\n            </div>\n            <div className=\"text-xs text-gray-500 capitalize\">\n              {user?.role?.replace('_', ' ') || 'User'}\n            </div>\n          </div>\n          \n          {showMenu ? (\n            <ChevronDown className=\"w-4 h-4 text-gray-400\" />\n          ) : (\n            <ChevronUp className=\"w-4 h-4 text-gray-400\" />\n          )}\n        </button>\n\n        {/* Expanded Menu */}\n        {showMenu && (\n          <div className=\"absolute bottom-full left-0 right-0 mb-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50\">\n            <div className=\"py-1\">\n              <button\n                onClick={() => {\n                  setShowMenu(false)\n                  // Navigate to settings\n                }}\n                className=\"flex items-center w-full px-3 py-2 text-sm text-gray-700 hover:bg-gray-100\"\n              >\n                <Settings className=\"w-4 h-4 mr-2\" />\n                Settings\n              </button>\n              <button\n                onClick={handleLogoutClick}\n                className=\"flex items-center w-full px-3 py-2 text-sm text-red-600 hover:bg-red-50\"\n              >\n                <LogOut className=\"w-4 h-4 mr-2\" />\n                Sign Out\n              </button>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Logout Confirmation Modal */}\n      {showLogoutConfirm && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white rounded-lg p-6 max-w-sm mx-4\">\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Confirm Sign Out\n            </h3>\n            <p className=\"text-sm text-gray-600 mb-4\">\n              Are you sure you want to sign out? You'll need to sign in again to access your account.\n            </p>\n            <div className=\"flex space-x-3\">\n              <button\n                onClick={handleCancelLogout}\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200 transition-colors\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={handleConfirmLogout}\n                className=\"flex-1 px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors\"\n              >\n                Sign Out\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default UserProfile\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAiBO,SAAS,YAAY,EAAE,IAAI,EAAE,WAAW,EAAE,QAAQ,EAAoB;IAC3E,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,MAAM,oBAAoB;QACxB,qBAAqB;QACrB,YAAY;IACd;IAEA,MAAM,sBAAsB;QAC1B;QACA,qBAAqB;IACvB;IAEA,MAAM,qBAAqB;QACzB,qBAAqB;IACvB;IAEA,IAAI,aAAa;QACf,qBACE,mVAAC;YAAI,WAAU;sBACb,cAAA,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBACC,SAAS,IAAM,YAAY,CAAC;wBAC5B,WAAU;wBACV,OAAO,MAAM,cAAc,YAAY,MAAM,SAAS;kCAEtD,cAAA,mVAAC;4BAAI,WAAU;sCACZ,MAAM,cAAc,uBACnB,mVAAC;gCACC,KAAK,KAAK,YAAY,CAAC,MAAM;gCAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;gCAC7C,WAAU;;;;;qDAGZ,mVAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;oBAMrB,0BACC,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;0CACb,cAAA,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;sDACZ,MAAM,cAAc,uBACnB,mVAAC;gDACC,KAAK,KAAK,YAAY,CAAC,MAAM;gDAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;gDAC7C,WAAU;;;;;qEAGZ,mVAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,mVAAC;4CAAI,WAAU;;8DACb,mVAAC;oDAAI,WAAU;8DACZ,MAAM,cAAc,YAAY,MAAM,SAAS;;;;;;8DAElD,mVAAC;oDAAI,WAAU;8DACZ,MAAM,MAAM,QAAQ,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAM1C,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCACC,SAAS;4CACP,YAAY;wCACZ,uBAAuB;wCACzB;wCACA,WAAU;;0DAEV,mVAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,mVAAC;wCACC,SAAS;wCACT,WAAU;;0DAEV,mVAAC,8RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASnD;IAEA,qBACE,mVAAC;QAAI,WAAU;;0BAEb,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBACC,SAAS,IAAM,YAAY,CAAC;wBAC5B,WAAU;;0CAEV,mVAAC;gCAAI,WAAU;0CACZ,MAAM,cAAc,uBACnB,mVAAC;oCACC,KAAK,KAAK,YAAY,CAAC,MAAM;oCAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;oCAC7C,WAAU;;;;;yDAGZ,mVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAIpB,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACZ,MAAM,cAAc,YAAY,MAAM,SAAS;;;;;;kDAElD,mVAAC;wCAAI,WAAU;kDACZ,MAAM,MAAM,QAAQ,KAAK,QAAQ;;;;;;;;;;;;4BAIrC,yBACC,mVAAC,wSAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,mVAAC,oSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;;oBAKxB,0BACC,mVAAC;wBAAI,WAAU;kCACb,cAAA,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCACC,SAAS;wCACP,YAAY;oCACZ,uBAAuB;oCACzB;oCACA,WAAU;;sDAEV,mVAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGvC,mVAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,mVAAC,8RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;;;;;;;YAS5C,mCACC,mVAAC;gBAAI,WAAU;0BACb,cAAA,mVAAC;oBAAI,WAAU;;sCACb,mVAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,mVAAC;4BAAE,WAAU;sCAA6B;;;;;;sCAG1C,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;8CAGD,mVAAC;oCACC,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;uCAEe", "debugId": null}}, {"offset": {"line": 2564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/SidebarSearch.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useRef, useEffect } from 'react'\nimport Link from 'next/link'\nimport { useSidebarStore } from '@/stores/sidebar/useSidebarStore'\nimport { Search, X } from 'lucide-react'\nimport * as Icons from 'lucide-react'\n\nexport function SidebarSearch() {\n  const {\n    searchQuery,\n    searchResults,\n    isSearching,\n    setSearchQuery,\n    performSearch,\n    clearSearch\n  } = useSidebarStore()\n  \n  const [showResults, setShowResults] = useState(false)\n  const searchRef = useRef<HTMLDivElement>(null)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  // Handle search input\n  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const query = e.target.value\n    setSearchQuery(query)\n    \n    if (query.trim()) {\n      performSearch(query)\n      setShowResults(true)\n    } else {\n      setShowResults(false)\n    }\n  }\n\n  // Handle search submit\n  const handleSearchSubmit = (e: React.FormEvent) => {\n    e.preventDefault()\n    if (searchQuery.trim()) {\n      performSearch(searchQuery)\n      setShowResults(true)\n    }\n  }\n\n  // Clear search\n  const handleClearSearch = () => {\n    clearSearch()\n    setShowResults(false)\n    inputRef.current?.focus()\n  }\n\n  // Close results when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setShowResults(false)\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [])\n\n  // Handle keyboard navigation\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (e.key === 'Escape') {\n      setShowResults(false)\n      inputRef.current?.blur()\n    }\n  }\n\n  return (\n    <div ref={searchRef} className=\"relative\">\n      <form onSubmit={handleSearchSubmit}>\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400\" />\n          <input\n            ref={inputRef}\n            type=\"text\"\n            placeholder=\"Search navigation...\"\n            value={searchQuery}\n            onChange={handleSearchChange}\n            onKeyDown={handleKeyDown}\n            onFocus={() => searchQuery && setShowResults(true)}\n            className=\"w-full pl-10 pr-8 py-2 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\n          />\n          {searchQuery && (\n            <button\n              type=\"button\"\n              onClick={handleClearSearch}\n              className=\"absolute right-2 top-1/2 transform -translate-y-1/2 p-1 hover:bg-gray-100 rounded\"\n            >\n              <X className=\"w-3 h-3 text-gray-400\" />\n            </button>\n          )}\n        </div>\n      </form>\n\n      {/* Search Results */}\n      {showResults && (\n        <div className=\"absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-64 overflow-y-auto\">\n          {isSearching ? (\n            <div className=\"p-3 text-center text-sm text-gray-500\">\n              Searching...\n            </div>\n          ) : searchResults.length > 0 ? (\n            <div className=\"py-1\">\n              {searchResults.map((item) => {\n                const IconComponent = (Icons as any)[item.icon]\n                \n                return (\n                  <Link\n                    key={item.id}\n                    href={item.href}\n                    onClick={() => {\n                      setShowResults(false)\n                      setSearchQuery('')\n                    }}\n                    className=\"flex items-center px-3 py-2 text-sm hover:bg-gray-50 transition-colors\"\n                  >\n                    <div className=\"flex-shrink-0 mr-3\">\n                      {IconComponent && (\n                        <IconComponent className=\"w-4 h-4 text-gray-400\" />\n                      )}\n                    </div>\n                    <div className=\"flex-1 min-w-0\">\n                      <div className=\"font-medium text-gray-900 truncate\">\n                        {item.label}\n                      </div>\n                      {item.description && (\n                        <div className=\"text-xs text-gray-500 truncate\">\n                          {item.description}\n                        </div>\n                      )}\n                    </div>\n                    {item.badge && item.badge > 0 && (\n                      <span className=\"ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full\">\n                        {item.badge > 99 ? '99+' : item.badge}\n                      </span>\n                    )}\n                  </Link>\n                )\n              })}\n            </div>\n          ) : searchQuery ? (\n            <div className=\"p-3 text-center text-sm text-gray-500\">\n              No results found for \"{searchQuery}\"\n            </div>\n          ) : null}\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default SidebarSearch\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AANA;;;;;;;AAQO,SAAS;IACd,MAAM,EACJ,WAAW,EACX,aAAa,EACb,WAAW,EACX,cAAc,EACd,aAAa,EACb,WAAW,EACZ,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,YAAY,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,WAAW,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,sBAAsB;IACtB,MAAM,qBAAqB,CAAC;QAC1B,MAAM,QAAQ,EAAE,MAAM,CAAC,KAAK;QAC5B,eAAe;QAEf,IAAI,MAAM,IAAI,IAAI;YAChB,cAAc;YACd,eAAe;QACjB,OAAO;YACL,eAAe;QACjB;IACF;IAEA,uBAAuB;IACvB,MAAM,qBAAqB,CAAC;QAC1B,EAAE,cAAc;QAChB,IAAI,YAAY,IAAI,IAAI;YACtB,cAAc;YACd,eAAe;QACjB;IACF;IAEA,eAAe;IACf,MAAM,oBAAoB;QACxB;QACA,eAAe;QACf,SAAS,OAAO,EAAE;IACpB;IAEA,sCAAsC;IACtC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC1E,eAAe;YACjB;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG,EAAE;IAEL,6BAA6B;IAC7B,MAAM,gBAAgB,CAAC;QACrB,IAAI,EAAE,GAAG,KAAK,UAAU;YACtB,eAAe;YACf,SAAS,OAAO,EAAE;QACpB;IACF;IAEA,qBACE,mVAAC;QAAI,KAAK;QAAW,WAAU;;0BAC7B,mVAAC;gBAAK,UAAU;0BACd,cAAA,mVAAC;oBAAI,WAAU;;sCACb,mVAAC,0RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,mVAAC;4BACC,KAAK;4BACL,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU;4BACV,WAAW;4BACX,SAAS,IAAM,eAAe,eAAe;4BAC7C,WAAU;;;;;;wBAEX,6BACC,mVAAC;4BACC,MAAK;4BACL,SAAS;4BACT,WAAU;sCAEV,cAAA,mVAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;YAOpB,6BACC,mVAAC;gBAAI,WAAU;0BACZ,4BACC,mVAAC;oBAAI,WAAU;8BAAwC;;;;;2BAGrD,cAAc,MAAM,GAAG,kBACzB,mVAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC;wBAClB,MAAM,gBAAgB,AAAC,qPAAa,CAAC,KAAK,IAAI,CAAC;wBAE/C,qBACE,mVAAC,iQAAA,CAAA,UAAI;4BAEH,MAAM,KAAK,IAAI;4BACf,SAAS;gCACP,eAAe;gCACf,eAAe;4BACjB;4BACA,WAAU;;8CAEV,mVAAC;oCAAI,WAAU;8CACZ,+BACC,mVAAC;wCAAc,WAAU;;;;;;;;;;;8CAG7B,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;sDACZ,KAAK,KAAK;;;;;;wCAEZ,KAAK,WAAW,kBACf,mVAAC;4CAAI,WAAU;sDACZ,KAAK,WAAW;;;;;;;;;;;;gCAItB,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,mVAAC;oCAAK,WAAU;8CACb,KAAK,KAAK,GAAG,KAAK,QAAQ,KAAK,KAAK;;;;;;;2BAzBpC,KAAK,EAAE;;;;;oBA8BlB;;;;;2BAEA,4BACF,mVAAC;oBAAI,WAAU;;wBAAwC;wBAC9B;wBAAY;;;;;;2BAEnC;;;;;;;;;;;;AAKd;uCAEe", "debugId": null}}, {"offset": {"line": 2795, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/frontend/src/components/layout/Sidebar.module.css [app-ssr] (css module)"], "sourcesContent": ["__turbopack_context__.v({\n  \"navigationContent\": \"Sidebar-module__ZM0Xeq__navigationContent\",\n  \"sidebarContainer\": \"Sidebar-module__ZM0Xeq__sidebarContainer\",\n  \"sidebarFooter\": \"Sidebar-module__ZM0Xeq__sidebarFooter\",\n  \"sidebarHeader\": \"Sidebar-module__ZM0Xeq__sidebarHeader\",\n  \"sidebarScrollContainer\": \"Sidebar-module__ZM0Xeq__sidebarScrollContainer\",\n  \"sidebarSearch\": \"Sidebar-module__ZM0Xeq__sidebarSearch\",\n});\n"], "names": [], "mappings": "AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA"}}, {"offset": {"line": 2809, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/Sidebar.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useSidebarStore, UserType } from '@/stores/sidebar/useSidebarStore'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport { usePermissionAwareNavigation } from '@/hooks/usePermissionAwareNavigation'\nimport {\n  ChevronLeft,\n  ChevronRight,\n  Search,\n  Star,\n  Clock,\n  LogOut,\n  User,\n  Settings,\n  Menu,\n  X,\n  Home,\n  Bookmark,\n  History\n} from 'lucide-react'\nimport * as Icons from 'lucide-react'\nimport { SidebarItem } from './SidebarItem'\nimport { UserProfile } from './UserProfile'\nimport { SidebarSearch } from './SidebarSearch'\nimport styles from './Sidebar.module.css'\n\ninterface SidebarProps {\n  userType: UserType\n}\n\nexport function Sidebar({ userType }: SidebarProps) {\n  const pathname = usePathname()\n  const {\n    isCollapsed,\n    isMobileOpen,\n    favoriteItems,\n    recentItems,\n    toggleSidebar,\n    setActiveItem,\n    setMobileSidebarOpen\n  } = useSidebarStore()\n\n  const { user, logout } = useAuthStore()\n  const { navigationItems } = usePermissionAwareNavigation()\n  const [showSearch, setShowSearch] = useState(false)\n\n  // Safeguard: Ensure navigationItems is always an array\n  const safeNavigationItems = Array.isArray(navigationItems) ? navigationItems : []\n\n  // Show loading state if navigation items are not loaded yet\n  const isNavigationLoading = safeNavigationItems.length === 0\n\n  // Get favorite navigation items\n  const favoriteNavItems = safeNavigationItems.filter(item =>\n    item && favoriteItems.includes(item.id)\n  )\n\n  // Get recent navigation items\n  const recentNavItems = safeNavigationItems.filter(item =>\n    item && recentItems.includes(item.id)\n  ).slice(0, 5)\n\n  const handleItemClick = (itemId: string) => {\n    setActiveItem(itemId)\n    // Close mobile sidebar when item is clicked\n    if (isMobileOpen) {\n      setMobileSidebarOpen(false)\n    }\n  }\n\n  const handleLogout = async () => {\n    try {\n      await logout()\n    } catch (error) {\n      console.error('Logout error:', error)\n    }\n  }\n\n  return (\n    <>\n      {/* Desktop Sidebar */}\n      <div\n        className={`${styles.sidebarContainer} sidebar-container ${\n          isCollapsed ? 'w-16' : 'w-64'\n        } hidden lg:block`}\n      >\n        {/* Sidebar Header */}\n        <div className={`${styles.sidebarHeader} sidebar-fixed-section flex items-center justify-between`}>\n          {!isCollapsed && (\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">LMS</span>\n              </div>\n              <span className=\"font-semibold text-gray-900\">Groups Exam</span>\n            </div>\n          )}\n\n          <button\n            onClick={toggleSidebar}\n            className=\"p-1.5 rounded-lg hover:bg-gray-100 transition-colors\"\n            title={isCollapsed ? 'Expand sidebar' : 'Collapse sidebar'}\n          >\n            {isCollapsed ? (\n              <ChevronRight className=\"w-4 h-4 text-gray-600\" />\n            ) : (\n              <ChevronLeft className=\"w-4 h-4 text-gray-600\" />\n            )}\n          </button>\n        </div>\n\n        {/* Search */}\n        {!isCollapsed && (\n          <div className={`${styles.sidebarSearch} sidebar-fixed-section`}>\n            <SidebarSearch />\n          </div>\n        )}\n\n        {/* Navigation - Scrollable Area */}\n        <div className={`${styles.sidebarScrollContainer} sidebar-scroll-area`}>\n          <div className={styles.navigationContent}>\n            {/* Main Navigation */}\n            <nav className=\"px-2 space-y-1\">\n              {safeNavigationItems.map((item) => (\n                <SidebarItem\n                  key={item.id}\n                  item={item}\n                  isActive={pathname === item.href}\n                  isCollapsed={isCollapsed}\n                  onClick={() => handleItemClick(item.id)}\n                />\n              ))}\n            </nav>\n\n            {/* Favorites Section */}\n            {!isCollapsed && favoriteNavItems.length > 0 && (\n              <div className=\"mt-6 px-2\">\n                <div className=\"flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  <Star className=\"w-3 h-3 mr-2\" />\n                  Favorites\n                </div>\n                <div className=\"space-y-1\">\n                  {favoriteNavItems.map((item) => (\n                    <SidebarItem\n                      key={`fav-${item.id}`}\n                      item={item}\n                      isActive={pathname === item.href}\n                      isCollapsed={false}\n                      onClick={() => handleItemClick(item.id)}\n                      variant=\"compact\"\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Recent Section */}\n            {!isCollapsed && recentNavItems.length > 0 && (\n              <div className=\"mt-6 px-2\">\n                <div className=\"flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  <Clock className=\"w-3 h-3 mr-2\" />\n                  Recent\n                </div>\n                <div className=\"space-y-1\">\n                  {recentNavItems.map((item) => (\n                    <SidebarItem\n                      key={`recent-${item.id}`}\n                      item={item}\n                      isActive={pathname === item.href}\n                      isCollapsed={false}\n                      onClick={() => handleItemClick(item.id)}\n                      variant=\"compact\"\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Bottom Padding for Better Scrolling */}\n            <div className=\"h-4\"></div>\n          </div>\n        </div>\n\n        {/* User Profile Section */}\n        <div className={`${styles.sidebarFooter} sidebar-fixed-section`}>\n          <UserProfile\n            user={user}\n            isCollapsed={isCollapsed}\n            onLogout={handleLogout}\n          />\n        </div>\n      </div>\n\n      {/* Mobile Sidebar */}\n      <div\n        className={`${styles.sidebarContainer} w-64 transform transition-transform duration-300 ease-in-out lg:hidden ${\n          isMobileOpen ? 'translate-x-0' : '-translate-x-full'\n        }`}\n        style={{ zIndex: 50 }}\n      >\n        {/* Mobile Sidebar Header */}\n        <div className={`${styles.sidebarHeader} flex items-center justify-between`}>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">LMS</span>\n            </div>\n            <span className=\"font-semibold text-gray-900\">Groups Exam</span>\n          </div>\n\n          <button\n            onClick={() => setMobileSidebarOpen(false)}\n            className=\"p-1.5 rounded-lg hover:bg-gray-100 transition-colors\"\n          >\n            <ChevronLeft className=\"w-4 h-4 text-gray-600\" />\n          </button>\n        </div>\n\n        {/* Mobile Search */}\n        <div className={styles.sidebarSearch}>\n          <SidebarSearch />\n        </div>\n\n        {/* Mobile Navigation - Scrollable Area */}\n        <div className={styles.sidebarScrollContainer}>\n          <div className={styles.navigationContent}>\n            <nav className=\"px-2 space-y-1\">\n              {safeNavigationItems.map((item) => (\n                <SidebarItem\n                  key={item.id}\n                  item={item}\n                  isActive={pathname === item.href}\n                  isCollapsed={false}\n                  onClick={() => handleItemClick(item.id)}\n                />\n              ))}\n            </nav>\n\n            {/* Mobile Favorites */}\n            {favoriteNavItems.length > 0 && (\n              <div className=\"mt-6 px-2\">\n                <div className=\"flex items-center px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                  <Star className=\"w-3 h-3 mr-2\" />\n                  Favorites\n                </div>\n                <div className=\"space-y-1\">\n                  {favoriteNavItems.map((item) => (\n                    <SidebarItem\n                      key={`mobile-fav-${item.id}`}\n                      item={item}\n                      isActive={pathname === item.href}\n                      isCollapsed={false}\n                      onClick={() => handleItemClick(item.id)}\n                      variant=\"compact\"\n                    />\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {/* Bottom Padding for Better Mobile Scrolling */}\n            <div className=\"h-4\"></div>\n          </div>\n        </div>\n\n        {/* Mobile User Profile */}\n        <div className={styles.sidebarFooter}>\n          <UserProfile\n            user={user}\n            isCollapsed={false}\n            onLogout={handleLogout}\n          />\n        </div>\n      </div>\n    </>\n  )\n}\n\nexport default Sidebar\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAgBA;AACA;AACA;AACA;AA3BA;;;;;;;;;;;;AAiCO,SAAS,QAAQ,EAAE,QAAQ,EAAgB;IAChD,MAAM,WAAW,CAAA,GAAA,uOAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EACJ,WAAW,EACX,YAAY,EACZ,aAAa,EACb,WAAW,EACX,aAAa,EACb,aAAa,EACb,oBAAoB,EACrB,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;IACpC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,+BAA4B,AAAD;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,uDAAuD;IACvD,MAAM,sBAAsB,MAAM,OAAO,CAAC,mBAAmB,kBAAkB,EAAE;IAEjF,4DAA4D;IAC5D,MAAM,sBAAsB,oBAAoB,MAAM,KAAK;IAE3D,gCAAgC;IAChC,MAAM,mBAAmB,oBAAoB,MAAM,CAAC,CAAA,OAClD,QAAQ,cAAc,QAAQ,CAAC,KAAK,EAAE;IAGxC,8BAA8B;IAC9B,MAAM,iBAAiB,oBAAoB,MAAM,CAAC,CAAA,OAChD,QAAQ,YAAY,QAAQ,CAAC,KAAK,EAAE,GACpC,KAAK,CAAC,GAAG;IAEX,MAAM,kBAAkB,CAAC;QACvB,cAAc;QACd,4CAA4C;QAC5C,IAAI,cAAc;YAChB,qBAAqB;QACvB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,qBACE;;0BAEE,mVAAC;gBACC,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,mBAAmB,EACvD,cAAc,SAAS,OACxB,gBAAgB,CAAC;;kCAGlB,mVAAC;wBAAI,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,aAAa,CAAC,wDAAwD,CAAC;;4BAC9F,CAAC,6BACA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,mVAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;0CAIlD,mVAAC;gCACC,SAAS;gCACT,WAAU;gCACV,OAAO,cAAc,mBAAmB;0CAEvC,4BACC,mVAAC,0SAAA,CAAA,eAAY;oCAAC,WAAU;;;;;yDAExB,mVAAC,wSAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;oBAM5B,CAAC,6BACA,mVAAC;wBAAI,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC;kCAC7D,cAAA,mVAAC,iKAAA,CAAA,gBAAa;;;;;;;;;;kCAKlB,mVAAC;wBAAI,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,sBAAsB,CAAC,oBAAoB,CAAC;kCACpE,cAAA,mVAAC;4BAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,iBAAiB;;8CAEtC,mVAAC;oCAAI,WAAU;8CACZ,oBAAoB,GAAG,CAAC,CAAC,qBACxB,mVAAC,+JAAA,CAAA,cAAW;4CAEV,MAAM;4CACN,UAAU,aAAa,KAAK,IAAI;4CAChC,aAAa;4CACb,SAAS,IAAM,gBAAgB,KAAK,EAAE;2CAJjC,KAAK,EAAE;;;;;;;;;;gCAUjB,CAAC,eAAe,iBAAiB,MAAM,GAAG,mBACzC,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,mVAAC;4CAAI,WAAU;sDACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,mVAAC,+JAAA,CAAA,cAAW;oDAEV,MAAM;oDACN,UAAU,aAAa,KAAK,IAAI;oDAChC,aAAa;oDACb,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,SAAQ;mDALH,CAAC,IAAI,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;;;;;;;gCAa9B,CAAC,eAAe,eAAe,MAAM,GAAG,mBACvC,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,mVAAC;4CAAI,WAAU;sDACZ,eAAe,GAAG,CAAC,CAAC,qBACnB,mVAAC,+JAAA,CAAA,cAAW;oDAEV,MAAM;oDACN,UAAU,aAAa,KAAK,IAAI;oDAChC,aAAa;oDACb,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,SAAQ;mDALH,CAAC,OAAO,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;;;;;;;8CAalC,mVAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,mVAAC;wBAAI,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,aAAa,CAAC,sBAAsB,CAAC;kCAC7D,cAAA,mVAAC,+JAAA,CAAA,cAAW;4BACV,MAAM;4BACN,aAAa;4BACb,UAAU;;;;;;;;;;;;;;;;;0BAMhB,mVAAC;gBACC,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,gBAAgB,CAAC,wEAAwE,EAC5G,eAAe,kBAAkB,qBACjC;gBACF,OAAO;oBAAE,QAAQ;gBAAG;;kCAGpB,mVAAC;wBAAI,WAAW,GAAG,sKAAA,CAAA,UAAM,CAAC,aAAa,CAAC,kCAAkC,CAAC;;0CACzE,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,mVAAC;wCAAK,WAAU;kDAA8B;;;;;;;;;;;;0CAGhD,mVAAC;gCACC,SAAS,IAAM,qBAAqB;gCACpC,WAAU;0CAEV,cAAA,mVAAC,wSAAA,CAAA,cAAW;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAK3B,mVAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,aAAa;kCAClC,cAAA,mVAAC,iKAAA,CAAA,gBAAa;;;;;;;;;;kCAIhB,mVAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,sBAAsB;kCAC3C,cAAA,mVAAC;4BAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,iBAAiB;;8CACtC,mVAAC;oCAAI,WAAU;8CACZ,oBAAoB,GAAG,CAAC,CAAC,qBACxB,mVAAC,+JAAA,CAAA,cAAW;4CAEV,MAAM;4CACN,UAAU,aAAa,KAAK,IAAI;4CAChC,aAAa;4CACb,SAAS,IAAM,gBAAgB,KAAK,EAAE;2CAJjC,KAAK,EAAE;;;;;;;;;;gCAUjB,iBAAiB,MAAM,GAAG,mBACzB,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGnC,mVAAC;4CAAI,WAAU;sDACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,mVAAC,+JAAA,CAAA,cAAW;oDAEV,MAAM;oDACN,UAAU,aAAa,KAAK,IAAI;oDAChC,aAAa;oDACb,SAAS,IAAM,gBAAgB,KAAK,EAAE;oDACtC,SAAQ;mDALH,CAAC,WAAW,EAAE,KAAK,EAAE,EAAE;;;;;;;;;;;;;;;;8CAatC,mVAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;kCAKnB,mVAAC;wBAAI,WAAW,sKAAA,CAAA,UAAM,CAAC,aAAa;kCAClC,cAAA,mVAAC,+JAAA,CAAA,cAAW;4BACV,MAAM;4BACN,aAAa;4BACb,UAAU;;;;;;;;;;;;;;;;;;;AAMtB;uCAEe", "debugId": null}}, {"offset": {"line": 3287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/config/navigation/superAdminNavigation.ts"], "sourcesContent": ["import { NavigationItem } from '@/stores/sidebar/useSidebarStore'\r\n\r\nexport const superAdminNavigationConfig: NavigationItem[] = [\r\n  {\r\n    id: 'dashboard',\r\n    label: 'Dashboard',\r\n    icon: 'LayoutDashboard',\r\n    href: '/super-admin',\r\n    description: 'Overview and analytics'\r\n  },\r\n  {\r\n    id: 'institutes',\r\n    label: 'Institute Management',\r\n    icon: 'Building2',\r\n    href: '/super-admin/institutes',\r\n    description: 'Manage institutes and verification'\r\n  },\r\n  {\r\n    id: 'users',\r\n    label: 'User Management',\r\n    icon: 'Users',\r\n    href: '/super-admin/users',\r\n    description: 'Manage all platform users'\r\n  },\r\n  {\r\n    id: 'themes',\r\n    label: 'Theme Management',\r\n    icon: 'Palette',\r\n    href: '/super-admin/themes',\r\n    description: 'Platform and institute themes'\r\n  },\r\n  {\r\n    id: 'analytics',\r\n    label: 'Analytics',\r\n    icon: 'BarChart3',\r\n    href: '/super-admin/analytics',\r\n    description: 'Platform analytics and insights'\r\n  },\r\n  {\r\n    id: 'role-permissions',\r\n    label: 'Roles & Permissions',\r\n    icon: 'Shield',\r\n    href: '/super-admin/role-permissions',\r\n    description: 'Manage user roles and permissions'\r\n  },\r\n  {\r\n    id: 'settings',\r\n    label: 'Settings',\r\n    icon: 'Settings',\r\n    href: '/super-admin/settings',\r\n    description: 'Platform configuration and settings'\r\n  }\r\n]"], "names": [], "mappings": ";;;AAEO,MAAM,6BAA+C;IAC1D;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;IACf;CACD", "debugId": null}}, {"offset": {"line": 3347, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/config/navigation/instituteAdminNavigation.ts"], "sourcesContent": ["import { NavigationItem } from '@/stores/sidebar/useSidebarStore'\n\nexport const instituteAdminNavigationConfig: NavigationItem[] = [\n  {\n    id: 'dashboard',\n    label: 'Dashboard',\n    icon: 'LayoutDashboard',\n    href: '/institute-admin',\n    description: 'Institute overview and analytics',\n    badge: 0,\n    section: 'main'\n  },\n  {\n    id: 'courses',\n    label: 'Course Management',\n    icon: 'BookOpen',\n    href: '/institute-admin/courses',\n    description: 'Manage courses and curriculum',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'courses-list',\n        label: 'All Courses',\n        icon: 'List',\n        href: '/institute-admin/courses',\n        description: 'View all courses'\n      },\n      {\n        id: 'courses-create',\n        label: 'Create Course',\n        icon: 'Plus',\n        href: '/institute-admin/courses/create',\n        description: 'Add new course'\n      },\n      {\n        id: 'courses-categories',\n        label: 'Categories',\n        icon: 'Tag',\n        href: '/institute-admin/courses/categories',\n        description: 'Manage course categories'\n      },\n      {\n        id: 'courses-analytics',\n        label: 'Course Analytics',\n        icon: 'BarChart3',\n        href: '/institute-admin/courses/analytics',\n        description: 'Course performance metrics'\n      }\n    ]\n  },\n  {\n    id: 'students',\n    label: 'Student Management',\n    icon: 'GraduationCap',\n    href: '/institute-admin/students',\n    description: 'Manage student accounts and enrollment',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'students-list',\n        label: 'All Students',\n        icon: 'Users',\n        href: '/institute-admin/students',\n        description: 'View all students'\n      },\n      {\n        id: 'students-enrollment',\n        label: 'Enrollments',\n        icon: 'UserPlus',\n        href: '/institute-admin/students/enrollment',\n        description: 'Manage course enrollments'\n      },\n      {\n        id: 'students-progress',\n        label: 'Progress Tracking',\n        icon: 'TrendingUp',\n        href: '/institute-admin/students/progress',\n        description: 'Track student progress'\n      },\n      {\n        id: 'students-certificates',\n        label: 'Certificates',\n        icon: 'Award',\n        href: '/institute-admin/students/certificates',\n        description: 'Manage certificates'\n      }\n    ]\n  },\n  {\n    id: 'instructors',\n    label: 'Instructor Management',\n    icon: 'UserCheck',\n    href: '/institute-admin/instructors',\n    description: 'Manage instructors and teaching staff',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'instructors-list',\n        label: 'All Instructors',\n        icon: 'Users',\n        href: '/institute-admin/instructors',\n        description: 'View all instructors'\n      },\n      {\n        id: 'instructors-schedule',\n        label: 'Schedules',\n        icon: 'Calendar',\n        href: '/institute-admin/instructors/schedule',\n        description: 'Manage teaching schedules'\n      },\n      {\n        id: 'instructors-performance',\n        label: 'Performance',\n        icon: 'BarChart3',\n        href: '/institute-admin/instructors/performance',\n        description: 'Instructor performance metrics'\n      }\n    ]\n  },\n  {\n    id: 'live-classes',\n    label: 'Live Classes',\n    icon: 'Video',\n    href: '/institute-admin/live-classes',\n    description: 'Manage live classes and sessions',\n    badge: 2,\n    section: 'main',\n    children: [\n      {\n        id: 'live-classes-schedule',\n        label: 'Class Schedule',\n        icon: 'Calendar',\n        href: '/institute-admin/live-classes/schedule',\n        description: 'Manage class schedules'\n      },\n      {\n        id: 'live-classes-rooms',\n        label: 'Virtual Rooms',\n        icon: 'Monitor',\n        href: '/institute-admin/live-classes/rooms',\n        description: 'Manage virtual classrooms'\n      },\n      {\n        id: 'live-classes-recordings',\n        label: 'Recordings',\n        icon: 'PlayCircle',\n        href: '/institute-admin/live-classes/recordings',\n        description: 'Class recordings'\n      }\n    ]\n  },\n  {\n    id: 'exams',\n    label: 'Exams & Assessments',\n    icon: 'FileText',\n    href: '/institute-admin/exams',\n    description: 'Manage exams and assessments',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'exams-list',\n        label: 'All Exams',\n        icon: 'List',\n        href: '/institute-admin/exams',\n        description: 'View all exams'\n      },\n      {\n        id: 'exams-create',\n        label: 'Create Exam',\n        icon: 'Plus',\n        href: '/institute-admin/exams/create',\n        description: 'Create new exam'\n      },\n      {\n        id: 'exams-results',\n        label: 'Results',\n        icon: 'BarChart3',\n        href: '/institute-admin/exams/results',\n        description: 'Exam results and analytics'\n      },\n      {\n        id: 'exams-question-bank',\n        label: 'Question Bank',\n        icon: 'HelpCircle',\n        href: '/institute-admin/exams/questions',\n        description: 'Manage question bank'\n      }\n    ]\n  },\n  {\n    id: 'billing',\n    label: 'Billing & Payments',\n    icon: 'CreditCard',\n    href: '/institute-admin/billing',\n    description: 'Manage billing and payments',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'billing-overview',\n        label: 'Billing Overview',\n        icon: 'DollarSign',\n        href: '/institute-admin/billing',\n        description: 'Revenue and billing overview'\n      },\n      {\n        id: 'billing-transactions',\n        label: 'Transactions',\n        icon: 'Receipt',\n        href: '/institute-admin/billing/transactions',\n        description: 'Payment transactions'\n      },\n      {\n        id: 'billing-subscriptions',\n        label: 'Subscriptions',\n        icon: 'CreditCard',\n        href: '/institute-admin/billing/subscriptions',\n        description: 'Student subscriptions'\n      },\n      {\n        id: 'billing-reports',\n        label: 'Financial Reports',\n        icon: 'FileText',\n        href: '/institute-admin/billing/reports',\n        description: 'Financial reports'\n      }\n    ]\n  },\n  {\n    id: 'marketplace',\n    label: 'Marketplace',\n    icon: 'ShoppingBag',\n    href: '/institute-admin/marketplace',\n    description: 'Manage course marketplace',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'marketplace-courses',\n        label: 'Published Courses',\n        icon: 'BookOpen',\n        href: '/institute-admin/marketplace/courses',\n        description: 'Courses in marketplace'\n      },\n      {\n        id: 'marketplace-orders',\n        label: 'Orders',\n        icon: 'ShoppingCart',\n        href: '/institute-admin/marketplace/orders',\n        description: 'Course orders'\n      },\n      {\n        id: 'marketplace-reviews',\n        label: 'Reviews & Ratings',\n        icon: 'Star',\n        href: '/institute-admin/marketplace/reviews',\n        description: 'Course reviews'\n      },\n      {\n        id: 'marketplace-analytics',\n        label: 'Sales Analytics',\n        icon: 'TrendingUp',\n        href: '/institute-admin/marketplace/analytics',\n        description: 'Sales performance'\n      }\n    ]\n  },\n  {\n    id: 'analytics',\n    label: 'Analytics & Reports',\n    icon: 'BarChart3',\n    href: '/institute-admin/analytics',\n    description: 'Institute analytics and insights',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'analytics-overview',\n        label: 'Overview',\n        icon: 'BarChart3',\n        href: '/institute-admin/analytics',\n        description: 'Institute overview metrics'\n      },\n      {\n        id: 'analytics-students',\n        label: 'Student Analytics',\n        icon: 'Users',\n        href: '/institute-admin/analytics/students',\n        description: 'Student engagement metrics'\n      },\n      {\n        id: 'analytics-courses',\n        label: 'Course Analytics',\n        icon: 'BookOpen',\n        href: '/institute-admin/analytics/courses',\n        description: 'Course performance metrics'\n      },\n      {\n        id: 'analytics-revenue',\n        label: 'Revenue Analytics',\n        icon: 'DollarSign',\n        href: '/institute-admin/analytics/revenue',\n        description: 'Revenue and financial metrics'\n      }\n    ]\n  },\n  {\n    id: 'settings',\n    label: 'Institute Settings',\n    icon: 'Settings',\n    href: '/institute-admin/settings',\n    description: 'Institute configuration and settings',\n    badge: 0,\n    section: 'settings',\n    children: [\n      {\n        id: 'settings-general',\n        label: 'General Settings',\n        icon: 'Settings',\n        href: '/institute-admin/settings/general',\n        description: 'Basic institute settings'\n      },\n      {\n        id: 'settings-domain',\n        label: 'Domain Settings',\n        icon: 'Globe',\n        href: '/institute-admin/settings/domain',\n        description: 'Custom domain configuration'\n      },\n      {\n        id: 'settings-theme',\n        label: 'Theme & Branding',\n        icon: 'Palette',\n        href: '/institute-admin/settings/theme',\n        description: 'Customize appearance'\n      },\n      {\n        id: 'settings-payment',\n        label: 'Payment Gateway',\n        icon: 'CreditCard',\n        href: '/institute-admin/settings/payment',\n        description: 'Payment gateway settings'\n      },\n      {\n        id: 'settings-notifications',\n        label: 'Notifications',\n        icon: 'Bell',\n        href: '/institute-admin/settings/notifications',\n        description: 'Notification preferences'\n      },\n      {\n        id: 'settings-integrations',\n        label: 'Integrations',\n        icon: 'Plug',\n        href: '/institute-admin/settings/integrations',\n        description: 'Third-party integrations'\n      }\n    ]\n  }\n]\n\n// Quick access items for institute admin\nexport const instituteAdminQuickAccess = [\n  {\n    id: 'quick-live-classes',\n    label: 'Live Classes Today',\n    icon: 'Video',\n    href: '/institute-admin/live-classes/schedule',\n    count: 2\n  },\n  {\n    id: 'quick-new-enrollments',\n    label: 'New Enrollments',\n    icon: 'UserPlus',\n    href: '/institute-admin/students/enrollment',\n    count: 8\n  },\n  {\n    id: 'quick-pending-exams',\n    label: 'Pending Exams',\n    icon: 'FileText',\n    href: '/institute-admin/exams',\n    count: 3\n  },\n  {\n    id: 'quick-revenue',\n    label: 'Today\\'s Revenue',\n    icon: 'DollarSign',\n    href: '/institute-admin/billing',\n    value: '$1,250'\n  }\n]\n\n// Favorite items for institute admin\nexport const instituteAdminFavorites = [\n  'courses',\n  'students',\n  'live-classes',\n  'analytics'\n]\n\n// Recent items for institute admin (this would be dynamic)\nexport const instituteAdminRecentItems = [\n  {\n    id: 'recent-1',\n    label: 'Course Analytics',\n    href: '/institute-admin/courses/analytics',\n    timestamp: new Date().toISOString()\n  },\n  {\n    id: 'recent-2',\n    label: 'Student Progress',\n    href: '/institute-admin/students/progress',\n    timestamp: new Date(Date.now() - 1800000).toISOString()\n  },\n  {\n    id: 'recent-3',\n    label: 'Live Class Schedule',\n    href: '/institute-admin/live-classes/schedule',\n    timestamp: new Date(Date.now() - 3600000).toISOString()\n  }\n]\n\nexport default instituteAdminNavigationConfig\n"], "names": [], "mappings": ";;;;;;;AAEO,MAAM,iCAAmD;IAC9D;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBAC<PERSON>,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;CACD;AAGM,MAAM,4BAA4B;IACvC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAGM,MAAM,0BAA0B;IACrC;IACA;IACA;IACA;CACD;AAGM,MAAM,4BAA4B;IACvC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;IACvD;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;IACvD;CACD;uCAEc", "debugId": null}}, {"offset": {"line": 3779, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/config/navigation/studentNavigation.ts"], "sourcesContent": ["import { NavigationItem } from '@/stores/sidebar/useSidebarStore'\n\nexport const studentNavigationConfig: NavigationItem[] = [\n  {\n    id: 'dashboard',\n    label: 'Dashboard',\n    icon: 'LayoutDashboard',\n    href: '/student',\n    description: 'Your learning overview',\n    badge: 0,\n    section: 'main'\n  },\n  {\n    id: 'my-courses',\n    label: 'My Courses',\n    icon: 'BookOpen',\n    href: '/student/courses',\n    description: 'Your enrolled courses',\n    badge: 2,\n    section: 'main',\n    children: [\n      {\n        id: 'courses-active',\n        label: 'Active Courses',\n        icon: 'Play',\n        href: '/student/courses/active',\n        description: 'Currently enrolled courses'\n      },\n      {\n        id: 'courses-completed',\n        label: 'Completed Courses',\n        icon: 'CheckCircle',\n        href: '/student/courses/completed',\n        description: 'Finished courses'\n      },\n      {\n        id: 'courses-favorites',\n        label: 'Favorites',\n        icon: 'Heart',\n        href: '/student/courses/favorites',\n        description: 'Your favorite courses'\n      },\n      {\n        id: 'courses-downloads',\n        label: 'Downloads',\n        icon: 'Download',\n        href: '/student/courses/downloads',\n        description: 'Downloaded content'\n      }\n    ]\n  },\n  {\n    id: 'marketplace',\n    label: 'Course Marketplace',\n    icon: 'ShoppingBag',\n    href: '/student/marketplace',\n    description: 'Browse and purchase courses',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'marketplace-browse',\n        label: 'Browse Courses',\n        icon: 'Search',\n        href: '/student/marketplace',\n        description: 'Explore available courses'\n      },\n      {\n        id: 'marketplace-categories',\n        label: 'Categories',\n        icon: 'Tag',\n        href: '/student/marketplace/categories',\n        description: 'Browse by category'\n      },\n      {\n        id: 'marketplace-wishlist',\n        label: 'Wishlist',\n        icon: 'Heart',\n        href: '/student/marketplace/wishlist',\n        description: 'Your course wishlist'\n      },\n      {\n        id: 'marketplace-cart',\n        label: 'Shopping Cart',\n        icon: 'ShoppingCart',\n        href: '/student/marketplace/cart',\n        description: 'Items in your cart',\n        badge: 1\n      }\n    ]\n  },\n  {\n    id: 'live-classes',\n    label: 'Live Classes',\n    icon: 'Video',\n    href: '/student/live-classes',\n    description: 'Attend live sessions',\n    badge: 1,\n    section: 'main',\n    children: [\n      {\n        id: 'live-classes-schedule',\n        label: 'Class Schedule',\n        icon: 'Calendar',\n        href: '/student/live-classes/schedule',\n        description: 'Your class schedule'\n      },\n      {\n        id: 'live-classes-upcoming',\n        label: 'Upcoming Classes',\n        icon: 'Clock',\n        href: '/student/live-classes/upcoming',\n        description: 'Classes starting soon',\n        badge: 1\n      },\n      {\n        id: 'live-classes-recordings',\n        label: 'Recorded Classes',\n        icon: 'PlayCircle',\n        href: '/student/live-classes/recordings',\n        description: 'Watch recorded sessions'\n      }\n    ]\n  },\n  {\n    id: 'assignments',\n    label: 'Assignments & Exams',\n    icon: 'FileText',\n    href: '/student/assignments',\n    description: 'Your assignments and tests',\n    badge: 3,\n    section: 'main',\n    children: [\n      {\n        id: 'assignments-pending',\n        label: 'Pending Assignments',\n        icon: 'Clock',\n        href: '/student/assignments/pending',\n        description: 'Assignments to complete',\n        badge: 2\n      },\n      {\n        id: 'assignments-submitted',\n        label: 'Submitted',\n        icon: 'CheckCircle',\n        href: '/student/assignments/submitted',\n        description: 'Completed assignments'\n      },\n      {\n        id: 'assignments-exams',\n        label: 'Exams',\n        icon: 'FileText',\n        href: '/student/assignments/exams',\n        description: 'Scheduled exams',\n        badge: 1\n      },\n      {\n        id: 'assignments-results',\n        label: 'Results',\n        icon: 'BarChart3',\n        href: '/student/assignments/results',\n        description: 'Your exam results'\n      }\n    ]\n  },\n  {\n    id: 'progress',\n    label: 'Progress & Analytics',\n    icon: 'TrendingUp',\n    href: '/student/progress',\n    description: 'Track your learning progress',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'progress-overview',\n        label: 'Learning Overview',\n        icon: 'BarChart3',\n        href: '/student/progress',\n        description: 'Your learning statistics'\n      },\n      {\n        id: 'progress-achievements',\n        label: 'Achievements',\n        icon: 'Award',\n        href: '/student/progress/achievements',\n        description: 'Badges and achievements'\n      },\n      {\n        id: 'progress-certificates',\n        label: 'Certificates',\n        icon: 'Award',\n        href: '/student/progress/certificates',\n        description: 'Your certificates'\n      },\n      {\n        id: 'progress-goals',\n        label: 'Learning Goals',\n        icon: 'Target',\n        href: '/student/progress/goals',\n        description: 'Set and track goals'\n      }\n    ]\n  },\n  {\n    id: 'discussions',\n    label: 'Discussions',\n    icon: 'MessageSquare',\n    href: '/student/discussions',\n    description: 'Course discussions and forums',\n    badge: 5,\n    section: 'main',\n    children: [\n      {\n        id: 'discussions-forums',\n        label: 'Course Forums',\n        icon: 'MessageSquare',\n        href: '/student/discussions/forums',\n        description: 'Course discussion forums'\n      },\n      {\n        id: 'discussions-qa',\n        label: 'Q&A',\n        icon: 'HelpCircle',\n        href: '/student/discussions/qa',\n        description: 'Ask questions'\n      },\n      {\n        id: 'discussions-study-groups',\n        label: 'Study Groups',\n        icon: 'Users',\n        href: '/student/discussions/study-groups',\n        description: 'Join study groups'\n      }\n    ]\n  },\n  {\n    id: 'library',\n    label: 'Digital Library',\n    icon: 'Library',\n    href: '/student/library',\n    description: 'Access learning resources',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'library-books',\n        label: 'E-Books',\n        icon: 'Book',\n        href: '/student/library/books',\n        description: 'Digital textbooks'\n      },\n      {\n        id: 'library-articles',\n        label: 'Articles',\n        icon: 'FileText',\n        href: '/student/library/articles',\n        description: 'Research articles'\n      },\n      {\n        id: 'library-videos',\n        label: 'Video Library',\n        icon: 'PlayCircle',\n        href: '/student/library/videos',\n        description: 'Educational videos'\n      },\n      {\n        id: 'library-notes',\n        label: 'My Notes',\n        icon: 'StickyNote',\n        href: '/student/library/notes',\n        description: 'Your personal notes'\n      }\n    ]\n  },\n  {\n    id: 'billing',\n    label: 'Billing & Payments',\n    icon: 'CreditCard',\n    href: '/student/billing',\n    description: 'Manage payments and subscriptions',\n    badge: 0,\n    section: 'main',\n    children: [\n      {\n        id: 'billing-overview',\n        label: 'Payment History',\n        icon: 'Receipt',\n        href: '/student/billing',\n        description: 'Your payment history'\n      },\n      {\n        id: 'billing-subscriptions',\n        label: 'Subscriptions',\n        icon: 'CreditCard',\n        href: '/student/billing/subscriptions',\n        description: 'Active subscriptions'\n      },\n      {\n        id: 'billing-invoices',\n        label: 'Invoices',\n        icon: 'FileText',\n        href: '/student/billing/invoices',\n        description: 'Download invoices'\n      },\n      {\n        id: 'billing-payment-methods',\n        label: 'Payment Methods',\n        icon: 'Wallet',\n        href: '/student/billing/payment-methods',\n        description: 'Manage payment methods'\n      }\n    ]\n  },\n  {\n    id: 'settings',\n    label: 'Account Settings',\n    icon: 'Settings',\n    href: '/student/settings',\n    description: 'Manage your account',\n    badge: 0,\n    section: 'settings',\n    children: [\n      {\n        id: 'settings-profile',\n        label: 'Profile Settings',\n        icon: 'User',\n        href: '/student/settings/profile',\n        description: 'Update your profile'\n      },\n      {\n        id: 'settings-preferences',\n        label: 'Learning Preferences',\n        icon: 'Settings',\n        href: '/student/settings/preferences',\n        description: 'Customize your experience'\n      },\n      {\n        id: 'settings-notifications',\n        label: 'Notifications',\n        icon: 'Bell',\n        href: '/student/settings/notifications',\n        description: 'Notification preferences'\n      },\n      {\n        id: 'settings-privacy',\n        label: 'Privacy & Security',\n        icon: 'Shield',\n        href: '/student/settings/privacy',\n        description: 'Privacy settings'\n      },\n      {\n        id: 'settings-downloads',\n        label: 'Download Settings',\n        icon: 'Download',\n        href: '/student/settings/downloads',\n        description: 'Offline content settings'\n      }\n    ]\n  }\n]\n\n// Quick access items for students\nexport const studentQuickAccess = [\n  {\n    id: 'quick-upcoming-class',\n    label: 'Next Live Class',\n    icon: 'Video',\n    href: '/student/live-classes/upcoming',\n    time: '2:30 PM'\n  },\n  {\n    id: 'quick-pending-assignments',\n    label: 'Pending Assignments',\n    icon: 'FileText',\n    href: '/student/assignments/pending',\n    count: 2\n  },\n  {\n    id: 'quick-new-messages',\n    label: 'New Messages',\n    icon: 'MessageSquare',\n    href: '/student/discussions',\n    count: 5\n  },\n  {\n    id: 'quick-progress',\n    label: 'Course Progress',\n    icon: 'TrendingUp',\n    href: '/student/progress',\n    value: '78%'\n  }\n]\n\n// Favorite items for students\nexport const studentFavorites = [\n  'my-courses',\n  'live-classes',\n  'assignments',\n  'progress'\n]\n\n// Recent items for students (this would be dynamic)\nexport const studentRecentItems = [\n  {\n    id: 'recent-1',\n    label: 'JavaScript Fundamentals',\n    href: '/student/courses/javascript-fundamentals',\n    timestamp: new Date().toISOString()\n  },\n  {\n    id: 'recent-2',\n    label: 'Assignment: React Components',\n    href: '/student/assignments/react-components',\n    timestamp: new Date(Date.now() - 1800000).toISOString()\n  },\n  {\n    id: 'recent-3',\n    label: 'Live Class: Advanced CSS',\n    href: '/student/live-classes/advanced-css',\n    timestamp: new Date(Date.now() - 3600000).toISOString()\n  }\n]\n\nexport default studentNavigationConfig\n"], "names": [], "mappings": ";;;;;;;AAEO,MAAM,0BAA4C;IACvD;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;IACX;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBAC<PERSON>,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;gBACb,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,aAAa;QACb,OAAO;QACP,SAAS;QACT,UAAU;YACR;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;YACA;gBACE,IAAI;gBACJ,OAAO;gBACP,MAAM;gBACN,MAAM;gBACN,aAAa;YACf;SACD;IACH;CACD;AAGM,MAAM,qBAAqB;IAChC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,MAAM;QACN,OAAO;IACT;CACD;AAGM,MAAM,mBAAmB;IAC9B;IACA;IACA;IACA;CACD;AAGM,MAAM,qBAAqB;IAChC;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,OAAO,WAAW;IACnC;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;IACvD;IACA;QACE,IAAI;QACJ,OAAO;QACP,MAAM;QACN,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,SAAS,WAAW;IACvD;CACD;uCAEc", "debugId": null}}, {"offset": {"line": 4208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/utils/navigationConfig.ts"], "sourcesContent": ["import { NavigationItem, UserType } from '@/stores/sidebar/useSidebarStore'\nimport { superAdminNavigationConfig } from '@/config/navigation/superAdminNavigation'\nimport { instituteAdminNavigationConfig } from '@/config/navigation/instituteAdminNavigation'\nimport { studentNavigationConfig } from '@/config/navigation/studentNavigation'\n\n// Navigation configuration mapping\nexport const navigationConfigs: Record<UserType, NavigationItem[]> = {\n  super_admin: superAdminNavigationConfig,\n  institute_admin: instituteAdminNavigationConfig,\n  student: studentNavigationConfig\n}\n\n// Get navigation items for a specific user type\nexport function getNavigationForUserType(userType: UserType): NavigationItem[] {\n  return navigationConfigs[userType] || []\n}\n\n// Get navigation item by ID (recursive search)\nexport function findNavigationItem(\n  items: NavigationItem[], \n  itemId: string\n): NavigationItem | null {\n  for (const item of items) {\n    if (item.id === itemId) {\n      return item\n    }\n    if (item.children) {\n      const found = findNavigationItem(item.children, itemId)\n      if (found) return found\n    }\n  }\n  return null\n}\n\n// Get navigation item by href (recursive search)\nexport function findNavigationItemByHref(\n  items: NavigationItem[], \n  href: string\n): NavigationItem | null {\n  for (const item of items) {\n    if (item.href === href) {\n      return item\n    }\n    if (item.children) {\n      const found = findNavigationItemByHref(item.children, href)\n      if (found) return found\n    }\n  }\n  return null\n}\n\n// Get all navigation items as flat array (for search)\nexport function getFlatNavigationItems(items: NavigationItem[]): NavigationItem[] {\n  const flatItems: NavigationItem[] = []\n  \n  function flatten(items: NavigationItem[]) {\n    for (const item of items) {\n      flatItems.push(item)\n      if (item.children) {\n        flatten(item.children)\n      }\n    }\n  }\n  \n  flatten(items)\n  return flatItems\n}\n\n// Search navigation items\nexport function searchNavigationItems(\n  items: NavigationItem[], \n  query: string\n): NavigationItem[] {\n  const flatItems = getFlatNavigationItems(items)\n  const searchTerm = query.toLowerCase().trim()\n  \n  if (!searchTerm) return []\n  \n  return flatItems.filter(item => \n    item.label.toLowerCase().includes(searchTerm) ||\n    item.description?.toLowerCase().includes(searchTerm) ||\n    item.href.toLowerCase().includes(searchTerm)\n  )\n}\n\n// Get navigation breadcrumbs for a given path\nexport function getNavigationBreadcrumbs(\n  items: NavigationItem[], \n  currentPath: string\n): NavigationItem[] {\n  const breadcrumbs: NavigationItem[] = []\n  \n  function findPath(items: NavigationItem[], path: NavigationItem[]): boolean {\n    for (const item of items) {\n      const currentPath = [...path, item]\n      \n      if (item.href === currentPath || currentPath.startsWith(item.href + '/')) {\n        breadcrumbs.push(...currentPath)\n        return true\n      }\n      \n      if (item.children && findPath(item.children, currentPath)) {\n        return true\n      }\n    }\n    return false\n  }\n  \n  findPath(items, [])\n  return breadcrumbs\n}\n\n// Get navigation items by section\nexport function getNavigationBySection(\n  items: NavigationItem[], \n  section: string\n): NavigationItem[] {\n  return items.filter(item => item.section === section)\n}\n\n// Get navigation items with badges\nexport function getNavigationItemsWithBadges(items: NavigationItem[]): NavigationItem[] {\n  const flatItems = getFlatNavigationItems(items)\n  return flatItems.filter(item => item.badge && item.badge > 0)\n}\n\n// Calculate total badge count\nexport function getTotalBadgeCount(items: NavigationItem[]): number {\n  const flatItems = getFlatNavigationItems(items)\n  return flatItems.reduce((total, item) => total + (item.badge || 0), 0)\n}\n\n// Get parent navigation item\nexport function getParentNavigationItem(\n  items: NavigationItem[], \n  childId: string\n): NavigationItem | null {\n  for (const item of items) {\n    if (item.children) {\n      const found = item.children.find(child => child.id === childId)\n      if (found) return item\n      \n      const parentInChildren = getParentNavigationItem(item.children, childId)\n      if (parentInChildren) return parentInChildren\n    }\n  }\n  return null\n}\n\n// Check if navigation item is active (including children)\nexport function isNavigationItemActive(\n  item: NavigationItem, \n  currentPath: string\n): boolean {\n  if (currentPath === item.href || currentPath.startsWith(item.href + '/')) {\n    return true\n  }\n  \n  if (item.children) {\n    return item.children.some(child => isNavigationItemActive(child, currentPath))\n  }\n  \n  return false\n}\n\n// Get navigation item depth\nexport function getNavigationItemDepth(\n  items: NavigationItem[], \n  itemId: string, \n  currentDepth: number = 0\n): number {\n  for (const item of items) {\n    if (item.id === itemId) {\n      return currentDepth\n    }\n    if (item.children) {\n      const depth = getNavigationItemDepth(item.children, itemId, currentDepth + 1)\n      if (depth !== -1) return depth\n    }\n  }\n  return -1\n}\n\n// Validate navigation structure\nexport function validateNavigationStructure(items: NavigationItem[]): {\n  isValid: boolean\n  errors: string[]\n} {\n  const errors: string[] = []\n  const seenIds = new Set<string>()\n  const seenHrefs = new Set<string>()\n  \n  function validate(items: NavigationItem[], path: string = '') {\n    for (const item of items) {\n      const currentPath = path ? `${path} > ${item.label}` : item.label\n      \n      // Check for duplicate IDs\n      if (seenIds.has(item.id)) {\n        errors.push(`Duplicate ID \"${item.id}\" found at ${currentPath}`)\n      }\n      seenIds.add(item.id)\n      \n      // Check for duplicate hrefs\n      if (seenHrefs.has(item.href)) {\n        errors.push(`Duplicate href \"${item.href}\" found at ${currentPath}`)\n      }\n      seenHrefs.add(item.href)\n      \n      // Check required fields\n      if (!item.label) {\n        errors.push(`Missing label at ${currentPath}`)\n      }\n      if (!item.href) {\n        errors.push(`Missing href at ${currentPath}`)\n      }\n      if (!item.icon) {\n        errors.push(`Missing icon at ${currentPath}`)\n      }\n      \n      // Validate children\n      if (item.children) {\n        validate(item.children, currentPath)\n      }\n    }\n  }\n  \n  validate(items)\n  \n  return {\n    isValid: errors.length === 0,\n    errors\n  }\n}\n\n// Get navigation statistics\nexport function getNavigationStatistics(items: NavigationItem[]) {\n  const flatItems = getFlatNavigationItems(items)\n  const sections = new Set(items.map(item => item.section).filter(Boolean))\n  const maxDepth = Math.max(...flatItems.map(item => \n    getNavigationItemDepth(items, item.id)\n  ))\n  const itemsWithBadges = getNavigationItemsWithBadges(items)\n  const totalBadgeCount = getTotalBadgeCount(items)\n  \n  return {\n    totalItems: flatItems.length,\n    topLevelItems: items.length,\n    sections: sections.size,\n    maxDepth: maxDepth + 1, // Convert 0-based to 1-based\n    itemsWithBadges: itemsWithBadges.length,\n    totalBadgeCount,\n    averageChildrenPerItem: items.reduce((sum, item) => \n      sum + (item.children?.length || 0), 0\n    ) / items.length\n  }\n}\n\n// Export utility functions\nexport const navigationUtils = {\n  getNavigationForUserType,\n  findNavigationItem,\n  findNavigationItemByHref,\n  getFlatNavigationItems,\n  searchNavigationItems,\n  getNavigationBreadcrumbs,\n  getNavigationBySection,\n  getNavigationItemsWithBadges,\n  getTotalBadgeCount,\n  getParentNavigationItem,\n  isNavigationItemActive,\n  getNavigationItemDepth,\n  validateNavigationStructure,\n  getNavigationStatistics\n}\n\nexport default navigationUtils\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;;;;AAGO,MAAM,oBAAwD;IACnE,aAAa,uKAAA,CAAA,6BAA0B;IACvC,iBAAiB,2KAAA,CAAA,iCAA8B;IAC/C,SAAS,oKAAA,CAAA,0BAAuB;AAClC;AAGO,SAAS,yBAAyB,QAAkB;IACzD,OAAO,iBAAiB,CAAC,SAAS,IAAI,EAAE;AAC1C;AAGO,SAAS,mBACd,KAAuB,EACvB,MAAc;IAEd,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,EAAE,KAAK,QAAQ;YACtB,OAAO;QACT;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,QAAQ,mBAAmB,KAAK,QAAQ,EAAE;YAChD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAGO,SAAS,yBACd,KAAuB,EACvB,IAAY;IAEZ,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,IAAI,KAAK,MAAM;YACtB,OAAO;QACT;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,QAAQ,yBAAyB,KAAK,QAAQ,EAAE;YACtD,IAAI,OAAO,OAAO;QACpB;IACF;IACA,OAAO;AACT;AAGO,SAAS,uBAAuB,KAAuB;IAC5D,MAAM,YAA8B,EAAE;IAEtC,SAAS,QAAQ,KAAuB;QACtC,KAAK,MAAM,QAAQ,MAAO;YACxB,UAAU,IAAI,CAAC;YACf,IAAI,KAAK,QAAQ,EAAE;gBACjB,QAAQ,KAAK,QAAQ;YACvB;QACF;IACF;IAEA,QAAQ;IACR,OAAO;AACT;AAGO,SAAS,sBACd,KAAuB,EACvB,KAAa;IAEb,MAAM,YAAY,uBAAuB;IACzC,MAAM,aAAa,MAAM,WAAW,GAAG,IAAI;IAE3C,IAAI,CAAC,YAAY,OAAO,EAAE;IAE1B,OAAO,UAAU,MAAM,CAAC,CAAA,OACtB,KAAK,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,eAClC,KAAK,WAAW,EAAE,cAAc,SAAS,eACzC,KAAK,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC;AAErC;AAGO,SAAS,yBACd,KAAuB,EACvB,WAAmB;IAEnB,MAAM,cAAgC,EAAE;IAExC,SAAS,SAAS,KAAuB,EAAE,IAAsB;QAC/D,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,cAAc;mBAAI;gBAAM;aAAK;YAEnC,IAAI,KAAK,IAAI,KAAK,eAAe,YAAY,UAAU,CAAC,KAAK,IAAI,GAAG,MAAM;gBACxE,YAAY,IAAI,IAAI;gBACpB,OAAO;YACT;YAEA,IAAI,KAAK,QAAQ,IAAI,SAAS,KAAK,QAAQ,EAAE,cAAc;gBACzD,OAAO;YACT;QACF;QACA,OAAO;IACT;IAEA,SAAS,OAAO,EAAE;IAClB,OAAO;AACT;AAGO,SAAS,uBACd,KAAuB,EACvB,OAAe;IAEf,OAAO,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,OAAO,KAAK;AAC/C;AAGO,SAAS,6BAA6B,KAAuB;IAClE,MAAM,YAAY,uBAAuB;IACzC,OAAO,UAAU,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG;AAC7D;AAGO,SAAS,mBAAmB,KAAuB;IACxD,MAAM,YAAY,uBAAuB;IACzC,OAAO,UAAU,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,CAAC,KAAK,KAAK,IAAI,CAAC,GAAG;AACtE;AAGO,SAAS,wBACd,KAAuB,EACvB,OAAe;IAEf,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,QAAQ,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,MAAM,EAAE,KAAK;YACvD,IAAI,OAAO,OAAO;YAElB,MAAM,mBAAmB,wBAAwB,KAAK,QAAQ,EAAE;YAChE,IAAI,kBAAkB,OAAO;QAC/B;IACF;IACA,OAAO;AACT;AAGO,SAAS,uBACd,IAAoB,EACpB,WAAmB;IAEnB,IAAI,gBAAgB,KAAK,IAAI,IAAI,YAAY,UAAU,CAAC,KAAK,IAAI,GAAG,MAAM;QACxE,OAAO;IACT;IAEA,IAAI,KAAK,QAAQ,EAAE;QACjB,OAAO,KAAK,QAAQ,CAAC,IAAI,CAAC,CAAA,QAAS,uBAAuB,OAAO;IACnE;IAEA,OAAO;AACT;AAGO,SAAS,uBACd,KAAuB,EACvB,MAAc,EACd,eAAuB,CAAC;IAExB,KAAK,MAAM,QAAQ,MAAO;QACxB,IAAI,KAAK,EAAE,KAAK,QAAQ;YACtB,OAAO;QACT;QACA,IAAI,KAAK,QAAQ,EAAE;YACjB,MAAM,QAAQ,uBAAuB,KAAK,QAAQ,EAAE,QAAQ,eAAe;YAC3E,IAAI,UAAU,CAAC,GAAG,OAAO;QAC3B;IACF;IACA,OAAO,CAAC;AACV;AAGO,SAAS,4BAA4B,KAAuB;IAIjE,MAAM,SAAmB,EAAE;IAC3B,MAAM,UAAU,IAAI;IACpB,MAAM,YAAY,IAAI;IAEtB,SAAS,SAAS,KAAuB,EAAE,OAAe,EAAE;QAC1D,KAAK,MAAM,QAAQ,MAAO;YACxB,MAAM,cAAc,OAAO,GAAG,KAAK,GAAG,EAAE,KAAK,KAAK,EAAE,GAAG,KAAK,KAAK;YAEjE,0BAA0B;YAC1B,IAAI,QAAQ,GAAG,CAAC,KAAK,EAAE,GAAG;gBACxB,OAAO,IAAI,CAAC,CAAC,cAAc,EAAE,KAAK,EAAE,CAAC,WAAW,EAAE,aAAa;YACjE;YACA,QAAQ,GAAG,CAAC,KAAK,EAAE;YAEnB,4BAA4B;YAC5B,IAAI,UAAU,GAAG,CAAC,KAAK,IAAI,GAAG;gBAC5B,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,KAAK,IAAI,CAAC,WAAW,EAAE,aAAa;YACrE;YACA,UAAU,GAAG,CAAC,KAAK,IAAI;YAEvB,wBAAwB;YACxB,IAAI,CAAC,KAAK,KAAK,EAAE;gBACf,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,aAAa;YAC/C;YACA,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa;YAC9C;YACA,IAAI,CAAC,KAAK,IAAI,EAAE;gBACd,OAAO,IAAI,CAAC,CAAC,gBAAgB,EAAE,aAAa;YAC9C;YAEA,oBAAoB;YACpB,IAAI,KAAK,QAAQ,EAAE;gBACjB,SAAS,KAAK,QAAQ,EAAE;YAC1B;QACF;IACF;IAEA,SAAS;IAET,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAGO,SAAS,wBAAwB,KAAuB;IAC7D,MAAM,YAAY,uBAAuB;IACzC,MAAM,WAAW,IAAI,IAAI,MAAM,GAAG,CAAC,CAAA,OAAQ,KAAK,OAAO,EAAE,MAAM,CAAC;IAChE,MAAM,WAAW,KAAK,GAAG,IAAI,UAAU,GAAG,CAAC,CAAA,OACzC,uBAAuB,OAAO,KAAK,EAAE;IAEvC,MAAM,kBAAkB,6BAA6B;IACrD,MAAM,kBAAkB,mBAAmB;IAE3C,OAAO;QACL,YAAY,UAAU,MAAM;QAC5B,eAAe,MAAM,MAAM;QAC3B,UAAU,SAAS,IAAI;QACvB,UAAU,WAAW;QACrB,iBAAiB,gBAAgB,MAAM;QACvC;QACA,wBAAwB,MAAM,MAAM,CAAC,CAAC,KAAK,OACzC,MAAM,CAAC,KAAK,QAAQ,EAAE,UAAU,CAAC,GAAG,KAClC,MAAM,MAAM;IAClB;AACF;AAGO,MAAM,kBAAkB;IAC7B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF;uCAEe", "debugId": null}}, {"offset": {"line": 4426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/shared/navigation/Breadcrumbs.tsx"], "sourcesContent": ["'use client'\n\nimport { Fragment } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useSidebarStore } from '@/stores/sidebar/useSidebarStore'\nimport { useResponsive } from '@/hooks/useResponsive'\nimport { \n  ChevronRight, \n  Home, \n  MoreHorizontal,\n  ArrowLeft\n} from 'lucide-react'\nimport { navigationUtils } from '@/utils/navigationConfig'\n\ninterface BreadcrumbItem {\n  label: string\n  href: string\n  isActive: boolean\n  icon?: React.ComponentType<any>\n}\n\ninterface BreadcrumbsProps {\n  maxItems?: number\n  showHomeIcon?: boolean\n  showBackButton?: boolean\n  onBack?: () => void\n  className?: string\n}\n\nexport function Breadcrumbs({ \n  maxItems = 4, \n  showHomeIcon = true, \n  showBackButton = false,\n  onBack,\n  className = '' \n}: BreadcrumbsProps) {\n  const pathname = usePathname()\n  const { navigationItems, userType } = useSidebarStore()\n  const { isMobile } = useResponsive()\n\n  // Generate breadcrumbs from current path and navigation structure\n  const generateBreadcrumbs = (): BreadcrumbItem[] => {\n    const pathSegments = pathname.split('/').filter(Boolean)\n    const breadcrumbs: BreadcrumbItem[] = []\n\n    // Add home breadcrumb\n    const homeHref = `/${userType?.replace('_', '-') || ''}`\n    breadcrumbs.push({\n      label: 'Dashboard',\n      href: homeHref,\n      isActive: pathname === homeHref,\n      icon: showHomeIcon ? Home : undefined\n    })\n\n    // Build breadcrumbs from navigation structure\n    let currentPath = ''\n    for (let i = 0; i < pathSegments.length; i++) {\n      currentPath += `/${pathSegments[i]}`\n      \n      // Skip the user type segment (e.g., 'super-admin', 'institute-admin', 'student')\n      if (i === 0 && pathSegments[i].includes('admin')) continue\n      if (i === 0 && pathSegments[i] === 'student') continue\n      \n      // Find matching navigation item\n      const navItem = navigationUtils.findNavigationItemByHref(navigationItems, currentPath)\n      \n      if (navItem) {\n        breadcrumbs.push({\n          label: navItem.label,\n          href: currentPath,\n          isActive: i === pathSegments.length - 1\n        })\n      } else {\n        // Fallback to formatted segment name\n        const label = pathSegments[i]\n          .split('-')\n          .map(word => word.charAt(0).toUpperCase() + word.slice(1))\n          .join(' ')\n        \n        breadcrumbs.push({\n          label,\n          href: currentPath,\n          isActive: i === pathSegments.length - 1\n        })\n      }\n    }\n\n    return breadcrumbs\n  }\n\n  const breadcrumbs = generateBreadcrumbs()\n\n  // Handle breadcrumb overflow\n  const getDisplayBreadcrumbs = () => {\n    if (breadcrumbs.length <= maxItems) {\n      return breadcrumbs\n    }\n\n    const firstItem = breadcrumbs[0]\n    const lastItems = breadcrumbs.slice(-2) // Always show last 2 items\n    const middleItems = breadcrumbs.slice(1, -2)\n\n    if (middleItems.length === 0) {\n      return breadcrumbs\n    }\n\n    return [\n      firstItem,\n      { label: '...', href: '#', isActive: false },\n      ...lastItems\n    ]\n  }\n\n  const displayBreadcrumbs = getDisplayBreadcrumbs()\n\n  // Mobile breadcrumbs (simplified)\n  if (isMobile) {\n    const currentItem = breadcrumbs[breadcrumbs.length - 1]\n    const parentItem = breadcrumbs.length > 1 ? breadcrumbs[breadcrumbs.length - 2] : null\n\n    return (\n      <div className={`flex items-center space-x-2 ${className}`}>\n        {showBackButton && onBack && (\n          <button\n            onClick={onBack}\n            className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n          >\n            <ArrowLeft className=\"w-4 h-4 text-gray-600\" />\n          </button>\n        )}\n        \n        {parentItem && (\n          <>\n            <Link\n              href={parentItem.href}\n              className=\"text-sm text-gray-600 hover:text-gray-900 transition-colors truncate max-w-24\"\n            >\n              {parentItem.label}\n            </Link>\n            <ChevronRight className=\"w-3 h-3 text-gray-400 flex-shrink-0\" />\n          </>\n        )}\n        \n        <span className=\"text-sm font-medium text-gray-900 truncate\">\n          {currentItem?.label}\n        </span>\n      </div>\n    )\n  }\n\n  // Desktop breadcrumbs\n  return (\n    <nav className={`flex items-center space-x-1 ${className}`} aria-label=\"Breadcrumb\">\n      <ol className=\"flex items-center space-x-1\">\n        {displayBreadcrumbs.map((item, index) => (\n          <li key={index} className=\"flex items-center\">\n            {index > 0 && (\n              <ChevronRight className=\"w-4 h-4 text-gray-400 mx-2 flex-shrink-0\" />\n            )}\n            \n            {item.label === '...' ? (\n              <div className=\"flex items-center space-x-1\">\n                <MoreHorizontal className=\"w-4 h-4 text-gray-400\" />\n              </div>\n            ) : item.isActive ? (\n              <span className=\"flex items-center space-x-1 text-sm font-medium text-gray-900\">\n                {item.icon && <item.icon className=\"w-4 h-4\" />}\n                <span>{item.label}</span>\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors\"\n              >\n                {item.icon && <item.icon className=\"w-4 h-4\" />}\n                <span>{item.label}</span>\n              </Link>\n            )}\n          </li>\n        ))}\n      </ol>\n    </nav>\n  )\n}\n\n// Breadcrumb separator component\nexport function BreadcrumbSeparator({ className = '' }: { className?: string }) {\n  return <ChevronRight className={`w-4 h-4 text-gray-400 ${className}`} />\n}\n\n// Custom breadcrumb component for manual breadcrumbs\ninterface CustomBreadcrumbsProps {\n  items: Array<{\n    label: string\n    href?: string\n    isActive?: boolean\n    icon?: React.ComponentType<any>\n  }>\n  className?: string\n}\n\nexport function CustomBreadcrumbs({ items, className = '' }: CustomBreadcrumbsProps) {\n  const { isMobile } = useResponsive()\n\n  if (isMobile && items.length > 2) {\n    const currentItem = items[items.length - 1]\n    const parentItem = items[items.length - 2]\n\n    return (\n      <div className={`flex items-center space-x-2 ${className}`}>\n        {parentItem.href ? (\n          <Link\n            href={parentItem.href}\n            className=\"text-sm text-gray-600 hover:text-gray-900 transition-colors truncate max-w-24\"\n          >\n            {parentItem.label}\n          </Link>\n        ) : (\n          <span className=\"text-sm text-gray-600 truncate max-w-24\">\n            {parentItem.label}\n          </span>\n        )}\n        <ChevronRight className=\"w-3 h-3 text-gray-400 flex-shrink-0\" />\n        <span className=\"text-sm font-medium text-gray-900 truncate\">\n          {currentItem.label}\n        </span>\n      </div>\n    )\n  }\n\n  return (\n    <nav className={`flex items-center space-x-1 ${className}`} aria-label=\"Breadcrumb\">\n      <ol className=\"flex items-center space-x-1\">\n        {items.map((item, index) => (\n          <li key={index} className=\"flex items-center\">\n            {index > 0 && (\n              <ChevronRight className=\"w-4 h-4 text-gray-400 mx-2 flex-shrink-0\" />\n            )}\n            \n            {item.isActive || !item.href ? (\n              <span className=\"flex items-center space-x-1 text-sm font-medium text-gray-900\">\n                {item.icon && <item.icon className=\"w-4 h-4\" />}\n                <span>{item.label}</span>\n              </span>\n            ) : (\n              <Link\n                href={item.href}\n                className=\"flex items-center space-x-1 text-sm text-gray-600 hover:text-gray-900 transition-colors\"\n              >\n                {item.icon && <item.icon className=\"w-4 h-4\" />}\n                <span>{item.label}</span>\n              </Link>\n            )}\n          </li>\n        ))}\n      </ol>\n    </nav>\n  )\n}\n\nexport default Breadcrumbs\n"], "names": [], "mappings": ";;;;;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAMA;AAbA;;;;;;;;AA8BO,SAAS,YAAY,EAC1B,WAAW,CAAC,EACZ,eAAe,IAAI,EACnB,iBAAiB,KAAK,EACtB,MAAM,EACN,YAAY,EAAE,EACG;IACjB,MAAM,WAAW,CAAA,GAAA,uOAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IACpD,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;IAEjC,kEAAkE;IAClE,MAAM,sBAAsB;QAC1B,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QAChD,MAAM,cAAgC,EAAE;QAExC,sBAAsB;QACtB,MAAM,WAAW,CAAC,CAAC,EAAE,UAAU,QAAQ,KAAK,QAAQ,IAAI;QACxD,YAAY,IAAI,CAAC;YACf,OAAO;YACP,MAAM;YACN,UAAU,aAAa;YACvB,MAAM,eAAe,uRAAA,CAAA,OAAI,GAAG;QAC9B;QAEA,8CAA8C;QAC9C,IAAI,cAAc;QAClB,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,eAAe,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,EAAE;YAEpC,iFAAiF;YACjF,IAAI,MAAM,KAAK,YAAY,CAAC,EAAE,CAAC,QAAQ,CAAC,UAAU;YAClD,IAAI,MAAM,KAAK,YAAY,CAAC,EAAE,KAAK,WAAW;YAE9C,gCAAgC;YAChC,MAAM,UAAU,oJAAA,CAAA,kBAAe,CAAC,wBAAwB,CAAC,iBAAiB;YAE1E,IAAI,SAAS;gBACX,YAAY,IAAI,CAAC;oBACf,OAAO,QAAQ,KAAK;oBACpB,MAAM;oBACN,UAAU,MAAM,aAAa,MAAM,GAAG;gBACxC;YACF,OAAO;gBACL,qCAAqC;gBACrC,MAAM,QAAQ,YAAY,CAAC,EAAE,CAC1B,KAAK,CAAC,KACN,GAAG,CAAC,CAAA,OAAQ,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,KAAK,CAAC,IACtD,IAAI,CAAC;gBAER,YAAY,IAAI,CAAC;oBACf;oBACA,MAAM;oBACN,UAAU,MAAM,aAAa,MAAM,GAAG;gBACxC;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;IAEpB,6BAA6B;IAC7B,MAAM,wBAAwB;QAC5B,IAAI,YAAY,MAAM,IAAI,UAAU;YAClC,OAAO;QACT;QAEA,MAAM,YAAY,WAAW,CAAC,EAAE;QAChC,MAAM,YAAY,YAAY,KAAK,CAAC,CAAC,GAAG,2BAA2B;;QACnE,MAAM,cAAc,YAAY,KAAK,CAAC,GAAG,CAAC;QAE1C,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B,OAAO;QACT;QAEA,OAAO;YACL;YACA;gBAAE,OAAO;gBAAO,MAAM;gBAAK,UAAU;YAAM;eACxC;SACJ;IACH;IAEA,MAAM,qBAAqB;IAE3B,kCAAkC;IAClC,IAAI,UAAU;QACZ,MAAM,cAAc,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE;QACvD,MAAM,aAAa,YAAY,MAAM,GAAG,IAAI,WAAW,CAAC,YAAY,MAAM,GAAG,EAAE,GAAG;QAElF,qBACE,mVAAC;YAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;;gBACvD,kBAAkB,wBACjB,mVAAC;oBACC,SAAS;oBACT,WAAU;8BAEV,cAAA,mVAAC,oSAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;gBAIxB,4BACC;;sCACE,mVAAC,iQAAA,CAAA,UAAI;4BACH,MAAM,WAAW,IAAI;4BACrB,WAAU;sCAET,WAAW,KAAK;;;;;;sCAEnB,mVAAC,0SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;8BAI5B,mVAAC;oBAAK,WAAU;8BACb,aAAa;;;;;;;;;;;;IAItB;IAEA,sBAAsB;IACtB,qBACE,mVAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;QAAE,cAAW;kBACrE,cAAA,mVAAC;YAAG,WAAU;sBACX,mBAAmB,GAAG,CAAC,CAAC,MAAM,sBAC7B,mVAAC;oBAAe,WAAU;;wBACvB,QAAQ,mBACP,mVAAC,0SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAGzB,KAAK,KAAK,KAAK,sBACd,mVAAC;4BAAI,WAAU;sCACb,cAAA,mVAAC,oSAAA,CAAA,iBAAc;gCAAC,WAAU;;;;;;;;;;mCAE1B,KAAK,QAAQ,iBACf,mVAAC;4BAAK,WAAU;;gCACb,KAAK,IAAI,kBAAI,mVAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACnC,mVAAC;8CAAM,KAAK,KAAK;;;;;;;;;;;iDAGnB,mVAAC,iQAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;;gCAET,KAAK,IAAI,kBAAI,mVAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACnC,mVAAC;8CAAM,KAAK,KAAK;;;;;;;;;;;;;mBApBd;;;;;;;;;;;;;;;AA4BnB;AAGO,SAAS,oBAAoB,EAAE,YAAY,EAAE,EAA0B;IAC5E,qBAAO,mVAAC,0SAAA,CAAA,eAAY;QAAC,WAAW,CAAC,sBAAsB,EAAE,WAAW;;;;;;AACtE;AAaO,SAAS,kBAAkB,EAAE,KAAK,EAAE,YAAY,EAAE,EAA0B;IACjF,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;IAEjC,IAAI,YAAY,MAAM,MAAM,GAAG,GAAG;QAChC,MAAM,cAAc,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAC3C,MAAM,aAAa,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAE1C,qBACE,mVAAC;YAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;;gBACvD,WAAW,IAAI,iBACd,mVAAC,iQAAA,CAAA,UAAI;oBACH,MAAM,WAAW,IAAI;oBACrB,WAAU;8BAET,WAAW,KAAK;;;;;yCAGnB,mVAAC;oBAAK,WAAU;8BACb,WAAW,KAAK;;;;;;8BAGrB,mVAAC,0SAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;8BACxB,mVAAC;oBAAK,WAAU;8BACb,YAAY,KAAK;;;;;;;;;;;;IAI1B;IAEA,qBACE,mVAAC;QAAI,WAAW,CAAC,4BAA4B,EAAE,WAAW;QAAE,cAAW;kBACrE,cAAA,mVAAC;YAAG,WAAU;sBACX,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,mVAAC;oBAAe,WAAU;;wBACvB,QAAQ,mBACP,mVAAC,0SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;wBAGzB,KAAK,QAAQ,IAAI,CAAC,KAAK,IAAI,iBAC1B,mVAAC;4BAAK,WAAU;;gCACb,KAAK,IAAI,kBAAI,mVAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACnC,mVAAC;8CAAM,KAAK,KAAK;;;;;;;;;;;iDAGnB,mVAAC,iQAAA,CAAA,UAAI;4BACH,MAAM,KAAK,IAAI;4BACf,WAAU;;gCAET,KAAK,IAAI,kBAAI,mVAAC,KAAK,IAAI;oCAAC,WAAU;;;;;;8CACnC,mVAAC;8CAAM,KAAK,KAAK;;;;;;;;;;;;;mBAhBd;;;;;;;;;;;;;;;AAwBnB;uCAEe", "debugId": null}}, {"offset": {"line": 4806, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/shared/navigation/NavigationSearch.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useEffect, useRef } from 'react'\nimport Link from 'next/link'\nimport { useSidebarStore } from '@/stores/sidebar/useSidebarStore'\nimport { useResponsive } from '@/hooks/useResponsive'\nimport { \n  Search, \n  X, \n  ArrowRight,\n  Clock,\n  Star,\n  Zap,\n  Command\n} from 'lucide-react'\nimport * as Icons from 'lucide-react'\nimport { navigationUtils } from '@/utils/navigationConfig'\n\ninterface NavigationSearchProps {\n  placeholder?: string\n  showShortcut?: boolean\n  onItemSelect?: (item: any) => void\n  className?: string\n}\n\nexport function NavigationSearch({ \n  placeholder = \"Search navigation...\", \n  showShortcut = true,\n  onItemSelect,\n  className = '' \n}: NavigationSearchProps) {\n  const { \n    navigationItems, \n    recentItems, \n    favoriteItems,\n    addToRecent \n  } = useSidebarStore()\n  const { isMobile } = useResponsive()\n  \n  const [isOpen, setIsOpen] = useState(false)\n  const [query, setQuery] = useState('')\n  const [selectedIndex, setSelectedIndex] = useState(0)\n  const [searchResults, setSearchResults] = useState<any[]>([])\n  \n  const searchRef = useRef<HTMLDivElement>(null)\n  const inputRef = useRef<HTMLInputElement>(null)\n\n  // Search navigation items\n  useEffect(() => {\n    if (query.trim()) {\n      const results = navigationUtils.searchNavigationItems(navigationItems, query)\n      setSearchResults(results)\n      setSelectedIndex(0)\n    } else {\n      setSearchResults([])\n    }\n  }, [query, navigationItems])\n\n  // Handle keyboard shortcuts\n  useEffect(() => {\n    const handleKeyDown = (e: KeyboardEvent) => {\n      // Cmd/Ctrl + K to open search\n      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {\n        e.preventDefault()\n        setIsOpen(true)\n        setTimeout(() => inputRef.current?.focus(), 100)\n      }\n      \n      // Escape to close\n      if (e.key === 'Escape') {\n        setIsOpen(false)\n        setQuery('')\n      }\n      \n      // Arrow navigation\n      if (isOpen && searchResults.length > 0) {\n        if (e.key === 'ArrowDown') {\n          e.preventDefault()\n          setSelectedIndex(prev => \n            prev < searchResults.length - 1 ? prev + 1 : 0\n          )\n        } else if (e.key === 'ArrowUp') {\n          e.preventDefault()\n          setSelectedIndex(prev => \n            prev > 0 ? prev - 1 : searchResults.length - 1\n          )\n        } else if (e.key === 'Enter') {\n          e.preventDefault()\n          const selectedItem = searchResults[selectedIndex]\n          if (selectedItem) {\n            handleItemSelect(selectedItem)\n          }\n        }\n      }\n    }\n\n    document.addEventListener('keydown', handleKeyDown)\n    return () => document.removeEventListener('keydown', handleKeyDown)\n  }, [isOpen, searchResults, selectedIndex])\n\n  // Click outside to close\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setIsOpen(false)\n      }\n    }\n\n    if (isOpen) {\n      document.addEventListener('mousedown', handleClickOutside)\n      return () => document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [isOpen])\n\n  const handleItemSelect = (item: any) => {\n    addToRecent(item)\n    onItemSelect?.(item)\n    setIsOpen(false)\n    setQuery('')\n  }\n\n  const getQuickActions = () => {\n    const actions = []\n    \n    // Recent items\n    if (recentItems.length > 0) {\n      actions.push({\n        category: 'Recent',\n        icon: Clock,\n        items: recentItems.slice(0, 3)\n      })\n    }\n    \n    // Favorite items\n    if (favoriteItems.length > 0) {\n      const favoriteNavItems = favoriteItems\n        .map(id => navigationUtils.findNavigationItem(navigationItems, id))\n        .filter(Boolean)\n        .slice(0, 3)\n      \n      if (favoriteNavItems.length > 0) {\n        actions.push({\n          category: 'Favorites',\n          icon: Star,\n          items: favoriteNavItems\n        })\n      }\n    }\n    \n    // Quick access items\n    const quickAccessItems = navigationItems\n      .filter(item => item.badge && item.badge > 0)\n      .slice(0, 3)\n    \n    if (quickAccessItems.length > 0) {\n      actions.push({\n        category: 'Quick Access',\n        icon: Zap,\n        items: quickAccessItems\n      })\n    }\n    \n    return actions\n  }\n\n  const quickActions = getQuickActions()\n\n  return (\n    <div ref={searchRef} className={`relative ${className}`}>\n      {/* Search Input */}\n      <div className=\"relative\">\n        <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n          <Search className=\"w-4 h-4 text-gray-400\" />\n        </div>\n        <input\n          ref={inputRef}\n          type=\"text\"\n          value={query}\n          onChange={(e) => setQuery(e.target.value)}\n          onFocus={() => setIsOpen(true)}\n          placeholder={placeholder}\n          className={`w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm ${\n            isMobile ? 'text-base' : ''\n          }`}\n        />\n        {showShortcut && !isMobile && (\n          <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none\">\n            <div className=\"flex items-center space-x-1 text-xs text-gray-400\">\n              <Command className=\"w-3 h-3\" />\n              <span>K</span>\n            </div>\n          </div>\n        )}\n      </div>\n\n      {/* Search Results Dropdown */}\n      {isOpen && (\n        <div className=\"absolute top-full left-0 right-0 mt-2 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto\">\n          {query.trim() ? (\n            // Search Results\n            <div>\n              {searchResults.length > 0 ? (\n                <div className=\"p-2\">\n                  <div className=\"text-xs font-medium text-gray-500 uppercase tracking-wider px-2 py-1 mb-2\">\n                    Search Results ({searchResults.length})\n                  </div>\n                  {searchResults.map((item, index) => (\n                    <SearchResultItem\n                      key={item.id}\n                      item={item}\n                      isSelected={index === selectedIndex}\n                      onClick={() => handleItemSelect(item)}\n                    />\n                  ))}\n                </div>\n              ) : (\n                <div className=\"p-4 text-center text-gray-500\">\n                  <Search className=\"w-8 h-8 mx-auto mb-2 text-gray-300\" />\n                  <div className=\"text-sm\">No results found for \"{query}\"</div>\n                </div>\n              )}\n            </div>\n          ) : (\n            // Quick Actions\n            <div className=\"p-2\">\n              {quickActions.length > 0 ? (\n                quickActions.map((section, sectionIndex) => (\n                  <div key={sectionIndex} className=\"mb-4 last:mb-0\">\n                    <div className=\"flex items-center space-x-2 px-2 py-1 mb-2\">\n                      <section.icon className=\"w-4 h-4 text-gray-400\" />\n                      <span className=\"text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                        {section.category}\n                      </span>\n                    </div>\n                    {section.items.map((item: any, itemIndex: number) => (\n                      <SearchResultItem\n                        key={item.id}\n                        item={item}\n                        isSelected={false}\n                        onClick={() => handleItemSelect(item)}\n                      />\n                    ))}\n                  </div>\n                ))\n              ) : (\n                <div className=\"p-4 text-center text-gray-500\">\n                  <div className=\"text-sm\">Start typing to search navigation...</div>\n                </div>\n              )}\n            </div>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n\n// Search result item component\ninterface SearchResultItemProps {\n  item: any\n  isSelected: boolean\n  onClick: () => void\n}\n\nfunction SearchResultItem({ item, isSelected, onClick }: SearchResultItemProps) {\n  const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>\n\n  return (\n    <button\n      onClick={onClick}\n      className={`w-full flex items-center space-x-3 px-2 py-2 rounded-lg text-left transition-colors ${\n        isSelected ? 'bg-blue-50 text-blue-700' : 'hover:bg-gray-50'\n      }`}\n    >\n      {/* Icon */}\n      <div className={`flex-shrink-0 ${\n        isSelected ? 'text-blue-600' : 'text-gray-400'\n      }`}>\n        {IconComponent && <IconComponent className=\"w-4 h-4\" />}\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 min-w-0\">\n        <div className=\"text-sm font-medium text-gray-900 truncate\">\n          {item.label}\n        </div>\n        {item.description && (\n          <div className=\"text-xs text-gray-500 truncate\">\n            {item.description}\n          </div>\n        )}\n      </div>\n\n      {/* Badge */}\n      {item.badge && item.badge > 0 && (\n        <span className=\"flex-shrink-0 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full\">\n          {item.badge > 9 ? '9+' : item.badge}\n        </span>\n      )}\n\n      {/* Arrow */}\n      <ArrowRight className=\"w-3 h-3 text-gray-400 flex-shrink-0\" />\n    </button>\n  )\n}\n\n// Global search modal for mobile\nexport function GlobalSearchModal() {\n  const [isOpen, setIsOpen] = useState(false)\n  const { isMobile } = useResponsive()\n\n  if (!isMobile) return null\n\n  return (\n    <>\n      {/* Search Button */}\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n      >\n        <Search className=\"w-5 h-5 text-gray-600\" />\n      </button>\n\n      {/* Modal */}\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 bg-black bg-opacity-50\">\n          <div className=\"bg-white h-full\">\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n              <h2 className=\"text-lg font-medium text-gray-900\">Search</h2>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n              >\n                <X className=\"w-5 h-5 text-gray-500\" />\n              </button>\n            </div>\n\n            {/* Search Content */}\n            <div className=\"p-4\">\n              <NavigationSearch\n                placeholder=\"Search navigation...\"\n                showShortcut={false}\n                onItemSelect={() => setIsOpen(false)}\n              />\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n\nexport default NavigationSearch\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAhBA;;;;;;;;AAyBO,SAAS,iBAAiB,EAC/B,cAAc,sBAAsB,EACpC,eAAe,IAAI,EACnB,YAAY,EACZ,YAAY,EAAE,EACQ;IACtB,MAAM,EACJ,eAAe,EACf,WAAW,EACX,aAAa,EACb,WAAW,EACZ,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAClB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;IAEjC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAE5D,MAAM,YAAY,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAkB;IACzC,MAAM,WAAW,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,0BAA0B;IAC1B,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,MAAM,IAAI,IAAI;YAChB,MAAM,UAAU,oJAAA,CAAA,kBAAe,CAAC,qBAAqB,CAAC,iBAAiB;YACvE,iBAAiB;YACjB,iBAAiB;QACnB,OAAO;YACL,iBAAiB,EAAE;QACrB;IACF,GAAG;QAAC;QAAO;KAAgB;IAE3B,4BAA4B;IAC5B,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,gBAAgB,CAAC;YACrB,8BAA8B;YAC9B,IAAI,CAAC,EAAE,OAAO,IAAI,EAAE,OAAO,KAAK,EAAE,GAAG,KAAK,KAAK;gBAC7C,EAAE,cAAc;gBAChB,UAAU;gBACV,WAAW,IAAM,SAAS,OAAO,EAAE,SAAS;YAC9C;YAEA,kBAAkB;YAClB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,UAAU;gBACV,SAAS;YACX;YAEA,mBAAmB;YACnB,IAAI,UAAU,cAAc,MAAM,GAAG,GAAG;gBACtC,IAAI,EAAE,GAAG,KAAK,aAAa;oBACzB,EAAE,cAAc;oBAChB,iBAAiB,CAAA,OACf,OAAO,cAAc,MAAM,GAAG,IAAI,OAAO,IAAI;gBAEjD,OAAO,IAAI,EAAE,GAAG,KAAK,WAAW;oBAC9B,EAAE,cAAc;oBAChB,iBAAiB,CAAA,OACf,OAAO,IAAI,OAAO,IAAI,cAAc,MAAM,GAAG;gBAEjD,OAAO,IAAI,EAAE,GAAG,KAAK,SAAS;oBAC5B,EAAE,cAAc;oBAChB,MAAM,eAAe,aAAa,CAAC,cAAc;oBACjD,IAAI,cAAc;wBAChB,iBAAiB;oBACnB;gBACF;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,WAAW;QACrC,OAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;IACvD,GAAG;QAAC;QAAQ;QAAe;KAAc;IAEzC,yBAAyB;IACzB,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC1E,UAAU;YACZ;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,aAAa;YACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;QACzD;IACF,GAAG;QAAC;KAAO;IAEX,MAAM,mBAAmB,CAAC;QACxB,YAAY;QACZ,eAAe;QACf,UAAU;QACV,SAAS;IACX;IAEA,MAAM,kBAAkB;QACtB,MAAM,UAAU,EAAE;QAElB,eAAe;QACf,IAAI,YAAY,MAAM,GAAG,GAAG;YAC1B,QAAQ,IAAI,CAAC;gBACX,UAAU;gBACV,MAAM,wRAAA,CAAA,QAAK;gBACX,OAAO,YAAY,KAAK,CAAC,GAAG;YAC9B;QACF;QAEA,iBAAiB;QACjB,IAAI,cAAc,MAAM,GAAG,GAAG;YAC5B,MAAM,mBAAmB,cACtB,GAAG,CAAC,CAAA,KAAM,oJAAA,CAAA,kBAAe,CAAC,kBAAkB,CAAC,iBAAiB,KAC9D,MAAM,CAAC,SACP,KAAK,CAAC,GAAG;YAEZ,IAAI,iBAAiB,MAAM,GAAG,GAAG;gBAC/B,QAAQ,IAAI,CAAC;oBACX,UAAU;oBACV,MAAM,sRAAA,CAAA,OAAI;oBACV,OAAO;gBACT;YACF;QACF;QAEA,qBAAqB;QACrB,MAAM,mBAAmB,gBACtB,MAAM,CAAC,CAAA,OAAQ,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,GAC1C,KAAK,CAAC,GAAG;QAEZ,IAAI,iBAAiB,MAAM,GAAG,GAAG;YAC/B,QAAQ,IAAI,CAAC;gBACX,UAAU;gBACV,MAAM,oRAAA,CAAA,MAAG;gBACT,OAAO;YACT;QACF;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,qBACE,mVAAC;QAAI,KAAK;QAAW,WAAW,CAAC,SAAS,EAAE,WAAW;;0BAErD,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAI,WAAU;kCACb,cAAA,mVAAC,0RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;kCAEpB,mVAAC;wBACC,KAAK;wBACL,MAAK;wBACL,OAAO;wBACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;wBACxC,SAAS,IAAM,UAAU;wBACzB,aAAa;wBACb,WAAW,CAAC,2HAA2H,EACrI,WAAW,cAAc,IACzB;;;;;;oBAEH,gBAAgB,CAAC,0BAChB,mVAAC;wBAAI,WAAU;kCACb,cAAA,mVAAC;4BAAI,WAAU;;8CACb,mVAAC,4RAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;8CACnB,mVAAC;8CAAK;;;;;;;;;;;;;;;;;;;;;;;YAOb,wBACC,mVAAC;gBAAI,WAAU;0BACZ,MAAM,IAAI,KACT,iBAAiB;8BACjB,mVAAC;8BACE,cAAc,MAAM,GAAG,kBACtB,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;;oCAA4E;oCACxE,cAAc,MAAM;oCAAC;;;;;;;4BAEvC,cAAc,GAAG,CAAC,CAAC,MAAM,sBACxB,mVAAC;oCAEC,MAAM;oCACN,YAAY,UAAU;oCACtB,SAAS,IAAM,iBAAiB;mCAH3B,KAAK,EAAE;;;;;;;;;;6CAQlB,mVAAC;wBAAI,WAAU;;0CACb,mVAAC,0RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,mVAAC;gCAAI,WAAU;;oCAAU;oCAAuB;oCAAM;;;;;;;;;;;;;;;;;2BAK5D,gBAAgB;8BAChB,mVAAC;oBAAI,WAAU;8BACZ,aAAa,MAAM,GAAG,IACrB,aAAa,GAAG,CAAC,CAAC,SAAS,6BACzB,mVAAC;4BAAuB,WAAU;;8CAChC,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,QAAQ,IAAI;4CAAC,WAAU;;;;;;sDACxB,mVAAC;4CAAK,WAAU;sDACb,QAAQ,QAAQ;;;;;;;;;;;;gCAGpB,QAAQ,KAAK,CAAC,GAAG,CAAC,CAAC,MAAW,0BAC7B,mVAAC;wCAEC,MAAM;wCACN,YAAY;wCACZ,SAAS,IAAM,iBAAiB;uCAH3B,KAAK,EAAE;;;;;;2BATR;;;;kDAkBZ,mVAAC;wBAAI,WAAU;kCACb,cAAA,mVAAC;4BAAI,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3C;AASA,SAAS,iBAAiB,EAAE,IAAI,EAAE,UAAU,EAAE,OAAO,EAAyB;IAC5E,MAAM,gBAAgB,AAAC,qPAAa,CAAC,KAAK,IAAI,CAAC;IAE/C,qBACE,mVAAC;QACC,SAAS;QACT,WAAW,CAAC,oFAAoF,EAC9F,aAAa,6BAA6B,oBAC1C;;0BAGF,mVAAC;gBAAI,WAAW,CAAC,cAAc,EAC7B,aAAa,kBAAkB,iBAC/B;0BACC,+BAAiB,mVAAC;oBAAc,WAAU;;;;;;;;;;;0BAI7C,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAI,WAAU;kCACZ,KAAK,KAAK;;;;;;oBAEZ,KAAK,WAAW,kBACf,mVAAC;wBAAI,WAAU;kCACZ,KAAK,WAAW;;;;;;;;;;;;YAMtB,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,mVAAC;gBAAK,WAAU;0BACb,KAAK,KAAK,GAAG,IAAI,OAAO,KAAK,KAAK;;;;;;0BAKvC,mVAAC,sSAAA,CAAA,aAAU;gBAAC,WAAU;;;;;;;;;;;;AAG5B;AAGO,SAAS;IACd,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;IAEjC,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE;;0BAEE,mVAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;0BAEV,cAAA,mVAAC,0RAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;;;;;;YAInB,wBACC,mVAAC;gBAAI,WAAU;0BACb,cAAA,mVAAC;oBAAI,WAAU;;sCAEb,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCAAG,WAAU;8CAAoC;;;;;;8CAClD,mVAAC;oCACC,SAAS,IAAM,UAAU;oCACzB,WAAU;8CAEV,cAAA,mVAAC,gRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,mVAAC;4BAAI,WAAU;sCACb,cAAA,mVAAC;gCACC,aAAY;gCACZ,cAAc;gCACd,cAAc,IAAM,UAAU;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;uCAEe", "debugId": null}}, {"offset": {"line": 5318, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/NotificationDropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport { useSidebarStore } from '@/stores/sidebar/useSidebarStore'\nimport { \n  Bell, \n  X, \n  CheckCheck,\n  Info,\n  AlertTriangle,\n  AlertCircle,\n  CheckCircle\n} from 'lucide-react'\n\ninterface NotificationDropdownProps {\n  notifications: Array<{\n    id: string\n    title: string\n    message: string\n    type: 'info' | 'warning' | 'error' | 'success'\n    timestamp: string\n    isRead: boolean\n  }>\n  onClose: () => void\n}\n\nexport function NotificationDropdown({ notifications, onClose }: NotificationDropdownProps) {\n  const {\n    markNotificationAsRead,\n    markAllNotificationsAsRead,\n    removeNotification,\n    clearNotifications\n  } = useSidebarStore()\n  \n  const dropdownRef = useRef<HTMLDivElement>(null)\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        onClose()\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [onClose])\n\n  const getNotificationIcon = (type: string) => {\n    switch (type) {\n      case 'info':\n        return <Info className=\"w-4 h-4 text-blue-500\" />\n      case 'warning':\n        return <AlertTriangle className=\"w-4 h-4 text-yellow-500\" />\n      case 'error':\n        return <AlertCircle className=\"w-4 h-4 text-red-500\" />\n      case 'success':\n        return <CheckCircle className=\"w-4 h-4 text-green-500\" />\n      default:\n        return <Bell className=\"w-4 h-4 text-gray-500\" />\n    }\n  }\n\n  const formatTimestamp = (timestamp: string) => {\n    const date = new Date(timestamp)\n    const now = new Date()\n    const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))\n    \n    if (diffInMinutes < 1) return 'Just now'\n    if (diffInMinutes < 60) return `${diffInMinutes}m ago`\n    if (diffInMinutes < 1440) return `${Math.floor(diffInMinutes / 60)}h ago`\n    return `${Math.floor(diffInMinutes / 1440)}d ago`\n  }\n\n  const handleNotificationClick = (notificationId: string, isRead: boolean) => {\n    if (!isRead) {\n      markNotificationAsRead(notificationId)\n    }\n  }\n\n  const handleMarkAllAsRead = () => {\n    markAllNotificationsAsRead()\n  }\n\n  const handleClearAll = () => {\n    clearNotifications()\n  }\n\n  return (\n    <div \n      ref={dropdownRef}\n      className=\"absolute right-0 top-full mt-2 w-80 bg-white border border-gray-200 rounded-lg shadow-lg z-50\"\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n        <h3 className=\"text-lg font-medium text-gray-900\">Notifications</h3>\n        <button\n          onClick={onClose}\n          className=\"p-1 hover:bg-gray-100 rounded transition-colors\"\n        >\n          <X className=\"w-4 h-4 text-gray-400\" />\n        </button>\n      </div>\n\n      {/* Actions */}\n      {notifications.length > 0 && (\n        <div className=\"flex items-center justify-between p-3 border-b border-gray-200 bg-gray-50\">\n          <button\n            onClick={handleMarkAllAsRead}\n            className=\"flex items-center text-sm text-blue-600 hover:text-blue-700 transition-colors\"\n          >\n            <CheckCheck className=\"w-4 h-4 mr-1\" />\n            Mark all as read\n          </button>\n          <button\n            onClick={handleClearAll}\n            className=\"text-sm text-red-600 hover:text-red-700 transition-colors\"\n          >\n            Clear all\n          </button>\n        </div>\n      )}\n\n      {/* Notifications List */}\n      <div className=\"max-h-96 overflow-y-auto\">\n        {notifications.length === 0 ? (\n          <div className=\"p-6 text-center\">\n            <Bell className=\"w-8 h-8 text-gray-300 mx-auto mb-2\" />\n            <p className=\"text-sm text-gray-500\">No notifications</p>\n          </div>\n        ) : (\n          <div className=\"divide-y divide-gray-200\">\n            {notifications.map((notification) => (\n              <div\n                key={notification.id}\n                onClick={() => handleNotificationClick(notification.id, notification.isRead)}\n                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${\n                  !notification.isRead ? 'bg-blue-50' : ''\n                }`}\n              >\n                <div className=\"flex items-start space-x-3\">\n                  <div className=\"flex-shrink-0 mt-0.5\">\n                    {getNotificationIcon(notification.type)}\n                  </div>\n                  \n                  <div className=\"flex-1 min-w-0\">\n                    <div className=\"flex items-center justify-between\">\n                      <p className={`text-sm font-medium ${\n                        !notification.isRead ? 'text-gray-900' : 'text-gray-700'\n                      }`}>\n                        {notification.title}\n                      </p>\n                      <button\n                        onClick={(e) => {\n                          e.stopPropagation()\n                          removeNotification(notification.id)\n                        }}\n                        className=\"p-1 hover:bg-gray-200 rounded transition-colors\"\n                      >\n                        <X className=\"w-3 h-3 text-gray-400\" />\n                      </button>\n                    </div>\n                    \n                    <p className=\"text-sm text-gray-600 mt-1\">\n                      {notification.message}\n                    </p>\n                    \n                    <div className=\"flex items-center justify-between mt-2\">\n                      <span className=\"text-xs text-gray-500\">\n                        {formatTimestamp(notification.timestamp)}\n                      </span>\n                      {!notification.isRead && (\n                        <div className=\"w-2 h-2 bg-blue-600 rounded-full\"></div>\n                      )}\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  )\n}\n\nexport default NotificationDropdown\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAJA;;;;;AA0BO,SAAS,qBAAqB,EAAE,aAAa,EAAE,OAAO,EAA6B;IACxF,MAAM,EACJ,sBAAsB,EACtB,0BAA0B,EAC1B,kBAAkB,EAClB,kBAAkB,EACnB,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,cAAc,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG;QAAC;KAAQ;IAEZ,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;gBACH,qBAAO,mVAAC,sRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;YACzB,KAAK;gBACH,qBAAO,mVAAC,4SAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC,KAAK;gBACH,qBAAO,mVAAC,wSAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,mVAAC,+SAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC;gBACE,qBAAO,mVAAC,sRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;QAC3B;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,OAAO,IAAI,KAAK;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,KAAK,OAAO,EAAE,IAAI,CAAC,OAAO,EAAE;QAE9E,IAAI,gBAAgB,GAAG,OAAO;QAC9B,IAAI,gBAAgB,IAAI,OAAO,GAAG,cAAc,KAAK,CAAC;QACtD,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;QACzE,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;IACnD;IAEA,MAAM,0BAA0B,CAAC,gBAAwB;QACvD,IAAI,CAAC,QAAQ;YACX,uBAAuB;QACzB;IACF;IAEA,MAAM,sBAAsB;QAC1B;IACF;IAEA,MAAM,iBAAiB;QACrB;IACF;IAEA,qBACE,mVAAC;QACC,KAAK;QACL,WAAU;;0BAGV,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAG,WAAU;kCAAoC;;;;;;kCAClD,mVAAC;wBACC,SAAS;wBACT,WAAU;kCAEV,cAAA,mVAAC,gRAAA,CAAA,IAAC;4BAAC,WAAU;;;;;;;;;;;;;;;;;YAKhB,cAAc,MAAM,GAAG,mBACtB,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBACC,SAAS;wBACT,WAAU;;0CAEV,mVAAC,sSAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGzC,mVAAC;wBACC,SAAS;wBACT,WAAU;kCACX;;;;;;;;;;;;0BAOL,mVAAC;gBAAI,WAAU;0BACZ,cAAc,MAAM,KAAK,kBACxB,mVAAC;oBAAI,WAAU;;sCACb,mVAAC,sRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,mVAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;yCAGvC,mVAAC;oBAAI,WAAU;8BACZ,cAAc,GAAG,CAAC,CAAC,6BAClB,mVAAC;4BAEC,SAAS,IAAM,wBAAwB,aAAa,EAAE,EAAE,aAAa,MAAM;4BAC3E,WAAW,CAAC,sDAAsD,EAChE,CAAC,aAAa,MAAM,GAAG,eAAe,IACtC;sCAEF,cAAA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACZ,oBAAoB,aAAa,IAAI;;;;;;kDAGxC,mVAAC;wCAAI,WAAU;;0DACb,mVAAC;gDAAI,WAAU;;kEACb,mVAAC;wDAAE,WAAW,CAAC,oBAAoB,EACjC,CAAC,aAAa,MAAM,GAAG,kBAAkB,iBACzC;kEACC,aAAa,KAAK;;;;;;kEAErB,mVAAC;wDACC,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,mBAAmB,aAAa,EAAE;wDACpC;wDACA,WAAU;kEAEV,cAAA,mVAAC,gRAAA,CAAA,IAAC;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAIjB,mVAAC;gDAAE,WAAU;0DACV,aAAa,OAAO;;;;;;0DAGvB,mVAAC;gDAAI,WAAU;;kEACb,mVAAC;wDAAK,WAAU;kEACb,gBAAgB,aAAa,SAAS;;;;;;oDAExC,CAAC,aAAa,MAAM,kBACnB,mVAAC;wDAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;2BAtClB,aAAa,EAAE;;;;;;;;;;;;;;;;;;;;;AAkDpC;uCAEe", "debugId": null}}, {"offset": {"line": 5640, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/ProfileDropdown.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect, useRef } from 'react'\nimport Link from 'next/link'\nimport { UserType } from '@/stores/sidebar/useSidebarStore'\nimport { \n  User, \n  Settings, \n  HelpCircle, \n  LogOut,\n  CreditCard,\n  Shield,\n  Bell\n} from 'lucide-react'\n\ninterface ProfileDropdownProps {\n  user: any // User type from auth store\n  userType: UserType\n  onLogout: () => void\n  onClose: () => void\n}\n\nexport function ProfileDropdown({ user, userType, onLogout, onClose }: ProfileDropdownProps) {\n  const dropdownRef = useRef<HTMLDivElement>(null)\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        onClose()\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => document.removeEventListener('mousedown', handleClickOutside)\n  }, [onClose])\n\n  const getProfileLinks = () => {\n    const baseLinks = [\n      {\n        icon: User,\n        label: 'Profile',\n        href: `/${userType.replace('_', '-')}/profile`,\n        description: 'Manage your profile information'\n      },\n      {\n        icon: Settings,\n        label: 'Settings',\n        href: `/${userType.replace('_', '-')}/settings`,\n        description: 'Account and preferences'\n      }\n    ]\n\n    // Add user-type specific links\n    if (userType === 'super_admin') {\n      baseLinks.push(\n        {\n          icon: Shield,\n          label: 'System Settings',\n          href: '/super-admin/system-settings',\n          description: 'Platform configuration'\n        }\n      )\n    } else if (userType === 'institute_admin') {\n      baseLinks.push(\n        {\n          icon: CreditCard,\n          label: 'Billing',\n          href: '/institute-admin/billing',\n          description: 'Manage billing and payments'\n        }\n      )\n    } else if (userType === 'student') {\n      baseLinks.push(\n        {\n          icon: CreditCard,\n          label: 'Payments',\n          href: '/student/payments',\n          description: 'Payment history and methods'\n        },\n        {\n          icon: Bell,\n          label: 'Notifications',\n          href: '/student/notifications',\n          description: 'Notification preferences'\n        }\n      )\n    }\n\n    baseLinks.push({\n      icon: HelpCircle,\n      label: 'Help & Support',\n      href: `/${userType.replace('_', '-')}/support`,\n      description: 'Get help and contact support'\n    })\n\n    return baseLinks\n  }\n\n  const profileLinks = getProfileLinks()\n\n  const handleLinkClick = () => {\n    onClose()\n  }\n\n  const handleLogoutClick = () => {\n    onLogout()\n    onClose()\n  }\n\n  return (\n    <div \n      ref={dropdownRef}\n      className=\"absolute right-0 top-full mt-2 w-72 bg-white border border-gray-200 rounded-lg shadow-lg z-50\"\n    >\n      {/* User Info Header */}\n      <div className=\"p-4 border-b border-gray-200\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center\">\n            {user?.personalInfo?.avatar ? (\n              <img \n                src={user.personalInfo.avatar} \n                alt={user.personalInfo.fullName || user.email}\n                className=\"w-12 h-12 rounded-full object-cover\"\n              />\n            ) : (\n              <User className=\"w-6 h-6 text-white\" />\n            )}\n          </div>\n          \n          <div className=\"flex-1 min-w-0\">\n            <div className=\"text-sm font-medium text-gray-900 truncate\">\n              {user?.personalInfo?.fullName || user?.email || 'User'}\n            </div>\n            <div className=\"text-sm text-gray-500 truncate\">\n              {user?.personalInfo?.email || user?.email}\n            </div>\n            <div className=\"text-xs text-gray-400 capitalize mt-1\">\n              {user?.role?.replace('_', ' ') || userType.replace('_', ' ')}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Menu Items */}\n      <div className=\"py-1\">\n        {profileLinks.map((link, index) => (\n          <Link\n            key={index}\n            href={link.href}\n            onClick={handleLinkClick}\n            className=\"flex items-center px-4 py-3 text-sm text-gray-700 hover:bg-gray-50 transition-colors\"\n          >\n            <link.icon className=\"w-4 h-4 mr-3 text-gray-400\" />\n            <div className=\"flex-1\">\n              <div className=\"font-medium\">{link.label}</div>\n              <div className=\"text-xs text-gray-500\">{link.description}</div>\n            </div>\n          </Link>\n        ))}\n      </div>\n\n      {/* Logout */}\n      <div className=\"border-t border-gray-200\">\n        <button\n          onClick={handleLogoutClick}\n          className=\"flex items-center w-full px-4 py-3 text-sm text-red-600 hover:bg-red-50 transition-colors\"\n        >\n          <LogOut className=\"w-4 h-4 mr-3\" />\n          <div className=\"flex-1 text-left\">\n            <div className=\"font-medium\">Sign Out</div>\n            <div className=\"text-xs text-red-500\">Sign out of your account</div>\n          </div>\n        </button>\n      </div>\n    </div>\n  )\n}\n\nexport default ProfileDropdown\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AALA;;;;;AAsBO,SAAS,gBAAgB,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,OAAO,EAAwB;IACzF,MAAM,cAAc,CAAA,GAAA,0SAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,uCAAuC;IACvC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,qBAAqB,CAAC;YAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC9E;YACF;QACF;QAEA,SAAS,gBAAgB,CAAC,aAAa;QACvC,OAAO,IAAM,SAAS,mBAAmB,CAAC,aAAa;IACzD,GAAG;QAAC;KAAQ;IAEZ,MAAM,kBAAkB;QACtB,MAAM,YAAY;YAChB;gBACE,MAAM,sRAAA,CAAA,OAAI;gBACV,OAAO;gBACP,MAAM,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC;gBAC9C,aAAa;YACf;YACA;gBACE,MAAM,8RAAA,CAAA,WAAQ;gBACd,OAAO;gBACP,MAAM,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;gBAC/C,aAAa;YACf;SACD;QAED,+BAA+B;QAC/B,IAAI,aAAa,eAAe;YAC9B,UAAU,IAAI,CACZ;gBACE,MAAM,0RAAA,CAAA,SAAM;gBACZ,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;QAEJ,OAAO,IAAI,aAAa,mBAAmB;YACzC,UAAU,IAAI,CACZ;gBACE,MAAM,sSAAA,CAAA,aAAU;gBAChB,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;QAEJ,OAAO,IAAI,aAAa,WAAW;YACjC,UAAU,IAAI,CACZ;gBACE,MAAM,sSAAA,CAAA,aAAU;gBAChB,OAAO;gBACP,MAAM;gBACN,aAAa;YACf,GACA;gBACE,MAAM,sRAAA,CAAA,OAAI;gBACV,OAAO;gBACP,MAAM;gBACN,aAAa;YACf;QAEJ;QAEA,UAAU,IAAI,CAAC;YACb,MAAM,kTAAA,CAAA,aAAU;YAChB,OAAO;YACP,MAAM,CAAC,CAAC,EAAE,SAAS,OAAO,CAAC,KAAK,KAAK,QAAQ,CAAC;YAC9C,aAAa;QACf;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,MAAM,kBAAkB;QACtB;IACF;IAEA,MAAM,oBAAoB;QACxB;QACA;IACF;IAEA,qBACE,mVAAC;QACC,KAAK;QACL,WAAU;;0BAGV,mVAAC;gBAAI,WAAU;0BACb,cAAA,mVAAC;oBAAI,WAAU;;sCACb,mVAAC;4BAAI,WAAU;sCACZ,MAAM,cAAc,uBACnB,mVAAC;gCACC,KAAK,KAAK,YAAY,CAAC,MAAM;gCAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;gCAC7C,WAAU;;;;;qDAGZ,mVAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIpB,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCAAI,WAAU;8CACZ,MAAM,cAAc,YAAY,MAAM,SAAS;;;;;;8CAElD,mVAAC;oCAAI,WAAU;8CACZ,MAAM,cAAc,SAAS,MAAM;;;;;;8CAEtC,mVAAC;oCAAI,WAAU;8CACZ,MAAM,MAAM,QAAQ,KAAK,QAAQ,SAAS,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOhE,mVAAC;gBAAI,WAAU;0BACZ,aAAa,GAAG,CAAC,CAAC,MAAM,sBACvB,mVAAC,iQAAA,CAAA,UAAI;wBAEH,MAAM,KAAK,IAAI;wBACf,SAAS;wBACT,WAAU;;0CAEV,mVAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;0CACrB,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDAAe,KAAK,KAAK;;;;;;kDACxC,mVAAC;wCAAI,WAAU;kDAAyB,KAAK,WAAW;;;;;;;;;;;;;uBARrD;;;;;;;;;;0BAeX,mVAAC;gBAAI,WAAU;0BACb,cAAA,mVAAC;oBACC,SAAS;oBACT,WAAU;;sCAEV,mVAAC,8RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCAAI,WAAU;8CAAc;;;;;;8CAC7B,mVAAC;oCAAI,WAAU;8CAAuB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlD;uCAEe", "debugId": null}}, {"offset": {"line": 5920, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { usePathname } from 'next/navigation'\nimport { useSidebarStore, UserType } from '@/stores/sidebar/useSidebarStore'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport {\n  Menu,\n  Search,\n  Bell,\n  ChevronDown,\n  ChevronRight,\n  User,\n  Settings,\n  LogOut,\n  HelpCircle\n} from 'lucide-react'\nimport { useResponsive } from '@/hooks/useResponsive'\nimport { Breadcrumbs } from '@/components/shared/navigation/Breadcrumbs'\nimport { NavigationSearch, GlobalSearchModal } from '@/components/shared/navigation/NavigationSearch'\nimport { NotificationDropdown } from './NotificationDropdown'\nimport { ProfileDropdown } from './ProfileDropdown'\n\ninterface HeaderProps {\n  userType: UserType\n}\n\nexport function Header({ userType }: HeaderProps) {\n  const pathname = usePathname()\n  const {\n    isCollapsed,\n    isMobileOpen,\n    toggleSidebar,\n    setMobileSidebarOpen,\n    navigationItems,\n    notifications,\n    unreadCount\n  } = useSidebarStore()\n\n  const { user, logout } = useAuthStore()\n  const { isMobile } = useResponsive()\n  const [showNotifications, setShowNotifications] = useState(false)\n  const [showProfile, setShowProfile] = useState(false)\n  const [searchQuery, setSearchQuery] = useState('')\n\n  // Generate breadcrumbs from current path\n  const generateBreadcrumbs = () => {\n    const pathSegments = pathname.split('/').filter(Boolean)\n    const breadcrumbs = []\n\n    // Add home/dashboard\n    const dashboardPath = `/${pathSegments[0] || ''}`\n    const dashboardItem = navigationItems.find(item => item.href === dashboardPath)\n    if (dashboardItem) {\n      breadcrumbs.push({\n        label: dashboardItem.label,\n        href: dashboardItem.href,\n        isActive: pathname === dashboardItem.href\n      })\n    }\n\n    // Add subsequent segments\n    for (let i = 1; i < pathSegments.length; i++) {\n      const segmentPath = `/${pathSegments.slice(0, i + 1).join('/')}`\n      const segmentItem = navigationItems.find(item => item.href === segmentPath)\n      \n      if (segmentItem) {\n        breadcrumbs.push({\n          label: segmentItem.label,\n          href: segmentItem.href,\n          isActive: pathname === segmentItem.href\n        })\n      } else {\n        // Fallback for dynamic segments\n        const label = pathSegments[i].replace(/-/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())\n        breadcrumbs.push({\n          label,\n          href: segmentPath,\n          isActive: i === pathSegments.length - 1\n        })\n      }\n    }\n\n    return breadcrumbs\n  }\n\n  const breadcrumbs = generateBreadcrumbs()\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault()\n    // Implement search functionality\n    console.log('Search query:', searchQuery)\n  }\n\n  const handleLogout = async () => {\n    try {\n      await logout()\n      setShowProfile(false)\n    } catch (error) {\n      console.error('Logout error:', error)\n    }\n  }\n\n  const handleMobileMenuToggle = () => {\n    if (isMobile) {\n      setMobileSidebarOpen(!isMobileOpen)\n    } else {\n      toggleSidebar()\n    }\n  }\n\n\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 sticky top-0 z-30\">\n      <div className=\"flex items-center justify-between px-4 lg:px-6 h-16\">\n        {/* Left Section */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Mobile Menu Button */}\n          <button\n            onClick={handleMobileMenuToggle}\n            className=\"p-2 rounded-lg hover:bg-gray-100 transition-colors lg:hidden\"\n          >\n            <Menu className=\"w-5 h-5 text-gray-600\" />\n          </button>\n\n          {/* Breadcrumbs */}\n          <div className=\"hidden md:block\">\n            <Breadcrumbs\n              maxItems={4}\n              showHomeIcon={true}\n              className=\"text-sm\"\n            />\n          </div>\n        </div>\n\n        {/* Center Section - Search */}\n        <div className=\"flex-1 max-w-md mx-4 hidden md:block\">\n          <NavigationSearch\n            placeholder=\"Search navigation...\"\n            showShortcut={true}\n          />\n        </div>\n\n        {/* Mobile Search */}\n        <div className=\"md:hidden\">\n          <GlobalSearchModal />\n        </div>\n\n        {/* Right Section */}\n        <div className=\"flex items-center space-x-2\">\n          {/* Notifications */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowNotifications(!showNotifications)}\n              className=\"relative p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n            >\n              <Bell className=\"w-5 h-5 text-gray-600\" />\n              {unreadCount > 0 && (\n                <span className=\"absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                  {unreadCount > 9 ? '9+' : unreadCount}\n                </span>\n              )}\n            </button>\n\n            {/* Notification Dropdown */}\n            {showNotifications && (\n              <NotificationDropdown\n                notifications={notifications}\n                onClose={() => setShowNotifications(false)}\n              />\n            )}\n          </div>\n\n          {/* Profile Dropdown */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowProfile(!showProfile)}\n              className=\"flex items-center space-x-2 p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n            >\n              <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n                {user?.personalInfo?.avatar ? (\n                  <img \n                    src={user.personalInfo.avatar} \n                    alt={user.personalInfo.fullName || user.email}\n                    className=\"w-8 h-8 rounded-full object-cover\"\n                  />\n                ) : (\n                  <User className=\"w-4 h-4 text-white\" />\n                )}\n              </div>\n              \n              <div className=\"hidden md:block text-left\">\n                <div className=\"text-sm font-medium text-gray-900\">\n                  {user?.personalInfo?.fullName || user?.email || 'User'}\n                </div>\n                <div className=\"text-xs text-gray-500 capitalize\">\n                  {user?.role?.replace('_', ' ') || userType.replace('_', ' ')}\n                </div>\n              </div>\n              \n              <ChevronDown className=\"w-4 h-4 text-gray-400\" />\n            </button>\n\n            {/* Profile Dropdown Menu */}\n            {showProfile && (\n              <ProfileDropdown\n                user={user}\n                userType={userType}\n                onLogout={handleLogout}\n                onClose={() => setShowProfile(false)}\n              />\n            )}\n          </div>\n        </div>\n      </div>\n    </header>\n  )\n}\n\nexport default Header\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAWA;AACA;AACA;AACA;AACA;AArBA;;;;;;;;;;;;AA2BO,SAAS,OAAO,EAAE,QAAQ,EAAe;IAC9C,MAAM,WAAW,CAAA,GAAA,uOAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EACJ,WAAW,EACX,YAAY,EACZ,aAAa,EACb,oBAAoB,EACpB,eAAe,EACf,aAAa,EACb,WAAW,EACZ,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAElB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;IACpC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;IACjC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,yCAAyC;IACzC,MAAM,sBAAsB;QAC1B,MAAM,eAAe,SAAS,KAAK,CAAC,KAAK,MAAM,CAAC;QAChD,MAAM,cAAc,EAAE;QAEtB,qBAAqB;QACrB,MAAM,gBAAgB,CAAC,CAAC,EAAE,YAAY,CAAC,EAAE,IAAI,IAAI;QACjD,MAAM,gBAAgB,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;QACjE,IAAI,eAAe;YACjB,YAAY,IAAI,CAAC;gBACf,OAAO,cAAc,KAAK;gBAC1B,MAAM,cAAc,IAAI;gBACxB,UAAU,aAAa,cAAc,IAAI;YAC3C;QACF;QAEA,0BAA0B;QAC1B,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;YAC5C,MAAM,cAAc,CAAC,CAAC,EAAE,aAAa,KAAK,CAAC,GAAG,IAAI,GAAG,IAAI,CAAC,MAAM;YAChE,MAAM,cAAc,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK;YAE/D,IAAI,aAAa;gBACf,YAAY,IAAI,CAAC;oBACf,OAAO,YAAY,KAAK;oBACxB,MAAM,YAAY,IAAI;oBACtB,UAAU,aAAa,YAAY,IAAI;gBACzC;YACF,OAAO;gBACL,gCAAgC;gBAChC,MAAM,QAAQ,YAAY,CAAC,EAAE,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;gBACpF,YAAY,IAAI,CAAC;oBACf;oBACA,MAAM;oBACN,UAAU,MAAM,aAAa,MAAM,GAAG;gBACxC;YACF;QACF;QAEA,OAAO;IACT;IAEA,MAAM,cAAc;IAEpB,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,iCAAiC;QACjC,QAAQ,GAAG,CAAC,iBAAiB;IAC/B;IAEA,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,eAAe;QACjB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI,UAAU;YACZ,qBAAqB,CAAC;QACxB,OAAO;YACL;QACF;IACF;IAIA,qBACE,mVAAC;QAAO,WAAU;kBAChB,cAAA,mVAAC;YAAI,WAAU;;8BAEb,mVAAC;oBAAI,WAAU;;sCAEb,mVAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,mVAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;sCAIlB,mVAAC;4BAAI,WAAU;sCACb,cAAA,mVAAC,6KAAA,CAAA,cAAW;gCACV,UAAU;gCACV,cAAc;gCACd,WAAU;;;;;;;;;;;;;;;;;8BAMhB,mVAAC;oBAAI,WAAU;8BACb,cAAA,mVAAC,kLAAA,CAAA,mBAAgB;wBACf,aAAY;wBACZ,cAAc;;;;;;;;;;;8BAKlB,mVAAC;oBAAI,WAAU;8BACb,cAAA,mVAAC,kLAAA,CAAA,oBAAiB;;;;;;;;;;8BAIpB,mVAAC;oBAAI,WAAU;;sCAEb,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCACC,SAAS,IAAM,qBAAqB,CAAC;oCACrC,WAAU;;sDAEV,mVAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;wCACf,cAAc,mBACb,mVAAC;4CAAK,WAAU;sDACb,cAAc,IAAI,OAAO;;;;;;;;;;;;gCAM/B,mCACC,mVAAC,wKAAA,CAAA,uBAAoB;oCACnB,eAAe;oCACf,SAAS,IAAM,qBAAqB;;;;;;;;;;;;sCAM1C,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCACC,SAAS,IAAM,eAAe,CAAC;oCAC/B,WAAU;;sDAEV,mVAAC;4CAAI,WAAU;sDACZ,MAAM,cAAc,uBACnB,mVAAC;gDACC,KAAK,KAAK,YAAY,CAAC,MAAM;gDAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;gDAC7C,WAAU;;;;;qEAGZ,mVAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAIpB,mVAAC;4CAAI,WAAU;;8DACb,mVAAC;oDAAI,WAAU;8DACZ,MAAM,cAAc,YAAY,MAAM,SAAS;;;;;;8DAElD,mVAAC;oDAAI,WAAU;8DACZ,MAAM,MAAM,QAAQ,KAAK,QAAQ,SAAS,OAAO,CAAC,KAAK;;;;;;;;;;;;sDAI5D,mVAAC,wSAAA,CAAA,cAAW;4CAAC,WAAU;;;;;;;;;;;;gCAIxB,6BACC,mVAAC,mKAAA,CAAA,kBAAe;oCACd,MAAM;oCACN,UAAU;oCACV,UAAU;oCACV,SAAS,IAAM,eAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;uCAEe", "debugId": null}}, {"offset": {"line": 6240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/shared/navigation/MobileNavigation.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport Link from 'next/link'\nimport { usePathname } from 'next/navigation'\nimport { useSidebarStore, NavigationItem } from '@/stores/sidebar/useSidebarStore'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport { useResponsive } from '@/hooks/useResponsive'\nimport { \n  Home, \n  Menu, \n  X, \n  User, \n  Settings, \n  LogOut,\n  ChevronRight\n} from 'lucide-react'\nimport * as Icons from 'lucide-react'\n\nexport function MobileNavigation() {\n  const pathname = usePathname()\n  const { isMobile } = useResponsive()\n  const { navigationItems } = useSidebarStore()\n  const { user, logout } = useAuthStore()\n  const [isOpen, setIsOpen] = useState(false)\n  const [expandedItems, setExpandedItems] = useState<string[]>([])\n\n  if (!isMobile) return null\n\n  const toggleExpanded = (itemId: string) => {\n    setExpandedItems(prev => \n      prev.includes(itemId) \n        ? prev.filter(id => id !== itemId)\n        : [...prev, itemId]\n    )\n  }\n\n  const handleItemClick = () => {\n    setIsOpen(false)\n    setExpandedItems([])\n  }\n\n  const renderNavigationItem = (item: NavigationItem, level: number = 0) => {\n    const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>\n    const isActive = pathname === item.href\n    const hasChildren = item.children && item.children.length > 0\n    const isExpanded = expandedItems.includes(item.id)\n\n    return (\n      <div key={item.id}>\n        {hasChildren ? (\n          <button\n            onClick={() => toggleExpanded(item.id)}\n            className={`w-full flex items-center justify-between px-4 py-3 text-left transition-colors ${\n              level > 0 ? 'pl-8' : ''\n            } ${\n              isActive \n                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' \n                : 'text-gray-700 hover:bg-gray-50'\n            }`}\n          >\n            <div className=\"flex items-center space-x-3\">\n              {IconComponent && (\n                <IconComponent className={`w-5 h-5 ${\n                  isActive ? 'text-blue-600' : 'text-gray-400'\n                }`} />\n              )}\n              <span className=\"font-medium\">{item.label}</span>\n              {item.badge && item.badge > 0 && (\n                <span className=\"ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full\">\n                  {item.badge > 9 ? '9+' : item.badge}\n                </span>\n              )}\n            </div>\n            <ChevronRight className={`w-4 h-4 text-gray-400 transition-transform ${\n              isExpanded ? 'rotate-90' : ''\n            }`} />\n          </button>\n        ) : (\n          <Link\n            href={item.href}\n            onClick={handleItemClick}\n            className={`flex items-center space-x-3 px-4 py-3 transition-colors ${\n              level > 0 ? 'pl-8' : ''\n            } ${\n              isActive \n                ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-600' \n                : 'text-gray-700 hover:bg-gray-50'\n            }`}\n          >\n            {IconComponent && (\n              <IconComponent className={`w-5 h-5 ${\n                isActive ? 'text-blue-600' : 'text-gray-400'\n              }`} />\n            )}\n            <span className=\"font-medium\">{item.label}</span>\n            {item.badge && item.badge > 0 && (\n              <span className=\"ml-auto px-2 py-0.5 text-xs bg-red-100 text-red-800 rounded-full\">\n                {item.badge > 9 ? '9+' : item.badge}\n              </span>\n            )}\n          </Link>\n        )}\n\n        {/* Children */}\n        {hasChildren && isExpanded && (\n          <div className=\"bg-gray-50\">\n            {item.children?.map(child => renderNavigationItem(child, level + 1))}\n          </div>\n        )}\n      </div>\n    )\n  }\n\n  return (\n    <>\n      {/* Mobile Navigation Button */}\n      <button\n        onClick={() => setIsOpen(true)}\n        className=\"fixed bottom-4 right-4 z-50 p-3 bg-blue-600 text-white rounded-full shadow-lg hover:bg-blue-700 transition-colors lg:hidden\"\n      >\n        <Menu className=\"w-6 h-6\" />\n      </button>\n\n      {/* Mobile Navigation Overlay */}\n      {isOpen && (\n        <div className=\"fixed inset-0 z-50 lg:hidden\">\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 bg-black bg-opacity-50\"\n            onClick={() => setIsOpen(false)}\n          />\n\n          {/* Navigation Panel */}\n          <div className=\"fixed inset-y-0 right-0 w-80 max-w-[85vw] bg-white shadow-xl\">\n            {/* Header */}\n            <div className=\"flex items-center justify-between p-4 border-b border-gray-200\">\n              <h2 className=\"text-lg font-semibold text-gray-900\">Navigation</h2>\n              <button\n                onClick={() => setIsOpen(false)}\n                className=\"p-2 hover:bg-gray-100 rounded-lg transition-colors\"\n              >\n                <X className=\"w-5 h-5 text-gray-500\" />\n              </button>\n            </div>\n\n            {/* User Info */}\n            <div className=\"p-4 border-b border-gray-200 bg-gray-50\">\n              <div className=\"flex items-center space-x-3\">\n                <div className=\"w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center\">\n                  {user?.personalInfo?.avatar ? (\n                    <img \n                      src={user.personalInfo.avatar} \n                      alt={user.personalInfo.fullName || user.email}\n                      className=\"w-10 h-10 rounded-full object-cover\"\n                    />\n                  ) : (\n                    <User className=\"w-5 h-5 text-white\" />\n                  )}\n                </div>\n                <div className=\"flex-1 min-w-0\">\n                  <div className=\"text-sm font-medium text-gray-900 truncate\">\n                    {user?.personalInfo?.fullName || user?.email || 'User'}\n                  </div>\n                  <div className=\"text-xs text-gray-500 capitalize\">\n                    {user?.role?.replace('_', ' ') || 'User'}\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            {/* Navigation Items */}\n            <div className=\"flex-1 overflow-y-auto\">\n              <nav className=\"py-2\">\n                {navigationItems.map(item => renderNavigationItem(item))}\n              </nav>\n            </div>\n\n            {/* Footer Actions */}\n            <div className=\"border-t border-gray-200 p-4 space-y-2\">\n              <Link\n                href=\"/settings\"\n                onClick={handleItemClick}\n                className=\"flex items-center space-x-3 px-3 py-2 text-gray-700 hover:bg-gray-50 rounded-lg transition-colors\"\n              >\n                <Settings className=\"w-5 h-5 text-gray-400\" />\n                <span className=\"font-medium\">Settings</span>\n              </Link>\n              <button\n                onClick={() => {\n                  logout()\n                  handleItemClick()\n                }}\n                className=\"flex items-center space-x-3 w-full px-3 py-2 text-red-600 hover:bg-red-50 rounded-lg transition-colors\"\n              >\n                <LogOut className=\"w-5 h-5\" />\n                <span className=\"font-medium\">Sign Out</span>\n              </button>\n            </div>\n          </div>\n        </div>\n      )}\n    </>\n  )\n}\n\n// Bottom Navigation Bar for Mobile\nexport function MobileBottomNavigation() {\n  const pathname = usePathname()\n  const { isMobile } = useResponsive()\n  const { navigationItems } = useSidebarStore()\n\n  if (!isMobile) return null\n\n  // Get the most important navigation items for bottom bar\n  const bottomNavItems = navigationItems.slice(0, 4).map(item => ({\n    ...item,\n    isActive: pathname === item.href || pathname.startsWith(item.href + '/')\n  }))\n\n  return (\n    <div className=\"fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 z-40 lg:hidden\">\n      <div className=\"grid grid-cols-4 h-16\">\n        {bottomNavItems.map(item => {\n          const IconComponent = (Icons as any)[item.icon] as React.ComponentType<any>\n          \n          return (\n            <Link\n              key={item.id}\n              href={item.href}\n              className={`flex flex-col items-center justify-center space-y-1 transition-colors ${\n                item.isActive \n                  ? 'text-blue-600 bg-blue-50' \n                  : 'text-gray-600 hover:text-gray-900'\n              }`}\n            >\n              <div className=\"relative\">\n                {IconComponent && <IconComponent className=\"w-5 h-5\" />}\n                {item.badge && item.badge > 0 && (\n                  <span className=\"absolute -top-2 -right-2 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center\">\n                    {item.badge > 9 ? '9+' : item.badge}\n                  </span>\n                )}\n              </div>\n              <span className=\"text-xs font-medium truncate max-w-full\">\n                {item.label}\n              </span>\n            </Link>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n\nexport default MobileNavigation\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAjBA;;;;;;;;;;AAmBO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,uOAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;IACjC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAC1C,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;IACpC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,IAAI,CAAC,UAAU,OAAO;IAEtB,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,UACV,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO,UACzB;mBAAI;gBAAM;aAAO;IAEzB;IAEA,MAAM,kBAAkB;QACtB,UAAU;QACV,iBAAiB,EAAE;IACrB;IAEA,MAAM,uBAAuB,CAAC,MAAsB,QAAgB,CAAC;QACnE,MAAM,gBAAgB,AAAC,qPAAa,CAAC,KAAK,IAAI,CAAC;QAC/C,MAAM,WAAW,aAAa,KAAK,IAAI;QACvC,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,EAAE;QAEjD,qBACE,mVAAC;;gBACE,4BACC,mVAAC;oBACC,SAAS,IAAM,eAAe,KAAK,EAAE;oBACrC,WAAW,CAAC,+EAA+E,EACzF,QAAQ,IAAI,SAAS,GACtB,CAAC,EACA,WACI,wDACA,kCACJ;;sCAEF,mVAAC;4BAAI,WAAU;;gCACZ,+BACC,mVAAC;oCAAc,WAAW,CAAC,QAAQ,EACjC,WAAW,kBAAkB,iBAC7B;;;;;;8CAEJ,mVAAC;oCAAK,WAAU;8CAAe,KAAK,KAAK;;;;;;gCACxC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,mVAAC;oCAAK,WAAU;8CACb,KAAK,KAAK,GAAG,IAAI,OAAO,KAAK,KAAK;;;;;;;;;;;;sCAIzC,mVAAC,0SAAA,CAAA,eAAY;4BAAC,WAAW,CAAC,2CAA2C,EACnE,aAAa,cAAc,IAC3B;;;;;;;;;;;yCAGJ,mVAAC,iQAAA,CAAA,UAAI;oBACH,MAAM,KAAK,IAAI;oBACf,SAAS;oBACT,WAAW,CAAC,wDAAwD,EAClE,QAAQ,IAAI,SAAS,GACtB,CAAC,EACA,WACI,wDACA,kCACJ;;wBAED,+BACC,mVAAC;4BAAc,WAAW,CAAC,QAAQ,EACjC,WAAW,kBAAkB,iBAC7B;;;;;;sCAEJ,mVAAC;4BAAK,WAAU;sCAAe,KAAK,KAAK;;;;;;wBACxC,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,mVAAC;4BAAK,WAAU;sCACb,KAAK,KAAK,GAAG,IAAI,OAAO,KAAK,KAAK;;;;;;;;;;;;gBAO1C,eAAe,4BACd,mVAAC;oBAAI,WAAU;8BACZ,KAAK,QAAQ,EAAE,IAAI,CAAA,QAAS,qBAAqB,OAAO,QAAQ;;;;;;;WA1D7D,KAAK,EAAE;;;;;IA+DrB;IAEA,qBACE;;0BAEE,mVAAC;gBACC,SAAS,IAAM,UAAU;gBACzB,WAAU;0BAEV,cAAA,mVAAC,sRAAA,CAAA,OAAI;oBAAC,WAAU;;;;;;;;;;;YAIjB,wBACC,mVAAC;gBAAI,WAAU;;kCAEb,mVAAC;wBACC,WAAU;wBACV,SAAS,IAAM,UAAU;;;;;;kCAI3B,mVAAC;wBAAI,WAAU;;0CAEb,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAG,WAAU;kDAAsC;;;;;;kDACpD,mVAAC;wCACC,SAAS,IAAM,UAAU;wCACzB,WAAU;kDAEV,cAAA,mVAAC,gRAAA,CAAA,IAAC;4CAAC,WAAU;;;;;;;;;;;;;;;;;0CAKjB,mVAAC;gCAAI,WAAU;0CACb,cAAA,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;sDACZ,MAAM,cAAc,uBACnB,mVAAC;gDACC,KAAK,KAAK,YAAY,CAAC,MAAM;gDAC7B,KAAK,KAAK,YAAY,CAAC,QAAQ,IAAI,KAAK,KAAK;gDAC7C,WAAU;;;;;qEAGZ,mVAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAGpB,mVAAC;4CAAI,WAAU;;8DACb,mVAAC;oDAAI,WAAU;8DACZ,MAAM,cAAc,YAAY,MAAM,SAAS;;;;;;8DAElD,mVAAC;oDAAI,WAAU;8DACZ,MAAM,MAAM,QAAQ,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;0CAO1C,mVAAC;gCAAI,WAAU;0CACb,cAAA,mVAAC;oCAAI,WAAU;8CACZ,gBAAgB,GAAG,CAAC,CAAA,OAAQ,qBAAqB;;;;;;;;;;;0CAKtD,mVAAC;gCAAI,WAAU;;kDACb,mVAAC,iQAAA,CAAA,UAAI;wCACH,MAAK;wCACL,SAAS;wCACT,WAAU;;0DAEV,mVAAC,8RAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,mVAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;kDAEhC,mVAAC;wCACC,SAAS;4CACP;4CACA;wCACF;wCACA,WAAU;;0DAEV,mVAAC,8RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,mVAAC;gDAAK,WAAU;0DAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;AAGO,SAAS;IACd,MAAM,WAAW,CAAA,GAAA,uOAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;IACjC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAE1C,IAAI,CAAC,UAAU,OAAO;IAEtB,yDAAyD;IACzD,MAAM,iBAAiB,gBAAgB,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAA,OAAQ,CAAC;YAC9D,GAAG,IAAI;YACP,UAAU,aAAa,KAAK,IAAI,IAAI,SAAS,UAAU,CAAC,KAAK,IAAI,GAAG;QACtE,CAAC;IAED,qBACE,mVAAC;QAAI,WAAU;kBACb,cAAA,mVAAC;YAAI,WAAU;sBACZ,eAAe,GAAG,CAAC,CAAA;gBAClB,MAAM,gBAAgB,AAAC,qPAAa,CAAC,KAAK,IAAI,CAAC;gBAE/C,qBACE,mVAAC,iQAAA,CAAA,UAAI;oBAEH,MAAM,KAAK,IAAI;oBACf,WAAW,CAAC,sEAAsE,EAChF,KAAK,QAAQ,GACT,6BACA,qCACJ;;sCAEF,mVAAC;4BAAI,WAAU;;gCACZ,+BAAiB,mVAAC;oCAAc,WAAU;;;;;;gCAC1C,KAAK,KAAK,IAAI,KAAK,KAAK,GAAG,mBAC1B,mVAAC;oCAAK,WAAU;8CACb,KAAK,KAAK,GAAG,IAAI,OAAO,KAAK,KAAK;;;;;;;;;;;;sCAIzC,mVAAC;4BAAK,WAAU;sCACb,KAAK,KAAK;;;;;;;mBAjBR,KAAK,EAAE;;;;;YAqBlB;;;;;;;;;;;AAIR;uCAEe", "debugId": null}}, {"offset": {"line": 6687, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/layout/SuperAdminLayout.tsx"], "sourcesContent": ["'use client'\n\nimport { ReactNode, useEffect } from 'react'\nimport { useSidebarStore } from '@/stores/sidebar/useSidebarStore'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport { useResponsive } from '@/hooks/useResponsive'\nimport { Sidebar } from './Sidebar'\nimport { Header } from './Header'\nimport { MobileNavigation } from '@/components/shared/navigation/MobileNavigation'\nimport { superAdminNavigationConfig } from '@/config/navigation/superAdminNavigation'\n\ninterface SuperAdminLayoutProps {\n  children: ReactNode\n}\n\nexport function SuperAdminLayout({ children }: SuperAdminLayoutProps) {\n  const {\n    isCollapsed,\n    isMobileOpen,\n    navigationItems,\n    setNavigationItems,\n    setMobileSidebarOpen,\n    setUserType\n  } = useSidebarStore()\n  const { user } = useAuthStore()\n  const { isMobile, isTablet } = useResponsive()\n\n  // Initialize navigation for super admin only once\n  useEffect(() => {\n    // Only set navigation items if they haven't been set yet or if they're different\n    if (navigationItems.length === 0 || navigationItems[0]?.id !== superAdminNavigationConfig[0]?.id) {\n      setNavigationItems(superAdminNavigationConfig)\n    }\n    setUserType('super_admin')\n  }, []) // Remove dependencies to prevent re-running\n\n  // Auto-collapse sidebar on mobile/tablet\n  useEffect(() => {\n    if (isMobile && isMobileOpen) {\n      setMobileSidebarOpen(false)\n    }\n  }, [isMobile, isMobileOpen, setMobileSidebarOpen])\n\n  // Verify user has super admin access\n  if (!user || user.role !== 'super_admin') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"text-red-500 text-lg font-medium mb-2\">Access Denied</div>\n          <div className=\"text-gray-600\">You don't have permission to access this area.</div>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile Navigation */}\n      {isMobile && <MobileNavigation />}\n\n      {/* Desktop/Tablet Layout */}\n      {!isMobile && (\n        <div className=\"flex h-screen\">\n          {/* Sidebar */}\n          <div className={`\n            ${isCollapsed ? 'w-16' : 'w-64'} \n            transition-all duration-300 ease-in-out\n            flex-shrink-0\n            ${isMobile ? 'hidden' : 'block'}\n          `}>\n            <Sidebar userType=\"super_admin\" />\n          </div>\n\n          {/* Main Content Area */}\n          <div className=\"flex-1 flex flex-col overflow-hidden\">\n            {/* Header */}\n            <Header userType=\"super_admin\" />\n\n            {/* Page Content */}\n            <main className=\"flex-1 overflow-y-auto\">\n              <div className=\"p-6\">\n                {children}\n              </div>\n            </main>\n          </div>\n        </div>\n      )}\n\n      {/* Mobile Layout */}\n      {isMobile && (\n        <div className=\"pb-20\">\n          {/* Mobile Header */}\n          <div className=\"bg-white border-b border-gray-200 sticky top-0 z-30\">\n            <Header userType=\"super_admin\" />\n          </div>\n\n          {/* Mobile Content */}\n          <main className=\"p-4\">\n            {children}\n          </main>\n        </div>\n      )}\n\n      {/* Mobile Sidebar Overlay */}\n      {isMobile && isMobileOpen && (\n        <div className=\"fixed inset-0 z-50\">\n          {/* Backdrop */}\n          <div \n            className=\"fixed inset-0 bg-black bg-opacity-50\"\n            onClick={() => setMobileSidebarOpen(false)}\n          />\n\n          {/* Sidebar */}\n          <div className=\"fixed inset-y-0 left-0 w-64 bg-white shadow-xl\">\n            <Sidebar userType=\"super_admin\" />\n          </div>\n        </div>\n      )}\n    </div>\n  )\n}\n\nexport default SuperAdminLayout\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA;;;;;;;;;;AAeO,SAAS,iBAAiB,EAAE,QAAQ,EAAyB;IAClE,MAAM,EACJ,WAAW,EACX,YAAY,EACZ,eAAe,EACf,kBAAkB,EAClB,oBAAoB,EACpB,WAAW,EACZ,GAAG,CAAA,GAAA,+JAAA,CAAA,kBAAe,AAAD;IAClB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;IAC5B,MAAM,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,iJAAA,CAAA,gBAAa,AAAD;IAE3C,kDAAkD;IAClD,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,iFAAiF;QACjF,IAAI,gBAAgB,MAAM,KAAK,KAAK,eAAe,CAAC,EAAE,EAAE,OAAO,uKAAA,CAAA,6BAA0B,CAAC,EAAE,EAAE,IAAI;YAChG,mBAAmB,uKAAA,CAAA,6BAA0B;QAC/C;QACA,YAAY;IACd,GAAG,EAAE,EAAE,4CAA4C;;IAEnD,yCAAyC;IACzC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,YAAY,cAAc;YAC5B,qBAAqB;QACvB;IACF,GAAG;QAAC;QAAU;QAAc;KAAqB;IAEjD,qCAAqC;IACrC,IAAI,CAAC,QAAQ,KAAK,IAAI,KAAK,eAAe;QACxC,qBACE,mVAAC;YAAI,WAAU;sBACb,cAAA,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAI,WAAU;kCAAwC;;;;;;kCACvD,mVAAC;wBAAI,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIvC;IAEA,qBACE,mVAAC;QAAI,WAAU;;YAEZ,0BAAY,mVAAC,kLAAA,CAAA,mBAAgB;;;;;YAG7B,CAAC,0BACA,mVAAC;gBAAI,WAAU;;kCAEb,mVAAC;wBAAI,WAAW,CAAC;YACf,EAAE,cAAc,SAAS,OAAO;;;YAGhC,EAAE,WAAW,WAAW,QAAQ;UAClC,CAAC;kCACC,cAAA,mVAAC,2JAAA,CAAA,UAAO;4BAAC,UAAS;;;;;;;;;;;kCAIpB,mVAAC;wBAAI,WAAU;;0CAEb,mVAAC,0JAAA,CAAA,SAAM;gCAAC,UAAS;;;;;;0CAGjB,mVAAC;gCAAK,WAAU;0CACd,cAAA,mVAAC;oCAAI,WAAU;8CACZ;;;;;;;;;;;;;;;;;;;;;;;YAQV,0BACC,mVAAC;gBAAI,WAAU;;kCAEb,mVAAC;wBAAI,WAAU;kCACb,cAAA,mVAAC,0JAAA,CAAA,SAAM;4BAAC,UAAS;;;;;;;;;;;kCAInB,mVAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;YAMN,YAAY,8BACX,mVAAC;gBAAI,WAAU;;kCAEb,mVAAC;wBACC,WAAU;wBACV,SAAS,IAAM,qBAAqB;;;;;;kCAItC,mVAAC;wBAAI,WAAU;kCACb,cAAA,mVAAC,2JAAA,CAAA,UAAO;4BAAC,UAAS;;;;;;;;;;;;;;;;;;;;;;;AAM9B;uCAEe", "debugId": null}}]}