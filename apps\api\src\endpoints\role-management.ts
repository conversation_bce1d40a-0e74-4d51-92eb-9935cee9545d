import { Endpoint } from 'payload/config'
import { requireAuth, requireSuperAdmin, requireAnyAuth } from '../middleware/auth'

// Helper function to create authenticated endpoint
const createAuthenticatedEndpoint = (
  path: string,
  method: 'get' | 'post' | 'put' | 'delete',
  handler: (req: any) => Promise<Response>,
  allowedRoles?: string[]
): Endpoint => {
  return {
    path,
    method,
    handler: async (req: any) => {
      // Apply authentication middleware
      const authMiddleware = allowedRoles ? requireAuth(allowedRoles) : requireAnyAuth
      const authResult = await authMiddleware(req)

      if (authResult) {
        return authResult // Return auth error response
      }

      // If auth passed, call the actual handler
      return handler(req)
    }
  }
}

// Get roles with filtering and pagination (similar to tax components)
export const getRolesEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/roles',
  'get',
  async (req: any) => {
    try {
      const { searchParams } = new URL(req.url!)
      const search = searchParams.get('search') || ''
      const level = searchParams.get('level')
      const isActive = searchParams.get('isActive') || 'true'
      const category = searchParams.get('category') // system, custom, all
      const scope = searchParams.get('scope') // global, institute, branch
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')

      const where: any = {}

      // Active filter
      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      // Level filter
      if (level) {
        where.level = { equals: parseInt(level) }
      }

      // Category filter (system vs custom)
      if (category === 'system') {
        where.isSystemRole = { equals: true }
      } else if (category === 'custom') {
        where.isSystemRole = { equals: false }
      }

      // Scope filter
      if (scope === 'global') {
        where['scope.institute'] = { exists: false }
        where['scope.branch'] = { exists: false }
      } else if (scope === 'institute') {
        where['scope.institute'] = { exists: true }
        where['scope.branch'] = { exists: false }
      } else if (scope === 'branch') {
        where['scope.branch'] = { exists: true }
      }

      // Search filter
      if (search) {
        where.or = [
          { name: { contains: search } },
          { code: { contains: search } },
          { description: { contains: search } }
        ]
      }

      // User access restrictions
      const currentUser = req.user
      if (currentUser?.role === 'institute_admin') {
        where.and = [
          {
            or: [
              { level: { greater_than_equal: 2 } },
              { 'scope.institute': { equals: currentUser.institute } },
              { 'scope.institute': { exists: false } }
            ]
          }
        ]
      }

      const roles = await req.payload.find({
        collection: 'roles',
        where,
        page,
        limit,
        sort: '-createdAt',
        populate: [
          'permissions.permission',
          'scope.institute',
          'scope.branch'
        ]
      })

      return Response.json({
        success: true,
        docs: roles.docs,
        page: roles.page,
        limit: roles.limit,
        totalPages: roles.totalPages,
        totalDocs: roles.totalDocs,
        hasNextPage: roles.hasNextPage,
        hasPrevPage: roles.hasPrevPage
      })

    } catch (error) {
      console.error('Roles fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
)

// Get permissions with filtering and pagination
export const getPermissionsEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/permissions',
  'get',
  async (req: any) => {
    try {
      const { searchParams } = new URL(req.url!)
      const search = searchParams.get('search') || ''
      const category = searchParams.get('category')
      const resource = searchParams.get('resource')
      const action = searchParams.get('action')
      const level = searchParams.get('level')
      const isActive = searchParams.get('isActive') || 'true'
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '20')

      const where: any = {}

      // Active filter
      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      // Category filter
      if (category) {
        where.category = { equals: category }
      }

      // Resource filter
      if (resource) {
        where.resource = { equals: resource }
      }

      // Action filter
      if (action) {
        where.action = { equals: action }
      }

      // Level filter
      if (level) {
        where.level = { equals: level }
      }

      // Search filter
      if (search) {
        where.or = [
          { name: { contains: search } },
          { code: { contains: search } },
          { description: { contains: search } },
          { resource: { contains: search } },
          { action: { contains: search } }
        ]
      }

      // User access restrictions
      const currentUser = req.user
      if (currentUser?.role === 'institute_admin') {
        where.level = { greater_than_equal: '2' }
      }

      const permissions = await req.payload.find({
        collection: 'permissions',
        where,
        page,
        limit,
        sort: 'category'
      })

      return Response.json({
        success: true,
        docs: permissions.docs,
        page: permissions.page,
        limit: permissions.limit,
        totalPages: permissions.totalPages,
        totalDocs: permissions.totalDocs,
        hasNextPage: permissions.hasNextPage,
        hasPrevPage: permissions.hasPrevPage
      })

    } catch (error) {
      console.error('Permissions fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
)

// Get role-permission assignments with filtering
export const getRolePermissionsEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/role-permissions',
  'get',
  async (req: any) => {
    try {
      const { searchParams } = new URL(req.url!)
      const roleId = searchParams.get('roleId')
      const permissionId = searchParams.get('permissionId')
      const isActive = searchParams.get('isActive') || 'true'
      const page = parseInt(searchParams.get('page') || '1')
      const limit = parseInt(searchParams.get('limit') || '50')

      const where: any = {}

      // Active filter
      if (isActive !== 'all') {
        where.isActive = { equals: isActive === 'true' }
      }

      // Role filter
      if (roleId) {
        where.role = { equals: roleId }
      }

      // Permission filter
      if (permissionId) {
        where.permission = { equals: permissionId }
      }

      const rolePermissions = await req.payload.find({
        collection: 'role-permissions',
        where,
        page,
        limit,
        sort: '-createdAt',
        populate: ['role', 'permission']
      })

      return Response.json({
        success: true,
        docs: rolePermissions.docs,
        page: rolePermissions.page,
        limit: rolePermissions.limit,
        totalPages: rolePermissions.totalPages,
        totalDocs: rolePermissions.totalDocs,
        hasNextPage: rolePermissions.hasNextPage,
        hasPrevPage: rolePermissions.hasPrevPage
      })

    } catch (error) {
      console.error('Role-permissions fetch error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
)

// Get roles with their permissions (enhanced version)
export const getRolesWithPermissionsEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/roles-with-permissions',
  'get',
  async (req: any) => {
    try {
      const { searchParams } = new URL(req.url!)
      const includeInactive = searchParams.get('includeInactive') === 'true'
      const level = searchParams.get('level')
      const category = searchParams.get('category')

      // Authentication check
      if (!req.user) {
        return Response.json({
          success: false,
          message: 'Authentication required',
        }, { status: 401 })
      }

      // Build role filter
      const roleWhere: any = {}
      
      if (!includeInactive) {
        roleWhere.isActive = { equals: true }
      }

      if (level) {
        roleWhere.level = { equals: parseInt(level) }
      }

      if (category === 'system') {
        roleWhere.isSystemRole = { equals: true }
      } else if (category === 'custom') {
        roleWhere.isSystemRole = { equals: false }
      }

      // Get all roles
      const rolesResult = await req.payload.find({
        collection: 'roles',
        where: roleWhere,
        limit: 1000,
        sort: 'name',
        populate: ['scope.institute', 'scope.branch']
      })

      // Get all active role-permission assignments
      const rolePermissionsResult = await req.payload.find({
        collection: 'role-permissions',
        limit: 10000,
        where: {
          isActive: { equals: true },
        },
        populate: ['permission']
      })

      // Group permissions by role
      const rolesWithPermissions = rolesResult.docs.map((role: any) => {
        const rolePermissions = rolePermissionsResult.docs
          .filter((rp: any) => rp.role === role.id)
          .map((rp: any) => rp.permission)
          .filter((permission: any) => permission && permission.isActive)

        return {
          ...role,
          permissions: rolePermissions,
          permissionCount: rolePermissions.length,
        }
      })

      return Response.json({
        success: true,
        data: rolesWithPermissions,
        totalDocs: rolesWithPermissions.length,
      })

    } catch (error: any) {
      console.error('Error fetching roles with permissions:', error)
      return Response.json({
        success: false,
        message: 'Failed to fetch roles with permissions',
        error: error?.message || 'Unknown error',
      }, { status: 500 })
    }
  }
)

// Get permissions by category (enhanced)
export const getPermissionsByCategoryEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/permissions-by-category',
  'get',
  async (req: any) => {
    try {
      const { searchParams } = new URL(req.url!)
      const level = searchParams.get('level')
      const includeInactive = searchParams.get('includeInactive') === 'true'

      const where: any = {}

      if (!includeInactive) {
        where.isActive = { equals: true }
      }

      if (level) {
        where.level = { greater_than_equal: level }
      }

      // Level restrictions for institute admins
      if (req.user.role === 'institute_admin') {
        where.level = { greater_than_equal: '2' }
      }

      const permissions = await req.payload.find({
        collection: 'permissions',
        where,
        limit: 1000,
        sort: ['category', 'resource', 'action']
      })

      // Group by category
      const permissionsByCategory = permissions.docs.reduce((acc: Record<string, any[]>, permission: any) => {
        if (!acc[permission.category]) {
          acc[permission.category] = []
        }
        acc[permission.category].push(permission)
        return acc
      }, {})

      return Response.json({
        success: true,
        data: permissionsByCategory,
        totalCategories: Object.keys(permissionsByCategory).length,
        totalPermissions: permissions.docs.length
      })

    } catch (error) {
      console.error('Get permissions by category error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
)

// Assign permissions to role
export const assignPermissionsToRoleEndpoint: Endpoint = createAuthenticatedEndpoint(
  '/roles/:roleId/permissions',
  'post',
  async (req: any) => {
    try {
      const { roleId } = req.params
      const { permissionIds } = await req.json()

      // Get the role
      const role = await req.payload.findByID({
        collection: 'roles',
        id: roleId
      })

      if (!role) {
        return Response.json({
          success: false,
          message: 'Role not found'
        }, { status: 404 })
      }

      // Check permissions for institute admins
      if (req.user.role === 'institute_admin') {
        if (role.level === 1 || role.isSystemRole) {
          return Response.json({
            success: false,
            message: 'Cannot modify system roles or Level 1 roles'
          }, { status: 403 })
        }
      }

      // Remove existing permissions for this role
      const existingPermissions = await req.payload.find({
        collection: 'role-permissions',
        where: { role: { equals: roleId } }
      })

      for (const rp of existingPermissions.docs) {
        await req.payload.delete({
          collection: 'role-permissions',
          id: rp.id
        })
      }

      // Add new permissions
      const assignedPermissions = []
      for (const permissionId of permissionIds) {
        const rolePermission = await req.payload.create({
          collection: 'role-permissions',
          data: {
            role: roleId,
            permission: permissionId,
            isActive: true,
            assignedBy: req.user.id,
            assignedAt: new Date()
          }
        })
        assignedPermissions.push(rolePermission)
      }

      return Response.json({
        success: true,
        message: 'Permissions assigned successfully',
        assignedCount: assignedPermissions.length
      })

    } catch (error) {
      console.error('Assign permissions error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  },
  ['super_admin', 'institute_admin'] // Only admins can assign permissions
)

// Remove permission from role
export const removePermissionFromRoleEndpoint: Endpoint = {
  path: '/roles/:roleId/permissions/:permissionId',
  method: 'delete',
  handler: async (req: any) => {
    try {
      const { roleId, permissionId } = req.params

      if (!req.user) {
        return Response.json({
          success: false,
          message: 'Authentication required'
        }, { status: 401 })
      }

      // Check if user can modify roles
      if (req.user.role !== 'super_admin' && req.user.role !== 'institute_admin') {
        return Response.json({
          success: false,
          message: 'Only admins can remove permissions'
        }, { status: 403 })
      }

      // Find the role-permission relationship
      const rolePermissions = await req.payload.find({
        collection: 'role-permissions',
        where: {
          and: [
            { role: { equals: roleId } },
            { permission: { equals: permissionId } }
          ]
        }
      })

      if (rolePermissions.docs.length === 0) {
        return Response.json({
          success: false,
          message: 'Permission assignment not found'
        }, { status: 404 })
      }

      // Remove the assignment
      await req.payload.delete({
        collection: 'role-permissions',
        id: rolePermissions.docs[0].id
      })

      return Response.json({
        success: true,
        message: 'Permission removed successfully'
      })

    } catch (error) {
      console.error('Remove permission error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Get role statistics
export const getRoleStatisticsEndpoint: Endpoint = {
  path: '/roles/statistics',
  method: 'get',
  handler: async (req: any) => {
    try {
      if (!req.user) {
        return Response.json({
          success: false,
          message: 'Authentication required'
        }, { status: 401 })
      }

      // Get total roles count
      const totalRoles = await req.payload.find({
        collection: 'roles',
        limit: 0 // Just get count
      })

      // Get active roles count
      const activeRoles = await req.payload.find({
        collection: 'roles',
        where: { isActive: { equals: true } },
        limit: 0
      })

      // Get system roles count
      const systemRoles = await req.payload.find({
        collection: 'roles',
        where: { isSystemRole: { equals: true } },
        limit: 0
      })

      // Get custom roles count
      const customRoles = await req.payload.find({
        collection: 'roles',
        where: { isSystemRole: { equals: false } },
        limit: 0
      })

      // Get roles by level
      const rolesByLevel = await Promise.all([
        req.payload.find({
          collection: 'roles',
          where: { level: { equals: 1 } },
          limit: 0
        }),
        req.payload.find({
          collection: 'roles',
          where: { level: { equals: 2 } },
          limit: 0
        }),
        req.payload.find({
          collection: 'roles',
          where: { level: { equals: 3 } },
          limit: 0
        })
      ])

      // Get total permissions count
      const totalPermissions = await req.payload.find({
        collection: 'permissions',
        limit: 0
      })

      // Get active permissions count
      const activePermissions = await req.payload.find({
        collection: 'permissions',
        where: { isActive: { equals: true } },
        limit: 0
      })

      // Get role-permission assignments count
      const totalAssignments = await req.payload.find({
        collection: 'role-permissions',
        where: { isActive: { equals: true } },
        limit: 0
      })

      return Response.json({
        success: true,
        data: {
          roles: {
            total: totalRoles.totalDocs,
            active: activeRoles.totalDocs,
            inactive: totalRoles.totalDocs - activeRoles.totalDocs,
            system: systemRoles.totalDocs,
            custom: customRoles.totalDocs,
            byLevel: {
              level1: rolesByLevel[0].totalDocs,
              level2: rolesByLevel[1].totalDocs,
              level3: rolesByLevel[2].totalDocs
            }
          },
          permissions: {
            total: totalPermissions.totalDocs,
            active: activePermissions.totalDocs,
            inactive: totalPermissions.totalDocs - activePermissions.totalDocs
          },
          assignments: {
            total: totalAssignments.totalDocs
          }
        }
      })

    } catch (error) {
      console.error('Get role statistics error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Get role permissions (specific role)
export const getSpecificRolePermissionsEndpoint: Endpoint = {
  path: '/roles/:roleId/permissions',
  method: 'get',
  handler: async (req: any) => {
    try {
      const { roleId } = req.params

      if (!req.user) {
        return Response.json({
          success: false,
          message: 'Authentication required'
        }, { status: 401 })
      }

      // Get the role
      const role = await req.payload.findByID({
        collection: 'roles',
        id: roleId
      })

      if (!role) {
        return Response.json({
          success: false,
          message: 'Role not found'
        }, { status: 404 })
      }

      // Get role permissions
      const rolePermissions = await req.payload.find({
        collection: 'role-permissions',
        where: {
          and: [
            { role: { equals: roleId } },
            { isActive: { equals: true } }
          ]
        },
        populate: ['permission'],
        limit: 1000
      })

      const permissions = rolePermissions.docs
        .map((rp: any) => rp.permission)
        .filter((permission: any) => permission && permission.isActive)

      return Response.json({
        success: true,
        data: {
          role,
          permissions,
          permissionCount: permissions.length
        }
      })

    } catch (error) {
      console.error('Get role permissions error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}

// Bulk operations for roles
export const bulkRoleOperationsEndpoint: Endpoint = {
  path: '/roles/bulk',
  method: 'post',
  handler: async (req: any) => {
    try {
      const { operation, roleIds } = await req.json()

      if (!req.user) {
        return Response.json({
          success: false,
          message: 'Authentication required'
        }, { status: 401 })
      }

      if (req.user.role !== 'super_admin') {
        return Response.json({
          success: false,
          message: 'Only super admins can perform bulk operations'
        }, { status: 403 })
      }

      let results = []

      switch (operation) {
        case 'activate':
          for (const roleId of roleIds) {
            try {
              const result = await req.payload.update({
                collection: 'roles',
                id: roleId,
                data: { isActive: true }
              })
              results.push({ id: roleId, success: true, data: result })
            } catch (error: any) {
              results.push({ id: roleId, success: false, error: error.message })
            }
          }
          break

        case 'deactivate':
          for (const roleId of roleIds) {
            try {
              const result = await req.payload.update({
                collection: 'roles',
                id: roleId,
                data: { isActive: false }
              })
              results.push({ id: roleId, success: true, data: result })
            } catch (error: any) {
              results.push({ id: roleId, success: false, error: error.message })
            }
          }
          break

        case 'delete':
          for (const roleId of roleIds) {
            try {
              // Check if role is in use
              const usersWithRole = await req.payload.find({
                collection: 'users',
                where: { role: { equals: roleId } },
                limit: 1
              })

              if (usersWithRole.totalDocs > 0) {
                results.push({
                  id: roleId,
                  success: false,
                  error: 'Role is assigned to users'
                })
                continue
              }

              await req.payload.delete({
                collection: 'roles',
                id: roleId
              })
              results.push({ id: roleId, success: true })
            } catch (error: any) {
              results.push({ id: roleId, success: false, error: error.message })
            }
          }
          break

        default:
          return Response.json({
            success: false,
            message: 'Invalid operation'
          }, { status: 400 })
      }

      const successCount = results.filter(r => r.success).length
      const failureCount = results.filter(r => !r.success).length

      return Response.json({
        success: true,
        message: `Bulk operation completed: ${successCount} successful, ${failureCount} failed`,
        results,
        summary: {
          total: results.length,
          successful: successCount,
          failed: failureCount
        }
      })

    } catch (error) {
      console.error('Bulk role operations error:', error)
      return Response.json(
        { success: false, error: 'Internal server error' },
        { status: 500 }
      )
    }
  }
}
