{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/api.ts"], "sourcesContent": ["/**\n * API utility functions for making requests to the backend\n */\n\n// Get the API base URL from environment variables\nconst getApiBaseUrl = (): string => {\n  // Always use the full backend URL for API calls\n  return process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'\n}\n\n/**\n * Create a full API URL from a relative path\n */\nexport const createApiUrl = (path: string): string => {\n  const baseUrl = getApiBaseUrl()\n\n  // Remove leading slash from path if present\n  const cleanPath = path.startsWith('/') ? path.slice(1) : path\n\n  // Combine base URL with path\n  return `${baseUrl}/${cleanPath}`\n}\n\n/**\n * Get the auth token from localStorage or Zustand storage\n */\nconst getAuthToken = (): string | null => {\n  if (typeof window !== 'undefined') {\n    // First try direct auth_token\n    let token = localStorage.getItem('auth_token')\n\n    // If not found, try Zustand auth storage\n    if (!token) {\n      try {\n        const authStorage = localStorage.getItem('auth-storage')\n        if (authStorage) {\n          const parsed = JSON.parse(authStorage)\n          token = parsed?.state?.token || null\n        }\n      } catch (error) {\n        console.error('Failed to parse auth storage:', error)\n      }\n    }\n\n    console.log('🔍 getAuthToken:', { hasToken: !!token, tokenLength: token?.length })\n    return token\n  }\n  return null\n}\n\n/**\n * Make an API request with proper error handling\n */\nexport const apiRequest = async (\n  path: string,\n  options: RequestInit = {}\n): Promise<Response> => {\n  const url = createApiUrl(path)\n  const token = getAuthToken()\n\n  const defaultOptions: RequestInit = {\n    credentials: 'include',\n    headers: {\n      'Content-Type': 'application/json',\n      ...(token && { 'Authorization': `Bearer ${token}` }),\n      ...options.headers,\n    },\n  }\n\n\n\n  const finalOptions = {\n    ...defaultOptions,\n    ...options,\n    headers: {\n      ...defaultOptions.headers,\n      ...options.headers,\n    },\n  }\n\n  console.log('🔍 API Request:', {\n    url,\n    method: finalOptions.method || 'GET',\n    hasAuth: !!(finalOptions.headers as any)?.['Authorization']\n  })\n\n  try {\n    const response = await fetch(url, finalOptions)\n    console.log('✅ API Response:', {\n      url,\n      status: response.status,\n      ok: response.ok\n    })\n    return response\n  } catch (error) {\n    console.error('❌ API request failed:', { url, error })\n    throw error\n  }\n}\n\n/**\n * Make a GET request to the API\n */\nexport const apiGet = async (path: string, params?: Record<string, string>): Promise<Response> => {\n  let url = path\n  \n  if (params) {\n    const searchParams = new URLSearchParams(params)\n    url = `${path}?${searchParams.toString()}`\n  }\n  \n  return apiRequest(url, { method: 'GET' })\n}\n\n/**\n * Make a POST request to the API\n */\nexport const apiPost = async (path: string, data?: any): Promise<Response> => {\n  return apiRequest(path, {\n    method: 'POST',\n    body: data ? JSON.stringify(data) : undefined,\n  })\n}\n\n/**\n * Make a PUT request to the API\n */\nexport const apiPut = async (path: string, data?: any): Promise<Response> => {\n  return apiRequest(path, {\n    method: 'PUT',\n    body: data ? JSON.stringify(data) : undefined,\n  })\n}\n\n/**\n * Make a DELETE request to the API\n */\nexport const apiDelete = async (path: string): Promise<Response> => {\n  return apiRequest(path, { method: 'DELETE' })\n}\n\n/**\n * Handle API response and extract JSON data\n */\nexport const handleApiResponse = async <T = any>(response: Response): Promise<T> => {\n  if (!response.ok) {\n    const errorData = await response.json().catch(() => ({}))\n    throw new Error(errorData.message || errorData.error || `HTTP ${response.status}`)\n  }\n  \n  return response.json()\n}\n\n/**\n * Make a complete API call with error handling and JSON parsing\n */\nexport const apiCall = async <T = any>(\n  path: string,\n  options: RequestInit = {}\n): Promise<T> => {\n  const response = await apiRequest(path, options)\n  return handleApiResponse<T>(response)\n}\n\n/**\n * Convenience methods for common API operations\n */\nexport const api = {\n  get: async <T = any>(path: string, params?: Record<string, string>): Promise<T> => {\n    const response = await apiGet(path, params)\n    return handleApiResponse<T>(response)\n  },\n  \n  post: async <T = any>(path: string, data?: any): Promise<T> => {\n    const response = await apiPost(path, data)\n    return handleApiResponse<T>(response)\n  },\n  \n  put: async <T = any>(path: string, data?: any): Promise<T> => {\n    const response = await apiPut(path, data)\n    return handleApiResponse<T>(response)\n  },\n  \n  delete: async <T = any>(path: string): Promise<T> => {\n    const response = await apiDelete(path)\n    return handleApiResponse<T>(response)\n  },\n}\n"], "names": [], "mappings": "AAAA;;CAEC,GAED,kDAAkD;;;;;;;;;;;;AAClD,MAAM,gBAAgB;IACpB,gDAAgD;IAChD,OAAO,6DAAmC;AAC5C;AAKO,MAAM,eAAe,CAAC;IAC3B,MAAM,UAAU;IAEhB,4CAA4C;IAC5C,MAAM,YAAY,KAAK,UAAU,CAAC,OAAO,KAAK,KAAK,CAAC,KAAK;IAEzD,6BAA6B;IAC7B,OAAO,GAAG,QAAQ,CAAC,EAAE,WAAW;AAClC;AAEA;;CAEC,GACD,MAAM,eAAe;IACnB,uCAAmC;;IAmBnC;IACA,OAAO;AACT;AAKO,MAAM,aAAa,OACxB,MACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,MAAM,aAAa;IACzB,MAAM,QAAQ;IAEd,MAAM,iBAA8B;QAClC,aAAa;QACb,SAAS;YACP,gBAAgB;YAChB,GAAI,SAAS;gBAAE,iBAAiB,CAAC,OAAO,EAAE,OAAO;YAAC,CAAC;YACnD,GAAG,QAAQ,OAAO;QACpB;IACF;IAIA,MAAM,eAAe;QACnB,GAAG,cAAc;QACjB,GAAG,OAAO;QACV,SAAS;YACP,GAAG,eAAe,OAAO;YACzB,GAAG,QAAQ,OAAO;QACpB;IACF;IAEA,QAAQ,GAAG,CAAC,mBAAmB;QAC7B;QACA,QAAQ,aAAa,MAAM,IAAI;QAC/B,SAAS,CAAC,CAAE,aAAa,OAAO,EAAU,CAAC,gBAAgB;IAC7D;IAEA,IAAI;QACF,MAAM,WAAW,MAAM,MAAM,KAAK;QAClC,QAAQ,GAAG,CAAC,mBAAmB;YAC7B;YACA,QAAQ,SAAS,MAAM;YACvB,IAAI,SAAS,EAAE;QACjB;QACA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;YAAE;YAAK;QAAM;QACpD,MAAM;IACR;AACF;AAKO,MAAM,SAAS,OAAO,MAAc;IACzC,IAAI,MAAM;IAEV,IAAI,QAAQ;QACV,MAAM,eAAe,IAAI,gBAAgB;QACzC,MAAM,GAAG,KAAK,CAAC,EAAE,aAAa,QAAQ,IAAI;IAC5C;IAEA,OAAO,WAAW,KAAK;QAAE,QAAQ;IAAM;AACzC;AAKO,MAAM,UAAU,OAAO,MAAc;IAC1C,OAAO,WAAW,MAAM;QACtB,QAAQ;QACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;IACtC;AACF;AAKO,MAAM,SAAS,OAAO,MAAc;IACzC,OAAO,WAAW,MAAM;QACtB,QAAQ;QACR,MAAM,OAAO,KAAK,SAAS,CAAC,QAAQ;IACtC;AACF;AAKO,MAAM,YAAY,OAAO;IAC9B,OAAO,WAAW,MAAM;QAAE,QAAQ;IAAS;AAC7C;AAKO,MAAM,oBAAoB,OAAgB;IAC/C,IAAI,CAAC,SAAS,EAAE,EAAE;QAChB,MAAM,YAAY,MAAM,SAAS,IAAI,GAAG,KAAK,CAAC,IAAM,CAAC,CAAC,CAAC;QACvD,MAAM,IAAI,MAAM,UAAU,OAAO,IAAI,UAAU,KAAK,IAAI,CAAC,KAAK,EAAE,SAAS,MAAM,EAAE;IACnF;IAEA,OAAO,SAAS,IAAI;AACtB;AAKO,MAAM,UAAU,OACrB,MACA,UAAuB,CAAC,CAAC;IAEzB,MAAM,WAAW,MAAM,WAAW,MAAM;IACxC,OAAO,kBAAqB;AAC9B;AAKO,MAAM,MAAM;IACjB,KAAK,OAAgB,MAAc;QACjC,MAAM,WAAW,MAAM,OAAO,MAAM;QACpC,OAAO,kBAAqB;IAC9B;IAEA,MAAM,OAAgB,MAAc;QAClC,MAAM,WAAW,MAAM,QAAQ,MAAM;QACrC,OAAO,kBAAqB;IAC9B;IAEA,KAAK,OAAgB,MAAc;QACjC,MAAM,WAAW,MAAM,OAAO,MAAM;QACpC,OAAO,kBAAqB;IAC9B;IAEA,QAAQ,OAAgB;QACtB,MAAM,WAAW,MAAM,UAAU;QACjC,OAAO,kBAAqB;IAC9B;AACF", "debugId": null}}, {"offset": {"line": 144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/super-admin/useRolePermissionsStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools } from 'zustand/middleware'\nimport { toast } from 'sonner'\nimport { api } from '@/lib/api'\n\n// Types - Following Location Management patterns exactly\nexport interface Permission {\n  id: string\n  name: string\n  code: string\n  description?: string\n  resource: string\n  action: string\n  scope?: string\n  isActive: boolean\n  priority: number\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Role {\n  id: string\n  name: string\n  code: string\n  description?: string\n  level: string\n  scope?: string\n  isActive: boolean\n  isSystemRole: boolean\n  priority: number\n  permissions?: Permission[]\n  createdAt: string\n  updatedAt: string\n}\n\n// Following Location Management pagination structure exactly\ninterface Pagination {\n  page: number\n  limit: number\n  totalDocs: number\n  totalPages: number\n  hasNextPage: boolean\n  hasPrevPage: boolean\n}\n\n// Following Location Management filters structure exactly\ninterface Filters {\n  search: string\n  isActive?: boolean\n  level?: string\n  scope?: string\n  resource?: string\n  action?: string\n}\n\n// Following Location Management state structure exactly\ninterface RolePermissionsState {\n  // Data\n  roles: Role[]\n  permissions: Permission[]\n  \n  // UI State\n  viewMode: 'list' | 'card'\n  isLoading: boolean\n  error: string | null\n  \n  // Pagination - separate for each entity like Location Management\n  rolesPagination: Pagination\n  permissionsPagination: Pagination\n  \n  // Filters - separate for each entity like Location Management\n  rolesFilters: Filters\n  permissionsFilters: Filters\n  \n  // Selected items\n  selectedRole: Role | null\n  selectedPermission: Permission | null\n  selectedPermissions: string[]\n  \n  // UI Actions - Following Location Management patterns exactly\n  setViewMode: (mode: 'list' | 'card') => void\n  setRolesFilters: (filters: Partial<Filters>) => void\n  setPermissionsFilters: (filters: Partial<Filters>) => void\n  setSelectedRole: (role: Role | null) => void\n  setSelectedPermission: (permission: Permission | null) => void\n  setSelectedPermissions: (permissions: string[]) => void\n  clearError: () => void\n  \n  // Data Actions - Following Location Management patterns exactly\n  fetchRoles: (page?: number, filters?: Partial<Filters>) => Promise<void>\n  fetchPermissions: (page?: number, filters?: Partial<Filters>) => Promise<void>\n  fetchRolesWithPermissions: () => Promise<void>\n  fetchRolePermissions: (roleId: string) => Promise<void>\n  \n  // CRUD Actions - Roles\n  createRole: (roleData: Partial<Role>) => Promise<boolean>\n  updateRole: (id: string, roleData: Partial<Role>) => Promise<boolean>\n  deleteRole: (id: string) => Promise<boolean>\n  \n  // CRUD Actions - Permissions\n  createPermission: (permissionData: Partial<Permission>) => Promise<boolean>\n  updatePermission: (id: string, permissionData: Partial<Permission>) => Promise<boolean>\n  deletePermission: (id: string) => Promise<boolean>\n  \n  // Role-Permission Assignment\n  assignPermissionsToRole: (roleId: string, permissionIds: string[]) => Promise<boolean>\n  removePermissionFromRole: (roleId: string, permissionId: string) => Promise<boolean>\n}\n\n// Initial values following Location Management patterns exactly\nconst initialPagination: Pagination = {\n  page: 1,\n  limit: 10,\n  totalDocs: 0,\n  totalPages: 0,\n  hasNextPage: false,\n  hasPrevPage: false,\n}\n\nconst initialFilters: Filters = {\n  search: '',\n  isActive: undefined,\n  level: '',\n  scope: '',\n  resource: '',\n  action: '',\n}\n\nexport const useRolePermissionsStore = create<RolePermissionsState>()(\n  devtools(\n    (set, get) => ({\n      // Initial State - Following Location Management structure exactly\n      roles: [],\n      permissions: [],\n      viewMode: 'list',\n      isLoading: false,\n      error: null,\n      rolesPagination: initialPagination,\n      permissionsPagination: initialPagination,\n      rolesFilters: initialFilters,\n      permissionsFilters: initialFilters,\n      selectedRole: null,\n      selectedPermission: null,\n      selectedPermissions: [],\n      \n      // UI Actions - Following Location Management patterns exactly\n      setViewMode: (mode) => set({ viewMode: mode }),\n      \n      setRolesFilters: (newFilters) => set((state) => ({\n        rolesFilters: { ...state.rolesFilters, ...newFilters }\n      })),\n      \n      setPermissionsFilters: (newFilters) => set((state) => ({\n        permissionsFilters: { ...state.permissionsFilters, ...newFilters }\n      })),\n      \n      setSelectedRole: (role) => set({ \n        selectedRole: role,\n        selectedPermission: null,\n        selectedPermissions: []\n      }),\n      \n      setSelectedPermission: (permission) => set({ selectedPermission: permission }),\n      setSelectedPermissions: (permissions) => set({ selectedPermissions: permissions }),\n      clearError: () => set({ error: null }),\n      \n      // Fetch Roles - Following Location Management patterns exactly\n      fetchRoles: async (page = 1, filters) => {\n        set({ isLoading: true, error: null })\n        try {\n          const currentFilters = filters || get().rolesFilters\n          const queryParams = new URLSearchParams({\n            page: page.toString(),\n            limit: '10',\n            ...(currentFilters.search && { search: currentFilters.search }),\n            ...(currentFilters.level && { level: currentFilters.level }),\n            ...(currentFilters.isActive !== undefined && { isActive: currentFilters.isActive.toString() }),\n            ...(currentFilters.scope && { scope: currentFilters.scope }),\n          })\n          \n          const response = await api.get(`/api/roles?${queryParams}`)\n          const data = response.data\n          \n          if (data.success) {\n            set({\n              roles: data.docs,\n              rolesPagination: {\n                page: data.page,\n                limit: data.limit,\n                totalDocs: data.totalDocs,\n                totalPages: data.totalPages,\n                hasNextPage: data.hasNextPage,\n                hasPrevPage: data.hasPrevPage,\n              },\n              isLoading: false,\n            })\n          } else {\n            throw new Error(data.message || 'Failed to fetch roles')\n          }\n        } catch (error: any) {\n          console.error('Error fetching roles:', error)\n          set({ \n            error: error.message || 'Failed to fetch roles',\n            isLoading: false \n          })\n          toast.error('Failed to fetch roles')\n        }\n      },\n      \n      // Fetch Permissions - Following Location Management patterns exactly\n      fetchPermissions: async (page = 1, filters) => {\n        set({ isLoading: true, error: null })\n        try {\n          const currentFilters = filters || get().permissionsFilters\n          const queryParams = new URLSearchParams({\n            page: page.toString(),\n            limit: '10',\n            ...(currentFilters.search && { search: currentFilters.search }),\n            ...(currentFilters.resource && { resource: currentFilters.resource }),\n            ...(currentFilters.action && { action: currentFilters.action }),\n            ...(currentFilters.isActive !== undefined && { isActive: currentFilters.isActive.toString() }),\n          })\n          \n          const response = await api.get(`/api/permissions?${queryParams}`)\n          const data = response.data\n          \n          if (data.success) {\n            set({\n              permissions: data.docs,\n              permissionsPagination: {\n                page: data.page,\n                limit: data.limit,\n                totalDocs: data.totalDocs,\n                totalPages: data.totalPages,\n                hasNextPage: data.hasNextPage,\n                hasPrevPage: data.hasPrevPage,\n              },\n              isLoading: false,\n            })\n          } else {\n            throw new Error(data.message || 'Failed to fetch permissions')\n          }\n        } catch (error: any) {\n          console.error('Error fetching permissions:', error)\n          set({ \n            error: error.message || 'Failed to fetch permissions',\n            isLoading: false \n          })\n          toast.error('Failed to fetch permissions')\n        }\n      },\n      \n      // Fetch Roles with Permissions - Custom endpoint\n      fetchRolesWithPermissions: async () => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.get('/api/roles-with-permissions')\n          const data = response.data\n          \n          if (data.success) {\n            set({\n              roles: data.data,\n              isLoading: false,\n            })\n          } else {\n            throw new Error(data.message || 'Failed to fetch roles with permissions')\n          }\n        } catch (error: any) {\n          console.error('Error fetching roles with permissions:', error)\n          set({ \n            error: error.message || 'Failed to fetch roles with permissions',\n            isLoading: false \n          })\n          toast.error('Failed to fetch roles with permissions')\n        }\n      },\n      \n      // Fetch Role Permissions\n      fetchRolePermissions: async (roleId: string) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.get(`/api/roles/${roleId}/permissions`)\n          const data = response.data\n          \n          if (data.success) {\n            // Update the selected role with its permissions\n            const { selectedRole } = get()\n            if (selectedRole && selectedRole.id === roleId) {\n              set({\n                selectedRole: {\n                  ...selectedRole,\n                  permissions: data.data\n                },\n                isLoading: false,\n              })\n            }\n          } else {\n            throw new Error(data.message || 'Failed to fetch role permissions')\n          }\n        } catch (error: any) {\n          console.error('Error fetching role permissions:', error)\n          set({ \n            error: error.message || 'Failed to fetch role permissions',\n            isLoading: false \n          })\n          toast.error('Failed to fetch role permissions')\n        }\n      },\n\n      // CRUD Operations - Roles\n      createRole: async (roleData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.post('/api/roles', roleData)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh roles list\n            await get().fetchRoles()\n            toast.success('Role created successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to create role')\n          }\n        } catch (error: any) {\n          console.error('Error creating role:', error)\n          set({\n            error: error.message || 'Failed to create role',\n            isLoading: false\n          })\n          toast.error('Failed to create role')\n          return false\n        }\n      },\n\n      updateRole: async (id, roleData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.put(`/api/roles/${id}`, roleData)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh roles list\n            await get().fetchRoles()\n            toast.success('Role updated successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to update role')\n          }\n        } catch (error: any) {\n          console.error('Error updating role:', error)\n          set({\n            error: error.message || 'Failed to update role',\n            isLoading: false\n          })\n          toast.error('Failed to update role')\n          return false\n        }\n      },\n\n      deleteRole: async (id) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.delete(`/api/roles/${id}`)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh roles list\n            await get().fetchRoles()\n            toast.success('Role deleted successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to delete role')\n          }\n        } catch (error: any) {\n          console.error('Error deleting role:', error)\n          set({\n            error: error.message || 'Failed to delete role',\n            isLoading: false\n          })\n          toast.error('Failed to delete role')\n          return false\n        }\n      },\n\n      // CRUD Operations - Permissions\n      createPermission: async (permissionData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.post('/api/permissions', permissionData)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh permissions list\n            await get().fetchPermissions()\n            toast.success('Permission created successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to create permission')\n          }\n        } catch (error: any) {\n          console.error('Error creating permission:', error)\n          set({\n            error: error.message || 'Failed to create permission',\n            isLoading: false\n          })\n          toast.error('Failed to create permission')\n          return false\n        }\n      },\n\n      updatePermission: async (id, permissionData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.put(`/api/permissions/${id}`, permissionData)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh permissions list\n            await get().fetchPermissions()\n            toast.success('Permission updated successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to update permission')\n          }\n        } catch (error: any) {\n          console.error('Error updating permission:', error)\n          set({\n            error: error.message || 'Failed to update permission',\n            isLoading: false\n          })\n          toast.error('Failed to update permission')\n          return false\n        }\n      },\n\n      deletePermission: async (id) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.delete(`/api/permissions/${id}`)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh permissions list\n            await get().fetchPermissions()\n            toast.success('Permission deleted successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to delete permission')\n          }\n        } catch (error: any) {\n          console.error('Error deleting permission:', error)\n          set({\n            error: error.message || 'Failed to delete permission',\n            isLoading: false\n          })\n          toast.error('Failed to delete permission')\n          return false\n        }\n      },\n\n      // Role-Permission Assignment\n      assignPermissionsToRole: async (roleId, permissionIds) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.post(`/api/roles/${roleId}/permissions`, { permissionIds })\n          const data = response.data\n\n          if (data.success) {\n            // Refresh roles with permissions\n            await get().fetchRolesWithPermissions()\n            toast.success('Permissions assigned successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to assign permissions')\n          }\n        } catch (error: any) {\n          console.error('Error assigning permissions:', error)\n          set({\n            error: error.message || 'Failed to assign permissions',\n            isLoading: false\n          })\n          toast.error('Failed to assign permissions')\n          return false\n        }\n      },\n\n      removePermissionFromRole: async (roleId, permissionId) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.delete(`/api/roles/${roleId}/permissions/${permissionId}`)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh roles with permissions\n            await get().fetchRolesWithPermissions()\n            toast.success('Permission removed successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to remove permission')\n          }\n        } catch (error: any) {\n          console.error('Error removing permission:', error)\n          set({\n            error: error.message || 'Failed to remove permission',\n            isLoading: false\n          })\n          toast.error('Failed to remove permission')\n          return false\n        }\n      },\n    }),\n    {\n      name: 'role-permissions-storage',\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AA0GA,gEAAgE;AAChE,MAAM,oBAAgC;IACpC,MAAM;IACN,OAAO;IACP,WAAW;IACX,YAAY;IACZ,aAAa;IACb,aAAa;AACf;AAEA,MAAM,iBAA0B;IAC9B,QAAQ;IACR,UAAU;IACV,OAAO;IACP,OAAO;IACP,UAAU;IACV,QAAQ;AACV;AAEO,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,SAAM,AAAD,IAC1C,CAAA,GAAA,0PAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,kEAAkE;QAClE,OAAO,EAAE;QACT,aAAa,EAAE;QACf,UAAU;QACV,WAAW;QACX,OAAO;QACP,iBAAiB;QACjB,uBAAuB;QACvB,cAAc;QACd,oBAAoB;QACpB,cAAc;QACd,oBAAoB;QACpB,qBAAqB,EAAE;QAEvB,8DAA8D;QAC9D,aAAa,CAAC,OAAS,IAAI;gBAAE,UAAU;YAAK;QAE5C,iBAAiB,CAAC,aAAe,IAAI,CAAC,QAAU,CAAC;oBAC/C,cAAc;wBAAE,GAAG,MAAM,YAAY;wBAAE,GAAG,UAAU;oBAAC;gBACvD,CAAC;QAED,uBAAuB,CAAC,aAAe,IAAI,CAAC,QAAU,CAAC;oBACrD,oBAAoB;wBAAE,GAAG,MAAM,kBAAkB;wBAAE,GAAG,UAAU;oBAAC;gBACnE,CAAC;QAED,iBAAiB,CAAC,OAAS,IAAI;gBAC7B,cAAc;gBACd,oBAAoB;gBACpB,qBAAqB,EAAE;YACzB;QAEA,uBAAuB,CAAC,aAAe,IAAI;gBAAE,oBAAoB;YAAW;QAC5E,wBAAwB,CAAC,cAAgB,IAAI;gBAAE,qBAAqB;YAAY;QAChF,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;QAEpC,+DAA+D;QAC/D,YAAY,OAAO,OAAO,CAAC,EAAE;YAC3B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,iBAAiB,WAAW,MAAM,YAAY;gBACpD,MAAM,cAAc,IAAI,gBAAgB;oBACtC,MAAM,KAAK,QAAQ;oBACnB,OAAO;oBACP,GAAI,eAAe,MAAM,IAAI;wBAAE,QAAQ,eAAe,MAAM;oBAAC,CAAC;oBAC9D,GAAI,eAAe,KAAK,IAAI;wBAAE,OAAO,eAAe,KAAK;oBAAC,CAAC;oBAC3D,GAAI,eAAe,QAAQ,KAAK,aAAa;wBAAE,UAAU,eAAe,QAAQ,CAAC,QAAQ;oBAAG,CAAC;oBAC7F,GAAI,eAAe,KAAK,IAAI;wBAAE,OAAO,eAAe,KAAK;oBAAC,CAAC;gBAC7D;gBAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,aAAa;gBAC1D,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBACF,OAAO,KAAK,IAAI;wBAChB,iBAAiB;4BACf,MAAM,KAAK,IAAI;4BACf,OAAO,KAAK,KAAK;4BACjB,WAAW,KAAK,SAAS;4BACzB,YAAY,KAAK,UAAU;4BAC3B,aAAa,KAAK,WAAW;4BAC7B,aAAa,KAAK,WAAW;wBAC/B;wBACA,WAAW;oBACb;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,qEAAqE;QACrE,kBAAkB,OAAO,OAAO,CAAC,EAAE;YACjC,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,iBAAiB,WAAW,MAAM,kBAAkB;gBAC1D,MAAM,cAAc,IAAI,gBAAgB;oBACtC,MAAM,KAAK,QAAQ;oBACnB,OAAO;oBACP,GAAI,eAAe,MAAM,IAAI;wBAAE,QAAQ,eAAe,MAAM;oBAAC,CAAC;oBAC9D,GAAI,eAAe,QAAQ,IAAI;wBAAE,UAAU,eAAe,QAAQ;oBAAC,CAAC;oBACpE,GAAI,eAAe,MAAM,IAAI;wBAAE,QAAQ,eAAe,MAAM;oBAAC,CAAC;oBAC9D,GAAI,eAAe,QAAQ,KAAK,aAAa;wBAAE,UAAU,eAAe,QAAQ,CAAC,QAAQ;oBAAG,CAAC;gBAC/F;gBAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,aAAa;gBAChE,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBACF,aAAa,KAAK,IAAI;wBACtB,uBAAuB;4BACrB,MAAM,KAAK,IAAI;4BACf,OAAO,KAAK,KAAK;4BACjB,WAAW,KAAK,SAAS;4BACzB,YAAY,KAAK,UAAU;4BAC3B,aAAa,KAAK,WAAW;4BAC7B,aAAa,KAAK,WAAW;wBAC/B;wBACA,WAAW;oBACb;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,iDAAiD;QACjD,2BAA2B;YACzB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;gBAC/B,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBACF,OAAO,KAAK,IAAI;wBAChB,WAAW;oBACb;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,yBAAyB;QACzB,sBAAsB,OAAO;YAC3B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,OAAO,YAAY,CAAC;gBACjE,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,gDAAgD;oBAChD,MAAM,EAAE,YAAY,EAAE,GAAG;oBACzB,IAAI,gBAAgB,aAAa,EAAE,KAAK,QAAQ;wBAC9C,IAAI;4BACF,cAAc;gCACZ,GAAG,YAAY;gCACf,aAAa,KAAK,IAAI;4BACxB;4BACA,WAAW;wBACb;oBACF;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,0BAA0B;QAC1B,YAAY,OAAO;YACjB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,cAAc;gBAC9C,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,qBAAqB;oBACrB,MAAM,MAAM,UAAU;oBACtB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,YAAY,OAAO,IAAI;YACrB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,WAAW,EAAE,IAAI,EAAE;gBACnD,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,qBAAqB;oBACrB,MAAM,MAAM,UAAU;oBACtB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,YAAY,OAAO;YACjB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,IAAI;gBACpD,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,qBAAqB;oBACrB,MAAM,MAAM,UAAU;oBACtB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,gCAAgC;QAChC,kBAAkB,OAAO;YACvB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,oBAAoB;gBACpD,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,2BAA2B;oBAC3B,MAAM,MAAM,gBAAgB;oBAC5B,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,kBAAkB,OAAO,IAAI;YAC3B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,iBAAiB,EAAE,IAAI,EAAE;gBACzD,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,2BAA2B;oBAC3B,MAAM,MAAM,gBAAgB;oBAC5B,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,kBAAkB,OAAO;YACvB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,iBAAiB,EAAE,IAAI;gBAC1D,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,2BAA2B;oBAC3B,MAAM,MAAM,gBAAgB;oBAC5B,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,6BAA6B;QAC7B,yBAAyB,OAAO,QAAQ;YACtC,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,CAAC,WAAW,EAAE,OAAO,YAAY,CAAC,EAAE;oBAAE;gBAAc;gBACpF,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,iCAAiC;oBACjC,MAAM,MAAM,yBAAyB;oBACrC,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,0BAA0B,OAAO,QAAQ;YACvC,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,WAAW,EAAE,OAAO,aAAa,EAAE,cAAc;gBACpF,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,iCAAiC;oBACjC,MAAM,MAAM,yBAAyB;oBACrC,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;IACF,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/stores/useRolePermissionsStore.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { devtools } from 'zustand/middleware'\nimport { toast } from 'sonner'\nimport { api } from '@/lib/api'\n\n// Types - Following Location Management patterns exactly\nexport interface Permission {\n  id: string\n  name: string\n  code: string\n  description?: string\n  resource: string\n  action: string\n  scope?: string\n  isActive: boolean\n  priority: number\n  createdAt: string\n  updatedAt: string\n}\n\nexport interface Role {\n  id: string\n  name: string\n  code: string\n  description?: string\n  level: string\n  scope?: string\n  isActive: boolean\n  isSystemRole: boolean\n  priority: number\n  permissions?: Permission[]\n  createdAt: string\n  updatedAt: string\n}\n\n// Following Location Management pagination structure exactly\ninterface Pagination {\n  page: number\n  limit: number\n  totalDocs: number\n  totalPages: number\n  hasNextPage: boolean\n  hasPrevPage: boolean\n}\n\n// Following Location Management filters structure exactly\ninterface Filters {\n  search: string\n  isActive?: boolean\n  level?: string\n  scope?: string\n  resource?: string\n  action?: string\n}\n\n// Following Location Management state structure exactly\ninterface RolePermissionsState {\n  // Data\n  roles: Role[]\n  permissions: Permission[]\n  \n  // UI State\n  viewMode: 'list' | 'card'\n  isLoading: boolean\n  error: string | null\n  \n  // Pagination - separate for each entity like Location Management\n  rolesPagination: Pagination\n  permissionsPagination: Pagination\n  \n  // Filters - separate for each entity like Location Management\n  rolesFilters: Filters\n  permissionsFilters: Filters\n  \n  // Selected items\n  selectedRole: Role | null\n  selectedPermission: Permission | null\n  selectedPermissions: string[]\n  \n  // UI Actions - Following Location Management patterns exactly\n  setViewMode: (mode: 'list' | 'card') => void\n  setRolesFilters: (filters: Partial<Filters>) => void\n  setPermissionsFilters: (filters: Partial<Filters>) => void\n  setSelectedRole: (role: Role | null) => void\n  setSelectedPermission: (permission: Permission | null) => void\n  setSelectedPermissions: (permissions: string[]) => void\n  clearError: () => void\n  \n  // Data Actions - Following Location Management patterns exactly\n  fetchRoles: (page?: number, filters?: Partial<Filters>) => Promise<void>\n  fetchPermissions: (page?: number, filters?: Partial<Filters>) => Promise<void>\n  fetchRolesWithPermissions: () => Promise<void>\n  fetchRolePermissions: (roleId: string) => Promise<void>\n  \n  // CRUD Actions - Roles\n  createRole: (roleData: Partial<Role>) => Promise<boolean>\n  updateRole: (id: string, roleData: Partial<Role>) => Promise<boolean>\n  deleteRole: (id: string) => Promise<boolean>\n  \n  // CRUD Actions - Permissions\n  createPermission: (permissionData: Partial<Permission>) => Promise<boolean>\n  updatePermission: (id: string, permissionData: Partial<Permission>) => Promise<boolean>\n  deletePermission: (id: string) => Promise<boolean>\n  \n  // Role-Permission Assignment\n  assignPermissionsToRole: (roleId: string, permissionIds: string[]) => Promise<boolean>\n  removePermissionFromRole: (roleId: string, permissionId: string) => Promise<boolean>\n}\n\n// Initial values following Location Management patterns exactly\nconst initialPagination: Pagination = {\n  page: 1,\n  limit: 10,\n  totalDocs: 0,\n  totalPages: 0,\n  hasNextPage: false,\n  hasPrevPage: false,\n}\n\nconst initialFilters: Filters = {\n  search: '',\n  isActive: undefined,\n  level: '',\n  scope: '',\n  resource: '',\n  action: '',\n}\n\nexport const useRolePermissionsStore = create<RolePermissionsState>()(\n  devtools(\n    (set, get) => ({\n      // Initial State - Following Location Management structure exactly\n      roles: [],\n      permissions: [],\n      viewMode: 'list',\n      isLoading: false,\n      error: null,\n      rolesPagination: initialPagination,\n      permissionsPagination: initialPagination,\n      rolesFilters: initialFilters,\n      permissionsFilters: initialFilters,\n      selectedRole: null,\n      selectedPermission: null,\n      selectedPermissions: [],\n      \n      // UI Actions - Following Location Management patterns exactly\n      setViewMode: (mode) => set({ viewMode: mode }),\n      \n      setRolesFilters: (newFilters) => set((state) => ({\n        rolesFilters: { ...state.rolesFilters, ...newFilters }\n      })),\n      \n      setPermissionsFilters: (newFilters) => set((state) => ({\n        permissionsFilters: { ...state.permissionsFilters, ...newFilters }\n      })),\n      \n      setSelectedRole: (role) => set({ \n        selectedRole: role,\n        selectedPermission: null,\n        selectedPermissions: []\n      }),\n      \n      setSelectedPermission: (permission) => set({ selectedPermission: permission }),\n      setSelectedPermissions: (permissions) => set({ selectedPermissions: permissions }),\n      clearError: () => set({ error: null }),\n      \n      // Fetch Roles - Following Location Management patterns exactly\n      fetchRoles: async (page = 1, filters) => {\n        set({ isLoading: true, error: null })\n        try {\n          const currentFilters = filters || get().rolesFilters\n          const queryParams = new URLSearchParams({\n            page: page.toString(),\n            limit: '10',\n            ...(currentFilters.search && { search: currentFilters.search }),\n            ...(currentFilters.level && { level: currentFilters.level }),\n            ...(currentFilters.isActive !== undefined && { isActive: currentFilters.isActive.toString() }),\n            ...(currentFilters.scope && { scope: currentFilters.scope }),\n          })\n          \n          const response = await api.get(`/roles?${queryParams}`)\n          const data = response.data\n          \n          if (data.success) {\n            set({\n              roles: data.docs,\n              rolesPagination: {\n                page: data.page,\n                limit: data.limit,\n                totalDocs: data.totalDocs,\n                totalPages: data.totalPages,\n                hasNextPage: data.hasNextPage,\n                hasPrevPage: data.hasPrevPage,\n              },\n              isLoading: false,\n            })\n          } else {\n            throw new Error(data.message || 'Failed to fetch roles')\n          }\n        } catch (error: any) {\n          console.error('Error fetching roles:', error)\n          set({ \n            error: error.message || 'Failed to fetch roles',\n            isLoading: false \n          })\n          toast.error('Failed to fetch roles')\n        }\n      },\n      \n      // Fetch Permissions - Following Location Management patterns exactly\n      fetchPermissions: async (page = 1, filters) => {\n        set({ isLoading: true, error: null })\n        try {\n          const currentFilters = filters || get().permissionsFilters\n          const queryParams = new URLSearchParams({\n            page: page.toString(),\n            limit: '10',\n            ...(currentFilters.search && { search: currentFilters.search }),\n            ...(currentFilters.resource && { resource: currentFilters.resource }),\n            ...(currentFilters.action && { action: currentFilters.action }),\n            ...(currentFilters.isActive !== undefined && { isActive: currentFilters.isActive.toString() }),\n          })\n          \n          const response = await api.get(`/permissions?${queryParams}`)\n          const data = response.data\n          \n          if (data.success) {\n            set({\n              permissions: data.docs,\n              permissionsPagination: {\n                page: data.page,\n                limit: data.limit,\n                totalDocs: data.totalDocs,\n                totalPages: data.totalPages,\n                hasNextPage: data.hasNextPage,\n                hasPrevPage: data.hasPrevPage,\n              },\n              isLoading: false,\n            })\n          } else {\n            throw new Error(data.message || 'Failed to fetch permissions')\n          }\n        } catch (error: any) {\n          console.error('Error fetching permissions:', error)\n          set({ \n            error: error.message || 'Failed to fetch permissions',\n            isLoading: false \n          })\n          toast.error('Failed to fetch permissions')\n        }\n      },\n      \n      // Fetch Roles with Permissions - Custom endpoint\n      fetchRolesWithPermissions: async () => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.get('/roles-with-permissions')\n          const data = response.data\n          \n          if (data.success) {\n            set({\n              roles: data.data,\n              isLoading: false,\n            })\n          } else {\n            throw new Error(data.message || 'Failed to fetch roles with permissions')\n          }\n        } catch (error: any) {\n          console.error('Error fetching roles with permissions:', error)\n          set({ \n            error: error.message || 'Failed to fetch roles with permissions',\n            isLoading: false \n          })\n          toast.error('Failed to fetch roles with permissions')\n        }\n      },\n      \n      // Fetch Role Permissions\n      fetchRolePermissions: async (roleId: string) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.get(`/roles/${roleId}/permissions`)\n          const data = response.data\n          \n          if (data.success) {\n            // Update the selected role with its permissions\n            const { selectedRole } = get()\n            if (selectedRole && selectedRole.id === roleId) {\n              set({\n                selectedRole: {\n                  ...selectedRole,\n                  permissions: data.data\n                },\n                isLoading: false,\n              })\n            }\n          } else {\n            throw new Error(data.message || 'Failed to fetch role permissions')\n          }\n        } catch (error: any) {\n          console.error('Error fetching role permissions:', error)\n          set({ \n            error: error.message || 'Failed to fetch role permissions',\n            isLoading: false \n          })\n          toast.error('Failed to fetch role permissions')\n        }\n      },\n\n      // CRUD Operations - Roles\n      createRole: async (roleData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.post('/roles', roleData)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh roles list\n            await get().fetchRoles()\n            toast.success('Role created successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to create role')\n          }\n        } catch (error: any) {\n          console.error('Error creating role:', error)\n          set({\n            error: error.message || 'Failed to create role',\n            isLoading: false\n          })\n          toast.error('Failed to create role')\n          return false\n        }\n      },\n\n      updateRole: async (id, roleData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.put(`/roles/${id}`, roleData)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh roles list\n            await get().fetchRoles()\n            toast.success('Role updated successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to update role')\n          }\n        } catch (error: any) {\n          console.error('Error updating role:', error)\n          set({\n            error: error.message || 'Failed to update role',\n            isLoading: false\n          })\n          toast.error('Failed to update role')\n          return false\n        }\n      },\n\n      deleteRole: async (id) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.delete(`/roles/${id}`)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh roles list\n            await get().fetchRoles()\n            toast.success('Role deleted successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to delete role')\n          }\n        } catch (error: any) {\n          console.error('Error deleting role:', error)\n          set({\n            error: error.message || 'Failed to delete role',\n            isLoading: false\n          })\n          toast.error('Failed to delete role')\n          return false\n        }\n      },\n\n      // CRUD Operations - Permissions\n      createPermission: async (permissionData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.post('/permissions', permissionData)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh permissions list\n            await get().fetchPermissions()\n            toast.success('Permission created successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to create permission')\n          }\n        } catch (error: any) {\n          console.error('Error creating permission:', error)\n          set({\n            error: error.message || 'Failed to create permission',\n            isLoading: false\n          })\n          toast.error('Failed to create permission')\n          return false\n        }\n      },\n\n      updatePermission: async (id, permissionData) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.put(`/permissions/${id}`, permissionData)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh permissions list\n            await get().fetchPermissions()\n            toast.success('Permission updated successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to update permission')\n          }\n        } catch (error: any) {\n          console.error('Error updating permission:', error)\n          set({\n            error: error.message || 'Failed to update permission',\n            isLoading: false\n          })\n          toast.error('Failed to update permission')\n          return false\n        }\n      },\n\n      deletePermission: async (id) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.delete(`/permissions/${id}`)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh permissions list\n            await get().fetchPermissions()\n            toast.success('Permission deleted successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to delete permission')\n          }\n        } catch (error: any) {\n          console.error('Error deleting permission:', error)\n          set({\n            error: error.message || 'Failed to delete permission',\n            isLoading: false\n          })\n          toast.error('Failed to delete permission')\n          return false\n        }\n      },\n\n      // Role-Permission Assignment\n      assignPermissionsToRole: async (roleId, permissionIds) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.post(`/roles/${roleId}/permissions`, { permissionIds })\n          const data = response.data\n\n          if (data.success) {\n            // Refresh roles with permissions\n            await get().fetchRolesWithPermissions()\n            toast.success('Permissions assigned successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to assign permissions')\n          }\n        } catch (error: any) {\n          console.error('Error assigning permissions:', error)\n          set({\n            error: error.message || 'Failed to assign permissions',\n            isLoading: false\n          })\n          toast.error('Failed to assign permissions')\n          return false\n        }\n      },\n\n      removePermissionFromRole: async (roleId, permissionId) => {\n        set({ isLoading: true, error: null })\n        try {\n          const response = await api.delete(`/roles/${roleId}/permissions/${permissionId}`)\n          const data = response.data\n\n          if (data.success) {\n            // Refresh roles with permissions\n            await get().fetchRolesWithPermissions()\n            toast.success('Permission removed successfully')\n            return true\n          } else {\n            throw new Error(data.message || 'Failed to remove permission')\n          }\n        } catch (error: any) {\n          console.error('Error removing permission:', error)\n          set({\n            error: error.message || 'Failed to remove permission',\n            isLoading: false\n          })\n          toast.error('Failed to remove permission')\n          return false\n        }\n      },\n    }),\n    {\n      name: 'role-permissions-storage',\n    }\n  )\n)\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AA0GA,gEAAgE;AAChE,MAAM,oBAAgC;IACpC,MAAM;IACN,OAAO;IACP,WAAW;IACX,YAAY;IACZ,aAAa;IACb,aAAa;AACf;AAEA,MAAM,iBAA0B;IAC9B,QAAQ;IACR,UAAU;IACV,OAAO;IACP,OAAO;IACP,UAAU;IACV,QAAQ;AACV;AAEO,MAAM,0BAA0B,CAAA,GAAA,qPAAA,CAAA,SAAM,AAAD,IAC1C,CAAA,GAAA,0PAAA,CAAA,WAAQ,AAAD,EACL,CAAC,KAAK,MAAQ,CAAC;QACb,kEAAkE;QAClE,OAAO,EAAE;QACT,aAAa,EAAE;QACf,UAAU;QACV,WAAW;QACX,OAAO;QACP,iBAAiB;QACjB,uBAAuB;QACvB,cAAc;QACd,oBAAoB;QACpB,cAAc;QACd,oBAAoB;QACpB,qBAAqB,EAAE;QAEvB,8DAA8D;QAC9D,aAAa,CAAC,OAAS,IAAI;gBAAE,UAAU;YAAK;QAE5C,iBAAiB,CAAC,aAAe,IAAI,CAAC,QAAU,CAAC;oBAC/C,cAAc;wBAAE,GAAG,MAAM,YAAY;wBAAE,GAAG,UAAU;oBAAC;gBACvD,CAAC;QAED,uBAAuB,CAAC,aAAe,IAAI,CAAC,QAAU,CAAC;oBACrD,oBAAoB;wBAAE,GAAG,MAAM,kBAAkB;wBAAE,GAAG,UAAU;oBAAC;gBACnE,CAAC;QAED,iBAAiB,CAAC,OAAS,IAAI;gBAC7B,cAAc;gBACd,oBAAoB;gBACpB,qBAAqB,EAAE;YACzB;QAEA,uBAAuB,CAAC,aAAe,IAAI;gBAAE,oBAAoB;YAAW;QAC5E,wBAAwB,CAAC,cAAgB,IAAI;gBAAE,qBAAqB;YAAY;QAChF,YAAY,IAAM,IAAI;gBAAE,OAAO;YAAK;QAEpC,+DAA+D;QAC/D,YAAY,OAAO,OAAO,CAAC,EAAE;YAC3B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,iBAAiB,WAAW,MAAM,YAAY;gBACpD,MAAM,cAAc,IAAI,gBAAgB;oBACtC,MAAM,KAAK,QAAQ;oBACnB,OAAO;oBACP,GAAI,eAAe,MAAM,IAAI;wBAAE,QAAQ,eAAe,MAAM;oBAAC,CAAC;oBAC9D,GAAI,eAAe,KAAK,IAAI;wBAAE,OAAO,eAAe,KAAK;oBAAC,CAAC;oBAC3D,GAAI,eAAe,QAAQ,KAAK,aAAa;wBAAE,UAAU,eAAe,QAAQ,CAAC,QAAQ;oBAAG,CAAC;oBAC7F,GAAI,eAAe,KAAK,IAAI;wBAAE,OAAO,eAAe,KAAK;oBAAC,CAAC;gBAC7D;gBAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,aAAa;gBACtD,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBACF,OAAO,KAAK,IAAI;wBAChB,iBAAiB;4BACf,MAAM,KAAK,IAAI;4BACf,OAAO,KAAK,KAAK;4BACjB,WAAW,KAAK,SAAS;4BACzB,YAAY,KAAK,UAAU;4BAC3B,aAAa,KAAK,WAAW;4BAC7B,aAAa,KAAK,WAAW;wBAC/B;wBACA,WAAW;oBACb;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,yBAAyB;gBACvC,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,qEAAqE;QACrE,kBAAkB,OAAO,OAAO,CAAC,EAAE;YACjC,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,iBAAiB,WAAW,MAAM,kBAAkB;gBAC1D,MAAM,cAAc,IAAI,gBAAgB;oBACtC,MAAM,KAAK,QAAQ;oBACnB,OAAO;oBACP,GAAI,eAAe,MAAM,IAAI;wBAAE,QAAQ,eAAe,MAAM;oBAAC,CAAC;oBAC9D,GAAI,eAAe,QAAQ,IAAI;wBAAE,UAAU,eAAe,QAAQ;oBAAC,CAAC;oBACpE,GAAI,eAAe,MAAM,IAAI;wBAAE,QAAQ,eAAe,MAAM;oBAAC,CAAC;oBAC9D,GAAI,eAAe,QAAQ,KAAK,aAAa;wBAAE,UAAU,eAAe,QAAQ,CAAC,QAAQ;oBAAG,CAAC;gBAC/F;gBAEA,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,aAAa;gBAC5D,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBACF,aAAa,KAAK,IAAI;wBACtB,uBAAuB;4BACrB,MAAM,KAAK,IAAI;4BACf,OAAO,KAAK,KAAK;4BACjB,WAAW,KAAK,SAAS;4BACzB,YAAY,KAAK,UAAU;4BAC3B,aAAa,KAAK,WAAW;4BAC7B,aAAa,KAAK,WAAW;wBAC/B;wBACA,WAAW;oBACb;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,+BAA+B;gBAC7C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,iDAAiD;QACjD,2BAA2B;YACzB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC;gBAC/B,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,IAAI;wBACF,OAAO,KAAK,IAAI;wBAChB,WAAW;oBACb;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,0CAA0C;gBACxD,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,yBAAyB;QACzB,sBAAsB,OAAO;YAC3B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,OAAO,YAAY,CAAC;gBAC7D,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,gDAAgD;oBAChD,MAAM,EAAE,YAAY,EAAE,GAAG;oBACzB,IAAI,gBAAgB,aAAa,EAAE,KAAK,QAAQ;wBAC9C,IAAI;4BACF,cAAc;gCACZ,GAAG,YAAY;gCACf,aAAa,KAAK,IAAI;4BACxB;4BACA,WAAW;wBACb;oBACF;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,oCAAoC;gBAClD,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd;QACF;QAEA,0BAA0B;QAC1B,YAAY,OAAO;YACjB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,UAAU;gBAC1C,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,qBAAqB;oBACrB,MAAM,MAAM,UAAU;oBACtB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,YAAY,OAAO,IAAI;YACrB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,OAAO,EAAE,IAAI,EAAE;gBAC/C,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,qBAAqB;oBACrB,MAAM,MAAM,UAAU;oBACtB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,YAAY,OAAO;YACjB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,IAAI;gBAChD,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,qBAAqB;oBACrB,MAAM,MAAM,UAAU;oBACtB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,gCAAgC;QAChC,kBAAkB,OAAO;YACvB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,gBAAgB;gBAChD,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,2BAA2B;oBAC3B,MAAM,MAAM,gBAAgB;oBAC5B,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,kBAAkB,OAAO,IAAI;YAC3B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,GAAG,CAAC,CAAC,aAAa,EAAE,IAAI,EAAE;gBACrD,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,2BAA2B;oBAC3B,MAAM,MAAM,gBAAgB;oBAC5B,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,kBAAkB,OAAO;YACvB,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,aAAa,EAAE,IAAI;gBACtD,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,2BAA2B;oBAC3B,MAAM,MAAM,gBAAgB;oBAC5B,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,6BAA6B;QAC7B,yBAAyB,OAAO,QAAQ;YACtC,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,IAAI,CAAC,CAAC,OAAO,EAAE,OAAO,YAAY,CAAC,EAAE;oBAAE;gBAAc;gBAChF,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,iCAAiC;oBACjC,MAAM,MAAM,yBAAyB;oBACrC,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,gCAAgC;gBAC9C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;QAEA,0BAA0B,OAAO,QAAQ;YACvC,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YACnC,IAAI;gBACF,MAAM,WAAW,MAAM,qIAAA,CAAA,MAAG,CAAC,MAAM,CAAC,CAAC,OAAO,EAAE,OAAO,aAAa,EAAE,cAAc;gBAChF,MAAM,OAAO,SAAS,IAAI;gBAE1B,IAAI,KAAK,OAAO,EAAE;oBAChB,iCAAiC;oBACjC,MAAM,MAAM,yBAAyB;oBACrC,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;oBACd,OAAO;gBACT,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,OAAO,IAAI;gBAClC;YACF,EAAE,OAAO,OAAY;gBACnB,QAAQ,KAAK,CAAC,8BAA8B;gBAC5C,IAAI;oBACF,OAAO,MAAM,OAAO,IAAI;oBACxB,WAAW;gBACb;gBACA,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACZ,OAAO;YACT;QACF;IACF,CAAC,GACD;IACE,MAAM;AACR", "debugId": null}}, {"offset": {"line": 1054, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 1070, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,mVAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1096, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1153, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,mVAAC,+WAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,mVAAC,+WAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,mVAAC,+WAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,mVAAC,+WAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,mVAAC,+WAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,mVAAC,4SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,mVAAC,+WAAA,CAAA,SAAsB;kBACrB,cAAA,mVAAC,+WAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,mVAAC;;;;;8BACD,mVAAC,+WAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,mVAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,mVAAC,+WAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,mVAAC,+WAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,mVAAC;gBAAK,WAAU;0BACd,cAAA,mVAAC,+WAAA,CAAA,gBAA6B;8BAC5B,cAAA,mVAAC,4RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,mVAAC,+WAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,mVAAC,+WAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,mVAAC,+WAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,mVAAC,wSAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,mVAAC,+WAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,mVAAC,4SAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 1378, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,mVAAC;QAAI,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 1420, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/utils/debounce.ts"], "sourcesContent": ["/**\n * Creates a debounced function that delays invoking func until after wait milliseconds \n * have elapsed since the last time the debounced function was invoked.\n */\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): T & { cancel: () => void } {\n  let timeoutId: NodeJS.Timeout | undefined\n  let lastArgs: Parameters<T>\n  let lastThis: any\n\n  const debounced = function (this: any, ...args: Parameters<T>) {\n    lastArgs = args\n    lastThis = this\n\n    if (timeoutId) {\n      clearTimeout(timeoutId)\n    }\n\n    timeoutId = setTimeout(() => {\n      func.apply(lastThis, lastArgs)\n    }, wait)\n  } as T & { cancel: () => void }\n\n  debounced.cancel = () => {\n    if (timeoutId) {\n      clearTimeout(timeoutId)\n      timeoutId = undefined\n    }\n  }\n\n  return debounced\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;AACM,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,IAAI;IACJ,IAAI;IAEJ,MAAM,YAAY,SAAqB,GAAG,IAAmB;QAC3D,WAAW;QACX,WAAW,IAAI;QAEf,IAAI,WAAW;YACb,aAAa;QACf;QAEA,YAAY,WAAW;YACrB,KAAK,KAAK,CAAC,UAAU;QACvB,GAAG;IACL;IAEA,UAAU,MAAM,GAAG;QACjB,IAAI,WAAW;YACb,aAAa;YACb,YAAY;QACd;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1454, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/role-permissions/RolePermissionsFilters.tsx"], "sourcesContent": ["'use client'\n\nimport { useState, useCallback } from 'react'\nimport { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'\nimport { Input } from '@/components/ui/input'\nimport { Button } from '@/components/ui/button'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Badge } from '@/components/ui/badge'\nimport { Search, Filter, X, LayoutGrid, List } from 'lucide-react'\nimport { debounce } from '@/utils/debounce'\n\ninterface RolePermissionsFiltersProps {\n  activeTab: 'roles' | 'permissions'\n}\n\nexport function RolePermissionsFilters({ activeTab }: RolePermissionsFiltersProps) {\n  const [showAdvanced, setShowAdvanced] = useState(false)\n\n  const {\n    rolesFilters,\n    permissionsFilters,\n    viewMode,\n    setRolesFilters,\n    setPermissionsFilters,\n    setViewMode,\n    fetchRoles,\n    fetchPermissions\n  } = useRolePermissionsStore()\n\n  const currentFilters = activeTab === 'roles' ? rolesFilters : permissionsFilters\n  const setCurrentFilters = activeTab === 'roles' ? setRolesFilters : setPermissionsFilters\n\n  // Debounced search function\n  const debouncedSearch = useCallback(\n    debounce((searchValue: string) => {\n      setCurrentFilters({ search: searchValue })\n      \n      if (activeTab === 'roles') {\n        fetchRoles()\n      } else {\n        fetchPermissions()\n      }\n    }, 300),\n    [activeTab, setCurrentFilters, fetchRoles, fetchPermissions]\n  )\n\n  const handleSearchChange = (value: string) => {\n    debouncedSearch(value)\n  }\n\n  const handleFilterChange = (key: string, value: any) => {\n    setCurrentFilters({ [key]: value })\n    \n    if (activeTab === 'roles') {\n      fetchRoles()\n    } else {\n      fetchPermissions()\n    }\n  }\n\n  const resetFilters = () => {\n    const defaultFilters = {\n      search: '',\n      isActive: undefined,\n      level: '',\n      scope: '',\n      resource: '',\n      action: '',\n    }\n    \n    setCurrentFilters(defaultFilters)\n    \n    if (activeTab === 'roles') {\n      fetchRoles()\n    } else {\n      fetchPermissions()\n    }\n  }\n\n  const getActiveFiltersCount = () => {\n    let count = 0\n    if (currentFilters.search) count++\n    if (currentFilters.isActive !== undefined) count++\n    if (currentFilters.level) count++\n    if (currentFilters.scope) count++\n    if (currentFilters.resource) count++\n    if (currentFilters.action) count++\n    return count\n  }\n\n  const activeFiltersCount = getActiveFiltersCount()\n\n  return (\n    <div className=\"space-y-4\">\n      {/* Main Filter Bar */}\n      <div className=\"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between\">\n        <div className=\"flex flex-1 gap-2 items-center\">\n          {/* Search Input */}\n          <div className=\"relative flex-1 max-w-sm\">\n            <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n            <Input\n              placeholder={`Search ${activeTab}...`}\n              defaultValue={currentFilters.search}\n              onChange={(e) => handleSearchChange(e.target.value)}\n              className=\"pl-10\"\n            />\n          </div>\n\n          {/* Advanced Filters Toggle */}\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={() => setShowAdvanced(!showAdvanced)}\n            className=\"gap-2\"\n          >\n            <Filter className=\"h-4 w-4\" />\n            Filters\n            {activeFiltersCount > 0 && (\n              <Badge variant=\"secondary\" className=\"ml-1\">\n                {activeFiltersCount}\n              </Badge>\n            )}\n          </Button>\n\n          {/* Reset Filters */}\n          {activeFiltersCount > 0 && (\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={resetFilters}\n              className=\"gap-2\"\n            >\n              <X className=\"h-4 w-4\" />\n              Reset\n            </Button>\n          )}\n        </div>\n\n        {/* View Mode Toggle */}\n        <div className=\"flex items-center gap-1 border rounded-lg p-1\">\n          <Button\n            variant={viewMode === 'list' ? 'default' : 'ghost'}\n            size=\"sm\"\n            onClick={() => setViewMode('list')}\n            className=\"h-8 w-8 p-0\"\n          >\n            <List className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant={viewMode === 'card' ? 'default' : 'ghost'}\n            size=\"sm\"\n            onClick={() => setViewMode('card')}\n            className=\"h-8 w-8 p-0\"\n          >\n            <LayoutGrid className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n\n      {/* Advanced Filters */}\n      {showAdvanced && (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg\">\n          {/* Status Filter */}\n          <div>\n            <label className=\"text-sm font-medium text-gray-700 mb-1 block\">\n              Status\n            </label>\n            <Select\n              value={currentFilters.isActive === undefined ? 'all' : currentFilters.isActive.toString()}\n              onValueChange={(value) => \n                handleFilterChange('isActive', value === 'all' ? undefined : value === 'true')\n              }\n            >\n              <SelectTrigger>\n                <SelectValue placeholder=\"All statuses\" />\n              </SelectTrigger>\n              <SelectContent>\n                <SelectItem value=\"all\">All statuses</SelectItem>\n                <SelectItem value=\"true\">Active</SelectItem>\n                <SelectItem value=\"false\">Inactive</SelectItem>\n              </SelectContent>\n            </Select>\n          </div>\n\n          {/* Role-specific filters */}\n          {activeTab === 'roles' && (\n            <>\n              <div>\n                <label className=\"text-sm font-medium text-gray-700 mb-1 block\">\n                  Level\n                </label>\n                <Select\n                  value={currentFilters.level || 'all'}\n                  onValueChange={(value) => \n                    handleFilterChange('level', value === 'all' ? '' : value)\n                  }\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"All levels\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">All levels</SelectItem>\n                    <SelectItem value=\"1\">Level 1 - Super Admin</SelectItem>\n                    <SelectItem value=\"2\">Level 2 - Institute Admin</SelectItem>\n                    <SelectItem value=\"3\">Level 3 - User Level</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div>\n                <label className=\"text-sm font-medium text-gray-700 mb-1 block\">\n                  Scope\n                </label>\n                <Input\n                  placeholder=\"Filter by scope\"\n                  value={currentFilters.scope || ''}\n                  onChange={(e) => handleFilterChange('scope', e.target.value)}\n                />\n              </div>\n            </>\n          )}\n\n          {/* Permission-specific filters */}\n          {activeTab === 'permissions' && (\n            <>\n              <div>\n                <label className=\"text-sm font-medium text-gray-700 mb-1 block\">\n                  Resource\n                </label>\n                <Select\n                  value={currentFilters.resource || 'all'}\n                  onValueChange={(value) => \n                    handleFilterChange('resource', value === 'all' ? '' : value)\n                  }\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"All resources\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">All resources</SelectItem>\n                    <SelectItem value=\"users\">Users</SelectItem>\n                    <SelectItem value=\"roles\">Roles</SelectItem>\n                    <SelectItem value=\"permissions\">Permissions</SelectItem>\n                    <SelectItem value=\"institutes\">Institutes</SelectItem>\n                    <SelectItem value=\"courses\">Courses</SelectItem>\n                    <SelectItem value=\"settings\">Settings</SelectItem>\n                    <SelectItem value=\"analytics\">Analytics</SelectItem>\n                    <SelectItem value=\"billing\">Billing</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div>\n                <label className=\"text-sm font-medium text-gray-700 mb-1 block\">\n                  Action\n                </label>\n                <Select\n                  value={currentFilters.action || 'all'}\n                  onValueChange={(value) => \n                    handleFilterChange('action', value === 'all' ? '' : value)\n                  }\n                >\n                  <SelectTrigger>\n                    <SelectValue placeholder=\"All actions\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">All actions</SelectItem>\n                    <SelectItem value=\"create\">Create</SelectItem>\n                    <SelectItem value=\"read\">Read</SelectItem>\n                    <SelectItem value=\"update\">Update</SelectItem>\n                    <SelectItem value=\"delete\">Delete</SelectItem>\n                    <SelectItem value=\"manage\">Manage</SelectItem>\n                    <SelectItem value=\"view\">View</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n            </>\n          )}\n        </div>\n      )}\n\n      {/* Active Filters Display */}\n      {activeFiltersCount > 0 && (\n        <div className=\"flex flex-wrap gap-2\">\n          {currentFilters.search && (\n            <Badge variant=\"secondary\" className=\"gap-1\">\n              Search: {currentFilters.search}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => handleFilterChange('search', '')}\n              />\n            </Badge>\n          )}\n          {currentFilters.isActive !== undefined && (\n            <Badge variant=\"secondary\" className=\"gap-1\">\n              Status: {currentFilters.isActive ? 'Active' : 'Inactive'}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => handleFilterChange('isActive', undefined)}\n              />\n            </Badge>\n          )}\n          {currentFilters.level && (\n            <Badge variant=\"secondary\" className=\"gap-1\">\n              Level: {currentFilters.level}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => handleFilterChange('level', '')}\n              />\n            </Badge>\n          )}\n          {currentFilters.resource && (\n            <Badge variant=\"secondary\" className=\"gap-1\">\n              Resource: {currentFilters.resource}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => handleFilterChange('resource', '')}\n              />\n            </Badge>\n          )}\n          {currentFilters.action && (\n            <Badge variant=\"secondary\" className=\"gap-1\">\n              Action: {currentFilters.action}\n              <X \n                className=\"h-3 w-3 cursor-pointer\" \n                onClick={() => handleFilterChange('action', '')}\n              />\n            </Badge>\n          )}\n        </div>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AATA;;;;;;;;;;AAeO,SAAS,uBAAuB,EAAE,SAAS,EAA+B;IAC/E,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,EACJ,YAAY,EACZ,kBAAkB,EAClB,QAAQ,EACR,eAAe,EACf,qBAAqB,EACrB,WAAW,EACX,UAAU,EACV,gBAAgB,EACjB,GAAG,CAAA,GAAA,4JAAA,CAAA,0BAAuB,AAAD;IAE1B,MAAM,iBAAiB,cAAc,UAAU,eAAe;IAC9D,MAAM,oBAAoB,cAAc,UAAU,kBAAkB;IAEpE,4BAA4B;IAC5B,MAAM,kBAAkB,CAAA,GAAA,0SAAA,CAAA,cAAW,AAAD,EAChC,CAAA,GAAA,4IAAA,CAAA,WAAQ,AAAD,EAAE,CAAC;QACR,kBAAkB;YAAE,QAAQ;QAAY;QAExC,IAAI,cAAc,SAAS;YACzB;QACF,OAAO;YACL;QACF;IACF,GAAG,MACH;QAAC;QAAW;QAAmB;QAAY;KAAiB;IAG9D,MAAM,qBAAqB,CAAC;QAC1B,gBAAgB;IAClB;IAEA,MAAM,qBAAqB,CAAC,KAAa;QACvC,kBAAkB;YAAE,CAAC,IAAI,EAAE;QAAM;QAEjC,IAAI,cAAc,SAAS;YACzB;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,iBAAiB;YACrB,QAAQ;YACR,UAAU;YACV,OAAO;YACP,OAAO;YACP,UAAU;YACV,QAAQ;QACV;QAEA,kBAAkB;QAElB,IAAI,cAAc,SAAS;YACzB;QACF,OAAO;YACL;QACF;IACF;IAEA,MAAM,wBAAwB;QAC5B,IAAI,QAAQ;QACZ,IAAI,eAAe,MAAM,EAAE;QAC3B,IAAI,eAAe,QAAQ,KAAK,WAAW;QAC3C,IAAI,eAAe,KAAK,EAAE;QAC1B,IAAI,eAAe,KAAK,EAAE;QAC1B,IAAI,eAAe,QAAQ,EAAE;QAC7B,IAAI,eAAe,MAAM,EAAE;QAC3B,OAAO;IACT;IAEA,MAAM,qBAAqB;IAE3B,qBACE,mVAAC;QAAI,WAAU;;0BAEb,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAI,WAAU;;0CAEb,mVAAC;gCAAI,WAAU;;kDACb,mVAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,mVAAC,qJAAA,CAAA,QAAK;wCACJ,aAAa,CAAC,OAAO,EAAE,UAAU,GAAG,CAAC;wCACrC,cAAc,eAAe,MAAM;wCACnC,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;wCAClD,WAAU;;;;;;;;;;;;0CAKd,mVAAC,sJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,gBAAgB,CAAC;gCAChC,WAAU;;kDAEV,mVAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;oCAE7B,qBAAqB,mBACpB,mVAAC,qJAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAClC;;;;;;;;;;;;4BAMN,qBAAqB,mBACpB,mVAAC,sJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,mVAAC,gRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAO/B,mVAAC;wBAAI,WAAU;;0CACb,mVAAC,sJAAA,CAAA,SAAM;gCACL,SAAS,aAAa,SAAS,YAAY;gCAC3C,MAAK;gCACL,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CAEV,cAAA,mVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;;;;;;0CAElB,mVAAC,sJAAA,CAAA,SAAM;gCACL,SAAS,aAAa,SAAS,YAAY;gCAC3C,MAAK;gCACL,SAAS,IAAM,YAAY;gCAC3B,WAAU;0CAEV,cAAA,mVAAC,sSAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;YAM3B,8BACC,mVAAC;gBAAI,WAAU;;kCAEb,mVAAC;;0CACC,mVAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,mVAAC,sJAAA,CAAA,SAAM;gCACL,OAAO,eAAe,QAAQ,KAAK,YAAY,QAAQ,eAAe,QAAQ,CAAC,QAAQ;gCACvF,eAAe,CAAC,QACd,mBAAmB,YAAY,UAAU,QAAQ,YAAY,UAAU;;kDAGzE,mVAAC,sJAAA,CAAA,gBAAa;kDACZ,cAAA,mVAAC,sJAAA,CAAA,cAAW;4CAAC,aAAY;;;;;;;;;;;kDAE3B,mVAAC,sJAAA,CAAA,gBAAa;;0DACZ,mVAAC,sJAAA,CAAA,aAAU;gDAAC,OAAM;0DAAM;;;;;;0DACxB,mVAAC,sJAAA,CAAA,aAAU;gDAAC,OAAM;0DAAO;;;;;;0DACzB,mVAAC,sJAAA,CAAA,aAAU;gDAAC,OAAM;0DAAQ;;;;;;;;;;;;;;;;;;;;;;;;oBAM/B,cAAc,yBACb;;0CACE,mVAAC;;kDACC,mVAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,mVAAC,sJAAA,CAAA,SAAM;wCACL,OAAO,eAAe,KAAK,IAAI;wCAC/B,eAAe,CAAC,QACd,mBAAmB,SAAS,UAAU,QAAQ,KAAK;;0DAGrD,mVAAC,sJAAA,CAAA,gBAAa;0DACZ,cAAA,mVAAC,sJAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,mVAAC,sJAAA,CAAA,gBAAa;;kEACZ,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;kEACtB,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;kEACtB,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAI;;;;;;;;;;;;;;;;;;;;;;;;0CAK5B,mVAAC;;kDACC,mVAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,mVAAC,qJAAA,CAAA,QAAK;wCACJ,aAAY;wCACZ,OAAO,eAAe,KAAK,IAAI;wCAC/B,UAAU,CAAC,IAAM,mBAAmB,SAAS,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;oBAOlE,cAAc,+BACb;;0CACE,mVAAC;;kDACC,mVAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,mVAAC,sJAAA,CAAA,SAAM;wCACL,OAAO,eAAe,QAAQ,IAAI;wCAClC,eAAe,CAAC,QACd,mBAAmB,YAAY,UAAU,QAAQ,KAAK;;0DAGxD,mVAAC,sJAAA,CAAA,gBAAa;0DACZ,cAAA,mVAAC,sJAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,mVAAC,sJAAA,CAAA,gBAAa;;kEACZ,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAQ;;;;;;kEAC1B,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAc;;;;;;kEAChC,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAa;;;;;;kEAC/B,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;kEAC5B,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAW;;;;;;kEAC7B,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAY;;;;;;kEAC9B,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAKlC,mVAAC;;kDACC,mVAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,mVAAC,sJAAA,CAAA,SAAM;wCACL,OAAO,eAAe,MAAM,IAAI;wCAChC,eAAe,CAAC,QACd,mBAAmB,UAAU,UAAU,QAAQ,KAAK;;0DAGtD,mVAAC,sJAAA,CAAA,gBAAa;0DACZ,cAAA,mVAAC,sJAAA,CAAA,cAAW;oDAAC,aAAY;;;;;;;;;;;0DAE3B,mVAAC,sJAAA,CAAA,gBAAa;;kEACZ,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAM;;;;;;kEACxB,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;kEACzB,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAS;;;;;;kEAC3B,mVAAC,sJAAA,CAAA,aAAU;wDAAC,OAAM;kEAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUtC,qBAAqB,mBACpB,mVAAC;gBAAI,WAAU;;oBACZ,eAAe,MAAM,kBACpB,mVAAC,qJAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAQ;4BAClC,eAAe,MAAM;0CAC9B,mVAAC,gRAAA,CAAA,IAAC;gCACA,WAAU;gCACV,SAAS,IAAM,mBAAmB,UAAU;;;;;;;;;;;;oBAIjD,eAAe,QAAQ,KAAK,2BAC3B,mVAAC,qJAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAQ;4BAClC,eAAe,QAAQ,GAAG,WAAW;0CAC9C,mVAAC,gRAAA,CAAA,IAAC;gCACA,WAAU;gCACV,SAAS,IAAM,mBAAmB,YAAY;;;;;;;;;;;;oBAInD,eAAe,KAAK,kBACnB,mVAAC,qJAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAQ;4BACnC,eAAe,KAAK;0CAC5B,mVAAC,gRAAA,CAAA,IAAC;gCACA,WAAU;gCACV,SAAS,IAAM,mBAAmB,SAAS;;;;;;;;;;;;oBAIhD,eAAe,QAAQ,kBACtB,mVAAC,qJAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAQ;4BAChC,eAAe,QAAQ;0CAClC,mVAAC,gRAAA,CAAA,IAAC;gCACA,WAAU;gCACV,SAAS,IAAM,mBAAmB,YAAY;;;;;;;;;;;;oBAInD,eAAe,MAAM,kBACpB,mVAAC,qJAAA,CAAA,QAAK;wBAAC,SAAQ;wBAAY,WAAU;;4BAAQ;4BAClC,eAAe,MAAM;0CAC9B,mVAAC,gRAAA,CAAA,IAAC;gCACA,WAAU;gCACV,SAAS,IAAM,mBAAmB,UAAU;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D", "debugId": null}}, {"offset": {"line": 2216, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2313, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,mVAAC,6WAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2341, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface SwitchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {\n  checked?: boolean\n  onCheckedChange?: (checked: boolean) => void\n}\n\nconst Switch = React.forwardRef<HTMLInputElement, SwitchProps>(\n  ({ className, checked, onCheckedChange, onChange, ...props }, ref) => {\n    const [internalChecked, setInternalChecked] = React.useState(checked || false)\n    const isChecked = checked !== undefined ? checked : internalChecked\n\n    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n      const newChecked = event.target.checked\n      if (checked === undefined) {\n        setInternalChecked(newChecked)\n      }\n      onCheckedChange?.(newChecked)\n      onChange?.(event)\n    }\n\n    return (\n      <label className=\"relative inline-flex items-center cursor-pointer\">\n        <input\n          type=\"checkbox\"\n          ref={ref}\n          className=\"sr-only peer\"\n          checked={isChecked}\n          onChange={handleChange}\n          {...props}\n        />\n        <div\n          className={cn(\n            \"relative w-11 h-6 rounded-full transition-colors duration-200 ease-in-out\",\n            \"focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2\",\n            isChecked\n              ? \"bg-blue-600\"\n              : \"bg-gray-200\",\n            className\n          )}\n        >\n          <div\n            className={cn(\n              \"absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md transition-transform duration-200 ease-in-out\",\n              isChecked ? \"translate-x-5\" : \"translate-x-0\"\n            )}\n          />\n        </div>\n      </label>\n    )\n  }\n)\nSwitch.displayName = \"Switch\"\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,uBAAS,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAc,AAAD,EAAE,WAAW;IACxE,MAAM,YAAY,YAAY,YAAY,UAAU;IAEpD,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,MAAM,MAAM,CAAC,OAAO;QACvC,IAAI,YAAY,WAAW;YACzB,mBAAmB;QACrB;QACA,kBAAkB;QAClB,WAAW;IACb;IAEA,qBACE,mVAAC;QAAM,WAAU;;0BACf,mVAAC;gBACC,MAAK;gBACL,KAAK;gBACL,WAAU;gBACV,SAAS;gBACT,UAAU;gBACT,GAAG,KAAK;;;;;;0BAEX,mVAAC;gBACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,6EACA,6EACA,YACI,gBACA,eACJ;0BAGF,cAAA,mVAAC;oBACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,mHACA,YAAY,kBAAkB;;;;;;;;;;;;;;;;;AAM1C;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2406, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Dialog({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Root>) {\n  return <DialogPrimitive.Root data-slot=\"dialog\" {...props} />\n}\n\nfunction DialogTrigger({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Trigger>) {\n  return <DialogPrimitive.Trigger data-slot=\"dialog-trigger\" {...props} />\n}\n\nfunction DialogPortal({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Portal>) {\n  return <DialogPrimitive.Portal data-slot=\"dialog-portal\" {...props} />\n}\n\nfunction DialogClose({\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Close>) {\n  return <DialogPrimitive.Close data-slot=\"dialog-close\" {...props} />\n}\n\nfunction DialogOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Overlay>) {\n  return (\n    <DialogPrimitive.Overlay\n      data-slot=\"dialog-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogContent({\n  className,\n  children,\n  showCloseButton = true,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Content> & {\n  showCloseButton?: boolean\n}) {\n  return (\n    <DialogPortal data-slot=\"dialog-portal\">\n      <DialogOverlay />\n      <DialogPrimitive.Content\n        data-slot=\"dialog-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 fixed top-[50%] left-[50%] z-50 grid w-full max-w-[calc(100%-2rem)] translate-x-[-50%] translate-y-[-50%] gap-4 rounded-lg border p-6 shadow-lg duration-200 sm:max-w-lg\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        {showCloseButton && (\n          <DialogPrimitive.Close\n            data-slot=\"dialog-close\"\n            className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-accent data-[state=open]:text-muted-foreground absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\"\n          >\n            <XIcon />\n            <span className=\"sr-only\">Close</span>\n          </DialogPrimitive.Close>\n        )}\n      </DialogPrimitive.Content>\n    </DialogPortal>\n  )\n}\n\nfunction DialogHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-header\"\n      className={cn(\"flex flex-col gap-2 text-center sm:text-left\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"dialog-footer\"\n      className={cn(\n        \"flex flex-col-reverse gap-2 sm:flex-row sm:justify-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DialogTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Title>) {\n  return (\n    <DialogPrimitive.Title\n      data-slot=\"dialog-title\"\n      className={cn(\"text-lg leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DialogDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof DialogPrimitive.Description>) {\n  return (\n    <DialogPrimitive.Description\n      data-slot=\"dialog-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Dialog,\n  DialogClose,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogOverlay,\n  DialogPortal,\n  DialogTitle,\n  DialogTrigger,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,mVAAC,gXAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,cAAc,EACrB,GAAG,OACkD;IACrD,qBAAO,mVAAC,gXAAA,CAAA,UAAuB;QAAC,aAAU;QAAkB,GAAG,KAAK;;;;;;AACtE;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,mVAAC,gXAAA,CAAA,SAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,mVAAC,gXAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,GAAG,OACkD;IACrD,qBACE,mVAAC,gXAAA,CAAA,UAAuB;QACtB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,kBAAkB,IAAI,EACtB,GAAG,OAGJ;IACC,qBACE,mVAAC;QAAa,aAAU;;0BACtB,mVAAC;;;;;0BACD,mVAAC,gXAAA,CAAA,UAAuB;gBACtB,aAAU;gBACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,+WACA;gBAED,GAAG,KAAK;;oBAER;oBACA,iCACC,mVAAC,gXAAA,CAAA,QAAqB;wBACpB,aAAU;wBACV,WAAU;;0CAEV,mVAAC,oRAAA,CAAA,QAAK;;;;;0CACN,mVAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EAAE,SAAS,EAAE,GAAG,OAAoC;IACxE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,0DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,mVAAC,gXAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,GAAG,OACsD;IACzD,qBACE,mVAAC,gXAAA,CAAA,cAA2B;QAC1B,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/role-permissions/RoleForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Switch } from '@/components/ui/switch'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { Plus, Edit, Save, X } from 'lucide-react'\nimport { Formik, Field, ErrorMessage } from 'formik'\nimport * as Yup from 'yup'\nimport { toast } from 'sonner'\n\n// Validation schema using Yup - Following Location Management patterns\nconst validationSchema = Yup.object({\n  name: Yup.string()\n    .required('Role name is required')\n    .min(2, 'Role name must be at least 2 characters')\n    .max(100, 'Role name must be less than 100 characters'),\n  code: Yup.string()\n    .required('Role code is required')\n    .min(2, 'Role code must be at least 2 characters')\n    .max(50, 'Role code must be at most 50 characters')\n    .matches(/^[a-z_]+$/, 'Role code must contain only lowercase letters and underscores'),\n  description: Yup.string()\n    .max(500, 'Description must be less than 500 characters'),\n  level: Yup.string()\n    .required('Role level is required'),\n  scope: Yup.string()\n    .max(100, 'Scope must be less than 100 characters'),\n  isActive: Yup.boolean(),\n  priority: Yup.number()\n    .min(0, 'Priority must be 0 or greater')\n    .integer('Priority must be a whole number')\n})\n\ninterface RoleFormProps {\n  role?: any\n  mode: 'create' | 'edit'\n  trigger?: React.ReactNode\n  onSuccess?: () => void\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}\n\nexport function RoleForm({ role, mode, trigger, onSuccess, open: externalOpen, onOpenChange }: RoleFormProps) {\n  const [internalOpen, setInternalOpen] = useState(false)\n  const open = externalOpen !== undefined ? externalOpen : internalOpen\n  const setOpen = onOpenChange || setInternalOpen\n  const { createRole, updateRole, isLoading } = useRolePermissionsStore()\n\n  // Initial values for Formik - Following Location Management patterns\n  const initialValues = {\n    name: role?.name || '',\n    code: role?.code || '',\n    description: role?.description || '',\n    level: role?.level || '3',\n    scope: role?.scope || '',\n    isActive: role?.isActive ?? true,\n    priority: role?.priority || 0\n  }\n\n  const handleSubmit = async (values: any, { setSubmitting, resetForm }: any) => {\n    try {\n      // Prepare data for submission - Following Location Management patterns\n      const submitData = {\n        name: values.name,\n        code: values.code.toLowerCase(),\n        description: values.description,\n        level: values.level,\n        scope: values.scope,\n        isActive: values.isActive,\n        priority: values.priority,\n        isSystemRole: false // New roles are not system roles\n      }\n\n      console.log('Submitting role data:', submitData)\n\n      if (mode === 'create') {\n        await createRole(submitData)\n        toast.success('Role created successfully')\n        resetForm()\n      } else {\n        await updateRole(role.id, submitData)\n        toast.success('Role updated successfully')\n      }\n\n      // Only close dialog on successful submission\n      setOpen(false)\n      onSuccess?.()\n    } catch (error) {\n      console.error('Form submission error:', error)\n      toast.error('Failed to save role')\n      // Don't close dialog on error - let user fix the issues\n    } finally {\n      setSubmitting(false)\n    }\n  }\n\n  const defaultTrigger = (\n    <Button size=\"sm\" className=\"gap-2\">\n      {mode === 'create' ? <Plus className=\"h-4 w-4\" /> : <Edit className=\"h-4 w-4\" />}\n      {mode === 'create' ? 'Add Role' : 'Edit Role'}\n    </Button>\n  )\n\n  return (\n    <Dialog open={open} onOpenChange={setOpen}>\n      <DialogTrigger asChild>\n        {trigger || defaultTrigger}\n      </DialogTrigger>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            {mode === 'create' ? <Plus className=\"h-5 w-5\" /> : <Edit className=\"h-5 w-5\" />}\n            {mode === 'create' ? 'Create New Role' : 'Edit Role'}\n          </DialogTitle>\n        </DialogHeader>\n\n        <Formik\n          initialValues={initialValues}\n          validationSchema={validationSchema}\n          onSubmit={handleSubmit}\n          enableReinitialize={true}\n        >\n          {({ values, errors, touched, handleChange, isSubmitting, setSubmitting, resetForm }) => (\n            <div className=\"space-y-6\">\n              {/* Basic Information */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Basic Information</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div>\n                    <Label htmlFor=\"name\">Role Name *</Label>\n                    <Field name=\"name\">\n                      {({ field }: any) => (\n                        <Input\n                          {...field}\n                          id=\"name\"\n                          placeholder=\"Enter role name\"\n                          className={errors.name && touched.name ? 'border-red-500' : ''}\n                        />\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"name\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"code\">Role Code *</Label>\n                    <Field name=\"code\">\n                      {({ field }: any) => (\n                        <Input\n                          {...field}\n                          id=\"code\"\n                          placeholder=\"Enter role code (lowercase, underscores allowed)\"\n                          className={errors.code && touched.code ? 'border-red-500' : ''}\n                        />\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"code\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"description\">Description</Label>\n                    <Field name=\"description\">\n                      {({ field }: any) => (\n                        <Textarea\n                          {...field}\n                          id=\"description\"\n                          placeholder=\"Enter role description\"\n                          rows={3}\n                          className={errors.description && touched.description ? 'border-red-500' : ''}\n                        />\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"description\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Role Configuration */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Role Configuration</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div>\n                    <Label htmlFor=\"level\">Role Level *</Label>\n                    <Field name=\"level\">\n                      {({ field, form }: any) => (\n                        <Select\n                          value={field.value}\n                          onValueChange={(value) => form.setFieldValue('level', value)}\n                        >\n                          <SelectTrigger className={errors.level && touched.level ? 'border-red-500' : ''}>\n                            <SelectValue placeholder=\"Select role level\" />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"1\">Level 1 - Super Admin</SelectItem>\n                            <SelectItem value=\"2\">Level 2 - Institute Admin</SelectItem>\n                            <SelectItem value=\"3\">Level 3 - User Level</SelectItem>\n                          </SelectContent>\n                        </Select>\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"level\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"scope\">Scope</Label>\n                    <Field name=\"scope\">\n                      {({ field }: any) => (\n                        <Input\n                          {...field}\n                          id=\"scope\"\n                          placeholder=\"Enter role scope (optional)\"\n                          className={errors.scope && touched.scope ? 'border-red-500' : ''}\n                        />\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"scope\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"priority\">Priority</Label>\n                    <Field name=\"priority\">\n                      {({ field }: any) => (\n                        <Input\n                          {...field}\n                          id=\"priority\"\n                          type=\"number\"\n                          min=\"0\"\n                          placeholder=\"Enter priority (0 = highest)\"\n                          className={errors.priority && touched.priority ? 'border-red-500' : ''}\n                        />\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"priority\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <Field name=\"isActive\">\n                      {({ field, form }: any) => (\n                        <Switch\n                          id=\"isActive\"\n                          checked={field.value}\n                          onCheckedChange={(checked) => form.setFieldValue('isActive', checked)}\n                        />\n                      )}\n                    </Field>\n                    <Label htmlFor=\"isActive\">Active Role</Label>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Form Actions */}\n              <div className=\"flex justify-end gap-3 pt-4 border-t\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setOpen(false)}\n                  disabled={isSubmitting}\n                >\n                  <X className=\"h-4 w-4 mr-2\" />\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  onClick={() => handleSubmit(values, { setSubmitting, resetForm })}\n                  disabled={isSubmitting}\n                  className=\"gap-2\"\n                >\n                  <Save className=\"h-4 w-4\" />\n                  {isSubmitting ? 'Saving...' : (mode === 'create' ? 'Create Role' : 'Update Role')}\n                </Button>\n              </div>\n            </div>\n          )}\n        </Formik>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;;AAiBA,uEAAuE;AACvE,MAAM,mBAAmB,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;IAClC,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACZ,QAAQ,CAAC,yBACT,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,KAAK;IACZ,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACZ,QAAQ,CAAC,yBACT,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,IAAI,2CACR,OAAO,CAAC,aAAa;IACxB,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACnB,GAAG,CAAC,KAAK;IACZ,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACb,QAAQ,CAAC;IACZ,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACb,GAAG,CAAC,KAAK;IACZ,UAAU,CAAA,GAAA,mLAAA,CAAA,UAAW,AAAD;IACpB,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IAChB,GAAG,CAAC,GAAG,iCACP,OAAO,CAAC;AACb;AAWO,SAAS,SAAS,EAAE,IAAI,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,EAAE,YAAY,EAAiB;IAC1G,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,OAAO,iBAAiB,YAAY,eAAe;IACzD,MAAM,UAAU,gBAAgB;IAChC,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,0BAAuB,AAAD;IAEpE,qEAAqE;IACrE,MAAM,gBAAgB;QACpB,MAAM,MAAM,QAAQ;QACpB,MAAM,MAAM,QAAQ;QACpB,aAAa,MAAM,eAAe;QAClC,OAAO,MAAM,SAAS;QACtB,OAAO,MAAM,SAAS;QACtB,UAAU,MAAM,YAAY;QAC5B,UAAU,MAAM,YAAY;IAC9B;IAEA,MAAM,eAAe,OAAO,QAAa,EAAE,aAAa,EAAE,SAAS,EAAO;QACxE,IAAI;YACF,uEAAuE;YACvE,MAAM,aAAa;gBACjB,MAAM,OAAO,IAAI;gBACjB,MAAM,OAAO,IAAI,CAAC,WAAW;gBAC7B,aAAa,OAAO,WAAW;gBAC/B,OAAO,OAAO,KAAK;gBACnB,OAAO,OAAO,KAAK;gBACnB,UAAU,OAAO,QAAQ;gBACzB,UAAU,OAAO,QAAQ;gBACzB,cAAc,MAAM,iCAAiC;YACvD;YAEA,QAAQ,GAAG,CAAC,yBAAyB;YAErC,IAAI,SAAS,UAAU;gBACrB,MAAM,WAAW;gBACjB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF,OAAO;gBACL,MAAM,WAAW,KAAK,EAAE,EAAE;gBAC1B,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA,6CAA6C;YAC7C,QAAQ;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACZ,wDAAwD;QAC1D,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,+BACJ,mVAAC,sJAAA,CAAA,SAAM;QAAC,MAAK;QAAK,WAAU;;YACzB,SAAS,yBAAW,mVAAC,sRAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAAe,mVAAC,+RAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACnE,SAAS,WAAW,aAAa;;;;;;;IAItC,qBACE,mVAAC,sJAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAChC,mVAAC,sJAAA,CAAA,gBAAa;gBAAC,OAAO;0BACnB,WAAW;;;;;;0BAEd,mVAAC,sJAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,mVAAC,sJAAA,CAAA,eAAY;kCACX,cAAA,mVAAC,sJAAA,CAAA,cAAW;4BAAC,WAAU;;gCACpB,SAAS,yBAAW,mVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAAe,mVAAC,+RAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACnE,SAAS,WAAW,oBAAoB;;;;;;;;;;;;kCAI7C,mVAAC,wNAAA,CAAA,SAAM;wBACL,eAAe;wBACf,kBAAkB;wBAClB,UAAU;wBACV,oBAAoB;kCAEnB,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,iBACjF,mVAAC;gCAAI,WAAU;;kDAEb,mVAAC,oJAAA,CAAA,OAAI;;0DACH,mVAAC,oJAAA,CAAA,aAAU;0DACT,cAAA,mVAAC,oJAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,mVAAC,oJAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAO;;;;;;0EACtB,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAO,iBACd,mVAAC,qJAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,IAAG;wEACH,aAAY;wEACZ,WAAW,OAAO,IAAI,IAAI,QAAQ,IAAI,GAAG,mBAAmB;;;;;;;;;;;0EAIlE,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAO,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAGtD,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAO;;;;;;0EACtB,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAO,iBACd,mVAAC,qJAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,IAAG;wEACH,aAAY;wEACZ,WAAW,OAAO,IAAI,IAAI,QAAQ,IAAI,GAAG,mBAAmB;;;;;;;;;;;0EAIlE,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAO,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAGtD,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAc;;;;;;0EAC7B,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAO,iBACd,mVAAC,wJAAA,CAAA,WAAQ;wEACN,GAAG,KAAK;wEACT,IAAG;wEACH,aAAY;wEACZ,MAAM;wEACN,WAAW,OAAO,WAAW,IAAI,QAAQ,WAAW,GAAG,mBAAmB;;;;;;;;;;;0EAIhF,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAc,WAAU;gEAAM,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAMjE,mVAAC,oJAAA,CAAA,OAAI;;0DACH,mVAAC,oJAAA,CAAA,aAAU;0DACT,cAAA,mVAAC,oJAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,mVAAC,oJAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAE,IAAI,EAAO,iBACpB,mVAAC,sJAAA,CAAA,SAAM;wEACL,OAAO,MAAM,KAAK;wEAClB,eAAe,CAAC,QAAU,KAAK,aAAa,CAAC,SAAS;;0FAEtD,mVAAC,sJAAA,CAAA,gBAAa;gFAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,GAAG,mBAAmB;0FAC3E,cAAA,mVAAC,sJAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,mVAAC,sJAAA,CAAA,gBAAa;;kGACZ,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAI;;;;;;kGACtB,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAI;;;;;;kGACtB,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAI;;;;;;;;;;;;;;;;;;;;;;;0EAK9B,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAQ,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAGvD,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAO,iBACd,mVAAC,qJAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,IAAG;wEACH,aAAY;wEACZ,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,GAAG,mBAAmB;;;;;;;;;;;0EAIpE,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAQ,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAGvD,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAO,iBACd,mVAAC,qJAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,IAAG;wEACH,MAAK;wEACL,KAAI;wEACJ,aAAY;wEACZ,WAAW,OAAO,QAAQ,IAAI,QAAQ,QAAQ,GAAG,mBAAmB;;;;;;;;;;;0EAI1E,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAW,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAG1D,mVAAC;wDAAI,WAAU;;0EACb,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAE,IAAI,EAAO,iBACpB,mVAAC,sJAAA,CAAA,SAAM;wEACL,IAAG;wEACH,SAAS,MAAM,KAAK;wEACpB,iBAAiB,CAAC,UAAY,KAAK,aAAa,CAAC,YAAY;;;;;;;;;;;0EAInE,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;kDAMhC,mVAAC;wCAAI,WAAU;;0DACb,mVAAC,sJAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,QAAQ;gDACvB,UAAU;;kEAEV,mVAAC,gRAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGhC,mVAAC,sJAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS,IAAM,aAAa,QAAQ;wDAAE;wDAAe;oDAAU;gDAC/D,UAAU;gDACV,WAAU;;kEAEV,mVAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,eAAe,cAAe,SAAS,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrF", "debugId": null}}, {"offset": {"line": 3254, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface DropdownMenuProps {\n  children: React.ReactNode\n}\n\ninterface DropdownMenuContextType {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nconst DropdownMenuContext = React.createContext<DropdownMenuContextType | undefined>(undefined)\n\nconst DropdownMenu = ({ children }: DropdownMenuProps) => {\n  const [open, setOpen] = React.useState(false)\n\n  return (\n    <DropdownMenuContext.Provider value={{ open, setOpen }}>\n      <div className=\"relative inline-block text-left\">\n        {children}\n      </div>\n    </DropdownMenuContext.Provider>\n  )\n}\n\nconst DropdownMenuTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & {\n    asChild?: boolean\n  }\n>(({ className, children, asChild = false, ...props }, ref) => {\n  const context = React.useContext(DropdownMenuContext)\n  if (!context) throw new Error(\"DropdownMenuTrigger must be used within DropdownMenu\")\n\n  const handleClick = () => {\n    context.setOpen(!context.open)\n  }\n\n  if (asChild) {\n    return React.cloneElement(children as React.ReactElement, {\n      onClick: handleClick,\n      ref,\n    })\n  }\n\n  return (\n    <button\n      ref={ref}\n      className={cn(\n        \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\",\n        className\n      )}\n      onClick={handleClick}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n})\nDropdownMenuTrigger.displayName = \"DropdownMenuTrigger\"\n\nconst DropdownMenuContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    align?: \"start\" | \"center\" | \"end\"\n    sideOffset?: number\n  }\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => {\n  const context = React.useContext(DropdownMenuContext)\n  if (!context) throw new Error(\"DropdownMenuContent must be used within DropdownMenu\")\n\n  const contentRef = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (contentRef.current && !contentRef.current.contains(event.target as Node)) {\n        context.setOpen(false)\n      }\n    }\n\n    if (context.open) {\n      document.addEventListener(\"mousedown\", handleClickOutside)\n    }\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside)\n    }\n  }, [context.open, context.setOpen])\n\n  if (!context.open) return null\n\n  return (\n    <div\n      ref={contentRef}\n      className={cn(\n        \"absolute z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\n        align === \"start\" && \"left-0\",\n        align === \"center\" && \"left-1/2 transform -translate-x-1/2\",\n        align === \"end\" && \"right-0\",\n        className\n      )}\n      style={{ top: `calc(100% + ${sideOffset}px)` }}\n      {...props}\n    />\n  )\n})\nDropdownMenuContent.displayName = \"DropdownMenuContent\"\n\nconst DropdownMenuItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => {\n  const context = React.useContext(DropdownMenuContext)\n\n  const handleClick = (event: React.MouseEvent) => {\n    if (props.onClick) {\n      props.onClick(event)\n    }\n    context?.setOpen(false)\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 hover:bg-accent hover:text-accent-foreground\",\n        inset && \"pl-8\",\n        className\n      )}\n      onClick={handleClick}\n      {...props}\n    />\n  )\n})\nDropdownMenuItem.displayName = \"DropdownMenuItem\"\n\nconst DropdownMenuSeparator = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = \"DropdownMenuSeparator\"\n\nconst DropdownMenuLabel = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = \"DropdownMenuLabel\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuLabel,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,oCAAsB,CAAA,GAAA,0SAAA,CAAA,gBAAmB,AAAD,EAAuC;AAErF,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAqB;IACnD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAc,AAAD,EAAE;IAEvC,qBACE,mVAAC,oBAAoB,QAAQ;QAAC,OAAO;YAAE;YAAM;QAAQ;kBACnD,cAAA,mVAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;AAEA,MAAM,oCAAsB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAKzC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACrD,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,cAAc;QAClB,QAAQ,OAAO,CAAC,CAAC,QAAQ,IAAI;IAC/B;IAEA,IAAI,SAAS;QACX,qBAAO,CAAA,GAAA,0SAAA,CAAA,eAAkB,AAAD,EAAE,UAAgC;YACxD,SAAS;YACT;QACF;IACF;IAEA,qBACE,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,iPACA;QAEF,SAAS;QACR,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,oCAAsB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAMzC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,aAAa,CAAA,GAAA,0SAAA,CAAA,SAAY,AAAD,EAAkB;IAEhD,CAAA,GAAA,0SAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,qBAAqB,CAAC;YAC1B,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC5E,QAAQ,OAAO,CAAC;YAClB;QACF;QAEA,IAAI,QAAQ,IAAI,EAAE;YAChB,SAAS,gBAAgB,CAAC,aAAa;QACzC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC,QAAQ,IAAI;QAAE,QAAQ,OAAO;KAAC;IAElC,IAAI,CAAC,QAAQ,IAAI,EAAE,OAAO;IAE1B,qBACE,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,iHACA,UAAU,WAAW,UACrB,UAAU,YAAY,uCACtB,UAAU,SAAS,WACnB;QAEF,OAAO;YAAE,KAAK,CAAC,YAAY,EAAE,WAAW,GAAG,CAAC;QAAC;QAC5C,GAAG,KAAK;;;;;;AAGf;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,iCAAmB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACjC,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,MAAM,cAAc,CAAC;QACnB,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,OAAO,CAAC;QAChB;QACA,SAAS,QAAQ;IACnB;IAEA,qBACE,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,gRACA,SAAS,QACT;QAEF,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,sCAAwB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG;AAEpC,MAAM,kCAAoB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 3398, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/role-permissions/RoleCard.tsx"], "sourcesContent": ["'use client'\n\nimport { Role } from '@/stores/useRolePermissionsStore'\nimport { Card, CardContent, CardHeader } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { RoleForm } from './RoleForm'\nimport { \n  Shield, \n  Edit, \n  Trash2, \n  Users, \n  Settings,\n  MoreVertical\n} from 'lucide-react'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'\nimport { toast } from 'sonner'\n\ninterface RoleCardProps {\n  role: Role\n  onSelect: (role: Role) => void\n}\n\nexport function RoleCard({ role, onSelect }: RoleCardProps) {\n  const { deleteRole, fetchRoles } = useRolePermissionsStore()\n\n  const handleDelete = async () => {\n    if (window.confirm(`Are you sure you want to delete the role \"${role.name}\"?`)) {\n      const success = await deleteRole(role.id)\n      if (success) {\n        fetchRoles()\n      }\n    }\n  }\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case '1': return 'bg-red-100 text-red-800'\n      case '2': return 'bg-orange-100 text-orange-800'\n      case '3': return 'bg-green-100 text-green-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getLevelLabel = (level: string) => {\n    switch (level) {\n      case '1': return 'Super Admin'\n      case '2': return 'Institute Admin'\n      case '3': return 'User Level'\n      default: return 'Unknown'\n    }\n  }\n\n  return (\n    <Card className=\"hover:shadow-md transition-shadow cursor-pointer group\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <Shield className=\"h-4 w-4 text-blue-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900 group-hover:text-blue-600 transition-colors\">\n                {role.name}\n              </h3>\n              <p className=\"text-sm text-gray-600\">{role.code}</p>\n            </div>\n          </div>\n          \n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\n                <MoreVertical className=\"h-4 w-4\" />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\">\n              <DropdownMenuItem onClick={() => onSelect(role)}>\n                <Users className=\"h-4 w-4 mr-2\" />\n                View Details\n              </DropdownMenuItem>\n              <RoleForm \n                mode=\"edit\" \n                role={role}\n                trigger={\n                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>\n                    <Edit className=\"h-4 w-4 mr-2\" />\n                    Edit Role\n                  </DropdownMenuItem>\n                }\n                onSuccess={() => fetchRoles()}\n              />\n              {!role.isSystemRole && (\n                <DropdownMenuItem onClick={handleDelete} className=\"text-red-600\">\n                  <Trash2 className=\"h-4 w-4 mr-2\" />\n                  Delete Role\n                </DropdownMenuItem>\n              )}\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"pt-0\">\n        <div className=\"space-y-3\">\n          {/* Description */}\n          {role.description && (\n            <p className=\"text-sm text-gray-600 line-clamp-2\">\n              {role.description}\n            </p>\n          )}\n          \n          {/* Badges */}\n          <div className=\"flex flex-wrap gap-2\">\n            <Badge className={getLevelColor(role.level)}>\n              {getLevelLabel(role.level)}\n            </Badge>\n            \n            <Badge variant={role.isActive ? \"default\" : \"secondary\"}>\n              {role.isActive ? \"Active\" : \"Inactive\"}\n            </Badge>\n            \n            {role.isSystemRole && (\n              <Badge variant=\"outline\" className=\"text-purple-600 border-purple-200\">\n                System Role\n              </Badge>\n            )}\n            \n            {role.scope && (\n              <Badge variant=\"outline\">\n                {role.scope}\n              </Badge>\n            )}\n          </div>\n          \n          {/* Permissions count */}\n          <div className=\"flex items-center gap-2 text-sm text-gray-600\">\n            <Settings className=\"h-4 w-4\" />\n            <span>{role.permissions?.length || 0} permissions</span>\n          </div>\n          \n          {/* Priority */}\n          <div className=\"text-xs text-gray-500\">\n            Priority: {role.priority}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAMA;AArBA;;;;;;;;;AA6BO,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAiB;IACxD,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,0BAAuB,AAAD;IAEzD,MAAM,eAAe;QACnB,IAAI,OAAO,OAAO,CAAC,CAAC,0CAA0C,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG;YAC9E,MAAM,UAAU,MAAM,WAAW,KAAK,EAAE;YACxC,IAAI,SAAS;gBACX;YACF;QACF;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,mVAAC,oJAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,mVAAC,oJAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,mVAAC;oBAAI,WAAU;;sCACb,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCAAI,WAAU;8CACb,cAAA,mVAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;8CAEpB,mVAAC;;sDACC,mVAAC;4CAAG,WAAU;sDACX,KAAK,IAAI;;;;;;sDAEZ,mVAAC;4CAAE,WAAU;sDAAyB,KAAK,IAAI;;;;;;;;;;;;;;;;;;sCAInD,mVAAC,gKAAA,CAAA,eAAY;;8CACX,mVAAC,gKAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,mVAAC,sJAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;kDAC1C,cAAA,mVAAC,8SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG5B,mVAAC,gKAAA,CAAA,sBAAmB;oCAAC,OAAM;;sDACzB,mVAAC,gKAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,SAAS;;8DACxC,mVAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGpC,mVAAC,yKAAA,CAAA,WAAQ;4CACP,MAAK;4CACL,MAAM;4CACN,uBACE,mVAAC,gKAAA,CAAA,mBAAgB;gDAAC,UAAU,CAAC,IAAM,EAAE,cAAc;;kEACjD,mVAAC,+RAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAIrC,WAAW,IAAM;;;;;;wCAElB,CAAC,KAAK,YAAY,kBACjB,mVAAC,gKAAA,CAAA,mBAAgB;4CAAC,SAAS;4CAAc,WAAU;;8DACjD,mVAAC,8RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/C,mVAAC,oJAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,mVAAC;oBAAI,WAAU;;wBAEZ,KAAK,WAAW,kBACf,mVAAC;4BAAE,WAAU;sCACV,KAAK,WAAW;;;;;;sCAKrB,mVAAC;4BAAI,WAAU;;8CACb,mVAAC,qJAAA,CAAA,QAAK;oCAAC,WAAW,cAAc,KAAK,KAAK;8CACvC,cAAc,KAAK,KAAK;;;;;;8CAG3B,mVAAC,qJAAA,CAAA,QAAK;oCAAC,SAAS,KAAK,QAAQ,GAAG,YAAY;8CACzC,KAAK,QAAQ,GAAG,WAAW;;;;;;gCAG7B,KAAK,YAAY,kBAChB,mVAAC,qJAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,WAAU;8CAAoC;;;;;;gCAKxE,KAAK,KAAK,kBACT,mVAAC,qJAAA,CAAA,QAAK;oCAAC,SAAQ;8CACZ,KAAK,KAAK;;;;;;;;;;;;sCAMjB,mVAAC;4BAAI,WAAU;;8CACb,mVAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;8CACpB,mVAAC;;wCAAM,KAAK,WAAW,EAAE,UAAU;wCAAE;;;;;;;;;;;;;sCAIvC,mVAAC;4BAAI,WAAU;;gCAAwB;gCAC1B,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAMpC", "debugId": null}}, {"offset": {"line": 3740, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/role-permissions/RoleListItem.tsx"], "sourcesContent": ["'use client'\n\nimport { Role } from '@/stores/useRolePermissionsStore'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { RoleForm } from './RoleForm'\nimport { \n  Shield, \n  Edit, \n  Trash2, \n  Users, \n  Settings,\n  MoreVertical\n} from 'lucide-react'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'\n\ninterface RoleListItemProps {\n  role: Role\n  onSelect: (role: Role) => void\n}\n\nexport function RoleListItem({ role, onSelect }: RoleListItemProps) {\n  const { deleteRole, fetchRoles } = useRolePermissionsStore()\n\n  const handleDelete = async () => {\n    if (window.confirm(`Are you sure you want to delete the role \"${role.name}\"?`)) {\n      const success = await deleteRole(role.id)\n      if (success) {\n        fetchRoles()\n      }\n    }\n  }\n\n  const getLevelColor = (level: string) => {\n    switch (level) {\n      case '1': return 'bg-red-100 text-red-800'\n      case '2': return 'bg-orange-100 text-orange-800'\n      case '3': return 'bg-green-100 text-green-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getLevelLabel = (level: string) => {\n    switch (level) {\n      case '1': return 'Super Admin'\n      case '2': return 'Institute Admin'\n      case '3': return 'User Level'\n      default: return 'Unknown'\n    }\n  }\n\n  return (\n    <Card className=\"hover:shadow-sm transition-shadow\">\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Left side - Role info */}\n          <div className=\"flex items-center gap-4 flex-1\">\n            <div className=\"p-2 bg-blue-100 rounded-lg\">\n              <Shield className=\"h-4 w-4 text-blue-600\" />\n            </div>\n            \n            <div className=\"flex-1 min-w-0\">\n              <div className=\"flex items-center gap-3 mb-1\">\n                <h3 className=\"font-semibold text-gray-900 truncate\">\n                  {role.name}\n                </h3>\n                <Badge className={getLevelColor(role.level)}>\n                  {getLevelLabel(role.level)}\n                </Badge>\n                <Badge variant={role.isActive ? \"default\" : \"secondary\"}>\n                  {role.isActive ? \"Active\" : \"Inactive\"}\n                </Badge>\n                {role.isSystemRole && (\n                  <Badge variant=\"outline\" className=\"text-purple-600 border-purple-200\">\n                    System\n                  </Badge>\n                )}\n              </div>\n              \n              <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n                <span>Code: {role.code}</span>\n                <span className=\"flex items-center gap-1\">\n                  <Settings className=\"h-3 w-3\" />\n                  {role.permissions?.length || 0} permissions\n                </span>\n                <span>Priority: {role.priority}</span>\n                {role.scope && <span>Scope: {role.scope}</span>}\n              </div>\n              \n              {role.description && (\n                <p className=\"text-sm text-gray-600 mt-1 line-clamp-1\">\n                  {role.description}\n                </p>\n              )}\n            </div>\n          </div>\n          \n          {/* Right side - Actions */}\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => onSelect(role)}\n              className=\"gap-2\"\n            >\n              <Users className=\"h-4 w-4\" />\n              View\n            </Button>\n            \n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\n                  <MoreVertical className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\">\n                <RoleForm \n                  mode=\"edit\" \n                  role={role}\n                  trigger={\n                    <DropdownMenuItem onSelect={(e) => e.preventDefault()}>\n                      <Edit className=\"h-4 w-4 mr-2\" />\n                      Edit Role\n                    </DropdownMenuItem>\n                  }\n                  onSuccess={() => fetchRoles()}\n                />\n                {!role.isSystemRole && (\n                  <DropdownMenuItem onClick={handleDelete} className=\"text-red-600\">\n                    <Trash2 className=\"h-4 w-4 mr-2\" />\n                    Delete Role\n                  </DropdownMenuItem>\n                )}\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAMA;AArBA;;;;;;;;;AA4BO,SAAS,aAAa,EAAE,IAAI,EAAE,QAAQ,EAAqB;IAChE,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,0BAAuB,AAAD;IAEzD,MAAM,eAAe;QACnB,IAAI,OAAO,OAAO,CAAC,CAAC,0CAA0C,EAAE,KAAK,IAAI,CAAC,EAAE,CAAC,GAAG;YAC9E,MAAM,UAAU,MAAM,WAAW,KAAK,EAAE;YACxC,IAAI,SAAS;gBACX;YACF;QACF;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB,KAAK;gBAAK,OAAO;YACjB;gBAAS,OAAO;QAClB;IACF;IAEA,qBACE,mVAAC,oJAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,mVAAC,oJAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,mVAAC;gBAAI,WAAU;;kCAEb,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;0CACb,cAAA,mVAAC,0RAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;;;;;;0CAGpB,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;;0DACb,mVAAC;gDAAG,WAAU;0DACX,KAAK,IAAI;;;;;;0DAEZ,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,cAAc,KAAK,KAAK;0DACvC,cAAc,KAAK,KAAK;;;;;;0DAE3B,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAS,KAAK,QAAQ,GAAG,YAAY;0DACzC,KAAK,QAAQ,GAAG,WAAW;;;;;;4CAE7B,KAAK,YAAY,kBAChB,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;0DAAoC;;;;;;;;;;;;kDAM3E,mVAAC;wCAAI,WAAU;;0DACb,mVAAC;;oDAAK;oDAAO,KAAK,IAAI;;;;;;;0DACtB,mVAAC;gDAAK,WAAU;;kEACd,mVAAC,8RAAA,CAAA,WAAQ;wDAAC,WAAU;;;;;;oDACnB,KAAK,WAAW,EAAE,UAAU;oDAAE;;;;;;;0DAEjC,mVAAC;;oDAAK;oDAAW,KAAK,QAAQ;;;;;;;4CAC7B,KAAK,KAAK,kBAAI,mVAAC;;oDAAK;oDAAQ,KAAK,KAAK;;;;;;;;;;;;;oCAGxC,KAAK,WAAW,kBACf,mVAAC;wCAAE,WAAU;kDACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;kCAOzB,mVAAC;wBAAI,WAAU;;0CACb,mVAAC,sJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS;gCACxB,WAAU;;kDAEV,mVAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAI/B,mVAAC,gKAAA,CAAA,eAAY;;kDACX,mVAAC,gKAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,mVAAC,sJAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,mVAAC,8SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,mVAAC,gKAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,mVAAC,yKAAA,CAAA,WAAQ;gDACP,MAAK;gDACL,MAAM;gDACN,uBACE,mVAAC,gKAAA,CAAA,mBAAgB;oDAAC,UAAU,CAAC,IAAM,EAAE,cAAc;;sEACjD,mVAAC,+RAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;gDAIrC,WAAW,IAAM;;;;;;4CAElB,CAAC,KAAK,YAAY,kBACjB,mVAAC,gKAAA,CAAA,mBAAgB;gDAAC,SAAS;gDAAc,WAAU;;kEACjD,mVAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWvD", "debugId": null}}, {"offset": {"line": 4081, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/role-permissions/RolePermissionsPagination.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react'\n\ninterface Pagination {\n  page: number\n  limit: number\n  totalPages: number\n  totalDocs: number\n  hasNextPage: boolean\n  hasPrevPage: boolean\n}\n\ninterface RolePermissionsPaginationProps {\n  pagination: Pagination\n  onPageChange: (page: number) => void\n}\n\nexport function RolePermissionsPagination({ pagination, onPageChange }: RolePermissionsPaginationProps) {\n  const { page, totalPages, totalDocs, hasNextPage, hasPrevPage } = pagination\n\n  if (totalDocs === 0) {\n    return null\n  }\n\n  const getVisiblePages = () => {\n    const delta = 2\n    const range = []\n    const rangeWithDots = []\n\n    for (\n      let i = Math.max(2, page - delta);\n      i <= Math.min(totalPages - 1, page + delta);\n      i++\n    ) {\n      range.push(i)\n    }\n\n    if (page - delta > 2) {\n      rangeWithDots.push(1, '...')\n    } else {\n      rangeWithDots.push(1)\n    }\n\n    rangeWithDots.push(...range)\n\n    if (page + delta < totalPages - 1) {\n      rangeWithDots.push('...', totalPages)\n    } else {\n      rangeWithDots.push(totalPages)\n    }\n\n    return rangeWithDots\n  }\n\n  const visiblePages = getVisiblePages()\n\n  return (\n    <div className=\"flex items-center justify-between px-2\">\n      <div className=\"flex-1 text-sm text-muted-foreground\">\n        Showing {((page - 1) * pagination.limit) + 1} to {Math.min(page * pagination.limit, totalDocs)} of {totalDocs} results\n      </div>\n      \n      <div className=\"flex items-center space-x-6 lg:space-x-8\">\n        <div className=\"flex items-center space-x-2\">\n          <p className=\"text-sm font-medium\">Rows per page</p>\n          <Select\n            value={`${pagination.limit}`}\n            onValueChange={(value) => {\n              // This would need to be implemented in the store\n              console.log('Change page size to:', value)\n            }}\n          >\n            <SelectTrigger className=\"h-8 w-[70px]\">\n              <SelectValue placeholder={pagination.limit} />\n            </SelectTrigger>\n            <SelectContent side=\"top\">\n              {[10, 20, 30, 40, 50].map((pageSize) => (\n                <SelectItem key={pageSize} value={`${pageSize}`}>\n                  {pageSize}\n                </SelectItem>\n              ))}\n            </SelectContent>\n          </Select>\n        </div>\n        \n        <div className=\"flex w-[100px] items-center justify-center text-sm font-medium\">\n          Page {page} of {totalPages}\n        </div>\n        \n        <div className=\"flex items-center space-x-2\">\n          <Button\n            variant=\"outline\"\n            className=\"hidden h-8 w-8 p-0 lg:flex\"\n            onClick={() => onPageChange(1)}\n            disabled={!hasPrevPage}\n          >\n            <span className=\"sr-only\">Go to first page</span>\n            <ChevronsLeft className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"outline\"\n            className=\"h-8 w-8 p-0\"\n            onClick={() => onPageChange(page - 1)}\n            disabled={!hasPrevPage}\n          >\n            <span className=\"sr-only\">Go to previous page</span>\n            <ChevronLeft className=\"h-4 w-4\" />\n          </Button>\n          \n          {/* Page numbers */}\n          <div className=\"flex items-center space-x-1\">\n            {visiblePages.map((pageNum, index) => (\n              <div key={index}>\n                {pageNum === '...' ? (\n                  <span className=\"px-2 py-1 text-sm\">...</span>\n                ) : (\n                  <Button\n                    variant={pageNum === page ? \"default\" : \"outline\"}\n                    className=\"h-8 w-8 p-0\"\n                    onClick={() => onPageChange(pageNum as number)}\n                  >\n                    {pageNum}\n                  </Button>\n                )}\n              </div>\n            ))}\n          </div>\n          \n          <Button\n            variant=\"outline\"\n            className=\"h-8 w-8 p-0\"\n            onClick={() => onPageChange(page + 1)}\n            disabled={!hasNextPage}\n          >\n            <span className=\"sr-only\">Go to next page</span>\n            <ChevronRight className=\"h-4 w-4\" />\n          </Button>\n          <Button\n            variant=\"outline\"\n            className=\"hidden h-8 w-8 p-0 lg:flex\"\n            onClick={() => onPageChange(totalPages)}\n            disabled={!hasNextPage}\n          >\n            <span className=\"sr-only\">Go to last page</span>\n            <ChevronsRight className=\"h-4 w-4\" />\n          </Button>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AAAA;AAAA;AAJA;;;;;AAoBO,SAAS,0BAA0B,EAAE,UAAU,EAAE,YAAY,EAAkC;IACpG,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,WAAW,EAAE,GAAG;IAElE,IAAI,cAAc,GAAG;QACnB,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,MAAM,QAAQ;QACd,MAAM,QAAQ,EAAE;QAChB,MAAM,gBAAgB,EAAE;QAExB,IACE,IAAI,IAAI,KAAK,GAAG,CAAC,GAAG,OAAO,QAC3B,KAAK,KAAK,GAAG,CAAC,aAAa,GAAG,OAAO,QACrC,IACA;YACA,MAAM,IAAI,CAAC;QACb;QAEA,IAAI,OAAO,QAAQ,GAAG;YACpB,cAAc,IAAI,CAAC,GAAG;QACxB,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QAEA,cAAc,IAAI,IAAI;QAEtB,IAAI,OAAO,QAAQ,aAAa,GAAG;YACjC,cAAc,IAAI,CAAC,OAAO;QAC5B,OAAO;YACL,cAAc,IAAI,CAAC;QACrB;QAEA,OAAO;IACT;IAEA,MAAM,eAAe;IAErB,qBACE,mVAAC;QAAI,WAAU;;0BACb,mVAAC;gBAAI,WAAU;;oBAAuC;oBAC1C,CAAC,OAAO,CAAC,IAAI,WAAW,KAAK,GAAI;oBAAE;oBAAK,KAAK,GAAG,CAAC,OAAO,WAAW,KAAK,EAAE;oBAAW;oBAAK;oBAAU;;;;;;;0BAGhH,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAE,WAAU;0CAAsB;;;;;;0CACnC,mVAAC,sJAAA,CAAA,SAAM;gCACL,OAAO,GAAG,WAAW,KAAK,EAAE;gCAC5B,eAAe,CAAC;oCACd,iDAAiD;oCACjD,QAAQ,GAAG,CAAC,wBAAwB;gCACtC;;kDAEA,mVAAC,sJAAA,CAAA,gBAAa;wCAAC,WAAU;kDACvB,cAAA,mVAAC,sJAAA,CAAA,cAAW;4CAAC,aAAa,WAAW,KAAK;;;;;;;;;;;kDAE5C,mVAAC,sJAAA,CAAA,gBAAa;wCAAC,MAAK;kDACjB;4CAAC;4CAAI;4CAAI;4CAAI;4CAAI;yCAAG,CAAC,GAAG,CAAC,CAAC,yBACzB,mVAAC,sJAAA,CAAA,aAAU;gDAAgB,OAAO,GAAG,UAAU;0DAC5C;+CADc;;;;;;;;;;;;;;;;;;;;;;kCAQzB,mVAAC;wBAAI,WAAU;;4BAAiE;4BACxE;4BAAK;4BAAK;;;;;;;kCAGlB,mVAAC;wBAAI,WAAU;;0CACb,mVAAC,sJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,aAAa;gCAC5B,UAAU,CAAC;;kDAEX,mVAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,mVAAC,0SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;0CAE1B,mVAAC,sJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,aAAa,OAAO;gCACnC,UAAU,CAAC;;kDAEX,mVAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,mVAAC,wSAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;;;;;;;0CAIzB,mVAAC;gCAAI,WAAU;0CACZ,aAAa,GAAG,CAAC,CAAC,SAAS,sBAC1B,mVAAC;kDACE,YAAY,sBACX,mVAAC;4CAAK,WAAU;sDAAoB;;;;;iEAEpC,mVAAC,sJAAA,CAAA,SAAM;4CACL,SAAS,YAAY,OAAO,YAAY;4CACxC,WAAU;4CACV,SAAS,IAAM,aAAa;sDAE3B;;;;;;uCATG;;;;;;;;;;0CAgBd,mVAAC,sJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,aAAa,OAAO;gCACnC,UAAU,CAAC;;kDAEX,mVAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,mVAAC,0SAAA,CAAA,eAAY;wCAAC,WAAU;;;;;;;;;;;;0CAE1B,mVAAC,sJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM,aAAa;gCAC5B,UAAU,CAAC;;kDAEX,mVAAC;wCAAK,WAAU;kDAAU;;;;;;kDAC1B,mVAAC,4SAAA,CAAA,gBAAa;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMrC", "debugId": null}}, {"offset": {"line": 4388, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/empty-state.tsx"], "sourcesContent": ["'use client'\n\nimport { But<PERSON> } from '@/components/ui/button'\nimport { LucideIcon } from 'lucide-react'\n\ninterface EmptyStateProps {\n  icon: LucideIcon\n  title: string\n  description: string\n  action?: {\n    label: string\n    onClick: () => void\n  }\n}\n\nexport function EmptyState({ icon: Icon, title, description, action }: EmptyStateProps) {\n  return (\n    <div className=\"flex flex-col items-center justify-center py-12 px-4 text-center\">\n      <div className=\"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4\">\n        <Icon className=\"h-8 w-8 text-gray-400\" />\n      </div>\n      <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">{title}</h3>\n      <p className=\"text-gray-600 mb-6 max-w-md\">{description}</p>\n      {action && (\n        <Button onClick={action.onClick}>\n          {action.label}\n        </Button>\n      )}\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAeO,SAAS,WAAW,EAAE,MAAM,IAAI,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAmB;IACpF,qBACE,mVAAC;QAAI,WAAU;;0BACb,mVAAC;gBAAI,WAAU;0BACb,cAAA,mVAAC;oBAAK,WAAU;;;;;;;;;;;0BAElB,mVAAC;gBAAG,WAAU;0BAA4C;;;;;;0BAC1D,mVAAC;gBAAE,WAAU;0BAA+B;;;;;;YAC3C,wBACC,mVAAC,sJAAA,CAAA,SAAM;gBAAC,SAAS,OAAO,OAAO;0BAC5B,OAAO,KAAK;;;;;;;;;;;;AAKvB", "debugId": null}}, {"offset": {"line": 4451, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/role-permissions/RolesList.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'\nimport { RoleCard } from './RoleCard'\nimport { RoleListItem } from './RoleListItem'\nimport { RolePermissionsPagination } from './RolePermissionsPagination'\nimport { EmptyState } from '@/components/ui/empty-state'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { AlertTriangle, Shield } from 'lucide-react'\n\nexport function RolesList() {\n  const {\n    roles,\n    viewMode,\n    rolesPagination,\n    isLoading,\n    fetchRoles,\n    setSelectedRole\n  } = useRolePermissionsStore()\n\n  useEffect(() => {\n    // Load roles when component mounts\n    if (roles.length === 0) {\n      fetchRoles()\n    }\n  }, [fetchRoles, roles.length])\n\n  const handlePageChange = (page: number) => {\n    fetchRoles(page)\n  }\n\n  const handleRoleSelect = (role: any) => {\n    setSelectedRole(role)\n  }\n\n  if (isLoading && roles.length === 0) {\n    return (\n      <div className=\"space-y-4\">\n        {Array.from({ length: 6 }).map((_, index) => (\n          <div key={index} className=\"animate-pulse\">\n            <div className=\"h-20 bg-gray-200 rounded-lg\"></div>\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  if (roles.length === 0) {\n    return (\n      <EmptyState\n        icon={Shield}\n        title=\"No roles found\"\n        description=\"No roles found. Try adjusting your search criteria or add new roles.\"\n      />\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Roles Grid/List */}\n      {viewMode === 'card' ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n          {roles.map((role) => (\n            <RoleCard\n              key={role.id}\n              role={role}\n              onSelect={handleRoleSelect}\n            />\n          ))}\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {roles.map((role) => (\n            <RoleListItem\n              key={role.id}\n              role={role}\n              onSelect={handleRoleSelect}\n            />\n          ))}\n        </div>\n      )}\n\n      {/* Pagination */}\n      <RolePermissionsPagination\n        pagination={rolesPagination}\n        onPageChange={handlePageChange}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AATA;;;;;;;;;AAWO,SAAS;IACd,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,eAAe,EACf,SAAS,EACT,UAAU,EACV,eAAe,EAChB,GAAG,CAAA,GAAA,4JAAA,CAAA,0BAAuB,AAAD;IAE1B,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,mCAAmC;QACnC,IAAI,MAAM,MAAM,KAAK,GAAG;YACtB;QACF;IACF,GAAG;QAAC;QAAY,MAAM,MAAM;KAAC;IAE7B,MAAM,mBAAmB,CAAC;QACxB,WAAW;IACb;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;IAClB;IAEA,IAAI,aAAa,MAAM,MAAM,KAAK,GAAG;QACnC,qBACE,mVAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,mVAAC;oBAAgB,WAAU;8BACzB,cAAA,mVAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,MAAM,MAAM,KAAK,GAAG;QACtB,qBACE,mVAAC,8JAAA,CAAA,aAAU;YACT,MAAM,0RAAA,CAAA,SAAM;YACZ,OAAM;YACN,aAAY;;;;;;IAGlB;IAEA,qBACE,mVAAC;QAAI,WAAU;;YAEZ,aAAa,uBACZ,mVAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,mVAAC,yKAAA,CAAA,WAAQ;wBAEP,MAAM;wBACN,UAAU;uBAFL,KAAK,EAAE;;;;;;;;;qCAOlB,mVAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,mVAAC,6KAAA,CAAA,eAAY;wBAEX,MAAM;wBACN,UAAU;uBAFL,KAAK,EAAE;;;;;;;;;;0BASpB,mVAAC,0LAAA,CAAA,4BAAyB;gBACxB,YAAY;gBACZ,cAAc;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 4577, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/role-permissions/PermissionForm.tsx"], "sourcesContent": ["'use client'\n\nimport { useState } from 'react'\nimport { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Label } from '@/components/ui/label'\nimport { Switch } from '@/components/ui/switch'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'\nimport { Plus, Edit, Save, X } from 'lucide-react'\nimport { Formik, Field, ErrorMessage } from 'formik'\nimport * as Yup from 'yup'\nimport { toast } from 'sonner'\n\n// Validation schema using Yup - Following Location Management patterns\nconst validationSchema = Yup.object({\n  name: Yup.string()\n    .required('Permission name is required')\n    .min(2, 'Permission name must be at least 2 characters')\n    .max(100, 'Permission name must be less than 100 characters'),\n  code: Yup.string()\n    .required('Permission code is required')\n    .min(2, 'Permission code must be at least 2 characters')\n    .max(50, 'Permission code must be at most 50 characters')\n    .matches(/^[a-z_]+$/, 'Permission code must contain only lowercase letters and underscores'),\n  description: Yup.string()\n    .max(500, 'Description must be less than 500 characters'),\n  resource: Yup.string()\n    .required('Resource is required'),\n  action: Yup.string()\n    .required('Action is required'),\n  scope: Yup.string()\n    .max(100, 'Scope must be less than 100 characters'),\n  isActive: Yup.boolean(),\n  priority: Yup.number()\n    .min(0, 'Priority must be 0 or greater')\n    .integer('Priority must be a whole number')\n})\n\ninterface PermissionFormProps {\n  permission?: any\n  mode: 'create' | 'edit'\n  trigger?: React.ReactNode\n  onSuccess?: () => void\n  open?: boolean\n  onOpenChange?: (open: boolean) => void\n}\n\nexport function PermissionForm({ permission, mode, trigger, onSuccess, open: externalOpen, onOpenChange }: PermissionFormProps) {\n  const [internalOpen, setInternalOpen] = useState(false)\n  const open = externalOpen !== undefined ? externalOpen : internalOpen\n  const setOpen = onOpenChange || setInternalOpen\n  const { createPermission, updatePermission, isLoading } = useRolePermissionsStore()\n\n  // Initial values for Formik - Following Location Management patterns\n  const initialValues = {\n    name: permission?.name || '',\n    code: permission?.code || '',\n    description: permission?.description || '',\n    resource: permission?.resource || '',\n    action: permission?.action || '',\n    scope: permission?.scope || '',\n    isActive: permission?.isActive ?? true,\n    priority: permission?.priority || 0\n  }\n\n  const handleSubmit = async (values: any, { setSubmitting, resetForm }: any) => {\n    try {\n      // Prepare data for submission - Following Location Management patterns\n      const submitData = {\n        name: values.name,\n        code: values.code.toLowerCase(),\n        description: values.description,\n        resource: values.resource,\n        action: values.action,\n        scope: values.scope,\n        isActive: values.isActive,\n        priority: values.priority\n      }\n\n      console.log('Submitting permission data:', submitData)\n\n      if (mode === 'create') {\n        await createPermission(submitData)\n        toast.success('Permission created successfully')\n        resetForm()\n      } else {\n        await updatePermission(permission.id, submitData)\n        toast.success('Permission updated successfully')\n      }\n\n      // Only close dialog on successful submission\n      setOpen(false)\n      onSuccess?.()\n    } catch (error) {\n      console.error('Form submission error:', error)\n      toast.error('Failed to save permission')\n      // Don't close dialog on error - let user fix the issues\n    } finally {\n      setSubmitting(false)\n    }\n  }\n\n  const defaultTrigger = (\n    <Button size=\"sm\" className=\"gap-2\">\n      {mode === 'create' ? <Plus className=\"h-4 w-4\" /> : <Edit className=\"h-4 w-4\" />}\n      {mode === 'create' ? 'Add Permission' : 'Edit Permission'}\n    </Button>\n  )\n\n  return (\n    <Dialog open={open} onOpenChange={setOpen}>\n      <DialogTrigger asChild>\n        {trigger || defaultTrigger}\n      </DialogTrigger>\n      <DialogContent className=\"max-w-2xl max-h-[90vh] overflow-y-auto\">\n        <DialogHeader>\n          <DialogTitle className=\"flex items-center gap-2\">\n            {mode === 'create' ? <Plus className=\"h-5 w-5\" /> : <Edit className=\"h-5 w-5\" />}\n            {mode === 'create' ? 'Create New Permission' : 'Edit Permission'}\n          </DialogTitle>\n        </DialogHeader>\n\n        <Formik\n          initialValues={initialValues}\n          validationSchema={validationSchema}\n          onSubmit={handleSubmit}\n          enableReinitialize={true}\n        >\n          {({ values, errors, touched, handleChange, isSubmitting, setSubmitting, resetForm }) => (\n            <div className=\"space-y-6\">\n              {/* Basic Information */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Basic Information</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div>\n                    <Label htmlFor=\"name\">Permission Name *</Label>\n                    <Field name=\"name\">\n                      {({ field }: any) => (\n                        <Input\n                          {...field}\n                          id=\"name\"\n                          placeholder=\"Enter permission name\"\n                          className={errors.name && touched.name ? 'border-red-500' : ''}\n                        />\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"name\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"code\">Permission Code *</Label>\n                    <Field name=\"code\">\n                      {({ field }: any) => (\n                        <Input\n                          {...field}\n                          id=\"code\"\n                          placeholder=\"Enter permission code (lowercase, underscores allowed)\"\n                          className={errors.code && touched.code ? 'border-red-500' : ''}\n                        />\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"code\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"description\">Description</Label>\n                    <Field name=\"description\">\n                      {({ field }: any) => (\n                        <Textarea\n                          {...field}\n                          id=\"description\"\n                          placeholder=\"Enter permission description\"\n                          rows={3}\n                          className={errors.description && touched.description ? 'border-red-500' : ''}\n                        />\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"description\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Permission Configuration */}\n              <Card>\n                <CardHeader>\n                  <CardTitle className=\"text-lg\">Permission Configuration</CardTitle>\n                </CardHeader>\n                <CardContent className=\"space-y-4\">\n                  <div>\n                    <Label htmlFor=\"resource\">Resource *</Label>\n                    <Field name=\"resource\">\n                      {({ field, form }: any) => (\n                        <Select\n                          value={field.value}\n                          onValueChange={(value) => form.setFieldValue('resource', value)}\n                        >\n                          <SelectTrigger className={errors.resource && touched.resource ? 'border-red-500' : ''}>\n                            <SelectValue placeholder=\"Select resource\" />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"users\">Users</SelectItem>\n                            <SelectItem value=\"roles\">Roles</SelectItem>\n                            <SelectItem value=\"permissions\">Permissions</SelectItem>\n                            <SelectItem value=\"institutes\">Institutes</SelectItem>\n                            <SelectItem value=\"courses\">Courses</SelectItem>\n                            <SelectItem value=\"settings\">Settings</SelectItem>\n                            <SelectItem value=\"analytics\">Analytics</SelectItem>\n                            <SelectItem value=\"billing\">Billing</SelectItem>\n                          </SelectContent>\n                        </Select>\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"resource\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"action\">Action *</Label>\n                    <Field name=\"action\">\n                      {({ field, form }: any) => (\n                        <Select\n                          value={field.value}\n                          onValueChange={(value) => form.setFieldValue('action', value)}\n                        >\n                          <SelectTrigger className={errors.action && touched.action ? 'border-red-500' : ''}>\n                            <SelectValue placeholder=\"Select action\" />\n                          </SelectTrigger>\n                          <SelectContent>\n                            <SelectItem value=\"create\">Create</SelectItem>\n                            <SelectItem value=\"read\">Read</SelectItem>\n                            <SelectItem value=\"update\">Update</SelectItem>\n                            <SelectItem value=\"delete\">Delete</SelectItem>\n                            <SelectItem value=\"manage\">Manage</SelectItem>\n                            <SelectItem value=\"view\">View</SelectItem>\n                          </SelectContent>\n                        </Select>\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"action\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"scope\">Scope</Label>\n                    <Field name=\"scope\">\n                      {({ field }: any) => (\n                        <Input\n                          {...field}\n                          id=\"scope\"\n                          placeholder=\"Enter permission scope (optional)\"\n                          className={errors.scope && touched.scope ? 'border-red-500' : ''}\n                        />\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"scope\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div>\n                    <Label htmlFor=\"priority\">Priority</Label>\n                    <Field name=\"priority\">\n                      {({ field }: any) => (\n                        <Input\n                          {...field}\n                          id=\"priority\"\n                          type=\"number\"\n                          min=\"0\"\n                          placeholder=\"Enter priority (0 = highest)\"\n                          className={errors.priority && touched.priority ? 'border-red-500' : ''}\n                        />\n                      )}\n                    </Field>\n                    <ErrorMessage name=\"priority\" component=\"div\" className=\"text-red-500 text-sm mt-1\" />\n                  </div>\n\n                  <div className=\"flex items-center space-x-2\">\n                    <Field name=\"isActive\">\n                      {({ field, form }: any) => (\n                        <Switch\n                          id=\"isActive\"\n                          checked={field.value}\n                          onCheckedChange={(checked) => form.setFieldValue('isActive', checked)}\n                        />\n                      )}\n                    </Field>\n                    <Label htmlFor=\"isActive\">Active Permission</Label>\n                  </div>\n                </CardContent>\n              </Card>\n\n              {/* Form Actions */}\n              <div className=\"flex justify-end gap-3 pt-4 border-t\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={() => setOpen(false)}\n                  disabled={isSubmitting}\n                >\n                  <X className=\"h-4 w-4 mr-2\" />\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  onClick={() => handleSubmit(values, { setSubmitting, resetForm })}\n                  disabled={isSubmitting}\n                  className=\"gap-2\"\n                >\n                  <Save className=\"h-4 w-4\" />\n                  {isSubmitting ? 'Saving...' : (mode === 'create' ? 'Create Permission' : 'Update Permission')}\n                </Button>\n              </div>\n            </div>\n          )}\n        </Formik>\n      </DialogContent>\n    </Dialog>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAfA;;;;;;;;;;;;;;;;AAiBA,uEAAuE;AACvE,MAAM,mBAAmB,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;IAClC,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACZ,QAAQ,CAAC,+BACT,GAAG,CAAC,GAAG,iDACP,GAAG,CAAC,KAAK;IACZ,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACZ,QAAQ,CAAC,+BACT,GAAG,CAAC,GAAG,iDACP,GAAG,CAAC,IAAI,iDACR,OAAO,CAAC,aAAa;IACxB,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACnB,GAAG,CAAC,KAAK;IACZ,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IAChB,QAAQ,CAAC;IACZ,QAAQ,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACd,QAAQ,CAAC;IACZ,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACb,GAAG,CAAC,KAAK;IACZ,UAAU,CAAA,GAAA,mLAAA,CAAA,UAAW,AAAD;IACpB,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IAChB,GAAG,CAAC,GAAG,iCACP,OAAO,CAAC;AACb;AAWO,SAAS,eAAe,EAAE,UAAU,EAAE,IAAI,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,YAAY,EAAE,YAAY,EAAuB;IAC5H,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,OAAO,iBAAiB,YAAY,eAAe;IACzD,MAAM,UAAU,gBAAgB;IAChC,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,0BAAuB,AAAD;IAEhF,qEAAqE;IACrE,MAAM,gBAAgB;QACpB,MAAM,YAAY,QAAQ;QAC1B,MAAM,YAAY,QAAQ;QAC1B,aAAa,YAAY,eAAe;QACxC,UAAU,YAAY,YAAY;QAClC,QAAQ,YAAY,UAAU;QAC9B,OAAO,YAAY,SAAS;QAC5B,UAAU,YAAY,YAAY;QAClC,UAAU,YAAY,YAAY;IACpC;IAEA,MAAM,eAAe,OAAO,QAAa,EAAE,aAAa,EAAE,SAAS,EAAO;QACxE,IAAI;YACF,uEAAuE;YACvE,MAAM,aAAa;gBACjB,MAAM,OAAO,IAAI;gBACjB,MAAM,OAAO,IAAI,CAAC,WAAW;gBAC7B,aAAa,OAAO,WAAW;gBAC/B,UAAU,OAAO,QAAQ;gBACzB,QAAQ,OAAO,MAAM;gBACrB,OAAO,OAAO,KAAK;gBACnB,UAAU,OAAO,QAAQ;gBACzB,UAAU,OAAO,QAAQ;YAC3B;YAEA,QAAQ,GAAG,CAAC,+BAA+B;YAE3C,IAAI,SAAS,UAAU;gBACrB,MAAM,iBAAiB;gBACvB,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBACd;YACF,OAAO;gBACL,MAAM,iBAAiB,WAAW,EAAE,EAAE;gBACtC,8OAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YAChB;YAEA,6CAA6C;YAC7C,QAAQ;YACR;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,8OAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACZ,wDAAwD;QAC1D,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,+BACJ,mVAAC,sJAAA,CAAA,SAAM;QAAC,MAAK;QAAK,WAAU;;YACzB,SAAS,yBAAW,mVAAC,sRAAA,CAAA,OAAI;gBAAC,WAAU;;;;;qCAAe,mVAAC,+RAAA,CAAA,OAAI;gBAAC,WAAU;;;;;;YACnE,SAAS,WAAW,mBAAmB;;;;;;;IAI5C,qBACE,mVAAC,sJAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;;0BAChC,mVAAC,sJAAA,CAAA,gBAAa;gBAAC,OAAO;0BACnB,WAAW;;;;;;0BAEd,mVAAC,sJAAA,CAAA,gBAAa;gBAAC,WAAU;;kCACvB,mVAAC,sJAAA,CAAA,eAAY;kCACX,cAAA,mVAAC,sJAAA,CAAA,cAAW;4BAAC,WAAU;;gCACpB,SAAS,yBAAW,mVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;yDAAe,mVAAC,+RAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCACnE,SAAS,WAAW,0BAA0B;;;;;;;;;;;;kCAInD,mVAAC,wNAAA,CAAA,SAAM;wBACL,eAAe;wBACf,kBAAkB;wBAClB,UAAU;wBACV,oBAAoB;kCAEnB,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,SAAS,EAAE,iBACjF,mVAAC;gCAAI,WAAU;;kDAEb,mVAAC,oJAAA,CAAA,OAAI;;0DACH,mVAAC,oJAAA,CAAA,aAAU;0DACT,cAAA,mVAAC,oJAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,mVAAC,oJAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAO;;;;;;0EACtB,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAO,iBACd,mVAAC,qJAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,IAAG;wEACH,aAAY;wEACZ,WAAW,OAAO,IAAI,IAAI,QAAQ,IAAI,GAAG,mBAAmB;;;;;;;;;;;0EAIlE,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAO,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAGtD,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAO;;;;;;0EACtB,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAO,iBACd,mVAAC,qJAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,IAAG;wEACH,aAAY;wEACZ,WAAW,OAAO,IAAI,IAAI,QAAQ,IAAI,GAAG,mBAAmB;;;;;;;;;;;0EAIlE,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAO,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAGtD,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAc;;;;;;0EAC7B,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAO,iBACd,mVAAC,wJAAA,CAAA,WAAQ;wEACN,GAAG,KAAK;wEACT,IAAG;wEACH,aAAY;wEACZ,MAAM;wEACN,WAAW,OAAO,WAAW,IAAI,QAAQ,WAAW,GAAG,mBAAmB;;;;;;;;;;;0EAIhF,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAc,WAAU;gEAAM,WAAU;;;;;;;;;;;;;;;;;;;;;;;;kDAMjE,mVAAC,oJAAA,CAAA,OAAI;;0DACH,mVAAC,oJAAA,CAAA,aAAU;0DACT,cAAA,mVAAC,oJAAA,CAAA,YAAS;oDAAC,WAAU;8DAAU;;;;;;;;;;;0DAEjC,mVAAC,oJAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAE,IAAI,EAAO,iBACpB,mVAAC,sJAAA,CAAA,SAAM;wEACL,OAAO,MAAM,KAAK;wEAClB,eAAe,CAAC,QAAU,KAAK,aAAa,CAAC,YAAY;;0FAEzD,mVAAC,sJAAA,CAAA,gBAAa;gFAAC,WAAW,OAAO,QAAQ,IAAI,QAAQ,QAAQ,GAAG,mBAAmB;0FACjF,cAAA,mVAAC,sJAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,mVAAC,sJAAA,CAAA,gBAAa;;kGACZ,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAQ;;;;;;kGAC1B,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAQ;;;;;;kGAC1B,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAc;;;;;;kGAChC,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAa;;;;;;kGAC/B,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;kGAC5B,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAW;;;;;;kGAC7B,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAY;;;;;;kGAC9B,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAU;;;;;;;;;;;;;;;;;;;;;;;0EAKpC,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAW,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAG1D,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAS;;;;;;0EACxB,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAE,IAAI,EAAO,iBACpB,mVAAC,sJAAA,CAAA,SAAM;wEACL,OAAO,MAAM,KAAK;wEAClB,eAAe,CAAC,QAAU,KAAK,aAAa,CAAC,UAAU;;0FAEvD,mVAAC,sJAAA,CAAA,gBAAa;gFAAC,WAAW,OAAO,MAAM,IAAI,QAAQ,MAAM,GAAG,mBAAmB;0FAC7E,cAAA,mVAAC,sJAAA,CAAA,cAAW;oFAAC,aAAY;;;;;;;;;;;0FAE3B,mVAAC,sJAAA,CAAA,gBAAa;;kGACZ,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAS;;;;;;kGAC3B,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAO;;;;;;kGACzB,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAS;;;;;;kGAC3B,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAS;;;;;;kGAC3B,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAS;;;;;;kGAC3B,mVAAC,sJAAA,CAAA,aAAU;wFAAC,OAAM;kGAAO;;;;;;;;;;;;;;;;;;;;;;;0EAKjC,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAS,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAGxD,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAQ;;;;;;0EACvB,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAO,iBACd,mVAAC,qJAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,IAAG;wEACH,aAAY;wEACZ,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,GAAG,mBAAmB;;;;;;;;;;;0EAIpE,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAQ,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAGvD,mVAAC;;0EACC,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;0EAC1B,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAO,iBACd,mVAAC,qJAAA,CAAA,QAAK;wEACH,GAAG,KAAK;wEACT,IAAG;wEACH,MAAK;wEACL,KAAI;wEACJ,aAAY;wEACZ,WAAW,OAAO,QAAQ,IAAI,QAAQ,QAAQ,GAAG,mBAAmB;;;;;;;;;;;0EAI1E,mVAAC,wNAAA,CAAA,eAAY;gEAAC,MAAK;gEAAW,WAAU;gEAAM,WAAU;;;;;;;;;;;;kEAG1D,mVAAC;wDAAI,WAAU;;0EACb,mVAAC,wNAAA,CAAA,QAAK;gEAAC,MAAK;0EACT,CAAC,EAAE,KAAK,EAAE,IAAI,EAAO,iBACpB,mVAAC,sJAAA,CAAA,SAAM;wEACL,IAAG;wEACH,SAAS,MAAM,KAAK;wEACpB,iBAAiB,CAAC,UAAY,KAAK,aAAa,CAAC,YAAY;;;;;;;;;;;0EAInE,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;kDAMhC,mVAAC;wCAAI,WAAU;;0DACb,mVAAC,sJAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,QAAQ;gDACvB,UAAU;;kEAEV,mVAAC,gRAAA,CAAA,IAAC;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGhC,mVAAC,sJAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS,IAAM,aAAa,QAAQ;wDAAE;wDAAe;oDAAU;gDAC/D,UAAU;gDACV,WAAU;;kEAEV,mVAAC,sRAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDACf,eAAe,cAAe,SAAS,WAAW,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS3F", "debugId": null}}, {"offset": {"line": 5380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/role-permissions/PermissionCard.tsx"], "sourcesContent": ["'use client'\n\nimport { Permission } from '@/stores/useRolePermissionsStore'\nimport { Card, CardContent, CardHeader } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { PermissionForm } from './PermissionForm'\nimport { \n  Key, \n  Edit, \n  Trash2, \n  Eye, \n  Settings,\n  MoreVertical\n} from 'lucide-react'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'\n\ninterface PermissionCardProps {\n  permission: Permission\n  onSelect: (permission: Permission) => void\n}\n\nexport function PermissionCard({ permission, onSelect }: PermissionCardProps) {\n  const { deletePermission, fetchPermissions } = useRolePermissionsStore()\n\n  const handleDelete = async () => {\n    if (window.confirm(`Are you sure you want to delete the permission \"${permission.name}\"?`)) {\n      const success = await deletePermission(permission.id)\n      if (success) {\n        fetchPermissions()\n      }\n    }\n  }\n\n  const getResourceColor = (resource: string) => {\n    const colors: Record<string, string> = {\n      'users': 'bg-blue-100 text-blue-800',\n      'roles': 'bg-purple-100 text-purple-800',\n      'permissions': 'bg-green-100 text-green-800',\n      'institutes': 'bg-orange-100 text-orange-800',\n      'courses': 'bg-yellow-100 text-yellow-800',\n      'settings': 'bg-gray-100 text-gray-800',\n      'analytics': 'bg-pink-100 text-pink-800',\n      'billing': 'bg-red-100 text-red-800',\n    }\n    return colors[resource.toLowerCase()] || 'bg-gray-100 text-gray-800'\n  }\n\n  const getActionColor = (action: string) => {\n    const colors: Record<string, string> = {\n      'create': 'bg-green-100 text-green-800',\n      'read': 'bg-blue-100 text-blue-800',\n      'update': 'bg-yellow-100 text-yellow-800',\n      'delete': 'bg-red-100 text-red-800',\n      'manage': 'bg-purple-100 text-purple-800',\n      'view': 'bg-gray-100 text-gray-800',\n    }\n    return colors[action.toLowerCase()] || 'bg-gray-100 text-gray-800'\n  }\n\n  return (\n    <Card className=\"hover:shadow-md transition-shadow cursor-pointer group\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"flex items-center gap-3\">\n            <div className=\"p-2 bg-green-100 rounded-lg\">\n              <Key className=\"h-4 w-4 text-green-600\" />\n            </div>\n            <div>\n              <h3 className=\"font-semibold text-gray-900 group-hover:text-green-600 transition-colors\">\n                {permission.name}\n              </h3>\n              <p className=\"text-sm text-gray-600\">{permission.code}</p>\n            </div>\n          </div>\n          \n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\n                <MoreVertical className=\"h-4 w-4\" />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\">\n              <DropdownMenuItem onClick={() => onSelect(permission)}>\n                <Eye className=\"h-4 w-4 mr-2\" />\n                View Details\n              </DropdownMenuItem>\n              <PermissionForm \n                mode=\"edit\" \n                permission={permission}\n                trigger={\n                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>\n                    <Edit className=\"h-4 w-4 mr-2\" />\n                    Edit Permission\n                  </DropdownMenuItem>\n                }\n                onSuccess={() => fetchPermissions()}\n              />\n              <DropdownMenuItem onClick={handleDelete} className=\"text-red-600\">\n                <Trash2 className=\"h-4 w-4 mr-2\" />\n                Delete Permission\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </CardHeader>\n      \n      <CardContent className=\"pt-0\">\n        <div className=\"space-y-3\">\n          {/* Description */}\n          {permission.description && (\n            <p className=\"text-sm text-gray-600 line-clamp-2\">\n              {permission.description}\n            </p>\n          )}\n          \n          {/* Resource and Action Badges */}\n          <div className=\"flex flex-wrap gap-2\">\n            <Badge className={getResourceColor(permission.resource)}>\n              {permission.resource}\n            </Badge>\n            \n            <Badge className={getActionColor(permission.action)}>\n              {permission.action}\n            </Badge>\n            \n            <Badge variant={permission.isActive ? \"default\" : \"secondary\"}>\n              {permission.isActive ? \"Active\" : \"Inactive\"}\n            </Badge>\n            \n            {permission.scope && (\n              <Badge variant=\"outline\">\n                {permission.scope}\n              </Badge>\n            )}\n          </div>\n          \n          {/* Priority */}\n          <div className=\"text-xs text-gray-500\">\n            Priority: {permission.priority}\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAQA;AAMA;AArBA;;;;;;;;;AA4BO,SAAS,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAuB;IAC1E,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,0BAAuB,AAAD;IAErE,MAAM,eAAe;QACnB,IAAI,OAAO,OAAO,CAAC,CAAC,gDAAgD,EAAE,WAAW,IAAI,CAAC,EAAE,CAAC,GAAG;YAC1F,MAAM,UAAU,MAAM,iBAAiB,WAAW,EAAE;YACpD,IAAI,SAAS;gBACX;YACF;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,SAAS;YACT,SAAS;YACT,eAAe;YACf,cAAc;YACd,WAAW;YACX,YAAY;YACZ,aAAa;YACb,WAAW;QACb;QACA,OAAO,MAAM,CAAC,SAAS,WAAW,GAAG,IAAI;IAC3C;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAiC;YACrC,UAAU;YACV,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,OAAO,WAAW,GAAG,IAAI;IACzC;IAEA,qBACE,mVAAC,oJAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,mVAAC,oJAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,mVAAC;oBAAI,WAAU;;sCACb,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCAAI,WAAU;8CACb,cAAA,mVAAC,oRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;;;;;;8CAEjB,mVAAC;;sDACC,mVAAC;4CAAG,WAAU;sDACX,WAAW,IAAI;;;;;;sDAElB,mVAAC;4CAAE,WAAU;sDAAyB,WAAW,IAAI;;;;;;;;;;;;;;;;;;sCAIzD,mVAAC,gKAAA,CAAA,eAAY;;8CACX,mVAAC,gKAAA,CAAA,sBAAmB;oCAAC,OAAO;8CAC1B,cAAA,mVAAC,sJAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,WAAU;kDAC1C,cAAA,mVAAC,8SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAG5B,mVAAC,gKAAA,CAAA,sBAAmB;oCAAC,OAAM;;sDACzB,mVAAC,gKAAA,CAAA,mBAAgB;4CAAC,SAAS,IAAM,SAAS;;8DACxC,mVAAC,oRAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;sDAGlC,mVAAC,+KAAA,CAAA,iBAAc;4CACb,MAAK;4CACL,YAAY;4CACZ,uBACE,mVAAC,gKAAA,CAAA,mBAAgB;gDAAC,UAAU,CAAC,IAAM,EAAE,cAAc;;kEACjD,mVAAC,+RAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAIrC,WAAW,IAAM;;;;;;sDAEnB,mVAAC,gKAAA,CAAA,mBAAgB;4CAAC,SAAS;4CAAc,WAAU;;8DACjD,mVAAC,8RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7C,mVAAC,oJAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,mVAAC;oBAAI,WAAU;;wBAEZ,WAAW,WAAW,kBACrB,mVAAC;4BAAE,WAAU;sCACV,WAAW,WAAW;;;;;;sCAK3B,mVAAC;4BAAI,WAAU;;8CACb,mVAAC,qJAAA,CAAA,QAAK;oCAAC,WAAW,iBAAiB,WAAW,QAAQ;8CACnD,WAAW,QAAQ;;;;;;8CAGtB,mVAAC,qJAAA,CAAA,QAAK;oCAAC,WAAW,eAAe,WAAW,MAAM;8CAC/C,WAAW,MAAM;;;;;;8CAGpB,mVAAC,qJAAA,CAAA,QAAK;oCAAC,SAAS,WAAW,QAAQ,GAAG,YAAY;8CAC/C,WAAW,QAAQ,GAAG,WAAW;;;;;;gCAGnC,WAAW,KAAK,kBACf,mVAAC,qJAAA,CAAA,QAAK;oCAAC,SAAQ;8CACZ,WAAW,KAAK;;;;;;;;;;;;sCAMvB,mVAAC;4BAAI,WAAU;;gCAAwB;gCAC1B,WAAW,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;AAM1C", "debugId": null}}, {"offset": {"line": 5694, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/role-permissions/PermissionListItem.tsx"], "sourcesContent": ["'use client'\n\nimport { Permission } from '@/stores/useRolePermissionsStore'\nimport { Card, CardContent } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Button } from '@/components/ui/button'\nimport { PermissionForm } from './PermissionForm'\nimport { \n  Key, \n  Edit, \n  Trash2, \n  Eye,\n  MoreVertical\n} from 'lucide-react'\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from '@/components/ui/dropdown-menu'\nimport { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'\n\ninterface PermissionListItemProps {\n  permission: Permission\n  onSelect: (permission: Permission) => void\n}\n\nexport function PermissionListItem({ permission, onSelect }: PermissionListItemProps) {\n  const { deletePermission, fetchPermissions } = useRolePermissionsStore()\n\n  const handleDelete = async () => {\n    if (window.confirm(`Are you sure you want to delete the permission \"${permission.name}\"?`)) {\n      const success = await deletePermission(permission.id)\n      if (success) {\n        fetchPermissions()\n      }\n    }\n  }\n\n  const getResourceColor = (resource: string) => {\n    const colors: Record<string, string> = {\n      'users': 'bg-blue-100 text-blue-800',\n      'roles': 'bg-purple-100 text-purple-800',\n      'permissions': 'bg-green-100 text-green-800',\n      'institutes': 'bg-orange-100 text-orange-800',\n      'courses': 'bg-yellow-100 text-yellow-800',\n      'settings': 'bg-gray-100 text-gray-800',\n      'analytics': 'bg-pink-100 text-pink-800',\n      'billing': 'bg-red-100 text-red-800',\n    }\n    return colors[resource.toLowerCase()] || 'bg-gray-100 text-gray-800'\n  }\n\n  const getActionColor = (action: string) => {\n    const colors: Record<string, string> = {\n      'create': 'bg-green-100 text-green-800',\n      'read': 'bg-blue-100 text-blue-800',\n      'update': 'bg-yellow-100 text-yellow-800',\n      'delete': 'bg-red-100 text-red-800',\n      'manage': 'bg-purple-100 text-purple-800',\n      'view': 'bg-gray-100 text-gray-800',\n    }\n    return colors[action.toLowerCase()] || 'bg-gray-100 text-gray-800'\n  }\n\n  return (\n    <Card className=\"hover:shadow-sm transition-shadow\">\n      <CardContent className=\"p-4\">\n        <div className=\"flex items-center justify-between\">\n          {/* Left side - Permission info */}\n          <div className=\"flex items-center gap-4 flex-1\">\n            <div className=\"p-2 bg-green-100 rounded-lg\">\n              <Key className=\"h-4 w-4 text-green-600\" />\n            </div>\n            \n            <div className=\"flex-1 min-w-0\">\n              <div className=\"flex items-center gap-3 mb-1\">\n                <h3 className=\"font-semibold text-gray-900 truncate\">\n                  {permission.name}\n                </h3>\n                <Badge className={getResourceColor(permission.resource)}>\n                  {permission.resource}\n                </Badge>\n                <Badge className={getActionColor(permission.action)}>\n                  {permission.action}\n                </Badge>\n                <Badge variant={permission.isActive ? \"default\" : \"secondary\"}>\n                  {permission.isActive ? \"Active\" : \"Inactive\"}\n                </Badge>\n              </div>\n              \n              <div className=\"flex items-center gap-4 text-sm text-gray-600\">\n                <span>Code: {permission.code}</span>\n                <span>Priority: {permission.priority}</span>\n                {permission.scope && <span>Scope: {permission.scope}</span>}\n              </div>\n              \n              {permission.description && (\n                <p className=\"text-sm text-gray-600 mt-1 line-clamp-1\">\n                  {permission.description}\n                </p>\n              )}\n            </div>\n          </div>\n          \n          {/* Right side - Actions */}\n          <div className=\"flex items-center gap-2\">\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={() => onSelect(permission)}\n              className=\"gap-2\"\n            >\n              <Eye className=\"h-4 w-4\" />\n              View\n            </Button>\n            \n            <DropdownMenu>\n              <DropdownMenuTrigger asChild>\n                <Button variant=\"ghost\" size=\"sm\" className=\"h-8 w-8 p-0\">\n                  <MoreVertical className=\"h-4 w-4\" />\n                </Button>\n              </DropdownMenuTrigger>\n              <DropdownMenuContent align=\"end\">\n                <PermissionForm \n                  mode=\"edit\" \n                  permission={permission}\n                  trigger={\n                    <DropdownMenuItem onSelect={(e) => e.preventDefault()}>\n                      <Edit className=\"h-4 w-4 mr-2\" />\n                      Edit Permission\n                    </DropdownMenuItem>\n                  }\n                  onSuccess={() => fetchPermissions()}\n                />\n                <DropdownMenuItem onClick={handleDelete} className=\"text-red-600\">\n                  <Trash2 className=\"h-4 w-4 mr-2\" />\n                  Delete Permission\n                </DropdownMenuItem>\n              </DropdownMenuContent>\n            </DropdownMenu>\n          </div>\n        </div>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAMA;AApBA;;;;;;;;;AA2BO,SAAS,mBAAmB,EAAE,UAAU,EAAE,QAAQ,EAA2B;IAClF,MAAM,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,GAAG,CAAA,GAAA,4JAAA,CAAA,0BAAuB,AAAD;IAErE,MAAM,eAAe;QACnB,IAAI,OAAO,OAAO,CAAC,CAAC,gDAAgD,EAAE,WAAW,IAAI,CAAC,EAAE,CAAC,GAAG;YAC1F,MAAM,UAAU,MAAM,iBAAiB,WAAW,EAAE;YACpD,IAAI,SAAS;gBACX;YACF;QACF;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAiC;YACrC,SAAS;YACT,SAAS;YACT,eAAe;YACf,cAAc;YACd,WAAW;YACX,YAAY;YACZ,aAAa;YACb,WAAW;QACb;QACA,OAAO,MAAM,CAAC,SAAS,WAAW,GAAG,IAAI;IAC3C;IAEA,MAAM,iBAAiB,CAAC;QACtB,MAAM,SAAiC;YACrC,UAAU;YACV,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,QAAQ;QACV;QACA,OAAO,MAAM,CAAC,OAAO,WAAW,GAAG,IAAI;IACzC;IAEA,qBACE,mVAAC,oJAAA,CAAA,OAAI;QAAC,WAAU;kBACd,cAAA,mVAAC,oJAAA,CAAA,cAAW;YAAC,WAAU;sBACrB,cAAA,mVAAC;gBAAI,WAAU;;kCAEb,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;0CACb,cAAA,mVAAC,oRAAA,CAAA,MAAG;oCAAC,WAAU;;;;;;;;;;;0CAGjB,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;;0DACb,mVAAC;gDAAG,WAAU;0DACX,WAAW,IAAI;;;;;;0DAElB,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,iBAAiB,WAAW,QAAQ;0DACnD,WAAW,QAAQ;;;;;;0DAEtB,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,eAAe,WAAW,MAAM;0DAC/C,WAAW,MAAM;;;;;;0DAEpB,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAS,WAAW,QAAQ,GAAG,YAAY;0DAC/C,WAAW,QAAQ,GAAG,WAAW;;;;;;;;;;;;kDAItC,mVAAC;wCAAI,WAAU;;0DACb,mVAAC;;oDAAK;oDAAO,WAAW,IAAI;;;;;;;0DAC5B,mVAAC;;oDAAK;oDAAW,WAAW,QAAQ;;;;;;;4CACnC,WAAW,KAAK,kBAAI,mVAAC;;oDAAK;oDAAQ,WAAW,KAAK;;;;;;;;;;;;;oCAGpD,WAAW,WAAW,kBACrB,mVAAC;wCAAE,WAAU;kDACV,WAAW,WAAW;;;;;;;;;;;;;;;;;;kCAO/B,mVAAC;wBAAI,WAAU;;0CACb,mVAAC,sJAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS,IAAM,SAAS;gCACxB,WAAU;;kDAEV,mVAAC,oRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAI7B,mVAAC,gKAAA,CAAA,eAAY;;kDACX,mVAAC,gKAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,mVAAC,sJAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;4CAAK,WAAU;sDAC1C,cAAA,mVAAC,8SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG5B,mVAAC,gKAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,mVAAC,+KAAA,CAAA,iBAAc;gDACb,MAAK;gDACL,YAAY;gDACZ,uBACE,mVAAC,gKAAA,CAAA,mBAAgB;oDAAC,UAAU,CAAC,IAAM,EAAE,cAAc;;sEACjD,mVAAC,+RAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;gDAIrC,WAAW,IAAM;;;;;;0DAEnB,mVAAC,gKAAA,CAAA,mBAAgB;gDAAC,SAAS;gDAAc,WAAU;;kEACjD,mVAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUrD", "debugId": null}}, {"offset": {"line": 6015, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/role-permissions/PermissionsList.tsx"], "sourcesContent": ["'use client'\n\nimport { useEffect } from 'react'\nimport { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'\nimport { PermissionCard } from './PermissionCard'\nimport { PermissionListItem } from './PermissionListItem'\nimport { RolePermissionsPagination } from './RolePermissionsPagination'\nimport { EmptyState } from '@/components/ui/empty-state'\nimport { Alert, AlertDescription } from '@/components/ui/alert'\nimport { AlertTriangle, Key } from 'lucide-react'\n\nexport function PermissionsList() {\n  const {\n    permissions,\n    viewMode,\n    permissionsPagination,\n    isLoading,\n    fetchPermissions,\n    setSelectedPermission\n  } = useRolePermissionsStore()\n\n  useEffect(() => {\n    // Load permissions when component mounts\n    if (permissions.length === 0) {\n      fetchPermissions()\n    }\n  }, [fetchPermissions, permissions.length])\n\n  const handlePageChange = (page: number) => {\n    fetchPermissions(page)\n  }\n\n  const handlePermissionSelect = (permission: any) => {\n    setSelectedPermission(permission)\n  }\n\n  if (isLoading && permissions.length === 0) {\n    return (\n      <div className=\"space-y-4\">\n        {Array.from({ length: 6 }).map((_, index) => (\n          <div key={index} className=\"animate-pulse\">\n            <div className=\"h-20 bg-gray-200 rounded-lg\"></div>\n          </div>\n        ))}\n      </div>\n    )\n  }\n\n  if (permissions.length === 0) {\n    return (\n      <EmptyState\n        icon={Key}\n        title=\"No permissions found\"\n        description=\"No permissions found. Try adjusting your search criteria or add new permissions.\"\n      />\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Permissions Grid/List */}\n      {viewMode === 'card' ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4\">\n          {permissions.map((permission) => (\n            <PermissionCard\n              key={permission.id}\n              permission={permission}\n              onSelect={handlePermissionSelect}\n            />\n          ))}\n        </div>\n      ) : (\n        <div className=\"space-y-2\">\n          {permissions.map((permission) => (\n            <PermissionListItem\n              key={permission.id}\n              permission={permission}\n              onSelect={handlePermissionSelect}\n            />\n          ))}\n        </div>\n      )}\n\n      {/* Pagination */}\n      <RolePermissionsPagination\n        pagination={permissionsPagination}\n        onPageChange={handlePageChange}\n      />\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAEA;AATA;;;;;;;;;AAWO,SAAS;IACd,MAAM,EACJ,WAAW,EACX,QAAQ,EACR,qBAAqB,EACrB,SAAS,EACT,gBAAgB,EAChB,qBAAqB,EACtB,GAAG,CAAA,GAAA,4JAAA,CAAA,0BAAuB,AAAD;IAE1B,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,yCAAyC;QACzC,IAAI,YAAY,MAAM,KAAK,GAAG;YAC5B;QACF;IACF,GAAG;QAAC;QAAkB,YAAY,MAAM;KAAC;IAEzC,MAAM,mBAAmB,CAAC;QACxB,iBAAiB;IACnB;IAEA,MAAM,yBAAyB,CAAC;QAC9B,sBAAsB;IACxB;IAEA,IAAI,aAAa,YAAY,MAAM,KAAK,GAAG;QACzC,qBACE,mVAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,mVAAC;oBAAgB,WAAU;8BACzB,cAAA,mVAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,YAAY,MAAM,KAAK,GAAG;QAC5B,qBACE,mVAAC,8JAAA,CAAA,aAAU;YACT,MAAM,oRAAA,CAAA,MAAG;YACT,OAAM;YACN,aAAY;;;;;;IAGlB;IAEA,qBACE,mVAAC;QAAI,WAAU;;YAEZ,aAAa,uBACZ,mVAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,mVAAC,+KAAA,CAAA,iBAAc;wBAEb,YAAY;wBACZ,UAAU;uBAFL,WAAW,EAAE;;;;;;;;;qCAOxB,mVAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,mVAAC,mLAAA,CAAA,qBAAkB;wBAEjB,YAAY;wBACZ,UAAU;uBAFL,WAAW,EAAE;;;;;;;;;;0BAS1B,mVAAC,0LAAA,CAAA,4BAAyB;gBACxB,YAAY;gBACZ,cAAc;;;;;;;;;;;;AAItB", "debugId": null}}, {"offset": {"line": 6141, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst alertVariants = cva(\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-background text-foreground\",\n        destructive:\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nconst Alert = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\n>(({ className, variant, ...props }, ref) => (\n  <div\n    ref={ref}\n    role=\"alert\"\n    className={cn(alertVariants({ variant }), className)}\n    {...props}\n  />\n))\nAlert.displayName = \"Alert\"\n\nconst AlertTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h5\n    ref={ref}\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\n    {...props}\n  />\n))\nAlertTitle.displayName = \"AlertTitle\"\n\nconst AlertDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\n    {...props}\n  />\n))\nAlertDescription.displayName = \"AlertDescription\"\n\nexport { Alert, AlertTitle, AlertDescription }\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,mVAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 6205, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface TabsProps {\n  defaultValue?: string\n  value?: string\n  onValueChange?: (value: string) => void\n  children: React.ReactNode\n  className?: string\n}\n\ninterface TabsContextType {\n  value: string\n  onValueChange: (value: string) => void\n}\n\nconst TabsContext = React.createContext<TabsContextType | undefined>(undefined)\n\nconst Tabs = React.forwardRef<HTMLDivElement, TabsProps>(\n  ({ defaultValue, value, onValueChange, children, className, ...props }, ref) => {\n    const [internalValue, setInternalValue] = React.useState(defaultValue || \"\")\n\n    const currentValue = value !== undefined ? value : internalValue\n    const handleValueChange = onValueChange || setInternalValue\n\n    return (\n      <TabsContext.Provider value={{ value: currentValue, onValueChange: handleValueChange }}>\n        <div ref={ref} className={cn(\"w-full\", className)} {...props}>\n          {children}\n        </div>\n      </TabsContext.Provider>\n    )\n  }\n)\nTabs.displayName = \"Tabs\"\n\nconst TabsList = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 text-gray-500\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = \"TabsList\"\n\nconst TabsTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & { value: string }\n>(({ className, value, children, ...props }, ref) => {\n  const context = React.useContext(TabsContext)\n  if (!context) throw new Error(\"TabsTrigger must be used within Tabs\")\n\n  const isActive = context.value === value\n\n  return (\n    <button\n      ref={ref}\n      className={cn(\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n        isActive\n          ? \"bg-white text-gray-900 shadow-sm\"\n          : \"text-gray-600 hover:text-gray-900\",\n        className\n      )}\n      onClick={() => context.onValueChange(value)}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n})\nTabsTrigger.displayName = \"TabsTrigger\"\n\nconst TabsContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & { value: string }\n>(({ className, value, children, ...props }, ref) => {\n  const context = React.useContext(TabsContext)\n  if (!context) throw new Error(\"TabsContent must be used within Tabs\")\n\n  if (context.value !== value) return null\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n})\nTabsContent.displayName = \"TabsContent\"\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,0SAAA,CAAA,gBAAmB,AAAD,EAA+B;AAErE,MAAM,qBAAO,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAc,AAAD,EAAE,gBAAgB;IAEzE,MAAM,eAAe,UAAU,YAAY,QAAQ;IACnD,MAAM,oBAAoB,iBAAiB;IAE3C,qBACE,mVAAC,YAAY,QAAQ;QAAC,OAAO;YAAE,OAAO;YAAc,eAAe;QAAkB;kBACnF,cAAA,mVAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,UAAU;YAAa,GAAG,KAAK;sBACzD;;;;;;;;;;;AAIT;AAEF,KAAK,WAAW,GAAG;AAEnB,MAAM,yBAAW,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,yFACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,4BAAc,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,WAAW,QAAQ,KAAK,KAAK;IAEnC,qBACE,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,gRACA,WACI,qCACA,qCACJ;QAEF,SAAS,IAAM,QAAQ,aAAa,CAAC;QACpC,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,4BAAc,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,IAAI,QAAQ,KAAK,KAAK,OAAO,OAAO;IAEpC,qBACE,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,gHACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 6295, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/app/super-admin/role-permissions/page.tsx"], "sourcesContent": ["'use client'\r\n\r\nimport { useState, useEffect } from 'react'\r\nimport { useRouter } from 'next/navigation'\r\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\r\nimport { useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'\r\nimport { usePermissions } from '@/contexts/PermissionContext'\r\nimport { RolePermissionsFilters } from '@/components/role-permissions/RolePermissionsFilters'\r\nimport { RolesList } from '@/components/role-permissions/RolesList'\r\nimport { PermissionsList } from '@/components/role-permissions/PermissionsList'\r\nimport { RoleForm } from '@/components/role-permissions/RoleForm'\r\nimport { PermissionForm } from '@/components/role-permissions/PermissionForm'\r\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\r\nimport { Button } from '@/components/ui/button'\r\nimport { Badge } from '@/components/ui/badge'\r\nimport { Alert, AlertDescription } from '@/components/ui/alert'\r\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'\r\nimport {\r\n  Shield,\r\n  Key,\r\n  Users,\r\n  Plus,\r\n  Settings,\r\n  AlertTriangle,\r\n  Loader2,\r\n  Download,\r\n  Upload\r\n} from 'lucide-react'\r\n\r\nexport default function RolePermissionsPage() {\r\n  const router = useRouter()\r\n  const [authChecked, setAuthChecked] = useState(false)\r\n  const [activeTab, setActiveTab] = useState<'roles' | 'permissions'>('roles')\r\n\r\n  const { user, isAuthenticated, isLoading, initialize } = useAuthStore()\r\n  const { userPermissions, isLoading: permissionsLoading } = usePermissions()\r\n  const {\r\n    roles,\r\n    permissions,\r\n    fetchRolesWithPermissions,\r\n    fetchRoles,\r\n    fetchPermissions,\r\n    error,\r\n    clearError\r\n  } = useRolePermissionsStore()\r\n\r\n  // Initialize auth on mount with delay to ensure proper loading\r\n  useEffect(() => {\r\n    console.log('🔄 Role-permissions page mounted, initializing auth...')\r\n\r\n    // Small delay to ensure auth store is ready\r\n    const timer = setTimeout(() => {\r\n      initialize()\r\n    }, 100)\r\n\r\n    return () => clearTimeout(timer)\r\n  }, [initialize])\r\n\r\n  // Check authentication and redirect if needed\r\n  useEffect(() => {\r\n    console.log('🔍 Role-permissions page auth check:', {\r\n      isLoading,\r\n      isAuthenticated,\r\n      hasUser: !!user,\r\n      userEmail: user?.email,\r\n      userLegacyRole: user?.legacyRole,\r\n      userRole: user?.role,\r\n      authChecked\r\n    })\r\n\r\n    if (!isLoading) {\r\n      setAuthChecked(true)\r\n\r\n      if (!isAuthenticated) {\r\n        console.log('❌ Not authenticated, redirecting to login')\r\n        router.push('/auth/admin/login')\r\n        return\r\n      }\r\n\r\n      // Check if user is super admin\r\n      if (!user) {\r\n        console.log('❌ No user object, redirecting to login')\r\n        router.push('/auth/admin/login')\r\n        return\r\n      }\r\n\r\n      if (user.legacyRole !== 'super_admin' && user.legacyRole !== 'platform_staff') {\r\n        console.log(`❌ User role ${user.legacyRole} not authorized, redirecting to login`)\r\n        router.push('/auth/admin/login')\r\n        return\r\n      }\r\n\r\n      console.log('✅ Authentication check passed for role-permissions page')\r\n    }\r\n  }, [user, isAuthenticated, isLoading, router])\r\n\r\n  // Load initial data based on active tab\r\n  useEffect(() => {\r\n    if (authChecked && isAuthenticated && user) {\r\n      switch (activeTab) {\r\n        case 'roles':\r\n          fetchRoles()\r\n          break\r\n        case 'permissions':\r\n          fetchPermissions()\r\n          break\r\n      }\r\n      // Also load roles with permissions for the assignment section\r\n      fetchRolesWithPermissions()\r\n    }\r\n  }, [authChecked, isAuthenticated, user, activeTab, fetchRoles, fetchPermissions, fetchRolesWithPermissions])\r\n\r\n  const handleAssignPermissions = (role: any) => {\r\n    // This will be handled by the role-permission assignment interface\r\n    console.log('Assign permissions to role:', role)\r\n  }\r\n\r\n\r\n\r\n  // Check access using permission context (which is working correctly)\r\n  const hasAccess = userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff'\r\n\r\n  console.log('🔍 Role-permissions access check:', {\r\n    authStoreAuth: isAuthenticated,\r\n    authStoreUser: !!user,\r\n    authStoreRole: user?.legacyRole,\r\n    permissionRole: userPermissions.role,\r\n    permissionCount: userPermissions.permissions.length,\r\n    hasAccess,\r\n    permissionsLoading\r\n  })\r\n\r\n  // Show loading if either auth or permissions are loading\r\n  if (isLoading || permissionsLoading) {\r\n    console.log('🔄 Still loading...', { isLoading, permissionsLoading })\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        <div className=\"flex items-center gap-2\">\r\n          <Loader2 className=\"h-6 w-6 animate-spin\" />\r\n          <span>Loading authentication...</span>\r\n        </div>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  // Use permission context for access control (since it's working correctly)\r\n  if (!hasAccess) {\r\n    console.log('❌ ACCESS DENIED - Role-permissions page:', {\r\n      permissionRole: userPermissions.role,\r\n      permissionCount: userPermissions.permissions.length,\r\n      authStoreAuth: isAuthenticated,\r\n      authStoreUser: !!user,\r\n      authStoreRole: user?.legacyRole\r\n    })\r\n\r\n    return (\r\n      <div className=\"flex items-center justify-center min-h-screen\">\r\n        <Alert className=\"max-w-md\">\r\n          <AlertTriangle className=\"h-4 w-4\" />\r\n          <AlertDescription>\r\n            Access Denied - You don't have permission to access this area.\r\n            <br />\r\n            <small className=\"text-xs mt-2 block\">\r\n              Permission Role: {userPermissions.role}, Auth Role: {user?.legacyRole || 'none'}\r\n            </small>\r\n          </AlertDescription>\r\n        </Alert>\r\n      </div>\r\n    )\r\n  }\r\n\r\n  return (\r\n    <div className=\"container mx-auto py-6 space-y-6\">\r\n      {/* Header */}\r\n      <div className=\"flex items-center justify-between\">\r\n        <div>\r\n          <h1 className=\"text-3xl font-bold text-gray-900\">Role & Permissions Management</h1>\r\n          <p className=\"text-gray-600 mt-1\">\r\n            Manage system roles and permissions for your platform\r\n          </p>\r\n        </div>\r\n        <div className=\"flex items-center gap-2\">\r\n          <Button variant=\"outline\" size=\"sm\" className=\"gap-2\">\r\n            <Download className=\"h-4 w-4\" />\r\n            Export\r\n          </Button>\r\n          <Button variant=\"outline\" size=\"sm\" className=\"gap-2\">\r\n            <Upload className=\"h-4 w-4\" />\r\n            Import\r\n          </Button>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Error Alert */}\r\n      {error && (\r\n        <Alert variant=\"destructive\">\r\n          <AlertTriangle className=\"h-4 w-4\" />\r\n          <AlertDescription className=\"flex items-center justify-between\">\r\n            {error}\r\n            <Button variant=\"ghost\" size=\"sm\" onClick={clearError}>\r\n              Dismiss\r\n            </Button>\r\n          </AlertDescription>\r\n        </Alert>\r\n      )}\r\n\r\n      {/* Main Content */}\r\n      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'roles' | 'permissions')} className=\"space-y-6\">\r\n        <div className=\"flex items-center justify-between\">\r\n          <TabsList className=\"grid w-full max-w-md grid-cols-2\">\r\n            <TabsTrigger value=\"roles\" className=\"flex items-center gap-2\">\r\n              <Shield className=\"h-4 w-4\" />\r\n              Roles\r\n            </TabsTrigger>\r\n            <TabsTrigger value=\"permissions\" className=\"flex items-center gap-2\">\r\n              <Key className=\"h-4 w-4\" />\r\n              Permissions\r\n            </TabsTrigger>\r\n          </TabsList>\r\n\r\n          {/* Add Button - Context-aware */}\r\n          <div className=\"flex items-center gap-2\">\r\n            {activeTab === 'roles' ? (\r\n              <RoleForm\r\n                mode=\"create\"\r\n                trigger={\r\n                  <Button className=\"gap-2\">\r\n                    <Plus className=\"h-4 w-4\" />\r\n                    Add Role\r\n                  </Button>\r\n                }\r\n                onSuccess={() => {\r\n                  fetchRoles()\r\n                  fetchRolesWithPermissions()\r\n                }}\r\n              />\r\n            ) : (\r\n              <PermissionForm\r\n                mode=\"create\"\r\n                trigger={\r\n                  <Button className=\"gap-2\">\r\n                    <Plus className=\"h-4 w-4\" />\r\n                    Add Permission\r\n                  </Button>\r\n                }\r\n                onSuccess={() => {\r\n                  fetchPermissions()\r\n                  fetchRolesWithPermissions()\r\n                }}\r\n              />\r\n            )}\r\n          </div>\r\n        </div>\r\n\r\n        {/* Filters */}\r\n        <RolePermissionsFilters activeTab={activeTab} />\r\n\r\n        {/* Roles Tab */}\r\n        <TabsContent value=\"roles\" className=\"space-y-6\">\r\n          <RolesList />\r\n        </TabsContent>\r\n\r\n        {/* Permissions Tab */}\r\n        <TabsContent value=\"permissions\" className=\"space-y-6\">\r\n          <PermissionsList />\r\n        </TabsContent>\r\n      </Tabs>\r\n\r\n      {/* Role-Permission Assignment Section */}\r\n      <Card>\r\n        <CardHeader>\r\n          <CardTitle className=\"flex items-center gap-2\">\r\n            <Users className=\"h-5 w-5\" />\r\n            Role Permission Assignment\r\n          </CardTitle>\r\n          <CardDescription>\r\n            Assign and manage permissions for each role. Control what actions each role can perform.\r\n          </CardDescription>\r\n        </CardHeader>\r\n        <CardContent>\r\n          <div className=\"space-y-6\">\r\n            {roles.map((role) => (\r\n              <div key={role.id} className=\"border rounded-lg p-4\">\r\n                <div className=\"flex items-center justify-between mb-4\">\r\n                  <div className=\"flex items-center gap-3\">\r\n                    <div className=\"p-2 bg-blue-100 rounded-lg\">\r\n                      <Shield className=\"h-4 w-4 text-blue-600\" />\r\n                    </div>\r\n                    <div>\r\n                      <h3 className=\"font-semibold text-gray-900\">{role.name}</h3>\r\n                      <p className=\"text-sm text-gray-600\">{role.description}</p>\r\n                    </div>\r\n                  </div>\r\n                  <div className=\"flex items-center gap-2\">\r\n                    <Badge variant={role.isActive ? \"default\" : \"secondary\"}>\r\n                      {role.isActive ? \"Active\" : \"Inactive\"}\r\n                    </Badge>\r\n                    <Button\r\n                      variant=\"outline\"\r\n                      size=\"sm\"\r\n                      onClick={() => handleAssignPermissions(role)}\r\n                      className=\"gap-2\"\r\n                    >\r\n                      <Settings className=\"h-4 w-4\" />\r\n                      Manage Permissions\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n\r\n                {/* Show current permissions for this role */}\r\n                <div className=\"flex flex-wrap gap-2\">\r\n                  {role.permissions && role.permissions.length > 0 ? (\r\n                    role.permissions.map((permission) => (\r\n                      <Badge key={permission.id} variant=\"outline\" className=\"text-xs\">\r\n                        {permission.name}\r\n                      </Badge>\r\n                    ))\r\n                  ) : (\r\n                    <span className=\"text-sm text-gray-500 italic\">No permissions assigned</span>\r\n                  )}\r\n                </div>\r\n              </div>\r\n            ))}\r\n\r\n            {roles.length === 0 && (\r\n              <div className=\"text-center py-8\">\r\n                <Shield className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\r\n                <p className=\"text-gray-600\">No roles found. Create a role first to assign permissions.</p>\r\n              </div>\r\n            )}\r\n          </div>\r\n        </CardContent>\r\n      </Card>\r\n    </div>\r\n  )\r\n}"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAjBA;;;;;;;;;;;;;;;;;;AA6Be,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,uOAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAA2B;IAEpE,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;IACpE,MAAM,EAAE,eAAe,EAAE,WAAW,kBAAkB,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,iBAAc,AAAD;IACxE,MAAM,EACJ,KAAK,EACL,WAAW,EACX,yBAAyB,EACzB,UAAU,EACV,gBAAgB,EAChB,KAAK,EACL,UAAU,EACX,GAAG,CAAA,GAAA,8KAAA,CAAA,0BAAuB,AAAD;IAE1B,+DAA+D;IAC/D,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC;QAEZ,4CAA4C;QAC5C,MAAM,QAAQ,WAAW;YACvB;QACF,GAAG;QAEH,OAAO,IAAM,aAAa;IAC5B,GAAG;QAAC;KAAW;IAEf,8CAA8C;IAC9C,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ,GAAG,CAAC,wCAAwC;YAClD;YACA;YACA,SAAS,CAAC,CAAC;YACX,WAAW,MAAM;YACjB,gBAAgB,MAAM;YACtB,UAAU,MAAM;YAChB;QACF;QAEA,IAAI,CAAC,WAAW;YACd,eAAe;YAEf,IAAI,CAAC,iBAAiB;gBACpB,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,+BAA+B;YAC/B,IAAI,CAAC,MAAM;gBACT,QAAQ,GAAG,CAAC;gBACZ,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,IAAI,KAAK,UAAU,KAAK,iBAAiB,KAAK,UAAU,KAAK,kBAAkB;gBAC7E,QAAQ,GAAG,CAAC,CAAC,YAAY,EAAE,KAAK,UAAU,CAAC,qCAAqC,CAAC;gBACjF,OAAO,IAAI,CAAC;gBACZ;YACF;YAEA,QAAQ,GAAG,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAiB;QAAW;KAAO;IAE7C,wCAAwC;IACxC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,mBAAmB,MAAM;YAC1C,OAAQ;gBACN,KAAK;oBACH;oBACA;gBACF,KAAK;oBACH;oBACA;YACJ;YACA,8DAA8D;YAC9D;QACF;IACF,GAAG;QAAC;QAAa;QAAiB;QAAM;QAAW;QAAY;QAAkB;KAA0B;IAE3G,MAAM,0BAA0B,CAAC;QAC/B,mEAAmE;QACnE,QAAQ,GAAG,CAAC,+BAA+B;IAC7C;IAIA,qEAAqE;IACrE,MAAM,YAAY,gBAAgB,IAAI,KAAK,iBAAiB,gBAAgB,IAAI,KAAK;IAErF,QAAQ,GAAG,CAAC,qCAAqC;QAC/C,eAAe;QACf,eAAe,CAAC,CAAC;QACjB,eAAe,MAAM;QACrB,gBAAgB,gBAAgB,IAAI;QACpC,iBAAiB,gBAAgB,WAAW,CAAC,MAAM;QACnD;QACA;IACF;IAEA,yDAAyD;IACzD,IAAI,aAAa,oBAAoB;QACnC,QAAQ,GAAG,CAAC,uBAAuB;YAAE;YAAW;QAAmB;QACnE,qBACE,mVAAC;YAAI,WAAU;sBACb,cAAA,mVAAC;gBAAI,WAAU;;kCACb,mVAAC,qSAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,mVAAC;kCAAK;;;;;;;;;;;;;;;;;IAId;IAEA,2EAA2E;IAC3E,IAAI,CAAC,WAAW;QACd,QAAQ,GAAG,CAAC,4CAA4C;YACtD,gBAAgB,gBAAgB,IAAI;YACpC,iBAAiB,gBAAgB,WAAW,CAAC,MAAM;YACnD,eAAe;YACf,eAAe,CAAC,CAAC;YACjB,eAAe,MAAM;QACvB;QAEA,qBACE,mVAAC;YAAI,WAAU;sBACb,cAAA,mVAAC,qJAAA,CAAA,QAAK;gBAAC,WAAU;;kCACf,mVAAC,4SAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,mVAAC,qJAAA,CAAA,mBAAgB;;4BAAC;0CAEhB,mVAAC;;;;;0CACD,mVAAC;gCAAM,WAAU;;oCAAqB;oCAClB,gBAAgB,IAAI;oCAAC;oCAAc,MAAM,cAAc;;;;;;;;;;;;;;;;;;;;;;;;IAMrF;IAEA,qBACE,mVAAC;QAAI,WAAU;;0BAEb,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;;0CACC,mVAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,mVAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,mVAAC;wBAAI,WAAU;;0CACb,mVAAC,sJAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;;kDAC5C,mVAAC,8RAAA,CAAA,WAAQ;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGlC,mVAAC,sJAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,MAAK;gCAAK,WAAU;;kDAC5C,mVAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;;;;;;;YAOnC,uBACC,mVAAC,qJAAA,CAAA,QAAK;gBAAC,SAAQ;;kCACb,mVAAC,4SAAA,CAAA,gBAAa;wBAAC,WAAU;;;;;;kCACzB,mVAAC,qJAAA,CAAA,mBAAgB;wBAAC,WAAU;;4BACzB;0CACD,mVAAC,sJAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,SAAS;0CAAY;;;;;;;;;;;;;;;;;;0BAQ7D,mVAAC,oJAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe,CAAC,QAAU,aAAa;gBAAmC,WAAU;;kCAC1G,mVAAC;wBAAI,WAAU;;0CACb,mVAAC,oJAAA,CAAA,WAAQ;gCAAC,WAAU;;kDAClB,mVAAC,oJAAA,CAAA,cAAW;wCAAC,OAAM;wCAAQ,WAAU;;0DACnC,mVAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAY;;;;;;;kDAGhC,mVAAC,oJAAA,CAAA,cAAW;wCAAC,OAAM;wCAAc,WAAU;;0DACzC,mVAAC,oRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CAAY;;;;;;;;;;;;;0CAM/B,mVAAC;gCAAI,WAAU;0CACZ,cAAc,wBACb,mVAAC,yKAAA,CAAA,WAAQ;oCACP,MAAK;oCACL,uBACE,mVAAC,sJAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,mVAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;oCAIhC,WAAW;wCACT;wCACA;oCACF;;;;;yDAGF,mVAAC,+KAAA,CAAA,iBAAc;oCACb,MAAK;oCACL,uBACE,mVAAC,sJAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,mVAAC,sRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAY;;;;;;;oCAIhC,WAAW;wCACT;wCACA;oCACF;;;;;;;;;;;;;;;;;kCAOR,mVAAC,uLAAA,CAAA,yBAAsB;wBAAC,WAAW;;;;;;kCAGnC,mVAAC,oJAAA,CAAA,cAAW;wBAAC,OAAM;wBAAQ,WAAU;kCACnC,cAAA,mVAAC,0KAAA,CAAA,YAAS;;;;;;;;;;kCAIZ,mVAAC,oJAAA,CAAA,cAAW;wBAAC,OAAM;wBAAc,WAAU;kCACzC,cAAA,mVAAC,gLAAA,CAAA,kBAAe;;;;;;;;;;;;;;;;0BAKpB,mVAAC,oJAAA,CAAA,OAAI;;kCACH,mVAAC,oJAAA,CAAA,aAAU;;0CACT,mVAAC,oJAAA,CAAA,YAAS;gCAAC,WAAU;;kDACnB,mVAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG/B,mVAAC,oJAAA,CAAA,kBAAe;0CAAC;;;;;;;;;;;;kCAInB,mVAAC,oJAAA,CAAA,cAAW;kCACV,cAAA,mVAAC;4BAAI,WAAU;;gCACZ,MAAM,GAAG,CAAC,CAAC,qBACV,mVAAC;wCAAkB,WAAU;;0DAC3B,mVAAC;gDAAI,WAAU;;kEACb,mVAAC;wDAAI,WAAU;;0EACb,mVAAC;gEAAI,WAAU;0EACb,cAAA,mVAAC,0RAAA,CAAA,SAAM;oEAAC,WAAU;;;;;;;;;;;0EAEpB,mVAAC;;kFACC,mVAAC;wEAAG,WAAU;kFAA+B,KAAK,IAAI;;;;;;kFACtD,mVAAC;wEAAE,WAAU;kFAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;kEAG1D,mVAAC;wDAAI,WAAU;;0EACb,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAS,KAAK,QAAQ,GAAG,YAAY;0EACzC,KAAK,QAAQ,GAAG,WAAW;;;;;;0EAE9B,mVAAC,sJAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,MAAK;gEACL,SAAS,IAAM,wBAAwB;gEACvC,WAAU;;kFAEV,mVAAC,8RAAA,CAAA,WAAQ;wEAAC,WAAU;;;;;;oEAAY;;;;;;;;;;;;;;;;;;;0DAOtC,mVAAC;gDAAI,WAAU;0DACZ,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,IAC7C,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,2BACpB,mVAAC,qJAAA,CAAA,QAAK;wDAAqB,SAAQ;wDAAU,WAAU;kEACpD,WAAW,IAAI;uDADN,WAAW,EAAE;;;;8EAK3B,mVAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;;uCApC3C,KAAK,EAAE;;;;;gCA0ClB,MAAM,MAAM,KAAK,mBAChB,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,mVAAC;4CAAE,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7C", "debugId": null}}]}