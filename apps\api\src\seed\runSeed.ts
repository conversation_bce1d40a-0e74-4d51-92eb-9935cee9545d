import { getPayloadHMR } from '@payloadcms/next/utilities'
import configPromise from '@payload-config'
import seedRolesAndPermissions from './seedRolesPermissions'

async function runSeed() {
  console.log('🚀 Starting LMS seeding process...')
  
  try {
    // Initialize Payload
    const payload = await getPayloadHMR({ config: configPromise })
    console.log('✅ Payload initialized successfully')

    // Run roles and permissions seeding
    await seedRolesAndPermissions(payload)

    console.log('🎉 All seeding completed successfully!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Seeding failed:', error)
    process.exit(1)
  }
}

// Run the seeding if this file is executed directly
if (require.main === module) {
  runSeed()
}

export default runSeed
