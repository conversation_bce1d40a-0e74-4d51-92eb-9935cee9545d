import type { Payload } from 'payload'

// Define all permissions for the LMS system
const PERMISSIONS = [
  // User Management
  { name: 'View Users', code: 'view_users', description: 'View user list and details', resource: 'users', action: 'read', category: 'user_management', level: 1 },
  { name: 'Create Users', code: 'create_users', description: 'Create new users', resource: 'users', action: 'create', category: 'user_management', level: 1 },
  { name: 'Update Users', code: 'update_users', description: 'Edit user information', resource: 'users', action: 'update', category: 'user_management', level: 1 },
  { name: 'Delete Users', code: 'delete_users', description: 'Delete users from system', resource: 'users', action: 'delete', category: 'user_management', level: 1 },
  { name: 'Manage Users', code: 'manage_users', description: 'Full user management access', resource: 'users', action: 'manage', category: 'user_management', level: 1 },

  // Role & Permission Management
  { name: 'View Roles', code: 'view_roles', description: 'View roles and their details', resource: 'roles', action: 'read', category: 'users', level: 1 },
  { name: 'Create Roles', code: 'create_roles', description: 'Create new roles', resource: 'roles', action: 'create', category: 'users', level: 1 },
  { name: 'Update Roles', code: 'update_roles', description: 'Edit role information', resource: 'roles', action: 'update', category: 'users', level: 1 },
  { name: 'Delete Roles', code: 'delete_roles', description: 'Delete roles from system', resource: 'roles', action: 'delete', category: 'users', level: 1 },
  { name: 'Manage Roles', code: 'manage_roles', description: 'Full role management access', resource: 'roles', action: 'manage', category: 'users', level: 1 },

  { name: 'View Permissions', code: 'view_permissions', description: 'View permissions list', resource: 'permissions', action: 'read', category: 'users', level: 1 },
  { name: 'Create Permissions', code: 'create_permissions', description: 'Create new permissions', resource: 'permissions', action: 'create', category: 'users', level: 1 },
  { name: 'Update Permissions', code: 'update_permissions', description: 'Edit permission details', resource: 'permissions', action: 'update', category: 'users', level: 1 },
  { name: 'Delete Permissions', code: 'delete_permissions', description: 'Delete permissions', resource: 'permissions', action: 'delete', category: 'users', level: 1 },
  { name: 'Manage Permissions', code: 'manage_permissions', description: 'Full permission management', resource: 'permissions', action: 'manage', category: 'users', level: 1 },

  // Institute Management
  { name: 'View Institutes', code: 'view_institutes', description: 'View institute list and details', resource: 'institutes', action: 'read' },
  { name: 'Create Institutes', code: 'create_institutes', description: 'Create new institutes', resource: 'institutes', action: 'create' },
  { name: 'Update Institutes', code: 'update_institutes', description: 'Edit institute information', resource: 'institutes', action: 'update' },
  { name: 'Delete Institutes', code: 'delete_institutes', description: 'Delete institutes', resource: 'institutes', action: 'delete' },
  { name: 'Manage Institutes', code: 'manage_institutes', description: 'Full institute management', resource: 'institutes', action: 'manage' },

  // Course Management
  { name: 'View Courses', code: 'view_courses', description: 'View course list and details', resource: 'courses', action: 'read' },
  { name: 'Create Courses', code: 'create_courses', description: 'Create new courses', resource: 'courses', action: 'create' },
  { name: 'Update Courses', code: 'update_courses', description: 'Edit course information', resource: 'courses', action: 'update' },
  { name: 'Delete Courses', code: 'delete_courses', description: 'Delete courses', resource: 'courses', action: 'delete' },
  { name: 'Manage Courses', code: 'manage_courses', description: 'Full course management', resource: 'courses', action: 'manage' },

  // Student Management
  { name: 'View Students', code: 'view_students', description: 'View student list and details', resource: 'students', action: 'read' },
  { name: 'Create Students', code: 'create_students', description: 'Create new student accounts', resource: 'students', action: 'create' },
  { name: 'Update Students', code: 'update_students', description: 'Edit student information', resource: 'students', action: 'update' },
  { name: 'Delete Students', code: 'delete_students', description: 'Delete student accounts', resource: 'students', action: 'delete' },
  { name: 'Manage Students', code: 'manage_students', description: 'Full student management', resource: 'students', action: 'manage' },

  // Branch Management
  { name: 'View Branches', code: 'view_branches', description: 'View branch list and details', resource: 'branches', action: 'read' },
  { name: 'Create Branches', code: 'create_branches', description: 'Create new branches', resource: 'branches', action: 'create' },
  { name: 'Update Branches', code: 'update_branches', description: 'Edit branch information', resource: 'branches', action: 'update' },
  { name: 'Delete Branches', code: 'delete_branches', description: 'Delete branches', resource: 'branches', action: 'delete' },
  { name: 'Manage Branches', code: 'manage_branches', description: 'Full branch management', resource: 'branches', action: 'manage' },

  // System Settings
  { name: 'View Settings', code: 'view_settings', description: 'View system settings', resource: 'settings', action: 'read' },
  { name: 'Update Settings', code: 'update_settings', description: 'Modify system settings', resource: 'settings', action: 'update' },
  { name: 'Manage Settings', code: 'manage_settings', description: 'Full settings management', resource: 'settings', action: 'manage' },

  // Analytics & Reports
  { name: 'View Analytics', code: 'view_analytics', description: 'View system analytics', resource: 'analytics', action: 'read' },
  { name: 'View Reports', code: 'view_reports', description: 'View system reports', resource: 'reports', action: 'read' },
  { name: 'Export Reports', code: 'export_reports', description: 'Export reports and data', resource: 'reports', action: 'export' },

  // Billing & Finance
  { name: 'View Billing', code: 'view_billing', description: 'View billing information', resource: 'billing', action: 'read' },
  { name: 'Manage Billing', code: 'manage_billing', description: 'Manage billing and payments', resource: 'billing', action: 'manage' },

  // Theme & Website Management
  { name: 'View Themes', code: 'view_themes', description: 'View available themes', resource: 'themes', action: 'read' },
  { name: 'Manage Themes', code: 'manage_themes', description: 'Manage themes and website appearance', resource: 'themes', action: 'manage' },
  { name: 'Manage Website', code: 'manage_website', description: 'Manage website content and settings', resource: 'website', action: 'manage' },

  // Content Management
  { name: 'View Content', code: 'view_content', description: 'View content and materials', resource: 'content', action: 'read' },
  { name: 'Create Content', code: 'create_content', description: 'Create new content', resource: 'content', action: 'create' },
  { name: 'Update Content', code: 'update_content', description: 'Edit content and materials', resource: 'content', action: 'update' },
  { name: 'Delete Content', code: 'delete_content', description: 'Delete content', resource: 'content', action: 'delete' },
  { name: 'Manage Content', code: 'manage_content', description: 'Full content management', resource: 'content', action: 'manage' },

  // Live Classes
  { name: 'View Live Classes', code: 'view_live_classes', description: 'View live class schedules', resource: 'live_classes', action: 'read' },
  { name: 'Create Live Classes', code: 'create_live_classes', description: 'Schedule live classes', resource: 'live_classes', action: 'create' },
  { name: 'Manage Live Classes', code: 'manage_live_classes', description: 'Full live class management', resource: 'live_classes', action: 'manage' },
  { name: 'Join Live Classes', code: 'join_live_classes', description: 'Join live classes as participant', resource: 'live_classes', action: 'join' },

  // Assignments & Exams
  { name: 'View Assignments', code: 'view_assignments', description: 'View assignments and exams', resource: 'assignments', action: 'read' },
  { name: 'Create Assignments', code: 'create_assignments', description: 'Create assignments and exams', resource: 'assignments', action: 'create' },
  { name: 'Grade Assignments', code: 'grade_assignments', description: 'Grade student submissions', resource: 'assignments', action: 'grade' },
  { name: 'Submit Assignments', code: 'submit_assignments', description: 'Submit assignment solutions', resource: 'assignments', action: 'submit' },

  // Communication
  { name: 'Send Notifications', code: 'send_notifications', description: 'Send notifications to users', resource: 'notifications', action: 'create' },
  { name: 'Manage Communication', code: 'manage_communication', description: 'Manage all communication features', resource: 'communication', action: 'manage' },

  // System Administration
  { name: 'System Admin', code: 'system_admin', description: 'Full system administration access', resource: 'system', action: 'admin' },
  { name: 'Access All', code: 'access_all', description: 'Access to all system features', resource: 'all', action: 'access' },
]

// Define roles with their basic information
const ROLES = [
  {
    name: 'Super Admin',
    code: 'super_admin',
    description: 'Full system access with all permissions',
    level: '1',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 1,
    metadata: {
      maxUsers: null,
      autoAssign: false,
      requiresApproval: false
    }
  },
  {
    name: 'Platform Staff',
    code: 'platform_staff',
    description: 'Platform staff with administrative access',
    level: '1',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 2,
    metadata: {
      maxUsers: null,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Institute Admin',
    code: 'institute_admin',
    description: 'Institute administrator with full institute access',
    level: '2',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 3,
    metadata: {
      maxUsers: null,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Branch Manager',
    code: 'branch_manager',
    description: 'Branch manager with branch-level access',
    level: '3',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 4,
    metadata: {
      maxUsers: 50,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Trainer',
    code: 'trainer',
    description: 'Course trainer with teaching permissions',
    level: '3',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 5,
    metadata: {
      maxUsers: 100,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Institute Staff',
    code: 'institute_staff',
    description: 'Institute staff with limited administrative access',
    level: '4',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 6,
    metadata: {
      maxUsers: 20,
      autoAssign: false,
      requiresApproval: true
    }
  },
  {
    name: 'Student',
    code: 'student',
    description: 'Student with learning access',
    level: '5',
    scope: { institute: null, branch: null },
    isActive: true,
    isSystemRole: true,
    priority: 7,
    metadata: {
      maxUsers: null,
      autoAssign: true,
      requiresApproval: false
    }
  }
]

// Define which permissions each role should have
const ROLE_PERMISSIONS = {
  super_admin: [
    // Super Admin gets ALL permissions
    ...PERMISSIONS.map(p => p.code)
  ],
  platform_staff: [
    'view_users', 'view_institutes', 'view_analytics', 'view_reports', 'view_settings',
    'manage_institutes', 'manage_themes', 'view_billing', 'export_reports'
  ],
  institute_admin: [
    'view_users', 'create_users', 'update_users', 'manage_users',
    'view_courses', 'create_courses', 'update_courses', 'delete_courses', 'manage_courses',
    'view_students', 'create_students', 'update_students', 'delete_students', 'manage_students',
    'view_branches', 'create_branches', 'update_branches', 'delete_branches', 'manage_branches',
    'view_content', 'create_content', 'update_content', 'delete_content', 'manage_content',
    'view_live_classes', 'create_live_classes', 'manage_live_classes',
    'view_assignments', 'create_assignments', 'grade_assignments',
    'view_analytics', 'view_reports', 'export_reports',
    'view_billing', 'manage_website', 'send_notifications', 'manage_communication'
  ],
  branch_manager: [
    'view_users', 'create_users', 'update_users',
    'view_courses', 'create_courses', 'update_courses', 'manage_courses',
    'view_students', 'create_students', 'update_students', 'manage_students',
    'view_content', 'create_content', 'update_content', 'manage_content',
    'view_live_classes', 'create_live_classes', 'manage_live_classes',
    'view_assignments', 'create_assignments', 'grade_assignments',
    'view_analytics', 'view_reports', 'send_notifications'
  ],
  trainer: [
    'view_courses', 'update_courses',
    'view_students', 'update_students',
    'view_content', 'create_content', 'update_content',
    'view_live_classes', 'create_live_classes', 'manage_live_classes',
    'view_assignments', 'create_assignments', 'grade_assignments',
    'send_notifications', 'join_live_classes'
  ],
  institute_staff: [
    'view_users', 'view_courses', 'view_students', 'update_students',
    'view_content', 'view_live_classes', 'view_assignments',
    'send_notifications'
  ],
  student: [
    'view_courses', 'view_content', 'view_live_classes', 'join_live_classes',
    'view_assignments', 'submit_assignments'
  ]
}

export async function seedRolesAndPermissions(payload: Payload) {
  console.log('🌱 Starting roles and permissions seeding...')

  try {
    // 1. Create Permissions
    console.log('📝 Creating permissions...')
    const createdPermissions: any[] = []
    
    for (const permission of PERMISSIONS) {
      try {
        // Check if permission already exists
        const existing = await payload.find({
          collection: 'permissions',
          where: { code: { equals: permission.code } }
        })

        if (existing.docs.length === 0) {
          const created = await payload.create({
            collection: 'permissions',
            data: permission as any
          })
          createdPermissions.push(created)
          console.log(`  ✅ Created permission: ${permission.name}`)
        } else {
          createdPermissions.push(existing.docs[0])
          console.log(`  ⏭️  Permission already exists: ${permission.name}`)
        }
      } catch (error) {
        console.error(`  ❌ Error creating permission ${permission.name}:`, error)
      }
    }

    // 2. Create Roles
    console.log('👥 Creating roles...')
    const createdRoles: any[] = []
    
    for (const role of ROLES) {
      try {
        // Check if role already exists
        const existing = await payload.find({
          collection: 'roles',
          where: { code: { equals: role.code } }
        })

        if (existing.docs.length === 0) {
          const created = await payload.create({
            collection: 'roles',
            data: role as any
          })
          createdRoles.push(created)
          console.log(`  ✅ Created role: ${role.name}`)
        } else {
          createdRoles.push(existing.docs[0])
          console.log(`  ⏭️  Role already exists: ${role.name}`)
        }
      } catch (error) {
        console.error(`  ❌ Error creating role ${role.name}:`, error)
      }
    }

    // 3. Create Role-Permission relationships
    console.log('🔗 Creating role-permission relationships...')
    
    for (const [roleCode, permissionCodes] of Object.entries(ROLE_PERMISSIONS)) {
      const role = createdRoles.find(r => r.code === roleCode)
      if (!role) {
        console.log(`  ⚠️  Role not found: ${roleCode}`)
        continue
      }

      console.log(`  🔗 Assigning permissions to ${role.name}...`)
      
      for (const permissionCode of permissionCodes) {
        const permission = createdPermissions.find(p => p.code === permissionCode)
        if (!permission) {
          console.log(`    ⚠️  Permission not found: ${permissionCode}`)
          continue
        }

        try {
          // Check if relationship already exists
          const existing = await payload.find({
            collection: 'role-permissions',
            where: {
              and: [
                { role: { equals: role.id } },
                { permission: { equals: permission.id } }
              ]
            }
          })

          if (existing.docs.length === 0) {
            await payload.create({
              collection: 'role-permissions',
              data: {
                role: role.id,
                permission: permission.id,
                scope: 'global',
                isActive: true,
                assignedAt: new Date().toISOString()
              } as any
            })
            console.log(`    ✅ Assigned ${permission.name} to ${role.name}`)
          } else {
            console.log(`    ⏭️  Permission already assigned: ${permission.name} to ${role.name}`)
          }
        } catch (error) {
          console.error(`    ❌ Error assigning ${permission.name} to ${role.name}:`, error)
        }
      }
    }

    console.log('🎉 Roles and permissions seeding completed successfully!')
    
    // Summary
    console.log('\n📊 Seeding Summary:')
    console.log(`  Permissions: ${createdPermissions.length}`)
    console.log(`  Roles: ${createdRoles.length}`)
    console.log(`  Super Admin permissions: ${ROLE_PERMISSIONS.super_admin.length}`)
    
    return {
      permissions: createdPermissions,
      roles: createdRoles,
      success: true
    }

  } catch (error) {
    console.error('❌ Error during roles and permissions seeding:', error)
    throw error
  }
}

export default seedRolesAndPermissions
