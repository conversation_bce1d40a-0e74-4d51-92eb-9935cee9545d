'use client'

import { Permission } from '@/stores/useRolePermissionsStore'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { PermissionForm } from './PermissionForm'
import { 
  Key, 
  Edit, 
  Trash2, 
  Eye, 
  Settings,
  MoreVertical
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'

interface PermissionCardProps {
  permission: Permission
  onSelect: (permission: Permission) => void
}

export function PermissionCard({ permission, onSelect }: PermissionCardProps) {
  const { deletePermission, fetchPermissions } = useRolePermissionsStore()

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete the permission "${permission.name}"?`)) {
      const success = await deletePermission(permission.id)
      if (success) {
        fetchPermissions()
      }
    }
  }

  const getResourceColor = (resource: string) => {
    const colors: Record<string, string> = {
      'users': 'bg-blue-100 text-blue-800',
      'roles': 'bg-purple-100 text-purple-800',
      'permissions': 'bg-green-100 text-green-800',
      'institutes': 'bg-orange-100 text-orange-800',
      'courses': 'bg-yellow-100 text-yellow-800',
      'settings': 'bg-gray-100 text-gray-800',
      'analytics': 'bg-pink-100 text-pink-800',
      'billing': 'bg-red-100 text-red-800',
    }
    return colors[resource.toLowerCase()] || 'bg-gray-100 text-gray-800'
  }

  const getActionColor = (action: string) => {
    const colors: Record<string, string> = {
      'create': 'bg-green-100 text-green-800',
      'read': 'bg-blue-100 text-blue-800',
      'update': 'bg-yellow-100 text-yellow-800',
      'delete': 'bg-red-100 text-red-800',
      'manage': 'bg-purple-100 text-purple-800',
      'view': 'bg-gray-100 text-gray-800',
    }
    return colors[action.toLowerCase()] || 'bg-gray-100 text-gray-800'
  }

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-100 rounded-lg">
              <Key className="h-4 w-4 text-green-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                {permission.name}
              </h3>
              <p className="text-sm text-gray-600">{permission.code}</p>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onSelect(permission)}>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <PermissionForm 
                mode="edit" 
                permission={permission}
                trigger={
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Permission
                  </DropdownMenuItem>
                }
                onSuccess={() => fetchPermissions()}
              />
              <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete Permission
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Description */}
          {permission.description && (
            <p className="text-sm text-gray-600 line-clamp-2">
              {permission.description}
            </p>
          )}
          
          {/* Resource and Action Badges */}
          <div className="flex flex-wrap gap-2">
            <Badge className={getResourceColor(permission.resource)}>
              {permission.resource}
            </Badge>
            
            <Badge className={getActionColor(permission.action)}>
              {permission.action}
            </Badge>
            
            <Badge variant={permission.isActive ? "default" : "secondary"}>
              {permission.isActive ? "Active" : "Inactive"}
            </Badge>
            
            {permission.scope && (
              <Badge variant="outline">
                {permission.scope}
              </Badge>
            )}
          </div>
          
          {/* Priority */}
          <div className="text-xs text-gray-500">
            Priority: {permission.priority}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
