/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bson-objectid@2.0.4";
exports.ids = ["vendor-chunks/bson-objectid@2.0.4"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js ***!
  \*******************************************************************************************/
/***/ ((module) => {

eval("\nvar MACHINE_ID = Math.floor(Math.random() * 0xFFFFFF);\nvar index = ObjectID.index = parseInt(Math.random() * 0xFFFFFF, 10);\nvar pid = (typeof process === 'undefined' || typeof process.pid !== 'number' ? Math.floor(Math.random() * 100000) : process.pid) % 0xFFFF;\n// <https://github.com/williamkapke/bson-objectid/pull/51>\n// Attempt to fallback Buffer if _Buffer is undefined (e.g. for Node.js).\n// Worst case fallback to null and handle with null checking before using.\nvar BufferCtr = (() => { try { return _Buffer; }catch(_){ try{ return Buffer; }catch(_){ return null; } } })();\n\n/**\n * Determine if an object is Buffer\n *\n * Author:   Feross Aboukhadijeh <<EMAIL>> <http://feross.org>\n * License:  MIT\n *\n */\nvar isBuffer = function (obj) {\n  return !!(\n  obj != null &&\n  obj.constructor &&\n  typeof obj.constructor.isBuffer === 'function' &&\n  obj.constructor.isBuffer(obj)\n  )\n};\n\n// Precomputed hex table enables speedy hex string conversion\nvar hexTable = [];\nfor (var i = 0; i < 256; i++) {\n  hexTable[i] = (i <= 15 ? '0' : '') + i.toString(16);\n}\n\n// Regular expression that checks for hex value\nvar checkForHexRegExp = new RegExp('^[0-9a-fA-F]{24}$');\n\n// Lookup tables\nvar decodeLookup = [];\ni = 0;\nwhile (i < 10) decodeLookup[0x30 + i] = i++;\nwhile (i < 16) decodeLookup[0x41 - 10 + i] = decodeLookup[0x61 - 10 + i] = i++;\n\n/**\n * Create a new immutable ObjectID instance\n *\n * @class Represents the BSON ObjectID type\n * @param {String|Number} id Can be a 24 byte hex string, 12 byte binary string or a Number.\n * @return {Object} instance of ObjectID.\n */\nfunction ObjectID(id) {\n  if(!(this instanceof ObjectID)) return new ObjectID(id);\n  if(id && ((id instanceof ObjectID) || id._bsontype===\"ObjectID\"))\n    return id;\n\n  this._bsontype = 'ObjectID';\n\n  // The most common usecase (blank id, new objectId instance)\n  if (id == null || typeof id === 'number') {\n    // Generate a new id\n    this.id = this.generate(id);\n    // Return the object\n    return;\n  }\n\n  // Check if the passed in id is valid\n  var valid = ObjectID.isValid(id);\n\n  // Throw an error if it's not a valid setup\n  if (!valid && id != null) {\n    throw new Error(\n      'Argument passed in must be a single String of 12 bytes or a string of 24 hex characters'\n    );\n  } else if (valid && typeof id === 'string' && id.length === 24) {\n    return ObjectID.createFromHexString(id);\n  } else if (id != null && id.length === 12) {\n    // assume 12 byte string\n    this.id = id;\n  } else if (id != null && typeof id.toHexString === 'function') {\n    // Duck-typing to support ObjectId from different npm packages\n    return id;\n  } else {\n    throw new Error(\n      'Argument passed in must be a single String of 12 bytes or a string of 24 hex characters'\n    );\n  }\n}\nmodule.exports = ObjectID;\nObjectID.default = ObjectID;\n\n/**\n * Creates an ObjectID from a second based number, with the rest of the ObjectID zeroed out. Used for comparisons or sorting the ObjectID.\n *\n * @param {Number} time an integer number representing a number of seconds.\n * @return {ObjectID} return the created ObjectID\n * @api public\n */\nObjectID.createFromTime = function(time){\n  time = parseInt(time, 10) % 0xFFFFFFFF;\n  return new ObjectID(hex(8,time)+\"0000000000000000\");\n};\n\n/**\n * Creates an ObjectID from a hex string representation of an ObjectID.\n *\n * @param {String} hexString create a ObjectID from a passed in 24 byte hexstring.\n * @return {ObjectID} return the created ObjectID\n * @api public\n */\nObjectID.createFromHexString = function(hexString) {\n  // Throw an error if it's not a valid setup\n  if (typeof hexString === 'undefined' || (hexString != null && hexString.length !== 24)) {\n    throw new Error(\n      'Argument passed in must be a single String of 12 bytes or a string of 24 hex characters'\n    );\n  }\n\n  // Calculate lengths\n  var data = '';\n  var i = 0;\n\n  while (i < 24) {\n    data += String.fromCharCode((decodeLookup[hexString.charCodeAt(i++)] << 4) | decodeLookup[hexString.charCodeAt(i++)]);\n  }\n\n  return new ObjectID(data);\n};\n\n/**\n * Checks if a value is a valid bson ObjectId\n *\n * @param {String} objectid Can be a 24 byte hex string or an instance of ObjectID.\n * @return {Boolean} return true if the value is a valid bson ObjectID, return false otherwise.\n * @api public\n *\n * THE NATIVE DOCUMENTATION ISN'T CLEAR ON THIS GUY!\n * http://mongodb.github.io/node-mongodb-native/api-bson-generated/objectid.html#objectid-isvalid\n */\nObjectID.isValid = function(id) {\n  if (id == null) return false;\n\n  if (typeof id === 'number') {\n    return true;\n  }\n\n  if (typeof id === 'string') {\n    return id.length === 12 || (id.length === 24 && checkForHexRegExp.test(id));\n  }\n\n  if (id instanceof ObjectID) {\n    return true;\n  }\n\n  // <https://github.com/williamkapke/bson-objectid/issues/53>\n  if (isBuffer(id)) {\n    return ObjectID.isValid(id.toString('hex'));\n  }\n\n  // Duck-Typing detection of ObjectId like objects\n  // <https://github.com/williamkapke/bson-objectid/pull/51>\n  if (typeof id.toHexString === 'function') {\n    if(\n      BufferCtr &&\n      (id.id instanceof BufferCtr || typeof id.id === 'string')\n    ) {\n      return id.id.length === 12 || (id.id.length === 24 && checkForHexRegExp.test(id.id));\n    }\n  }\n\n  return false;\n};\n\nObjectID.prototype = {\n  constructor: ObjectID,\n\n  /**\n   * Return the ObjectID id as a 24 byte hex string representation\n   *\n   * @return {String} return the 24 byte hex string representation.\n   * @api public\n   */\n  toHexString: function() {\n    if (!this.id || !this.id.length) {\n      throw new Error(\n        'invalid ObjectId, ObjectId.id must be either a string or a Buffer, but is [' +\n          JSON.stringify(this.id) +\n          ']'\n      );\n    }\n\n    if (this.id.length === 24) {\n      return this.id;\n    }\n\n    if (isBuffer(this.id)) {\n      return this.id.toString('hex')\n    }\n\n    var hexString = '';\n    for (var i = 0; i < this.id.length; i++) {\n      hexString += hexTable[this.id.charCodeAt(i)];\n    }\n\n    return hexString;\n  },\n\n  /**\n   * Compares the equality of this ObjectID with `otherID`.\n   *\n   * @param {Object} otherId ObjectID instance to compare against.\n   * @return {Boolean} the result of comparing two ObjectID's\n   * @api public\n   */\n  equals: function (otherId){\n    if (otherId instanceof ObjectID) {\n      return this.toString() === otherId.toString();\n    } else if (\n      typeof otherId === 'string' &&\n      ObjectID.isValid(otherId) &&\n      otherId.length === 12 &&\n      isBuffer(this.id)\n    ) {\n      return otherId === this.id.toString('binary');\n    } else if (typeof otherId === 'string' && ObjectID.isValid(otherId) && otherId.length === 24) {\n      return otherId.toLowerCase() === this.toHexString();\n    } else if (typeof otherId === 'string' && ObjectID.isValid(otherId) && otherId.length === 12) {\n      return otherId === this.id;\n    } else if (otherId != null && (otherId instanceof ObjectID || otherId.toHexString)) {\n      return otherId.toHexString() === this.toHexString();\n    } else {\n      return false;\n    }\n  },\n\n  /**\n   * Returns the generation date (accurate up to the second) that this ID was generated.\n   *\n   * @return {Date} the generation date\n   * @api public\n   */\n  getTimestamp: function(){\n    var timestamp = new Date();\n    var time;\n    if (isBuffer(this.id)) {\n      time = this.id[3] | (this.id[2] << 8) | (this.id[1] << 16) | (this.id[0] << 24);\n    } else {\n      time = this.id.charCodeAt(3) | (this.id.charCodeAt(2) << 8) | (this.id.charCodeAt(1) << 16) | (this.id.charCodeAt(0) << 24);\n    }\n    timestamp.setTime(Math.floor(time) * 1000);\n    return timestamp;\n  },\n\n  /**\n  * Generate a 12 byte id buffer used in ObjectID's\n  *\n  * @method\n  * @param {number} [time] optional parameter allowing to pass in a second based timestamp.\n  * @return {string} return the 12 byte id buffer string.\n  */\n  generate: function (time) {\n    if ('number' !== typeof time) {\n      time = ~~(Date.now() / 1000);\n    }\n\n    //keep it in the ring!\n    time = parseInt(time, 10) % 0xFFFFFFFF;\n\n    var inc = next();\n\n    return String.fromCharCode(\n      ((time >> 24) & 0xFF),\n      ((time >> 16) & 0xFF),\n      ((time >> 8) & 0xFF),\n      (time & 0xFF),\n      ((MACHINE_ID >> 16) & 0xFF),\n      ((MACHINE_ID >> 8) & 0xFF),\n      (MACHINE_ID & 0xFF),\n      ((pid >> 8) & 0xFF),\n      (pid & 0xFF),\n      ((inc >> 16) & 0xFF),\n      ((inc >> 8) & 0xFF),\n      (inc & 0xFF)\n    )\n  },\n};\n\nfunction next() {\n  return index = (index+1) % 0xFFFFFF;\n}\n\nfunction hex(length, n) {\n  n = n.toString(16);\n  return (n.length===length)? n : \"00000000\".substring(n.length, length) + n;\n}\n\nfunction buffer(str) {\n  var i=0,out=[];\n\n  if(str.length===24)\n    for(;i<24; out.push(parseInt(str[i]+str[i+1], 16)),i+=2);\n\n  else if(str.length===12)\n    for(;i<12; out.push(str.charCodeAt(i)),i++);\n\n  return out;\n}\n\nvar inspect = (Symbol && Symbol.for && Symbol.for('nodejs.util.inspect.custom')) || 'inspect';\n\n/**\n * Converts to a string representation of this Id.\n *\n * @return {String} return the 24 byte hex string representation.\n * @api private\n */\nObjectID.prototype[inspect] = function() { return \"ObjectID(\"+this+\")\" };\nObjectID.prototype.toJSON = ObjectID.prototype.toHexString;\nObjectID.prototype.toString = ObjectID.prototype.toHexString;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js":
/*!*******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js ***!
  \*******************************************************************************************/
/***/ ((module) => {

eval("\nvar MACHINE_ID = Math.floor(Math.random() * 0xFFFFFF);\nvar index = ObjectID.index = parseInt(Math.random() * 0xFFFFFF, 10);\nvar pid = (typeof process === 'undefined' || typeof process.pid !== 'number' ? Math.floor(Math.random() * 100000) : process.pid) % 0xFFFF;\n// <https://github.com/williamkapke/bson-objectid/pull/51>\n// Attempt to fallback Buffer if _Buffer is undefined (e.g. for Node.js).\n// Worst case fallback to null and handle with null checking before using.\nvar BufferCtr = (() => { try { return _Buffer; }catch(_){ try{ return Buffer; }catch(_){ return null; } } })();\n\n/**\n * Determine if an object is Buffer\n *\n * Author:   Feross Aboukhadijeh <<EMAIL>> <http://feross.org>\n * License:  MIT\n *\n */\nvar isBuffer = function (obj) {\n  return !!(\n  obj != null &&\n  obj.constructor &&\n  typeof obj.constructor.isBuffer === 'function' &&\n  obj.constructor.isBuffer(obj)\n  )\n};\n\n// Precomputed hex table enables speedy hex string conversion\nvar hexTable = [];\nfor (var i = 0; i < 256; i++) {\n  hexTable[i] = (i <= 15 ? '0' : '') + i.toString(16);\n}\n\n// Regular expression that checks for hex value\nvar checkForHexRegExp = new RegExp('^[0-9a-fA-F]{24}$');\n\n// Lookup tables\nvar decodeLookup = [];\ni = 0;\nwhile (i < 10) decodeLookup[0x30 + i] = i++;\nwhile (i < 16) decodeLookup[0x41 - 10 + i] = decodeLookup[0x61 - 10 + i] = i++;\n\n/**\n * Create a new immutable ObjectID instance\n *\n * @class Represents the BSON ObjectID type\n * @param {String|Number} id Can be a 24 byte hex string, 12 byte binary string or a Number.\n * @return {Object} instance of ObjectID.\n */\nfunction ObjectID(id) {\n  if(!(this instanceof ObjectID)) return new ObjectID(id);\n  if(id && ((id instanceof ObjectID) || id._bsontype===\"ObjectID\"))\n    return id;\n\n  this._bsontype = 'ObjectID';\n\n  // The most common usecase (blank id, new objectId instance)\n  if (id == null || typeof id === 'number') {\n    // Generate a new id\n    this.id = this.generate(id);\n    // Return the object\n    return;\n  }\n\n  // Check if the passed in id is valid\n  var valid = ObjectID.isValid(id);\n\n  // Throw an error if it's not a valid setup\n  if (!valid && id != null) {\n    throw new Error(\n      'Argument passed in must be a single String of 12 bytes or a string of 24 hex characters'\n    );\n  } else if (valid && typeof id === 'string' && id.length === 24) {\n    return ObjectID.createFromHexString(id);\n  } else if (id != null && id.length === 12) {\n    // assume 12 byte string\n    this.id = id;\n  } else if (id != null && typeof id.toHexString === 'function') {\n    // Duck-typing to support ObjectId from different npm packages\n    return id;\n  } else {\n    throw new Error(\n      'Argument passed in must be a single String of 12 bytes or a string of 24 hex characters'\n    );\n  }\n}\nmodule.exports = ObjectID;\nObjectID.default = ObjectID;\n\n/**\n * Creates an ObjectID from a second based number, with the rest of the ObjectID zeroed out. Used for comparisons or sorting the ObjectID.\n *\n * @param {Number} time an integer number representing a number of seconds.\n * @return {ObjectID} return the created ObjectID\n * @api public\n */\nObjectID.createFromTime = function(time){\n  time = parseInt(time, 10) % 0xFFFFFFFF;\n  return new ObjectID(hex(8,time)+\"0000000000000000\");\n};\n\n/**\n * Creates an ObjectID from a hex string representation of an ObjectID.\n *\n * @param {String} hexString create a ObjectID from a passed in 24 byte hexstring.\n * @return {ObjectID} return the created ObjectID\n * @api public\n */\nObjectID.createFromHexString = function(hexString) {\n  // Throw an error if it's not a valid setup\n  if (typeof hexString === 'undefined' || (hexString != null && hexString.length !== 24)) {\n    throw new Error(\n      'Argument passed in must be a single String of 12 bytes or a string of 24 hex characters'\n    );\n  }\n\n  // Calculate lengths\n  var data = '';\n  var i = 0;\n\n  while (i < 24) {\n    data += String.fromCharCode((decodeLookup[hexString.charCodeAt(i++)] << 4) | decodeLookup[hexString.charCodeAt(i++)]);\n  }\n\n  return new ObjectID(data);\n};\n\n/**\n * Checks if a value is a valid bson ObjectId\n *\n * @param {String} objectid Can be a 24 byte hex string or an instance of ObjectID.\n * @return {Boolean} return true if the value is a valid bson ObjectID, return false otherwise.\n * @api public\n *\n * THE NATIVE DOCUMENTATION ISN'T CLEAR ON THIS GUY!\n * http://mongodb.github.io/node-mongodb-native/api-bson-generated/objectid.html#objectid-isvalid\n */\nObjectID.isValid = function(id) {\n  if (id == null) return false;\n\n  if (typeof id === 'number') {\n    return true;\n  }\n\n  if (typeof id === 'string') {\n    return id.length === 12 || (id.length === 24 && checkForHexRegExp.test(id));\n  }\n\n  if (id instanceof ObjectID) {\n    return true;\n  }\n\n  // <https://github.com/williamkapke/bson-objectid/issues/53>\n  if (isBuffer(id)) {\n    return ObjectID.isValid(id.toString('hex'));\n  }\n\n  // Duck-Typing detection of ObjectId like objects\n  // <https://github.com/williamkapke/bson-objectid/pull/51>\n  if (typeof id.toHexString === 'function') {\n    if(\n      BufferCtr &&\n      (id.id instanceof BufferCtr || typeof id.id === 'string')\n    ) {\n      return id.id.length === 12 || (id.id.length === 24 && checkForHexRegExp.test(id.id));\n    }\n  }\n\n  return false;\n};\n\nObjectID.prototype = {\n  constructor: ObjectID,\n\n  /**\n   * Return the ObjectID id as a 24 byte hex string representation\n   *\n   * @return {String} return the 24 byte hex string representation.\n   * @api public\n   */\n  toHexString: function() {\n    if (!this.id || !this.id.length) {\n      throw new Error(\n        'invalid ObjectId, ObjectId.id must be either a string or a Buffer, but is [' +\n          JSON.stringify(this.id) +\n          ']'\n      );\n    }\n\n    if (this.id.length === 24) {\n      return this.id;\n    }\n\n    if (isBuffer(this.id)) {\n      return this.id.toString('hex')\n    }\n\n    var hexString = '';\n    for (var i = 0; i < this.id.length; i++) {\n      hexString += hexTable[this.id.charCodeAt(i)];\n    }\n\n    return hexString;\n  },\n\n  /**\n   * Compares the equality of this ObjectID with `otherID`.\n   *\n   * @param {Object} otherId ObjectID instance to compare against.\n   * @return {Boolean} the result of comparing two ObjectID's\n   * @api public\n   */\n  equals: function (otherId){\n    if (otherId instanceof ObjectID) {\n      return this.toString() === otherId.toString();\n    } else if (\n      typeof otherId === 'string' &&\n      ObjectID.isValid(otherId) &&\n      otherId.length === 12 &&\n      isBuffer(this.id)\n    ) {\n      return otherId === this.id.toString('binary');\n    } else if (typeof otherId === 'string' && ObjectID.isValid(otherId) && otherId.length === 24) {\n      return otherId.toLowerCase() === this.toHexString();\n    } else if (typeof otherId === 'string' && ObjectID.isValid(otherId) && otherId.length === 12) {\n      return otherId === this.id;\n    } else if (otherId != null && (otherId instanceof ObjectID || otherId.toHexString)) {\n      return otherId.toHexString() === this.toHexString();\n    } else {\n      return false;\n    }\n  },\n\n  /**\n   * Returns the generation date (accurate up to the second) that this ID was generated.\n   *\n   * @return {Date} the generation date\n   * @api public\n   */\n  getTimestamp: function(){\n    var timestamp = new Date();\n    var time;\n    if (isBuffer(this.id)) {\n      time = this.id[3] | (this.id[2] << 8) | (this.id[1] << 16) | (this.id[0] << 24);\n    } else {\n      time = this.id.charCodeAt(3) | (this.id.charCodeAt(2) << 8) | (this.id.charCodeAt(1) << 16) | (this.id.charCodeAt(0) << 24);\n    }\n    timestamp.setTime(Math.floor(time) * 1000);\n    return timestamp;\n  },\n\n  /**\n  * Generate a 12 byte id buffer used in ObjectID's\n  *\n  * @method\n  * @param {number} [time] optional parameter allowing to pass in a second based timestamp.\n  * @return {string} return the 12 byte id buffer string.\n  */\n  generate: function (time) {\n    if ('number' !== typeof time) {\n      time = ~~(Date.now() / 1000);\n    }\n\n    //keep it in the ring!\n    time = parseInt(time, 10) % 0xFFFFFFFF;\n\n    var inc = next();\n\n    return String.fromCharCode(\n      ((time >> 24) & 0xFF),\n      ((time >> 16) & 0xFF),\n      ((time >> 8) & 0xFF),\n      (time & 0xFF),\n      ((MACHINE_ID >> 16) & 0xFF),\n      ((MACHINE_ID >> 8) & 0xFF),\n      (MACHINE_ID & 0xFF),\n      ((pid >> 8) & 0xFF),\n      (pid & 0xFF),\n      ((inc >> 16) & 0xFF),\n      ((inc >> 8) & 0xFF),\n      (inc & 0xFF)\n    )\n  },\n};\n\nfunction next() {\n  return index = (index+1) % 0xFFFFFF;\n}\n\nfunction hex(length, n) {\n  n = n.toString(16);\n  return (n.length===length)? n : \"00000000\".substring(n.length, length) + n;\n}\n\nfunction buffer(str) {\n  var i=0,out=[];\n\n  if(str.length===24)\n    for(;i<24; out.push(parseInt(str[i]+str[i+1], 16)),i+=2);\n\n  else if(str.length===12)\n    for(;i<12; out.push(str.charCodeAt(i)),i++);\n\n  return out;\n}\n\nvar inspect = (Symbol && Symbol.for && Symbol.for('nodejs.util.inspect.custom')) || 'inspect';\n\n/**\n * Converts to a string representation of this Id.\n *\n * @return {String} return the 24 byte hex string representation.\n * @api private\n */\nObjectID.prototype[inspect] = function() { return \"ObjectID(\"+this+\")\" };\nObjectID.prototype.toJSON = ObjectID.prototype.toHexString;\nObjectID.prototype.toString = ObjectID.prototype.toHexString;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/bson-objectid@2.0.4/node_modules/bson-objectid/objectid.js\n");

/***/ })

};
;