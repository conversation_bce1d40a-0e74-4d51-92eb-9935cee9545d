import type { CollectionConfig } from 'payload'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
  },
  auth: true,
  fields: [
    // Email and password added by default with auth: true
    {
      name: 'role',
      type: 'relationship',
      relationTo: 'roles',
      required: false, // Make it optional for now to allow user creation
      admin: {
        position: 'sidebar',
        description: 'Select a role for this user',
      },
    },
    // Keep legacy role field for backward compatibility during migration
    {
      name: 'legacyRole',
      type: 'select',
      required: true,
      options: [
        {
          label: 'Super Admin',
          value: 'super_admin',
        },
        {
          label: 'Platform Staff',
          value: 'platform_staff',
        },
        {
          label: 'Institute Admin',
          value: 'institute_admin',
        },
        {
          label: 'Branch Manager',
          value: 'branch_manager',
        },
        {
          label: 'Trainer',
          value: 'trainer',
        },
        {
          label: 'Institute Staff',
          value: 'institute_staff',
        },
        {
          label: 'Student',
          value: 'student',
        },
      ],
      defaultValue: 'student',
      admin: {
        position: 'sidebar',
        description: 'Legacy role field - currently used for access control',
      },
    },
    {
      name: 'firstName',
      type: 'text',
      required: true,
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
    },
    {
      name: 'phone',
      type: 'text',
      validate: (val: any) => {
        if (val && !/^[+]?[\d\s\-\(\)]+$/.test(val)) {
          return 'Please enter a valid phone number'
        }
        return true
      },
    },
    {
      name: 'avatar',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      admin: {
        condition: (data) => {
          // Use legacy role field for conditional display
          return ['institute_admin', 'branch_manager', 'trainer', 'institute_staff', 'student'].includes(data.legacyRole)
        },
      },
    },
    {
      name: 'branch',
      type: 'text',
      admin: {
        condition: (data) => {
          // Use legacy role field for conditional display
          return ['branch_manager', 'trainer', 'institute_staff', 'student'].includes(data.legacyRole)
        },
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'emailVerified',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'lastLogin',
      type: 'date',
      admin: {
        position: 'sidebar',
        readOnly: true,
      },
    },
    {
      name: 'loginAttempts',
      type: 'number',
      defaultValue: 0,
      admin: {
        position: 'sidebar',
        readOnly: true,
      },
    },
    {
      name: 'lockedUntil',
      type: 'date',
      admin: {
        position: 'sidebar',
        readOnly: true,
      },
    },
  ],
  access: {
    // Simplified access control - only super admins can manage users
    // This ensures existing super admin users can still access the system
    create: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    read: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    update: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      return user?.legacyRole === 'super_admin'
    },
  },
}
