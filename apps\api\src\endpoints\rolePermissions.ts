import { Endpoint } from 'payload'

export const rolePermissionsEndpoints: Endpoint[] = [
  // Get all roles with their permissions
  {
    path: '/roles-with-permissions',
    method: 'get',
    handler: async (req: any) => {
      try {
        const { payload } = req

        console.log('Request user:', req.user)
        console.log('Request headers:', req.headers)

        // Simple authentication check - look for user in request
        if (!req.user) {
          console.log('No user found in request')
          return Response.json({
            success: false,
            message: 'Authentication required',
          }, { status: 401 })
        }

        // Check if user is super admin (legacy or new role system)
        const user = req.user
        console.log('User details:', { email: user.email, role: user.role, legacyRole: user.legacyRole })

        const isAdmin = user.legacyRole === 'super_admin' ||
                       user.role === 'super_admin' ||
                       (user.role && typeof user.role === 'object' &&
                        (user.role.code === 'super_admin' || user.role.level === '1'))

        if (!isAdmin) {
          console.log('User is not admin:', { isAdmin, userRole: user.role, legacyRole: user.legacyRole })
          return Response.json({
            success: false,
            message: 'Super Admin access required',
          }, { status: 403 })
        }

        console.log('User authenticated as admin')

        // Get all roles
        const rolesResult = await payload.find({
          collection: 'roles',
          limit: 1000,
          sort: 'name',
        })

        // Get all role-permission assignments
        const rolePermissionsResult = await payload.find({
          collection: 'role-permissions',
          limit: 10000,
          where: {
            isActive: {
              equals: true,
            },
          },
          populate: {
            permission: true,
          },
        })

        // Group permissions by role
        const rolesWithPermissions = rolesResult.docs.map((role: any) => {
          const rolePermissions = rolePermissionsResult.docs
            .filter((rp: any) => rp.role === role.id)
            .map((rp: any) => rp.permission)

          return {
            ...role,
            permissions: rolePermissions,
            permissionCount: rolePermissions.length,
          }
        })

        return Response.json({
          success: true,
          data: rolesWithPermissions,
          totalDocs: rolesWithPermissions.length,
        })
      } catch (error: any) {
        console.error('Error fetching roles with permissions:', error)
        return Response.json({
          success: false,
          message: 'Failed to fetch roles with permissions',
          error: error?.message || 'Unknown error',
        }, { status: 500 })
      }
    },
  },
]
