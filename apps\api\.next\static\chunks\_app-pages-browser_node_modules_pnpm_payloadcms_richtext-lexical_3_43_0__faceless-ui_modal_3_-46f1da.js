"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_payloadcms_richtext-lexical_3_43_0__faceless-ui_modal_3_-46f1da"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/RelationshipComponent-APF3CN47.js":
/*!*********************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/RelationshipComponent-APF3CN47.js ***!
  \*********************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RelationshipComponent: () => (/* binding */ Y)\n/* harmony export */ });\n/* harmony import */ var _chunk_DBWINSQN_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./chunk-DBWINSQN.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/chunk-DBWINSQN.js\");\n/* harmony import */ var _chunk_F26IQ5RE_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./chunk-F26IQ5RE.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/chunk-F26IQ5RE.js\");\n/* harmony import */ var _chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./chunk-BZZVLW4U.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/chunk-BZZVLW4U.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var _lexical_react_LexicalComposerContext_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _payloadcms_translations__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @payloadcms/translations */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+translations@3.43.0/node_modules/@payloadcms/translations/dist/utilities/getTranslation.js\");\n/* harmony import */ var _payloadcms_ui__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @payloadcms/ui */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+ui@3.43.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_payload@3.43.0_rea_ktz7gjeu6woombyossqsn5mdmm/node_modules/@payloadcms/ui/dist/exports/client/chunk-CNCOIY3Y.js\");\n/* harmony import */ var _payloadcms_ui__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @payloadcms/ui */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+ui@3.43.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_payload@3.43.0_rea_ktz7gjeu6woombyossqsn5mdmm/node_modules/@payloadcms/ui/dist/exports/client/chunk-TIQCV7VX.js\");\n/* harmony import */ var _payloadcms_ui__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @payloadcms/ui */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+ui@3.43.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_payload@3.43.0_rea_ktz7gjeu6woombyossqsn5mdmm/node_modules/@payloadcms/ui/dist/exports/client/index.js\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! lexical */ \"(app-pages-browser)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ RelationshipComponent auto */ var _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nvar e = \"lexical-relationship\", C = {\n    depth: 0\n}, W = (i)=>{\n    var _t_labels, _t_labels1, _t_admin;\n    _s();\n    let { data: { relationTo: R, value: s }, nodeKey: l } = i;\n    if (typeof s == \"object\") throw new Error(\"Relationship value should be a string or number. The Lexical Relationship component should not receive the populated value object.\");\n    let v = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), [a] = (0,_lexical_react_LexicalComposerContext_js__WEBPACK_IMPORTED_MODULE_2__.useLexicalComposerContext)(), { fieldProps: { readOnly: u } } = (0,_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_3__.b)(), { config: { routes: { api: x }, serverURL: N }, getEntityConfig: $ } = (0,_payloadcms_ui__WEBPACK_IMPORTED_MODULE_4__.b)(), [t] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(()=>$({\n            collectionSlug: R\n        })), { i18n: w, t: n } = (0,_payloadcms_ui__WEBPACK_IMPORTED_MODULE_5__.d)(), [d, D] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useReducer)((r)=>r + 1, 0), [{ data: m }, { setParams: p }] = (0,_payloadcms_ui__WEBPACK_IMPORTED_MODULE_6__.usePayloadAPI)(\"\".concat(N).concat(x, \"/\").concat(t.slug, \"/\").concat(s), {\n        initialParams: C\n    }), { closeDocumentDrawer: h, DocumentDrawer: y, DocumentDrawerToggler: E } = (0,_chunk_F26IQ5RE_js__WEBPACK_IMPORTED_MODULE_7__.a)({\n        id: s,\n        collectionSlug: t.slug\n    }), T = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        a.update(()=>{\n            var _L;\n            (_L = (0,lexical__WEBPACK_IMPORTED_MODULE_8__.$getNodeByKey)(l)) === null || _L === void 0 ? void 0 : _L.remove();\n        });\n    }, [\n        a,\n        l\n    ]), S = react__WEBPACK_IMPORTED_MODULE_1__.useCallback({\n        \"W.useCallback[S]\": ()=>{\n            p({\n                ...C,\n                cacheBust: d\n            }), h(), D();\n        }\n    }[\"W.useCallback[S]\"], [\n        d,\n        p,\n        h\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: e,\n        contentEditable: !1,\n        ref: v,\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                className: \"\".concat(e, \"__wrap\"),\n                children: [\n                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n                        className: \"\".concat(e, \"__label\"),\n                        children: n(\"fields:labelRelationship\", {\n                            label: ((_t_labels = t.labels) === null || _t_labels === void 0 ? void 0 : _t_labels.singular) ? (0,_payloadcms_translations__WEBPACK_IMPORTED_MODULE_9__.getTranslation)((_t_labels1 = t.labels) === null || _t_labels1 === void 0 ? void 0 : _t_labels1.singular, w) : t.slug\n                        })\n                    }),\n                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(E, {\n                        className: \"\".concat(e, \"__doc-drawer-toggler\"),\n                        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n                            className: \"\".concat(e, \"__title\"),\n                            children: m ? m[(t === null || t === void 0 ? void 0 : (_t_admin = t.admin) === null || _t_admin === void 0 ? void 0 : _t_admin.useAsTitle) || \"id\"] : s\n                        })\n                    })\n                ]\n            }),\n            a.isEditable() && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                className: \"\".concat(e, \"__actions\"),\n                children: [\n                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_payloadcms_ui__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        buttonStyle: \"icon-label\",\n                        className: \"\".concat(e, \"__swapButton\"),\n                        disabled: u,\n                        el: \"button\",\n                        icon: \"swap\",\n                        onClick: ()=>{\n                            l && a.dispatchCommand(_chunk_DBWINSQN_js__WEBPACK_IMPORTED_MODULE_10__.a, {\n                                replace: {\n                                    nodeKey: l\n                                }\n                            });\n                        },\n                        round: !0,\n                        tooltip: n(\"fields:swapRelationship\")\n                    }),\n                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_payloadcms_ui__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                        buttonStyle: \"icon-label\",\n                        className: \"\".concat(e, \"__removeButton\"),\n                        disabled: u,\n                        icon: \"x\",\n                        onClick: (r)=>{\n                            r.preventDefault(), T();\n                        },\n                        round: !0,\n                        tooltip: n(\"fields:removeRelationship\")\n                    })\n                ]\n            }),\n            !!s && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(y, {\n                onSave: S\n            })\n        ]\n    });\n}, Y = (i)=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(W, {\n        ...i\n    });\n_s(W, \"eAj8jM/98hFum5bgcAWYaXUiNiw=\");\n //# sourceMappingURL=RelationshipComponent-APF3CN47.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/RelationshipComponent-APF3CN47.js\n"));

/***/ })

}]);