'use client'

import { Role } from '@/stores/super-admin/useRolePermissionsStore'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { RoleForm } from './RoleForm'
import { 
  Shield, 
  Edit, 
  Trash2, 
  Users, 
  Settings,
  MoreVertical
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'

interface RoleListItemProps {
  role: Role
  onSelect: (role: Role) => void
}

export function RoleListItem({ role, onSelect }: RoleListItemProps) {
  const { deleteRole, fetchRoles } = useRolePermissionsStore()

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
      const success = await deleteRole(role.id)
      if (success) {
        fetchRoles()
      }
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case '1': return 'bg-red-100 text-red-800'
      case '2': return 'bg-orange-100 text-orange-800'
      case '3': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelLabel = (level: string) => {
    switch (level) {
      case '1': return 'Super Admin'
      case '2': return 'Institute Admin'
      case '3': return 'User Level'
      default: return 'Unknown'
    }
  }

  return (
    <Card className="hover:shadow-sm transition-shadow">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          {/* Left side - Role info */}
          <div className="flex items-center gap-4 flex-1">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Shield className="h-4 w-4 text-blue-600" />
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-3 mb-1">
                <h3 className="font-semibold text-gray-900 truncate">
                  {role.name}
                </h3>
                <Badge className={getLevelColor(role.level)}>
                  {getLevelLabel(role.level)}
                </Badge>
                <Badge variant={role.isActive ? "default" : "secondary"}>
                  {role.isActive ? "Active" : "Inactive"}
                </Badge>
                {role.isSystemRole && (
                  <Badge variant="outline" className="text-purple-600 border-purple-200">
                    System
                  </Badge>
                )}
              </div>
              
              <div className="flex items-center gap-4 text-sm text-gray-600">
                <span>Code: {role.code}</span>
                <span className="flex items-center gap-1">
                  <Settings className="h-3 w-3" />
                  {role.permissions?.length || 0} permissions
                </span>
                <span>Priority: {role.priority}</span>
                {role.scope && <span>Scope: {role.scope}</span>}
              </div>
              
              {role.description && (
                <p className="text-sm text-gray-600 mt-1 line-clamp-1">
                  {role.description}
                </p>
              )}
            </div>
          </div>
          
          {/* Right side - Actions */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onSelect(role)}
              className="gap-2"
            >
              <Users className="h-4 w-4" />
              View
            </Button>
            
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <RoleForm 
                  mode="edit" 
                  role={role}
                  trigger={
                    <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Role
                    </DropdownMenuItem>
                  }
                  onSuccess={() => fetchRoles()}
                />
                {!role.isSystemRole && (
                  <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                    <Trash2 className="h-4 w-4 mr-2" />
                    Delete Role
                  </DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
