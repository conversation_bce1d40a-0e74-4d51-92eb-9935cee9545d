"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_pl_js"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2lzU2FtZVdlZWsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUEwRDtBQUNYO0FBRS9DOztDQUVDLEdBRUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0ErQkMsR0FDTSxTQUFTRSxXQUFXQyxTQUFTLEVBQUVDLFdBQVcsRUFBRUMsT0FBTztJQUN4RCxNQUFNLENBQUNDLFlBQVlDLGFBQWEsR0FBR1Asc0VBQWNBLENBQy9DSyxvQkFBQUEsOEJBQUFBLFFBQVNHLEVBQUUsRUFDWEwsV0FDQUM7SUFFRixPQUNFLENBQUNILDREQUFXQSxDQUFDSyxZQUFZRCxhQUFhLENBQUNKLDREQUFXQSxDQUFDTSxjQUFjRjtBQUVyRTtBQUVBLG9DQUFvQztBQUNwQyxpRUFBZUgsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXHByb2plY3RzXFxsbXNcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGRhdGUtZm5zQDQuMS4wXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxpc1NhbWVXZWVrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vcm1hbGl6ZURhdGVzIH0gZnJvbSBcIi4vX2xpYi9ub3JtYWxpemVEYXRlcy5qc1wiO1xuaW1wb3J0IHsgc3RhcnRPZldlZWsgfSBmcm9tIFwiLi9zdGFydE9mV2Vlay5qc1wiO1xuXG4vKipcbiAqIFRoZSB7QGxpbmsgaXNTYW1lV2Vla30gZnVuY3Rpb24gb3B0aW9ucy5cbiAqL1xuXG4vKipcbiAqIEBuYW1lIGlzU2FtZVdlZWtcbiAqIEBjYXRlZ29yeSBXZWVrIEhlbHBlcnNcbiAqIEBzdW1tYXJ5IEFyZSB0aGUgZ2l2ZW4gZGF0ZXMgaW4gdGhlIHNhbWUgd2VlayAoYW5kIG1vbnRoIGFuZCB5ZWFyKT9cbiAqXG4gKiBAZGVzY3JpcHRpb25cbiAqIEFyZSB0aGUgZ2l2ZW4gZGF0ZXMgaW4gdGhlIHNhbWUgd2VlayAoYW5kIG1vbnRoIGFuZCB5ZWFyKT9cbiAqXG4gKiBAcGFyYW0gbGF0ZXJEYXRlIC0gVGhlIGZpcnN0IGRhdGUgdG8gY2hlY2tcbiAqIEBwYXJhbSBlYXJsaWVyRGF0ZSAtIFRoZSBzZWNvbmQgZGF0ZSB0byBjaGVja1xuICogQHBhcmFtIG9wdGlvbnMgLSBBbiBvYmplY3Qgd2l0aCBvcHRpb25zXG4gKlxuICogQHJldHVybnMgVGhlIGRhdGVzIGFyZSBpbiB0aGUgc2FtZSB3ZWVrIChhbmQgbW9udGggYW5kIHllYXIpXG4gKlxuICogQGV4YW1wbGVcbiAqIC8vIEFyZSAzMSBBdWd1c3QgMjAxNCBhbmQgNCBTZXB0ZW1iZXIgMjAxNCBpbiB0aGUgc2FtZSB3ZWVrP1xuICogY29uc3QgcmVzdWx0ID0gaXNTYW1lV2VlayhuZXcgRGF0ZSgyMDE0LCA3LCAzMSksIG5ldyBEYXRlKDIwMTQsIDgsIDQpKVxuICogLy89PiB0cnVlXG4gKlxuICogQGV4YW1wbGVcbiAqIC8vIElmIHdlZWsgc3RhcnRzIHdpdGggTW9uZGF5LFxuICogLy8gYXJlIDMxIEF1Z3VzdCAyMDE0IGFuZCA0IFNlcHRlbWJlciAyMDE0IGluIHRoZSBzYW1lIHdlZWs/XG4gKiBjb25zdCByZXN1bHQgPSBpc1NhbWVXZWVrKG5ldyBEYXRlKDIwMTQsIDcsIDMxKSwgbmV3IERhdGUoMjAxNCwgOCwgNCksIHtcbiAqICAgd2Vla1N0YXJ0c09uOiAxXG4gKiB9KVxuICogLy89PiBmYWxzZVxuICpcbiAqIEBleGFtcGxlXG4gKiAvLyBBcmUgMSBKYW51YXJ5IDIwMTQgYW5kIDEgSmFudWFyeSAyMDE1IGluIHRoZSBzYW1lIHdlZWs/XG4gKiBjb25zdCByZXN1bHQgPSBpc1NhbWVXZWVrKG5ldyBEYXRlKDIwMTQsIDAsIDEpLCBuZXcgRGF0ZSgyMDE1LCAwLCAxKSlcbiAqIC8vPT4gZmFsc2VcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzU2FtZVdlZWsobGF0ZXJEYXRlLCBlYXJsaWVyRGF0ZSwgb3B0aW9ucykge1xuICBjb25zdCBbbGF0ZXJEYXRlXywgZWFybGllckRhdGVfXSA9IG5vcm1hbGl6ZURhdGVzKFxuICAgIG9wdGlvbnM/LmluLFxuICAgIGxhdGVyRGF0ZSxcbiAgICBlYXJsaWVyRGF0ZSxcbiAgKTtcbiAgcmV0dXJuIChcbiAgICArc3RhcnRPZldlZWsobGF0ZXJEYXRlXywgb3B0aW9ucykgPT09ICtzdGFydE9mV2VlayhlYXJsaWVyRGF0ZV8sIG9wdGlvbnMpXG4gICk7XG59XG5cbi8vIEZhbGxiYWNrIGZvciBtb2R1bGFyaXplZCBpbXBvcnRzOlxuZXhwb3J0IGRlZmF1bHQgaXNTYW1lV2VlaztcbiJdLCJuYW1lcyI6WyJub3JtYWxpemVEYXRlcyIsInN0YXJ0T2ZXZWVrIiwiaXNTYW1lV2VlayIsImxhdGVyRGF0ZSIsImVhcmxpZXJEYXRlIiwib3B0aW9ucyIsImxhdGVyRGF0ZV8iLCJlYXJsaWVyRGF0ZV8iLCJpbiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   pl: () => (/* binding */ pl)\n/* harmony export */ });\n/* harmony import */ var _pl_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./pl/_lib/formatDistance.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatDistance.js\");\n/* harmony import */ var _pl_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pl/_lib/formatLong.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatLong.js\");\n/* harmony import */ var _pl_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./pl/_lib/formatRelative.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatRelative.js\");\n/* harmony import */ var _pl_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pl/_lib/localize.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/localize.js\");\n/* harmony import */ var _pl_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./pl/_lib/match.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Polish locale.\n * @language Polish\n * @iso-639-2 pol\n * <AUTHOR> Derks [@ertrzyiks](https://github.com/ertrzyiks)\n * <AUTHOR> RAG [@justrag](https://github.com/justrag)\n * <AUTHOR> Grzyb [@mikolajgrzyb](https://github.com/mikolajgrzyb)\n * <AUTHOR> Tokarski [@mutisz](https://github.com/mutisz)\n */ const pl = {\n    code: \"pl\",\n    formatDistance: _pl_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _pl_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _pl_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _pl_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _pl_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (pl);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatDistance.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatDistance.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: {\n            regular: \"mniej niż sekunda\",\n            past: \"mniej niż sekundę\",\n            future: \"mniej niż sekundę\"\n        },\n        twoFour: \"mniej niż {{count}} sekundy\",\n        other: \"mniej niż {{count}} sekund\"\n    },\n    xSeconds: {\n        one: {\n            regular: \"sekunda\",\n            past: \"sekundę\",\n            future: \"sekundę\"\n        },\n        twoFour: \"{{count}} sekundy\",\n        other: \"{{count}} sekund\"\n    },\n    halfAMinute: {\n        one: \"pół minuty\",\n        twoFour: \"pół minuty\",\n        other: \"pół minuty\"\n    },\n    lessThanXMinutes: {\n        one: {\n            regular: \"mniej niż minuta\",\n            past: \"mniej niż minutę\",\n            future: \"mniej niż minutę\"\n        },\n        twoFour: \"mniej niż {{count}} minuty\",\n        other: \"mniej niż {{count}} minut\"\n    },\n    xMinutes: {\n        one: {\n            regular: \"minuta\",\n            past: \"minutę\",\n            future: \"minutę\"\n        },\n        twoFour: \"{{count}} minuty\",\n        other: \"{{count}} minut\"\n    },\n    aboutXHours: {\n        one: {\n            regular: \"około godziny\",\n            past: \"około godziny\",\n            future: \"około godzinę\"\n        },\n        twoFour: \"około {{count}} godziny\",\n        other: \"około {{count}} godzin\"\n    },\n    xHours: {\n        one: {\n            regular: \"godzina\",\n            past: \"godzinę\",\n            future: \"godzinę\"\n        },\n        twoFour: \"{{count}} godziny\",\n        other: \"{{count}} godzin\"\n    },\n    xDays: {\n        one: {\n            regular: \"dzień\",\n            past: \"dzień\",\n            future: \"1 dzień\"\n        },\n        twoFour: \"{{count}} dni\",\n        other: \"{{count}} dni\"\n    },\n    aboutXWeeks: {\n        one: \"około tygodnia\",\n        twoFour: \"około {{count}} tygodni\",\n        other: \"około {{count}} tygodni\"\n    },\n    xWeeks: {\n        one: \"tydzień\",\n        twoFour: \"{{count}} tygodnie\",\n        other: \"{{count}} tygodni\"\n    },\n    aboutXMonths: {\n        one: \"około miesiąc\",\n        twoFour: \"około {{count}} miesiące\",\n        other: \"około {{count}} miesięcy\"\n    },\n    xMonths: {\n        one: \"miesiąc\",\n        twoFour: \"{{count}} miesiące\",\n        other: \"{{count}} miesięcy\"\n    },\n    aboutXYears: {\n        one: \"około rok\",\n        twoFour: \"około {{count}} lata\",\n        other: \"około {{count}} lat\"\n    },\n    xYears: {\n        one: \"rok\",\n        twoFour: \"{{count}} lata\",\n        other: \"{{count}} lat\"\n    },\n    overXYears: {\n        one: \"ponad rok\",\n        twoFour: \"ponad {{count}} lata\",\n        other: \"ponad {{count}} lat\"\n    },\n    almostXYears: {\n        one: \"prawie rok\",\n        twoFour: \"prawie {{count}} lata\",\n        other: \"prawie {{count}} lat\"\n    }\n};\nfunction declensionGroup(scheme, count) {\n    if (count === 1) {\n        return scheme.one;\n    }\n    const rem100 = count % 100;\n    // ends with 11-20\n    if (rem100 <= 20 && rem100 > 10) {\n        return scheme.other;\n    }\n    const rem10 = rem100 % 10;\n    // ends with 2, 3, 4\n    if (rem10 >= 2 && rem10 <= 4) {\n        return scheme.twoFour;\n    }\n    return scheme.other;\n}\nfunction declension(scheme, count, time) {\n    const group = declensionGroup(scheme, count);\n    const finalText = typeof group === \"string\" ? group : group[time];\n    return finalText.replace(\"{{count}}\", String(count));\n}\nconst formatDistance = (token, count, options)=>{\n    const scheme = formatDistanceLocale[token];\n    if (!(options === null || options === void 0 ? void 0 : options.addSuffix)) {\n        return declension(scheme, count, \"regular\");\n    }\n    if (options.comparison && options.comparison > 0) {\n        return \"za \" + declension(scheme, count, \"future\");\n    } else {\n        return declension(scheme, count, \"past\") + \" temu\";\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatLong.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatLong.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, do MMMM y\",\n    long: \"do MMMM y\",\n    medium: \"do MMM y\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} {{time}}\",\n    long: \"{{date}} {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatRelative.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatRelative.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\");\n\nconst adjectivesLastWeek = {\n    masculine: \"ostatni\",\n    feminine: \"ostatnia\"\n};\nconst adjectivesThisWeek = {\n    masculine: \"ten\",\n    feminine: \"ta\"\n};\nconst adjectivesNextWeek = {\n    masculine: \"następny\",\n    feminine: \"następna\"\n};\nconst dayGrammaticalGender = {\n    0: \"feminine\",\n    1: \"masculine\",\n    2: \"masculine\",\n    3: \"feminine\",\n    4: \"masculine\",\n    5: \"masculine\",\n    6: \"feminine\"\n};\nfunction dayAndTimeWithAdjective(token, date, baseDate, options) {\n    let adjectives;\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n        adjectives = adjectivesThisWeek;\n    } else if (token === \"lastWeek\") {\n        adjectives = adjectivesLastWeek;\n    } else if (token === \"nextWeek\") {\n        adjectives = adjectivesNextWeek;\n    } else {\n        throw new Error(\"Cannot determine adjectives for token \".concat(token));\n    }\n    const day = date.getDay();\n    const grammaticalGender = dayGrammaticalGender[day];\n    const adjective = adjectives[grammaticalGender];\n    return \"'\".concat(adjective, \"' eeee 'o' p\");\n}\nconst formatRelativeLocale = {\n    lastWeek: dayAndTimeWithAdjective,\n    yesterday: \"'wczoraj o' p\",\n    today: \"'dzisiaj o' p\",\n    tomorrow: \"'jutro o' p\",\n    nextWeek: dayAndTimeWithAdjective,\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(token, date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/localize.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/localize.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"p.n.e.\",\n        \"n.e.\"\n    ],\n    abbreviated: [\n        \"p.n.e.\",\n        \"n.e.\"\n    ],\n    wide: [\n        \"przed naszą erą\",\n        \"naszej ery\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"I kw.\",\n        \"II kw.\",\n        \"III kw.\",\n        \"IV kw.\"\n    ],\n    wide: [\n        \"I kwartał\",\n        \"II kwartał\",\n        \"III kwartał\",\n        \"IV kwartał\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"S\",\n        \"L\",\n        \"M\",\n        \"K\",\n        \"M\",\n        \"C\",\n        \"L\",\n        \"S\",\n        \"W\",\n        \"P\",\n        \"L\",\n        \"G\"\n    ],\n    abbreviated: [\n        \"sty\",\n        \"lut\",\n        \"mar\",\n        \"kwi\",\n        \"maj\",\n        \"cze\",\n        \"lip\",\n        \"sie\",\n        \"wrz\",\n        \"paź\",\n        \"lis\",\n        \"gru\"\n    ],\n    wide: [\n        \"styczeń\",\n        \"luty\",\n        \"marzec\",\n        \"kwiecień\",\n        \"maj\",\n        \"czerwiec\",\n        \"lipiec\",\n        \"sierpień\",\n        \"wrzesień\",\n        \"październik\",\n        \"listopad\",\n        \"grudzień\"\n    ]\n};\nconst monthFormattingValues = {\n    narrow: [\n        \"s\",\n        \"l\",\n        \"m\",\n        \"k\",\n        \"m\",\n        \"c\",\n        \"l\",\n        \"s\",\n        \"w\",\n        \"p\",\n        \"l\",\n        \"g\"\n    ],\n    abbreviated: [\n        \"sty\",\n        \"lut\",\n        \"mar\",\n        \"kwi\",\n        \"maj\",\n        \"cze\",\n        \"lip\",\n        \"sie\",\n        \"wrz\",\n        \"paź\",\n        \"lis\",\n        \"gru\"\n    ],\n    wide: [\n        \"stycznia\",\n        \"lutego\",\n        \"marca\",\n        \"kwietnia\",\n        \"maja\",\n        \"czerwca\",\n        \"lipca\",\n        \"sierpnia\",\n        \"września\",\n        \"października\",\n        \"listopada\",\n        \"grudnia\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"N\",\n        \"P\",\n        \"W\",\n        \"Ś\",\n        \"C\",\n        \"P\",\n        \"S\"\n    ],\n    short: [\n        \"nie\",\n        \"pon\",\n        \"wto\",\n        \"śro\",\n        \"czw\",\n        \"pią\",\n        \"sob\"\n    ],\n    abbreviated: [\n        \"niedz.\",\n        \"pon.\",\n        \"wt.\",\n        \"śr.\",\n        \"czw.\",\n        \"pt.\",\n        \"sob.\"\n    ],\n    wide: [\n        \"niedziela\",\n        \"poniedziałek\",\n        \"wtorek\",\n        \"środa\",\n        \"czwartek\",\n        \"piątek\",\n        \"sobota\"\n    ]\n};\nconst dayFormattingValues = {\n    narrow: [\n        \"n\",\n        \"p\",\n        \"w\",\n        \"ś\",\n        \"c\",\n        \"p\",\n        \"s\"\n    ],\n    short: [\n        \"nie\",\n        \"pon\",\n        \"wto\",\n        \"śro\",\n        \"czw\",\n        \"pią\",\n        \"sob\"\n    ],\n    abbreviated: [\n        \"niedz.\",\n        \"pon.\",\n        \"wt.\",\n        \"śr.\",\n        \"czw.\",\n        \"pt.\",\n        \"sob.\"\n    ],\n    wide: [\n        \"niedziela\",\n        \"poniedziałek\",\n        \"wtorek\",\n        \"środa\",\n        \"czwartek\",\n        \"piątek\",\n        \"sobota\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"a\",\n        pm: \"p\",\n        midnight: \"półn.\",\n        noon: \"poł\",\n        morning: \"rano\",\n        afternoon: \"popoł.\",\n        evening: \"wiecz.\",\n        night: \"noc\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"północ\",\n        noon: \"południe\",\n        morning: \"rano\",\n        afternoon: \"popołudnie\",\n        evening: \"wieczór\",\n        night: \"noc\"\n    },\n    wide: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"północ\",\n        noon: \"południe\",\n        morning: \"rano\",\n        afternoon: \"popołudnie\",\n        evening: \"wieczór\",\n        night: \"noc\"\n    }\n};\nconst dayPeriodFormattingValues = {\n    narrow: {\n        am: \"a\",\n        pm: \"p\",\n        midnight: \"o półn.\",\n        noon: \"w poł.\",\n        morning: \"rano\",\n        afternoon: \"po poł.\",\n        evening: \"wiecz.\",\n        night: \"w nocy\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o północy\",\n        noon: \"w południe\",\n        morning: \"rano\",\n        afternoon: \"po południu\",\n        evening: \"wieczorem\",\n        night: \"w nocy\"\n    },\n    wide: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o północy\",\n        noon: \"w południe\",\n        morning: \"rano\",\n        afternoon: \"po południu\",\n        evening: \"wieczorem\",\n        night: \"w nocy\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    return String(dirtyNumber);\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: monthFormattingValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\",\n        formattingValues: dayFormattingValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: dayPeriodFormattingValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/match.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/match.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(p\\.?\\s*n\\.?\\s*e\\.?\\s*|n\\.?\\s*e\\.?\\s*)/i,\n    abbreviated: /^(p\\.?\\s*n\\.?\\s*e\\.?\\s*|n\\.?\\s*e\\.?\\s*)/i,\n    wide: /^(przed\\s*nasz(ą|a)\\s*er(ą|a)|naszej\\s*ery)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^p/i,\n        /^n/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^(I|II|III|IV)\\s*kw\\.?/i,\n    wide: /^(I|II|III|IV)\\s*kwarta(ł|l)/i\n};\nconst parseQuarterPatterns = {\n    narrow: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ],\n    any: [\n        /^I kw/i,\n        /^II kw/i,\n        /^III kw/i,\n        /^IV kw/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[slmkcwpg]/i,\n    abbreviated: /^(sty|lut|mar|kwi|maj|cze|lip|sie|wrz|pa(ź|z)|lis|gru)/i,\n    wide: /^(stycznia|stycze(ń|n)|lutego|luty|marca|marzec|kwietnia|kwiecie(ń|n)|maja|maj|czerwca|czerwiec|lipca|lipiec|sierpnia|sierpie(ń|n)|wrze(ś|s)nia|wrzesie(ń|n)|pa(ź|z)dziernika|pa(ź|z)dziernik|listopada|listopad|grudnia|grudzie(ń|n))/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^s/i,\n        /^l/i,\n        /^m/i,\n        /^k/i,\n        /^m/i,\n        /^c/i,\n        /^l/i,\n        /^s/i,\n        /^w/i,\n        /^p/i,\n        /^l/i,\n        /^g/i\n    ],\n    any: [\n        /^st/i,\n        /^lu/i,\n        /^mar/i,\n        /^k/i,\n        /^maj/i,\n        /^c/i,\n        /^lip/i,\n        /^si/i,\n        /^w/i,\n        /^p/i,\n        /^lis/i,\n        /^g/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[npwścs]/i,\n    short: /^(nie|pon|wto|(ś|s)ro|czw|pi(ą|a)|sob)/i,\n    abbreviated: /^(niedz|pon|wt|(ś|s)r|czw|pt|sob)\\.?/i,\n    wide: /^(niedziela|poniedzia(ł|l)ek|wtorek|(ś|s)roda|czwartek|pi(ą|a)tek|sobota)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^n/i,\n        /^p/i,\n        /^w/i,\n        /^ś/i,\n        /^c/i,\n        /^p/i,\n        /^s/i\n    ],\n    abbreviated: [\n        /^n/i,\n        /^po/i,\n        /^w/i,\n        /^(ś|s)r/i,\n        /^c/i,\n        /^pt/i,\n        /^so/i\n    ],\n    any: [\n        /^n/i,\n        /^po/i,\n        /^w/i,\n        /^(ś|s)r/i,\n        /^c/i,\n        /^pi/i,\n        /^so/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(^a$|^p$|pó(ł|l)n\\.?|o\\s*pó(ł|l)n\\.?|po(ł|l)\\.?|w\\s*po(ł|l)\\.?|po\\s*po(ł|l)\\.?|rano|wiecz\\.?|noc|w\\s*nocy)/i,\n    any: /^(am|pm|pó(ł|l)noc|o\\s*pó(ł|l)nocy|po(ł|l)udnie|w\\s*po(ł|l)udnie|popo(ł|l)udnie|po\\s*po(ł|l)udniu|rano|wieczór|wieczorem|noc|w\\s*nocy)/i\n};\nconst parseDayPeriodPatterns = {\n    narrow: {\n        am: /^a$/i,\n        pm: /^p$/i,\n        midnight: /pó(ł|l)n/i,\n        noon: /po(ł|l)/i,\n        morning: /rano/i,\n        afternoon: /po\\s*po(ł|l)/i,\n        evening: /wiecz/i,\n        night: /noc/i\n    },\n    any: {\n        am: /^am/i,\n        pm: /^pm/i,\n        midnight: /pó(ł|l)n/i,\n        noon: /po(ł|l)/i,\n        morning: /rano/i,\n        afternoon: /po\\s*po(ł|l)/i,\n        evening: /wiecz/i,\n        night: /noc/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/pl/_lib/match.js\n"));

/***/ })

}]);