'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'
import { usePermissions } from '@/contexts/PermissionContext'
import { RolePermissionsFilters } from '@/components/role-permissions/RolePermissionsFilters'
import { RolesList } from '@/components/role-permissions/RolesList'
import { PermissionsList } from '@/components/role-permissions/PermissionsList'
import { RoleForm } from '@/components/role-permissions/RoleForm'
import { PermissionForm } from '@/components/role-permissions/PermissionForm'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import {
  Shield,
  Key,
  Users,
  Plus,
  Settings,
  AlertTriangle,
  Loader2,
  Download,
  Upload
} from 'lucide-react'

export default function RolePermissionsPage() {
  const router = useRouter()
  const [authChecked, setAuthChecked] = useState(false)
  const [activeTab, setActiveTab] = useState<'roles' | 'permissions'>('roles')

  const { user, isAuthenticated, isLoading, initialize } = useAuthStore()
  const { userPermissions, isLoading: permissionsLoading } = usePermissions()
  const {
    roles,
    permissions,
    fetchRolesWithPermissions,
    fetchRoles,
    fetchPermissions,
    error,
    clearError
  } = useRolePermissionsStore()

  // Initialize auth on mount with delay to ensure proper loading
  useEffect(() => {
    console.log('🔄 Role-permissions page mounted, initializing auth...')

    // Small delay to ensure auth store is ready
    const timer = setTimeout(() => {
      initialize()
    }, 100)

    return () => clearTimeout(timer)
  }, [initialize])

  // Check authentication and redirect if needed
  useEffect(() => {
    console.log('🔍 Role-permissions page auth check:', {
      isLoading,
      isAuthenticated,
      hasUser: !!user,
      userEmail: user?.email,
      userLegacyRole: user?.legacyRole,
      userRole: user?.role,
      authChecked
    })

    if (!isLoading) {
      setAuthChecked(true)

      if (!isAuthenticated) {
        console.log('❌ Not authenticated, redirecting to login')
        router.push('/auth/admin/login')
        return
      }

      // Check if user is super admin
      if (!user) {
        console.log('❌ No user object, redirecting to login')
        router.push('/auth/admin/login')
        return
      }

      if (user.legacyRole !== 'super_admin' && user.legacyRole !== 'platform_staff') {
        console.log(`❌ User role ${user.legacyRole} not authorized, redirecting to login`)
        router.push('/auth/admin/login')
        return
      }

      console.log('✅ Authentication check passed for role-permissions page')
    }
  }, [user, isAuthenticated, isLoading, router])

  // Load initial data based on active tab
  useEffect(() => {
    if (authChecked && isAuthenticated && user) {
      switch (activeTab) {
        case 'roles':
          fetchRoles()
          break
        case 'permissions':
          fetchPermissions()
          break
      }
      // Also load roles with permissions for the assignment section
      fetchRolesWithPermissions()
    }
  }, [authChecked, isAuthenticated, user, activeTab, fetchRoles, fetchPermissions, fetchRolesWithPermissions])

 


 

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">Role & Permissions Management</h1>
          <p className="text-gray-600 mt-1">
            Manage system roles and permissions for your platform
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" size="sm" className="gap-2">
            <Download className="h-4 w-4" />
            Export
          </Button>
          <Button variant="outline" size="sm" className="gap-2">
            <Upload className="h-4 w-4" />
            Import
          </Button>
        </div>
      </div>

      {/* Error Alert */}
      {error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription className="flex items-center justify-between">
            {error}
            <Button variant="ghost" size="sm" onClick={clearError}>
              Dismiss
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as 'roles' | 'permissions')} className="space-y-6">
        <div className="flex items-center justify-between">
          <TabsList className="grid w-full max-w-md grid-cols-2">
            <TabsTrigger value="roles" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Roles
            </TabsTrigger>
            <TabsTrigger value="permissions" className="flex items-center gap-2">
              <Key className="h-4 w-4" />
              Permissions
            </TabsTrigger>
          </TabsList>

          {/* Add Button - Context-aware */}
          <div className="flex items-center gap-2">
            {activeTab === 'roles' ? (
              <RoleForm
                mode="create"
                trigger={
                  <Button className="gap-2">
                    <Plus className="h-4 w-4" />
                    Add Role
                  </Button>
                }
                onSuccess={() => {
                  fetchRoles()
                  fetchRolesWithPermissions()
                }}
              />
            ) : (
              <PermissionForm
                mode="create"
                trigger={
                  <Button className="gap-2">
                    <Plus className="h-4 w-4" />
                    Add Permission
                  </Button>
                }
                onSuccess={() => {
                  fetchPermissions()
                  fetchRolesWithPermissions()
                }}
              />
            )}
          </div>
        </div>

        {/* Filters */}
        <RolePermissionsFilters activeTab={activeTab} />

        {/* Roles Tab */}
        <TabsContent value="roles" className="space-y-6">
          <RolesList />
        </TabsContent>

        {/* Permissions Tab */}
        <TabsContent value="permissions" className="space-y-6">
          <PermissionsList />
        </TabsContent>
      </Tabs>

      {/* Role-Permission Assignment Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Role Permission Assignment
          </CardTitle>
          <CardDescription>
            Assign and manage permissions for each role. Control what actions each role can perform.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {roles.map((role) => (
              <div key={role.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <div className="p-2 bg-blue-100 rounded-lg">
                      <Shield className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{role.name}</h3>
                      <p className="text-sm text-gray-600">{role.description}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={role.isActive ? "default" : "secondary"}>
                      {role.isActive ? "Active" : "Inactive"}
                    </Badge>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleAssignPermissions(role)}
                      className="gap-2"
                    >
                      <Settings className="h-4 w-4" />
                      Manage Permissions
                    </Button>
                  </div>
                </div>

                {/* Show current permissions for this role */}
                <div className="flex flex-wrap gap-2">
                  {role.permissions && role.permissions.length > 0 ? (
                    role.permissions.map((permission) => (
                      <Badge key={permission.id} variant="outline" className="text-xs">
                        {permission.name}
                      </Badge>
                    ))
                  ) : (
                    <span className="text-sm text-gray-500 italic">No permissions assigned</span>
                  )}
                </div>
              </div>
            ))}

            {roles.length === 0 && (
              <div className="text-center py-8">
                <Shield className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-600">No roles found. Create a role first to assign permissions.</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}