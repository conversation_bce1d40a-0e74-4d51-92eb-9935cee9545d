"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_payloadcms_richtext-lexical_3_43_0__faceless-ui_modal_3_-793720"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@lexical+dragon@0.28.0/node_modules/@lexical/dragon/LexicalDragon.dev.mjs":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+dragon@0.28.0/node_modules/@lexical/dragon/LexicalDragon.dev.mjs ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerDragonSupport: () => (/* binding */ registerDragonSupport)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(app-pages-browser)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction registerDragonSupport(editor) {\n  const origin = window.location.origin;\n  const handler = event => {\n    if (event.origin !== origin) {\n      return;\n    }\n    const rootElement = editor.getRootElement();\n    if (document.activeElement !== rootElement) {\n      return;\n    }\n    const data = event.data;\n    if (typeof data === 'string') {\n      let parsedData;\n      try {\n        parsedData = JSON.parse(data);\n      } catch (e) {\n        return;\n      }\n      if (parsedData && parsedData.protocol === 'nuanria_messaging' && parsedData.type === 'request') {\n        const payload = parsedData.payload;\n        if (payload && payload.functionId === 'makeChanges') {\n          const args = payload.args;\n          if (args) {\n            const [elementStart, elementLength, text, selStart, selLength, formatCommand] = args;\n            editor.update(() => {\n              const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n              if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n                const anchor = selection.anchor;\n                let anchorNode = anchor.getNode();\n                let setSelStart = 0;\n                let setSelEnd = 0;\n                if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(anchorNode)) {\n                  // set initial selection\n                  if (elementStart >= 0 && elementLength >= 0) {\n                    setSelStart = elementStart;\n                    setSelEnd = elementStart + elementLength;\n                    // If the offset is more than the end, make it the end\n                    selection.setTextNodeRange(anchorNode, setSelStart, anchorNode, setSelEnd);\n                  }\n                }\n                if (setSelStart !== setSelEnd || text !== '') {\n                  selection.insertRawText(text);\n                  anchorNode = anchor.getNode();\n                }\n                if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(anchorNode)) {\n                  // set final selection\n                  setSelStart = selStart;\n                  setSelEnd = selStart + selLength;\n                  const anchorNodeTextLength = anchorNode.getTextContentSize();\n                  // If the offset is more than the end, make it the end\n                  setSelStart = setSelStart > anchorNodeTextLength ? anchorNodeTextLength : setSelStart;\n                  setSelEnd = setSelEnd > anchorNodeTextLength ? anchorNodeTextLength : setSelEnd;\n                  selection.setTextNodeRange(anchorNode, setSelStart, anchorNode, setSelEnd);\n                }\n\n                // block the chrome extension from handling this event\n                event.stopImmediatePropagation();\n              }\n            });\n          }\n        }\n      }\n    }\n  };\n  window.addEventListener('message', handler, true);\n  return () => {\n    window.removeEventListener('message', handler, true);\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vQGxleGljYWwrZHJhZ29uQDAuMjguMC9ub2RlX21vZHVsZXMvQGxleGljYWwvZHJhZ29uL0xleGljYWxEcmFnb24uZGV2Lm1qcyIsIm1hcHBpbmdzIjoiOzs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUV3RTs7QUFFeEU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsUUFBUTtBQUNSO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQyxzREFBYTtBQUM3QyxrQkFBa0IsMERBQWlCO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLG9EQUFXO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG9CQUFvQixvREFBVztBQUMvQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVpQyIsInNvdXJjZXMiOlsiQzpcXHByb2plY3RzXFxsbXNcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBsZXhpY2FsK2RyYWdvbkAwLjI4LjBcXG5vZGVfbW9kdWxlc1xcQGxleGljYWxcXGRyYWdvblxcTGV4aWNhbERyYWdvbi5kZXYubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKi9cblxuaW1wb3J0IHsgJGdldFNlbGVjdGlvbiwgJGlzUmFuZ2VTZWxlY3Rpb24sICRpc1RleHROb2RlIH0gZnJvbSAnbGV4aWNhbCc7XG5cbi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKi9cblxuZnVuY3Rpb24gcmVnaXN0ZXJEcmFnb25TdXBwb3J0KGVkaXRvcikge1xuICBjb25zdCBvcmlnaW4gPSB3aW5kb3cubG9jYXRpb24ub3JpZ2luO1xuICBjb25zdCBoYW5kbGVyID0gZXZlbnQgPT4ge1xuICAgIGlmIChldmVudC5vcmlnaW4gIT09IG9yaWdpbikge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCByb290RWxlbWVudCA9IGVkaXRvci5nZXRSb290RWxlbWVudCgpO1xuICAgIGlmIChkb2N1bWVudC5hY3RpdmVFbGVtZW50ICE9PSByb290RWxlbWVudCkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCBkYXRhID0gZXZlbnQuZGF0YTtcbiAgICBpZiAodHlwZW9mIGRhdGEgPT09ICdzdHJpbmcnKSB7XG4gICAgICBsZXQgcGFyc2VkRGF0YTtcbiAgICAgIHRyeSB7XG4gICAgICAgIHBhcnNlZERhdGEgPSBKU09OLnBhcnNlKGRhdGEpO1xuICAgICAgfSBjYXRjaCAoZSkge1xuICAgICAgICByZXR1cm47XG4gICAgICB9XG4gICAgICBpZiAocGFyc2VkRGF0YSAmJiBwYXJzZWREYXRhLnByb3RvY29sID09PSAnbnVhbnJpYV9tZXNzYWdpbmcnICYmIHBhcnNlZERhdGEudHlwZSA9PT0gJ3JlcXVlc3QnKSB7XG4gICAgICAgIGNvbnN0IHBheWxvYWQgPSBwYXJzZWREYXRhLnBheWxvYWQ7XG4gICAgICAgIGlmIChwYXlsb2FkICYmIHBheWxvYWQuZnVuY3Rpb25JZCA9PT0gJ21ha2VDaGFuZ2VzJykge1xuICAgICAgICAgIGNvbnN0IGFyZ3MgPSBwYXlsb2FkLmFyZ3M7XG4gICAgICAgICAgaWYgKGFyZ3MpIHtcbiAgICAgICAgICAgIGNvbnN0IFtlbGVtZW50U3RhcnQsIGVsZW1lbnRMZW5ndGgsIHRleHQsIHNlbFN0YXJ0LCBzZWxMZW5ndGgsIGZvcm1hdENvbW1hbmRdID0gYXJncztcbiAgICAgICAgICAgIGVkaXRvci51cGRhdGUoKCkgPT4ge1xuICAgICAgICAgICAgICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gICAgICAgICAgICAgIGlmICgkaXNSYW5nZVNlbGVjdGlvbihzZWxlY3Rpb24pKSB7XG4gICAgICAgICAgICAgICAgY29uc3QgYW5jaG9yID0gc2VsZWN0aW9uLmFuY2hvcjtcbiAgICAgICAgICAgICAgICBsZXQgYW5jaG9yTm9kZSA9IGFuY2hvci5nZXROb2RlKCk7XG4gICAgICAgICAgICAgICAgbGV0IHNldFNlbFN0YXJ0ID0gMDtcbiAgICAgICAgICAgICAgICBsZXQgc2V0U2VsRW5kID0gMDtcbiAgICAgICAgICAgICAgICBpZiAoJGlzVGV4dE5vZGUoYW5jaG9yTm9kZSkpIHtcbiAgICAgICAgICAgICAgICAgIC8vIHNldCBpbml0aWFsIHNlbGVjdGlvblxuICAgICAgICAgICAgICAgICAgaWYgKGVsZW1lbnRTdGFydCA+PSAwICYmIGVsZW1lbnRMZW5ndGggPj0gMCkge1xuICAgICAgICAgICAgICAgICAgICBzZXRTZWxTdGFydCA9IGVsZW1lbnRTdGFydDtcbiAgICAgICAgICAgICAgICAgICAgc2V0U2VsRW5kID0gZWxlbWVudFN0YXJ0ICsgZWxlbWVudExlbmd0aDtcbiAgICAgICAgICAgICAgICAgICAgLy8gSWYgdGhlIG9mZnNldCBpcyBtb3JlIHRoYW4gdGhlIGVuZCwgbWFrZSBpdCB0aGUgZW5kXG4gICAgICAgICAgICAgICAgICAgIHNlbGVjdGlvbi5zZXRUZXh0Tm9kZVJhbmdlKGFuY2hvck5vZGUsIHNldFNlbFN0YXJ0LCBhbmNob3JOb2RlLCBzZXRTZWxFbmQpO1xuICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBpZiAoc2V0U2VsU3RhcnQgIT09IHNldFNlbEVuZCB8fCB0ZXh0ICE9PSAnJykge1xuICAgICAgICAgICAgICAgICAgc2VsZWN0aW9uLmluc2VydFJhd1RleHQodGV4dCk7XG4gICAgICAgICAgICAgICAgICBhbmNob3JOb2RlID0gYW5jaG9yLmdldE5vZGUoKTtcbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgaWYgKCRpc1RleHROb2RlKGFuY2hvck5vZGUpKSB7XG4gICAgICAgICAgICAgICAgICAvLyBzZXQgZmluYWwgc2VsZWN0aW9uXG4gICAgICAgICAgICAgICAgICBzZXRTZWxTdGFydCA9IHNlbFN0YXJ0O1xuICAgICAgICAgICAgICAgICAgc2V0U2VsRW5kID0gc2VsU3RhcnQgKyBzZWxMZW5ndGg7XG4gICAgICAgICAgICAgICAgICBjb25zdCBhbmNob3JOb2RlVGV4dExlbmd0aCA9IGFuY2hvck5vZGUuZ2V0VGV4dENvbnRlbnRTaXplKCk7XG4gICAgICAgICAgICAgICAgICAvLyBJZiB0aGUgb2Zmc2V0IGlzIG1vcmUgdGhhbiB0aGUgZW5kLCBtYWtlIGl0IHRoZSBlbmRcbiAgICAgICAgICAgICAgICAgIHNldFNlbFN0YXJ0ID0gc2V0U2VsU3RhcnQgPiBhbmNob3JOb2RlVGV4dExlbmd0aCA/IGFuY2hvck5vZGVUZXh0TGVuZ3RoIDogc2V0U2VsU3RhcnQ7XG4gICAgICAgICAgICAgICAgICBzZXRTZWxFbmQgPSBzZXRTZWxFbmQgPiBhbmNob3JOb2RlVGV4dExlbmd0aCA/IGFuY2hvck5vZGVUZXh0TGVuZ3RoIDogc2V0U2VsRW5kO1xuICAgICAgICAgICAgICAgICAgc2VsZWN0aW9uLnNldFRleHROb2RlUmFuZ2UoYW5jaG9yTm9kZSwgc2V0U2VsU3RhcnQsIGFuY2hvck5vZGUsIHNldFNlbEVuZCk7XG4gICAgICAgICAgICAgICAgfVxuXG4gICAgICAgICAgICAgICAgLy8gYmxvY2sgdGhlIGNocm9tZSBleHRlbnNpb24gZnJvbSBoYW5kbGluZyB0aGlzIGV2ZW50XG4gICAgICAgICAgICAgICAgZXZlbnQuc3RvcEltbWVkaWF0ZVByb3BhZ2F0aW9uKCk7XG4gICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIH1cbiAgfTtcbiAgd2luZG93LmFkZEV2ZW50TGlzdGVuZXIoJ21lc3NhZ2UnLCBoYW5kbGVyLCB0cnVlKTtcbiAgcmV0dXJuICgpID0+IHtcbiAgICB3aW5kb3cucmVtb3ZlRXZlbnRMaXN0ZW5lcignbWVzc2FnZScsIGhhbmRsZXIsIHRydWUpO1xuICB9O1xufVxuXG5leHBvcnQgeyByZWdpc3RlckRyYWdvblN1cHBvcnQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@lexical+dragon@0.28.0/node_modules/@lexical/dragon/LexicalDragon.dev.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@lexical+history@0.28.0/node_modules/@lexical/history/LexicalHistory.dev.mjs":
/*!*************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+history@0.28.0/node_modules/@lexical/history/LexicalHistory.dev.mjs ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmptyHistoryState: () => (/* binding */ createEmptyHistoryState),\n/* harmony export */   registerHistory: () => (/* binding */ registerHistory)\n/* harmony export */ });\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(app-pages-browser)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst HISTORY_MERGE = 0;\nconst HISTORY_PUSH = 1;\nconst DISCARD_HISTORY_CANDIDATE = 2;\nconst OTHER = 0;\nconst COMPOSING_CHARACTER = 1;\nconst INSERT_CHARACTER_AFTER_SELECTION = 2;\nconst DELETE_CHARACTER_BEFORE_SELECTION = 3;\nconst DELETE_CHARACTER_AFTER_SELECTION = 4;\nfunction getDirtyNodes(editorState, dirtyLeaves, dirtyElements) {\n  const nodeMap = editorState._nodeMap;\n  const nodes = [];\n  for (const dirtyLeafKey of dirtyLeaves) {\n    const dirtyLeaf = nodeMap.get(dirtyLeafKey);\n    if (dirtyLeaf !== undefined) {\n      nodes.push(dirtyLeaf);\n    }\n  }\n  for (const [dirtyElementKey, intentionallyMarkedAsDirty] of dirtyElements) {\n    if (!intentionallyMarkedAsDirty) {\n      continue;\n    }\n    const dirtyElement = nodeMap.get(dirtyElementKey);\n    if (dirtyElement !== undefined && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(dirtyElement)) {\n      nodes.push(dirtyElement);\n    }\n  }\n  return nodes;\n}\nfunction getChangeType(prevEditorState, nextEditorState, dirtyLeavesSet, dirtyElementsSet, isComposing) {\n  if (prevEditorState === null || dirtyLeavesSet.size === 0 && dirtyElementsSet.size === 0 && !isComposing) {\n    return OTHER;\n  }\n  const nextSelection = nextEditorState._selection;\n  const prevSelection = prevEditorState._selection;\n  if (isComposing) {\n    return COMPOSING_CHARACTER;\n  }\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(nextSelection) || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) || !prevSelection.isCollapsed() || !nextSelection.isCollapsed()) {\n    return OTHER;\n  }\n  const dirtyNodes = getDirtyNodes(nextEditorState, dirtyLeavesSet, dirtyElementsSet);\n  if (dirtyNodes.length === 0) {\n    return OTHER;\n  }\n\n  // Catching the case when inserting new text node into an element (e.g. first char in paragraph/list),\n  // or after existing node.\n  if (dirtyNodes.length > 1) {\n    const nextNodeMap = nextEditorState._nodeMap;\n    const nextAnchorNode = nextNodeMap.get(nextSelection.anchor.key);\n    const prevAnchorNode = nextNodeMap.get(prevSelection.anchor.key);\n    if (nextAnchorNode && prevAnchorNode && !prevEditorState._nodeMap.has(nextAnchorNode.__key) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(nextAnchorNode) && nextAnchorNode.__text.length === 1 && nextSelection.anchor.offset === 1) {\n      return INSERT_CHARACTER_AFTER_SELECTION;\n    }\n    return OTHER;\n  }\n  const nextDirtyNode = dirtyNodes[0];\n  const prevDirtyNode = prevEditorState._nodeMap.get(nextDirtyNode.__key);\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevDirtyNode) || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(nextDirtyNode) || prevDirtyNode.__mode !== nextDirtyNode.__mode) {\n    return OTHER;\n  }\n  const prevText = prevDirtyNode.__text;\n  const nextText = nextDirtyNode.__text;\n  if (prevText === nextText) {\n    return OTHER;\n  }\n  const nextAnchor = nextSelection.anchor;\n  const prevAnchor = prevSelection.anchor;\n  if (nextAnchor.key !== prevAnchor.key || nextAnchor.type !== 'text') {\n    return OTHER;\n  }\n  const nextAnchorOffset = nextAnchor.offset;\n  const prevAnchorOffset = prevAnchor.offset;\n  const textDiff = nextText.length - prevText.length;\n  if (textDiff === 1 && prevAnchorOffset === nextAnchorOffset - 1) {\n    return INSERT_CHARACTER_AFTER_SELECTION;\n  }\n  if (textDiff === -1 && prevAnchorOffset === nextAnchorOffset + 1) {\n    return DELETE_CHARACTER_BEFORE_SELECTION;\n  }\n  if (textDiff === -1 && prevAnchorOffset === nextAnchorOffset) {\n    return DELETE_CHARACTER_AFTER_SELECTION;\n  }\n  return OTHER;\n}\nfunction isTextNodeUnchanged(key, prevEditorState, nextEditorState) {\n  const prevNode = prevEditorState._nodeMap.get(key);\n  const nextNode = nextEditorState._nodeMap.get(key);\n  const prevSelection = prevEditorState._selection;\n  const nextSelection = nextEditorState._selection;\n  const isDeletingLine = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(nextSelection) && prevSelection.anchor.type === 'element' && prevSelection.focus.type === 'element' && nextSelection.anchor.type === 'text' && nextSelection.focus.type === 'text';\n  if (!isDeletingLine && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevNode) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(nextNode) && prevNode.__parent === nextNode.__parent) {\n    // This has the assumption that object key order won't change if the\n    // content did not change, which should normally be safe given\n    // the manner in which nodes and exportJSON are typically implemented.\n    return JSON.stringify(prevEditorState.read(() => prevNode.exportJSON())) === JSON.stringify(nextEditorState.read(() => nextNode.exportJSON()));\n  }\n  return false;\n}\nfunction createMergeActionGetter(editor, delay) {\n  let prevChangeTime = Date.now();\n  let prevChangeType = OTHER;\n  return (prevEditorState, nextEditorState, currentHistoryEntry, dirtyLeaves, dirtyElements, tags) => {\n    const changeTime = Date.now();\n\n    // If applying changes from history stack there's no need\n    // to run history logic again, as history entries already calculated\n    if (tags.has('historic')) {\n      prevChangeType = OTHER;\n      prevChangeTime = changeTime;\n      return DISCARD_HISTORY_CANDIDATE;\n    }\n    const changeType = getChangeType(prevEditorState, nextEditorState, dirtyLeaves, dirtyElements, editor.isComposing());\n    const mergeAction = (() => {\n      const isSameEditor = currentHistoryEntry === null || currentHistoryEntry.editor === editor;\n      const shouldPushHistory = tags.has('history-push');\n      const shouldMergeHistory = !shouldPushHistory && isSameEditor && tags.has('history-merge');\n      if (shouldMergeHistory) {\n        return HISTORY_MERGE;\n      }\n      if (prevEditorState === null) {\n        return HISTORY_PUSH;\n      }\n      const selection = nextEditorState._selection;\n      const hasDirtyNodes = dirtyLeaves.size > 0 || dirtyElements.size > 0;\n      if (!hasDirtyNodes) {\n        if (selection !== null) {\n          return HISTORY_MERGE;\n        }\n        return DISCARD_HISTORY_CANDIDATE;\n      }\n      if (shouldPushHistory === false && changeType !== OTHER && changeType === prevChangeType && changeTime < prevChangeTime + delay && isSameEditor) {\n        return HISTORY_MERGE;\n      }\n\n      // A single node might have been marked as dirty, but not have changed\n      // due to some node transform reverting the change.\n      if (dirtyLeaves.size === 1) {\n        const dirtyLeafKey = Array.from(dirtyLeaves)[0];\n        if (isTextNodeUnchanged(dirtyLeafKey, prevEditorState, nextEditorState)) {\n          return HISTORY_MERGE;\n        }\n      }\n      return HISTORY_PUSH;\n    })();\n    prevChangeTime = changeTime;\n    prevChangeType = changeType;\n    return mergeAction;\n  };\n}\nfunction redo(editor, historyState) {\n  const redoStack = historyState.redoStack;\n  const undoStack = historyState.undoStack;\n  if (redoStack.length !== 0) {\n    const current = historyState.current;\n    if (current !== null) {\n      undoStack.push(current);\n      editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_UNDO_COMMAND, true);\n    }\n    const historyStateEntry = redoStack.pop();\n    if (redoStack.length === 0) {\n      editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_REDO_COMMAND, false);\n    }\n    historyState.current = historyStateEntry || null;\n    if (historyStateEntry) {\n      historyStateEntry.editor.setEditorState(historyStateEntry.editorState, {\n        tag: 'historic'\n      });\n    }\n  }\n}\nfunction undo(editor, historyState) {\n  const redoStack = historyState.redoStack;\n  const undoStack = historyState.undoStack;\n  const undoStackLength = undoStack.length;\n  if (undoStackLength !== 0) {\n    const current = historyState.current;\n    const historyStateEntry = undoStack.pop();\n    if (current !== null) {\n      redoStack.push(current);\n      editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_REDO_COMMAND, true);\n    }\n    if (undoStack.length === 0) {\n      editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_UNDO_COMMAND, false);\n    }\n    historyState.current = historyStateEntry || null;\n    if (historyStateEntry) {\n      historyStateEntry.editor.setEditorState(historyStateEntry.editorState, {\n        tag: 'historic'\n      });\n    }\n  }\n}\nfunction clearHistory(historyState) {\n  historyState.undoStack = [];\n  historyState.redoStack = [];\n  historyState.current = null;\n}\n\n/**\n * Registers necessary listeners to manage undo/redo history stack and related editor commands.\n * It returns `unregister` callback that cleans up all listeners and should be called on editor unmount.\n * @param editor - The lexical editor.\n * @param historyState - The history state, containing the current state and the undo/redo stack.\n * @param delay - The time (in milliseconds) the editor should delay generating a new history stack,\n * instead of merging the current changes with the current stack.\n * @returns The listeners cleanup callback function.\n */\nfunction registerHistory(editor, historyState, delay) {\n  const getMergeAction = createMergeActionGetter(editor, delay);\n  const applyChange = ({\n    editorState,\n    prevEditorState,\n    dirtyLeaves,\n    dirtyElements,\n    tags\n  }) => {\n    const current = historyState.current;\n    const redoStack = historyState.redoStack;\n    const undoStack = historyState.undoStack;\n    const currentEditorState = current === null ? null : current.editorState;\n    if (current !== null && editorState === currentEditorState) {\n      return;\n    }\n    const mergeAction = getMergeAction(prevEditorState, editorState, current, dirtyLeaves, dirtyElements, tags);\n    if (mergeAction === HISTORY_PUSH) {\n      if (redoStack.length !== 0) {\n        historyState.redoStack = [];\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_REDO_COMMAND, false);\n      }\n      if (current !== null) {\n        undoStack.push({\n          ...current\n        });\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_UNDO_COMMAND, true);\n      }\n    } else if (mergeAction === DISCARD_HISTORY_CANDIDATE) {\n      return;\n    }\n\n    // Else we merge\n    historyState.current = {\n      editor,\n      editorState\n    };\n  };\n  const unregister = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.mergeRegister)(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.UNDO_COMMAND, () => {\n    undo(editor, historyState);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.REDO_COMMAND, () => {\n    redo(editor, historyState);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CLEAR_EDITOR_COMMAND, () => {\n    clearHistory(historyState);\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CLEAR_HISTORY_COMMAND, () => {\n    clearHistory(historyState);\n    editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_REDO_COMMAND, false);\n    editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_UNDO_COMMAND, false);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerUpdateListener(applyChange));\n  return unregister;\n}\n\n/**\n * Creates an empty history state.\n * @returns - The empty history state, as an object.\n */\nfunction createEmptyHistoryState() {\n  return {\n    current: null,\n    redoStack: [],\n    undoStack: []\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vQGxleGljYWwraGlzdG9yeUAwLjI4LjAvbm9kZV9tb2R1bGVzL0BsZXhpY2FsL2hpc3RvcnkvTGV4aWNhbEhpc3RvcnkuZGV2Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRStDO0FBQzZKOztBQUU1TTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHVDQUF1QyxvREFBVztBQUNsRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTywwREFBaUIsb0JBQW9CLDBEQUFpQjtBQUM3RDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsbUdBQW1HLG9EQUFXO0FBQzlHO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU8sb0RBQVcsb0JBQW9CLG9EQUFXO0FBQ2pEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLDBEQUFpQixtQkFBbUIsMERBQWlCO0FBQzlFLHlCQUF5QixvREFBVyxjQUFjLG9EQUFXO0FBQzdEO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIscURBQWdCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBLDZCQUE2QixxREFBZ0I7QUFDN0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw2QkFBNkIscURBQWdCO0FBQzdDO0FBQ0E7QUFDQSw2QkFBNkIscURBQWdCO0FBQzdDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSwrQkFBK0IscURBQWdCO0FBQy9DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUztBQUNULCtCQUErQixxREFBZ0I7QUFDL0M7QUFDQSxNQUFNO0FBQ047QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxxQkFBcUIsNkRBQWEsd0JBQXdCLGlEQUFZO0FBQ3RFO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQixpREFBWTtBQUNsRTtBQUNBO0FBQ0EsR0FBRyxFQUFFLDREQUF1QiwwQkFBMEIseURBQW9CO0FBQzFFO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQiwwREFBcUI7QUFDM0U7QUFDQSwyQkFBMkIscURBQWdCO0FBQzNDLDJCQUEyQixxREFBZ0I7QUFDM0M7QUFDQSxHQUFHLEVBQUUsNERBQXVCO0FBQzVCO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFb0QiLCJzb3VyY2VzIjpbIkM6XFxwcm9qZWN0c1xcbG1zXFxub2RlX21vZHVsZXNcXC5wbnBtXFxAbGV4aWNhbCtoaXN0b3J5QDAuMjguMFxcbm9kZV9tb2R1bGVzXFxAbGV4aWNhbFxcaGlzdG9yeVxcTGV4aWNhbEhpc3RvcnkuZGV2Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICovXG5cbmltcG9ydCB7IG1lcmdlUmVnaXN0ZXIgfSBmcm9tICdAbGV4aWNhbC91dGlscyc7XG5pbXBvcnQgeyBVTkRPX0NPTU1BTkQsIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SLCBSRURPX0NPTU1BTkQsIENMRUFSX0VESVRPUl9DT01NQU5ELCBDTEVBUl9ISVNUT1JZX0NPTU1BTkQsIENBTl9SRURPX0NPTU1BTkQsIENBTl9VTkRPX0NPTU1BTkQsICRpc1JhbmdlU2VsZWN0aW9uLCAkaXNUZXh0Tm9kZSwgJGlzUm9vdE5vZGUgfSBmcm9tICdsZXhpY2FsJztcblxuLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqL1xuXG5jb25zdCBISVNUT1JZX01FUkdFID0gMDtcbmNvbnN0IEhJU1RPUllfUFVTSCA9IDE7XG5jb25zdCBESVNDQVJEX0hJU1RPUllfQ0FORElEQVRFID0gMjtcbmNvbnN0IE9USEVSID0gMDtcbmNvbnN0IENPTVBPU0lOR19DSEFSQUNURVIgPSAxO1xuY29uc3QgSU5TRVJUX0NIQVJBQ1RFUl9BRlRFUl9TRUxFQ1RJT04gPSAyO1xuY29uc3QgREVMRVRFX0NIQVJBQ1RFUl9CRUZPUkVfU0VMRUNUSU9OID0gMztcbmNvbnN0IERFTEVURV9DSEFSQUNURVJfQUZURVJfU0VMRUNUSU9OID0gNDtcbmZ1bmN0aW9uIGdldERpcnR5Tm9kZXMoZWRpdG9yU3RhdGUsIGRpcnR5TGVhdmVzLCBkaXJ0eUVsZW1lbnRzKSB7XG4gIGNvbnN0IG5vZGVNYXAgPSBlZGl0b3JTdGF0ZS5fbm9kZU1hcDtcbiAgY29uc3Qgbm9kZXMgPSBbXTtcbiAgZm9yIChjb25zdCBkaXJ0eUxlYWZLZXkgb2YgZGlydHlMZWF2ZXMpIHtcbiAgICBjb25zdCBkaXJ0eUxlYWYgPSBub2RlTWFwLmdldChkaXJ0eUxlYWZLZXkpO1xuICAgIGlmIChkaXJ0eUxlYWYgIT09IHVuZGVmaW5lZCkge1xuICAgICAgbm9kZXMucHVzaChkaXJ0eUxlYWYpO1xuICAgIH1cbiAgfVxuICBmb3IgKGNvbnN0IFtkaXJ0eUVsZW1lbnRLZXksIGludGVudGlvbmFsbHlNYXJrZWRBc0RpcnR5XSBvZiBkaXJ0eUVsZW1lbnRzKSB7XG4gICAgaWYgKCFpbnRlbnRpb25hbGx5TWFya2VkQXNEaXJ0eSkge1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGNvbnN0IGRpcnR5RWxlbWVudCA9IG5vZGVNYXAuZ2V0KGRpcnR5RWxlbWVudEtleSk7XG4gICAgaWYgKGRpcnR5RWxlbWVudCAhPT0gdW5kZWZpbmVkICYmICEkaXNSb290Tm9kZShkaXJ0eUVsZW1lbnQpKSB7XG4gICAgICBub2Rlcy5wdXNoKGRpcnR5RWxlbWVudCk7XG4gICAgfVxuICB9XG4gIHJldHVybiBub2Rlcztcbn1cbmZ1bmN0aW9uIGdldENoYW5nZVR5cGUocHJldkVkaXRvclN0YXRlLCBuZXh0RWRpdG9yU3RhdGUsIGRpcnR5TGVhdmVzU2V0LCBkaXJ0eUVsZW1lbnRzU2V0LCBpc0NvbXBvc2luZykge1xuICBpZiAocHJldkVkaXRvclN0YXRlID09PSBudWxsIHx8IGRpcnR5TGVhdmVzU2V0LnNpemUgPT09IDAgJiYgZGlydHlFbGVtZW50c1NldC5zaXplID09PSAwICYmICFpc0NvbXBvc2luZykge1xuICAgIHJldHVybiBPVEhFUjtcbiAgfVxuICBjb25zdCBuZXh0U2VsZWN0aW9uID0gbmV4dEVkaXRvclN0YXRlLl9zZWxlY3Rpb247XG4gIGNvbnN0IHByZXZTZWxlY3Rpb24gPSBwcmV2RWRpdG9yU3RhdGUuX3NlbGVjdGlvbjtcbiAgaWYgKGlzQ29tcG9zaW5nKSB7XG4gICAgcmV0dXJuIENPTVBPU0lOR19DSEFSQUNURVI7XG4gIH1cbiAgaWYgKCEkaXNSYW5nZVNlbGVjdGlvbihuZXh0U2VsZWN0aW9uKSB8fCAhJGlzUmFuZ2VTZWxlY3Rpb24ocHJldlNlbGVjdGlvbikgfHwgIXByZXZTZWxlY3Rpb24uaXNDb2xsYXBzZWQoKSB8fCAhbmV4dFNlbGVjdGlvbi5pc0NvbGxhcHNlZCgpKSB7XG4gICAgcmV0dXJuIE9USEVSO1xuICB9XG4gIGNvbnN0IGRpcnR5Tm9kZXMgPSBnZXREaXJ0eU5vZGVzKG5leHRFZGl0b3JTdGF0ZSwgZGlydHlMZWF2ZXNTZXQsIGRpcnR5RWxlbWVudHNTZXQpO1xuICBpZiAoZGlydHlOb2Rlcy5sZW5ndGggPT09IDApIHtcbiAgICByZXR1cm4gT1RIRVI7XG4gIH1cblxuICAvLyBDYXRjaGluZyB0aGUgY2FzZSB3aGVuIGluc2VydGluZyBuZXcgdGV4dCBub2RlIGludG8gYW4gZWxlbWVudCAoZS5nLiBmaXJzdCBjaGFyIGluIHBhcmFncmFwaC9saXN0KSxcbiAgLy8gb3IgYWZ0ZXIgZXhpc3Rpbmcgbm9kZS5cbiAgaWYgKGRpcnR5Tm9kZXMubGVuZ3RoID4gMSkge1xuICAgIGNvbnN0IG5leHROb2RlTWFwID0gbmV4dEVkaXRvclN0YXRlLl9ub2RlTWFwO1xuICAgIGNvbnN0IG5leHRBbmNob3JOb2RlID0gbmV4dE5vZGVNYXAuZ2V0KG5leHRTZWxlY3Rpb24uYW5jaG9yLmtleSk7XG4gICAgY29uc3QgcHJldkFuY2hvck5vZGUgPSBuZXh0Tm9kZU1hcC5nZXQocHJldlNlbGVjdGlvbi5hbmNob3Iua2V5KTtcbiAgICBpZiAobmV4dEFuY2hvck5vZGUgJiYgcHJldkFuY2hvck5vZGUgJiYgIXByZXZFZGl0b3JTdGF0ZS5fbm9kZU1hcC5oYXMobmV4dEFuY2hvck5vZGUuX19rZXkpICYmICRpc1RleHROb2RlKG5leHRBbmNob3JOb2RlKSAmJiBuZXh0QW5jaG9yTm9kZS5fX3RleHQubGVuZ3RoID09PSAxICYmIG5leHRTZWxlY3Rpb24uYW5jaG9yLm9mZnNldCA9PT0gMSkge1xuICAgICAgcmV0dXJuIElOU0VSVF9DSEFSQUNURVJfQUZURVJfU0VMRUNUSU9OO1xuICAgIH1cbiAgICByZXR1cm4gT1RIRVI7XG4gIH1cbiAgY29uc3QgbmV4dERpcnR5Tm9kZSA9IGRpcnR5Tm9kZXNbMF07XG4gIGNvbnN0IHByZXZEaXJ0eU5vZGUgPSBwcmV2RWRpdG9yU3RhdGUuX25vZGVNYXAuZ2V0KG5leHREaXJ0eU5vZGUuX19rZXkpO1xuICBpZiAoISRpc1RleHROb2RlKHByZXZEaXJ0eU5vZGUpIHx8ICEkaXNUZXh0Tm9kZShuZXh0RGlydHlOb2RlKSB8fCBwcmV2RGlydHlOb2RlLl9fbW9kZSAhPT0gbmV4dERpcnR5Tm9kZS5fX21vZGUpIHtcbiAgICByZXR1cm4gT1RIRVI7XG4gIH1cbiAgY29uc3QgcHJldlRleHQgPSBwcmV2RGlydHlOb2RlLl9fdGV4dDtcbiAgY29uc3QgbmV4dFRleHQgPSBuZXh0RGlydHlOb2RlLl9fdGV4dDtcbiAgaWYgKHByZXZUZXh0ID09PSBuZXh0VGV4dCkge1xuICAgIHJldHVybiBPVEhFUjtcbiAgfVxuICBjb25zdCBuZXh0QW5jaG9yID0gbmV4dFNlbGVjdGlvbi5hbmNob3I7XG4gIGNvbnN0IHByZXZBbmNob3IgPSBwcmV2U2VsZWN0aW9uLmFuY2hvcjtcbiAgaWYgKG5leHRBbmNob3Iua2V5ICE9PSBwcmV2QW5jaG9yLmtleSB8fCBuZXh0QW5jaG9yLnR5cGUgIT09ICd0ZXh0Jykge1xuICAgIHJldHVybiBPVEhFUjtcbiAgfVxuICBjb25zdCBuZXh0QW5jaG9yT2Zmc2V0ID0gbmV4dEFuY2hvci5vZmZzZXQ7XG4gIGNvbnN0IHByZXZBbmNob3JPZmZzZXQgPSBwcmV2QW5jaG9yLm9mZnNldDtcbiAgY29uc3QgdGV4dERpZmYgPSBuZXh0VGV4dC5sZW5ndGggLSBwcmV2VGV4dC5sZW5ndGg7XG4gIGlmICh0ZXh0RGlmZiA9PT0gMSAmJiBwcmV2QW5jaG9yT2Zmc2V0ID09PSBuZXh0QW5jaG9yT2Zmc2V0IC0gMSkge1xuICAgIHJldHVybiBJTlNFUlRfQ0hBUkFDVEVSX0FGVEVSX1NFTEVDVElPTjtcbiAgfVxuICBpZiAodGV4dERpZmYgPT09IC0xICYmIHByZXZBbmNob3JPZmZzZXQgPT09IG5leHRBbmNob3JPZmZzZXQgKyAxKSB7XG4gICAgcmV0dXJuIERFTEVURV9DSEFSQUNURVJfQkVGT1JFX1NFTEVDVElPTjtcbiAgfVxuICBpZiAodGV4dERpZmYgPT09IC0xICYmIHByZXZBbmNob3JPZmZzZXQgPT09IG5leHRBbmNob3JPZmZzZXQpIHtcbiAgICByZXR1cm4gREVMRVRFX0NIQVJBQ1RFUl9BRlRFUl9TRUxFQ1RJT047XG4gIH1cbiAgcmV0dXJuIE9USEVSO1xufVxuZnVuY3Rpb24gaXNUZXh0Tm9kZVVuY2hhbmdlZChrZXksIHByZXZFZGl0b3JTdGF0ZSwgbmV4dEVkaXRvclN0YXRlKSB7XG4gIGNvbnN0IHByZXZOb2RlID0gcHJldkVkaXRvclN0YXRlLl9ub2RlTWFwLmdldChrZXkpO1xuICBjb25zdCBuZXh0Tm9kZSA9IG5leHRFZGl0b3JTdGF0ZS5fbm9kZU1hcC5nZXQoa2V5KTtcbiAgY29uc3QgcHJldlNlbGVjdGlvbiA9IHByZXZFZGl0b3JTdGF0ZS5fc2VsZWN0aW9uO1xuICBjb25zdCBuZXh0U2VsZWN0aW9uID0gbmV4dEVkaXRvclN0YXRlLl9zZWxlY3Rpb247XG4gIGNvbnN0IGlzRGVsZXRpbmdMaW5lID0gJGlzUmFuZ2VTZWxlY3Rpb24ocHJldlNlbGVjdGlvbikgJiYgJGlzUmFuZ2VTZWxlY3Rpb24obmV4dFNlbGVjdGlvbikgJiYgcHJldlNlbGVjdGlvbi5hbmNob3IudHlwZSA9PT0gJ2VsZW1lbnQnICYmIHByZXZTZWxlY3Rpb24uZm9jdXMudHlwZSA9PT0gJ2VsZW1lbnQnICYmIG5leHRTZWxlY3Rpb24uYW5jaG9yLnR5cGUgPT09ICd0ZXh0JyAmJiBuZXh0U2VsZWN0aW9uLmZvY3VzLnR5cGUgPT09ICd0ZXh0JztcbiAgaWYgKCFpc0RlbGV0aW5nTGluZSAmJiAkaXNUZXh0Tm9kZShwcmV2Tm9kZSkgJiYgJGlzVGV4dE5vZGUobmV4dE5vZGUpICYmIHByZXZOb2RlLl9fcGFyZW50ID09PSBuZXh0Tm9kZS5fX3BhcmVudCkge1xuICAgIC8vIFRoaXMgaGFzIHRoZSBhc3N1bXB0aW9uIHRoYXQgb2JqZWN0IGtleSBvcmRlciB3b24ndCBjaGFuZ2UgaWYgdGhlXG4gICAgLy8gY29udGVudCBkaWQgbm90IGNoYW5nZSwgd2hpY2ggc2hvdWxkIG5vcm1hbGx5IGJlIHNhZmUgZ2l2ZW5cbiAgICAvLyB0aGUgbWFubmVyIGluIHdoaWNoIG5vZGVzIGFuZCBleHBvcnRKU09OIGFyZSB0eXBpY2FsbHkgaW1wbGVtZW50ZWQuXG4gICAgcmV0dXJuIEpTT04uc3RyaW5naWZ5KHByZXZFZGl0b3JTdGF0ZS5yZWFkKCgpID0+IHByZXZOb2RlLmV4cG9ydEpTT04oKSkpID09PSBKU09OLnN0cmluZ2lmeShuZXh0RWRpdG9yU3RhdGUucmVhZCgoKSA9PiBuZXh0Tm9kZS5leHBvcnRKU09OKCkpKTtcbiAgfVxuICByZXR1cm4gZmFsc2U7XG59XG5mdW5jdGlvbiBjcmVhdGVNZXJnZUFjdGlvbkdldHRlcihlZGl0b3IsIGRlbGF5KSB7XG4gIGxldCBwcmV2Q2hhbmdlVGltZSA9IERhdGUubm93KCk7XG4gIGxldCBwcmV2Q2hhbmdlVHlwZSA9IE9USEVSO1xuICByZXR1cm4gKHByZXZFZGl0b3JTdGF0ZSwgbmV4dEVkaXRvclN0YXRlLCBjdXJyZW50SGlzdG9yeUVudHJ5LCBkaXJ0eUxlYXZlcywgZGlydHlFbGVtZW50cywgdGFncykgPT4ge1xuICAgIGNvbnN0IGNoYW5nZVRpbWUgPSBEYXRlLm5vdygpO1xuXG4gICAgLy8gSWYgYXBwbHlpbmcgY2hhbmdlcyBmcm9tIGhpc3Rvcnkgc3RhY2sgdGhlcmUncyBubyBuZWVkXG4gICAgLy8gdG8gcnVuIGhpc3RvcnkgbG9naWMgYWdhaW4sIGFzIGhpc3RvcnkgZW50cmllcyBhbHJlYWR5IGNhbGN1bGF0ZWRcbiAgICBpZiAodGFncy5oYXMoJ2hpc3RvcmljJykpIHtcbiAgICAgIHByZXZDaGFuZ2VUeXBlID0gT1RIRVI7XG4gICAgICBwcmV2Q2hhbmdlVGltZSA9IGNoYW5nZVRpbWU7XG4gICAgICByZXR1cm4gRElTQ0FSRF9ISVNUT1JZX0NBTkRJREFURTtcbiAgICB9XG4gICAgY29uc3QgY2hhbmdlVHlwZSA9IGdldENoYW5nZVR5cGUocHJldkVkaXRvclN0YXRlLCBuZXh0RWRpdG9yU3RhdGUsIGRpcnR5TGVhdmVzLCBkaXJ0eUVsZW1lbnRzLCBlZGl0b3IuaXNDb21wb3NpbmcoKSk7XG4gICAgY29uc3QgbWVyZ2VBY3Rpb24gPSAoKCkgPT4ge1xuICAgICAgY29uc3QgaXNTYW1lRWRpdG9yID0gY3VycmVudEhpc3RvcnlFbnRyeSA9PT0gbnVsbCB8fCBjdXJyZW50SGlzdG9yeUVudHJ5LmVkaXRvciA9PT0gZWRpdG9yO1xuICAgICAgY29uc3Qgc2hvdWxkUHVzaEhpc3RvcnkgPSB0YWdzLmhhcygnaGlzdG9yeS1wdXNoJyk7XG4gICAgICBjb25zdCBzaG91bGRNZXJnZUhpc3RvcnkgPSAhc2hvdWxkUHVzaEhpc3RvcnkgJiYgaXNTYW1lRWRpdG9yICYmIHRhZ3MuaGFzKCdoaXN0b3J5LW1lcmdlJyk7XG4gICAgICBpZiAoc2hvdWxkTWVyZ2VIaXN0b3J5KSB7XG4gICAgICAgIHJldHVybiBISVNUT1JZX01FUkdFO1xuICAgICAgfVxuICAgICAgaWYgKHByZXZFZGl0b3JTdGF0ZSA9PT0gbnVsbCkge1xuICAgICAgICByZXR1cm4gSElTVE9SWV9QVVNIO1xuICAgICAgfVxuICAgICAgY29uc3Qgc2VsZWN0aW9uID0gbmV4dEVkaXRvclN0YXRlLl9zZWxlY3Rpb247XG4gICAgICBjb25zdCBoYXNEaXJ0eU5vZGVzID0gZGlydHlMZWF2ZXMuc2l6ZSA+IDAgfHwgZGlydHlFbGVtZW50cy5zaXplID4gMDtcbiAgICAgIGlmICghaGFzRGlydHlOb2Rlcykge1xuICAgICAgICBpZiAoc2VsZWN0aW9uICE9PSBudWxsKSB7XG4gICAgICAgICAgcmV0dXJuIEhJU1RPUllfTUVSR0U7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIERJU0NBUkRfSElTVE9SWV9DQU5ESURBVEU7XG4gICAgICB9XG4gICAgICBpZiAoc2hvdWxkUHVzaEhpc3RvcnkgPT09IGZhbHNlICYmIGNoYW5nZVR5cGUgIT09IE9USEVSICYmIGNoYW5nZVR5cGUgPT09IHByZXZDaGFuZ2VUeXBlICYmIGNoYW5nZVRpbWUgPCBwcmV2Q2hhbmdlVGltZSArIGRlbGF5ICYmIGlzU2FtZUVkaXRvcikge1xuICAgICAgICByZXR1cm4gSElTVE9SWV9NRVJHRTtcbiAgICAgIH1cblxuICAgICAgLy8gQSBzaW5nbGUgbm9kZSBtaWdodCBoYXZlIGJlZW4gbWFya2VkIGFzIGRpcnR5LCBidXQgbm90IGhhdmUgY2hhbmdlZFxuICAgICAgLy8gZHVlIHRvIHNvbWUgbm9kZSB0cmFuc2Zvcm0gcmV2ZXJ0aW5nIHRoZSBjaGFuZ2UuXG4gICAgICBpZiAoZGlydHlMZWF2ZXMuc2l6ZSA9PT0gMSkge1xuICAgICAgICBjb25zdCBkaXJ0eUxlYWZLZXkgPSBBcnJheS5mcm9tKGRpcnR5TGVhdmVzKVswXTtcbiAgICAgICAgaWYgKGlzVGV4dE5vZGVVbmNoYW5nZWQoZGlydHlMZWFmS2V5LCBwcmV2RWRpdG9yU3RhdGUsIG5leHRFZGl0b3JTdGF0ZSkpIHtcbiAgICAgICAgICByZXR1cm4gSElTVE9SWV9NRVJHRTtcbiAgICAgICAgfVxuICAgICAgfVxuICAgICAgcmV0dXJuIEhJU1RPUllfUFVTSDtcbiAgICB9KSgpO1xuICAgIHByZXZDaGFuZ2VUaW1lID0gY2hhbmdlVGltZTtcbiAgICBwcmV2Q2hhbmdlVHlwZSA9IGNoYW5nZVR5cGU7XG4gICAgcmV0dXJuIG1lcmdlQWN0aW9uO1xuICB9O1xufVxuZnVuY3Rpb24gcmVkbyhlZGl0b3IsIGhpc3RvcnlTdGF0ZSkge1xuICBjb25zdCByZWRvU3RhY2sgPSBoaXN0b3J5U3RhdGUucmVkb1N0YWNrO1xuICBjb25zdCB1bmRvU3RhY2sgPSBoaXN0b3J5U3RhdGUudW5kb1N0YWNrO1xuICBpZiAocmVkb1N0YWNrLmxlbmd0aCAhPT0gMCkge1xuICAgIGNvbnN0IGN1cnJlbnQgPSBoaXN0b3J5U3RhdGUuY3VycmVudDtcbiAgICBpZiAoY3VycmVudCAhPT0gbnVsbCkge1xuICAgICAgdW5kb1N0YWNrLnB1c2goY3VycmVudCk7XG4gICAgICBlZGl0b3IuZGlzcGF0Y2hDb21tYW5kKENBTl9VTkRPX0NPTU1BTkQsIHRydWUpO1xuICAgIH1cbiAgICBjb25zdCBoaXN0b3J5U3RhdGVFbnRyeSA9IHJlZG9TdGFjay5wb3AoKTtcbiAgICBpZiAocmVkb1N0YWNrLmxlbmd0aCA9PT0gMCkge1xuICAgICAgZWRpdG9yLmRpc3BhdGNoQ29tbWFuZChDQU5fUkVET19DT01NQU5ELCBmYWxzZSk7XG4gICAgfVxuICAgIGhpc3RvcnlTdGF0ZS5jdXJyZW50ID0gaGlzdG9yeVN0YXRlRW50cnkgfHwgbnVsbDtcbiAgICBpZiAoaGlzdG9yeVN0YXRlRW50cnkpIHtcbiAgICAgIGhpc3RvcnlTdGF0ZUVudHJ5LmVkaXRvci5zZXRFZGl0b3JTdGF0ZShoaXN0b3J5U3RhdGVFbnRyeS5lZGl0b3JTdGF0ZSwge1xuICAgICAgICB0YWc6ICdoaXN0b3JpYydcbiAgICAgIH0pO1xuICAgIH1cbiAgfVxufVxuZnVuY3Rpb24gdW5kbyhlZGl0b3IsIGhpc3RvcnlTdGF0ZSkge1xuICBjb25zdCByZWRvU3RhY2sgPSBoaXN0b3J5U3RhdGUucmVkb1N0YWNrO1xuICBjb25zdCB1bmRvU3RhY2sgPSBoaXN0b3J5U3RhdGUudW5kb1N0YWNrO1xuICBjb25zdCB1bmRvU3RhY2tMZW5ndGggPSB1bmRvU3RhY2subGVuZ3RoO1xuICBpZiAodW5kb1N0YWNrTGVuZ3RoICE9PSAwKSB7XG4gICAgY29uc3QgY3VycmVudCA9IGhpc3RvcnlTdGF0ZS5jdXJyZW50O1xuICAgIGNvbnN0IGhpc3RvcnlTdGF0ZUVudHJ5ID0gdW5kb1N0YWNrLnBvcCgpO1xuICAgIGlmIChjdXJyZW50ICE9PSBudWxsKSB7XG4gICAgICByZWRvU3RhY2sucHVzaChjdXJyZW50KTtcbiAgICAgIGVkaXRvci5kaXNwYXRjaENvbW1hbmQoQ0FOX1JFRE9fQ09NTUFORCwgdHJ1ZSk7XG4gICAgfVxuICAgIGlmICh1bmRvU3RhY2subGVuZ3RoID09PSAwKSB7XG4gICAgICBlZGl0b3IuZGlzcGF0Y2hDb21tYW5kKENBTl9VTkRPX0NPTU1BTkQsIGZhbHNlKTtcbiAgICB9XG4gICAgaGlzdG9yeVN0YXRlLmN1cnJlbnQgPSBoaXN0b3J5U3RhdGVFbnRyeSB8fCBudWxsO1xuICAgIGlmIChoaXN0b3J5U3RhdGVFbnRyeSkge1xuICAgICAgaGlzdG9yeVN0YXRlRW50cnkuZWRpdG9yLnNldEVkaXRvclN0YXRlKGhpc3RvcnlTdGF0ZUVudHJ5LmVkaXRvclN0YXRlLCB7XG4gICAgICAgIHRhZzogJ2hpc3RvcmljJ1xuICAgICAgfSk7XG4gICAgfVxuICB9XG59XG5mdW5jdGlvbiBjbGVhckhpc3RvcnkoaGlzdG9yeVN0YXRlKSB7XG4gIGhpc3RvcnlTdGF0ZS51bmRvU3RhY2sgPSBbXTtcbiAgaGlzdG9yeVN0YXRlLnJlZG9TdGFjayA9IFtdO1xuICBoaXN0b3J5U3RhdGUuY3VycmVudCA9IG51bGw7XG59XG5cbi8qKlxuICogUmVnaXN0ZXJzIG5lY2Vzc2FyeSBsaXN0ZW5lcnMgdG8gbWFuYWdlIHVuZG8vcmVkbyBoaXN0b3J5IHN0YWNrIGFuZCByZWxhdGVkIGVkaXRvciBjb21tYW5kcy5cbiAqIEl0IHJldHVybnMgYHVucmVnaXN0ZXJgIGNhbGxiYWNrIHRoYXQgY2xlYW5zIHVwIGFsbCBsaXN0ZW5lcnMgYW5kIHNob3VsZCBiZSBjYWxsZWQgb24gZWRpdG9yIHVubW91bnQuXG4gKiBAcGFyYW0gZWRpdG9yIC0gVGhlIGxleGljYWwgZWRpdG9yLlxuICogQHBhcmFtIGhpc3RvcnlTdGF0ZSAtIFRoZSBoaXN0b3J5IHN0YXRlLCBjb250YWluaW5nIHRoZSBjdXJyZW50IHN0YXRlIGFuZCB0aGUgdW5kby9yZWRvIHN0YWNrLlxuICogQHBhcmFtIGRlbGF5IC0gVGhlIHRpbWUgKGluIG1pbGxpc2Vjb25kcykgdGhlIGVkaXRvciBzaG91bGQgZGVsYXkgZ2VuZXJhdGluZyBhIG5ldyBoaXN0b3J5IHN0YWNrLFxuICogaW5zdGVhZCBvZiBtZXJnaW5nIHRoZSBjdXJyZW50IGNoYW5nZXMgd2l0aCB0aGUgY3VycmVudCBzdGFjay5cbiAqIEByZXR1cm5zIFRoZSBsaXN0ZW5lcnMgY2xlYW51cCBjYWxsYmFjayBmdW5jdGlvbi5cbiAqL1xuZnVuY3Rpb24gcmVnaXN0ZXJIaXN0b3J5KGVkaXRvciwgaGlzdG9yeVN0YXRlLCBkZWxheSkge1xuICBjb25zdCBnZXRNZXJnZUFjdGlvbiA9IGNyZWF0ZU1lcmdlQWN0aW9uR2V0dGVyKGVkaXRvciwgZGVsYXkpO1xuICBjb25zdCBhcHBseUNoYW5nZSA9ICh7XG4gICAgZWRpdG9yU3RhdGUsXG4gICAgcHJldkVkaXRvclN0YXRlLFxuICAgIGRpcnR5TGVhdmVzLFxuICAgIGRpcnR5RWxlbWVudHMsXG4gICAgdGFnc1xuICB9KSA9PiB7XG4gICAgY29uc3QgY3VycmVudCA9IGhpc3RvcnlTdGF0ZS5jdXJyZW50O1xuICAgIGNvbnN0IHJlZG9TdGFjayA9IGhpc3RvcnlTdGF0ZS5yZWRvU3RhY2s7XG4gICAgY29uc3QgdW5kb1N0YWNrID0gaGlzdG9yeVN0YXRlLnVuZG9TdGFjaztcbiAgICBjb25zdCBjdXJyZW50RWRpdG9yU3RhdGUgPSBjdXJyZW50ID09PSBudWxsID8gbnVsbCA6IGN1cnJlbnQuZWRpdG9yU3RhdGU7XG4gICAgaWYgKGN1cnJlbnQgIT09IG51bGwgJiYgZWRpdG9yU3RhdGUgPT09IGN1cnJlbnRFZGl0b3JTdGF0ZSkge1xuICAgICAgcmV0dXJuO1xuICAgIH1cbiAgICBjb25zdCBtZXJnZUFjdGlvbiA9IGdldE1lcmdlQWN0aW9uKHByZXZFZGl0b3JTdGF0ZSwgZWRpdG9yU3RhdGUsIGN1cnJlbnQsIGRpcnR5TGVhdmVzLCBkaXJ0eUVsZW1lbnRzLCB0YWdzKTtcbiAgICBpZiAobWVyZ2VBY3Rpb24gPT09IEhJU1RPUllfUFVTSCkge1xuICAgICAgaWYgKHJlZG9TdGFjay5sZW5ndGggIT09IDApIHtcbiAgICAgICAgaGlzdG9yeVN0YXRlLnJlZG9TdGFjayA9IFtdO1xuICAgICAgICBlZGl0b3IuZGlzcGF0Y2hDb21tYW5kKENBTl9SRURPX0NPTU1BTkQsIGZhbHNlKTtcbiAgICAgIH1cbiAgICAgIGlmIChjdXJyZW50ICE9PSBudWxsKSB7XG4gICAgICAgIHVuZG9TdGFjay5wdXNoKHtcbiAgICAgICAgICAuLi5jdXJyZW50XG4gICAgICAgIH0pO1xuICAgICAgICBlZGl0b3IuZGlzcGF0Y2hDb21tYW5kKENBTl9VTkRPX0NPTU1BTkQsIHRydWUpO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAobWVyZ2VBY3Rpb24gPT09IERJU0NBUkRfSElTVE9SWV9DQU5ESURBVEUpIHtcbiAgICAgIHJldHVybjtcbiAgICB9XG5cbiAgICAvLyBFbHNlIHdlIG1lcmdlXG4gICAgaGlzdG9yeVN0YXRlLmN1cnJlbnQgPSB7XG4gICAgICBlZGl0b3IsXG4gICAgICBlZGl0b3JTdGF0ZVxuICAgIH07XG4gIH07XG4gIGNvbnN0IHVucmVnaXN0ZXIgPSBtZXJnZVJlZ2lzdGVyKGVkaXRvci5yZWdpc3RlckNvbW1hbmQoVU5ET19DT01NQU5ELCAoKSA9PiB7XG4gICAgdW5kbyhlZGl0b3IsIGhpc3RvcnlTdGF0ZSk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChSRURPX0NPTU1BTkQsICgpID0+IHtcbiAgICByZWRvKGVkaXRvciwgaGlzdG9yeVN0YXRlKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKENMRUFSX0VESVRPUl9DT01NQU5ELCAoKSA9PiB7XG4gICAgY2xlYXJIaXN0b3J5KGhpc3RvcnlTdGF0ZSk7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9LCBDT01NQU5EX1BSSU9SSVRZX0VESVRPUiksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoQ0xFQVJfSElTVE9SWV9DT01NQU5ELCAoKSA9PiB7XG4gICAgY2xlYXJIaXN0b3J5KGhpc3RvcnlTdGF0ZSk7XG4gICAgZWRpdG9yLmRpc3BhdGNoQ29tbWFuZChDQU5fUkVET19DT01NQU5ELCBmYWxzZSk7XG4gICAgZWRpdG9yLmRpc3BhdGNoQ29tbWFuZChDQU5fVU5ET19DT01NQU5ELCBmYWxzZSk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyVXBkYXRlTGlzdGVuZXIoYXBwbHlDaGFuZ2UpKTtcbiAgcmV0dXJuIHVucmVnaXN0ZXI7XG59XG5cbi8qKlxuICogQ3JlYXRlcyBhbiBlbXB0eSBoaXN0b3J5IHN0YXRlLlxuICogQHJldHVybnMgLSBUaGUgZW1wdHkgaGlzdG9yeSBzdGF0ZSwgYXMgYW4gb2JqZWN0LlxuICovXG5mdW5jdGlvbiBjcmVhdGVFbXB0eUhpc3RvcnlTdGF0ZSgpIHtcbiAgcmV0dXJuIHtcbiAgICBjdXJyZW50OiBudWxsLFxuICAgIHJlZG9TdGFjazogW10sXG4gICAgdW5kb1N0YWNrOiBbXVxuICB9O1xufVxuXG5leHBvcnQgeyBjcmVhdGVFbXB0eUhpc3RvcnlTdGF0ZSwgcmVnaXN0ZXJIaXN0b3J5IH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@lexical+history@0.28.0/node_modules/@lexical/history/LexicalHistory.dev.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposer.dev.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposer.dev.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LexicalComposer: () => (/* binding */ LexicalComposer)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lexical */ \"(app-pages-browser)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst HISTORY_MERGE_OPTIONS = {\n  tag: 'history-merge'\n};\nfunction LexicalComposer({\n  initialConfig,\n  children\n}) {\n  const composerContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const {\n      theme,\n      namespace,\n      nodes,\n      onError,\n      editorState: initialEditorState,\n      html\n    } = initialConfig;\n    const context = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__.createLexicalComposerContext)(null, theme);\n    const editor = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.createEditor)({\n      editable: initialConfig.editable,\n      html,\n      namespace,\n      nodes,\n      onError: error => onError(error, editor),\n      theme\n    });\n    initializeEditor(editor, initialEditorState);\n    return [editor, context];\n  },\n  // We only do this for init\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n  useLayoutEffectImpl(() => {\n    const isEditable = initialConfig.editable;\n    const [editor] = composerContext;\n    editor.setEditable(isEditable !== undefined ? isEditable : true);\n\n    // We only do this for init\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__.LexicalComposerContext.Provider, {\n    value: composerContext,\n    children: children\n  });\n}\nfunction initializeEditor(editor, initialEditorState) {\n  if (initialEditorState === null) {\n    return;\n  } else if (initialEditorState === undefined) {\n    editor.update(() => {\n      const root = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getRoot)();\n      if (root.isEmpty()) {\n        const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$createParagraphNode)();\n        root.append(paragraph);\n        const activeElement = CAN_USE_DOM ? document.activeElement : null;\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getSelection)() !== null || activeElement !== null && activeElement === editor.getRootElement()) {\n          paragraph.select();\n        }\n      }\n    }, HISTORY_MERGE_OPTIONS);\n  } else if (initialEditorState !== null) {\n    switch (typeof initialEditorState) {\n      case 'string':\n        {\n          const parsedEditorState = editor.parseEditorState(initialEditorState);\n          editor.setEditorState(parsedEditorState, HISTORY_MERGE_OPTIONS);\n          break;\n        }\n      case 'object':\n        {\n          editor.setEditorState(initialEditorState, HISTORY_MERGE_OPTIONS);\n          break;\n        }\n      case 'function':\n        {\n          editor.update(() => {\n            const root = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getRoot)();\n            if (root.isEmpty()) {\n              initialEditorState(editor);\n            }\n          }, HISTORY_MERGE_OPTIONS);\n          break;\n        }\n    }\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposer.dev.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalContentEditable.dev.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalContentEditable.dev.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentEditable: () => (/* binding */ ContentEditable)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var _lexical_text__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/text */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+text@0.28.0/node_modules/@lexical/text/LexicalText.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/utils */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n// Source: https://github.com/gregberge/react-merge-refs/blob/main/src/index.tsx\n\nfunction mergeRefs(...refs) {\n  return value => {\n    refs.forEach(ref => {\n      if (typeof ref === 'function') {\n        ref(value);\n      } else if (ref != null) {\n        ref.current = value;\n      }\n    });\n  };\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction ContentEditableElementImpl({\n  editor,\n  ariaActiveDescendant,\n  ariaAutoComplete,\n  ariaControls,\n  ariaDescribedBy,\n  ariaErrorMessage,\n  ariaExpanded,\n  ariaInvalid,\n  ariaLabel,\n  ariaLabelledBy,\n  ariaMultiline,\n  ariaOwns,\n  ariaRequired,\n  autoCapitalize,\n  className,\n  id,\n  role = 'textbox',\n  spellCheck = true,\n  style,\n  tabIndex,\n  'data-testid': testid,\n  ...rest\n}, ref) {\n  const [isEditable, setEditable] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(editor.isEditable());\n  const handleRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(rootElement => {\n    // defaultView is required for a root element.\n    // In multi-window setups, the defaultView may not exist at certain points.\n    if (rootElement && rootElement.ownerDocument && rootElement.ownerDocument.defaultView) {\n      editor.setRootElement(rootElement);\n    } else {\n      editor.setRootElement(null);\n    }\n  }, [editor]);\n  const mergedRefs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => mergeRefs(ref, handleRef), [handleRef, ref]);\n  useLayoutEffectImpl(() => {\n    setEditable(editor.isEditable());\n    return editor.registerEditableListener(currentIsEditable => {\n      setEditable(currentIsEditable);\n    });\n  }, [editor]);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n    \"aria-activedescendant\": isEditable ? ariaActiveDescendant : undefined,\n    \"aria-autocomplete\": isEditable ? ariaAutoComplete : 'none',\n    \"aria-controls\": isEditable ? ariaControls : undefined,\n    \"aria-describedby\": ariaDescribedBy\n    // for compat, only override aria-errormessage if ariaErrorMessage is defined\n    ,\n    ...(ariaErrorMessage != null ? {\n      'aria-errormessage': ariaErrorMessage\n    } : {}),\n    \"aria-expanded\": isEditable && role === 'combobox' ? !!ariaExpanded : undefined\n    // for compat, only override aria-invalid if ariaInvalid is defined\n    ,\n    ...(ariaInvalid != null ? {\n      'aria-invalid': ariaInvalid\n    } : {}),\n    \"aria-label\": ariaLabel,\n    \"aria-labelledby\": ariaLabelledBy,\n    \"aria-multiline\": ariaMultiline,\n    \"aria-owns\": isEditable ? ariaOwns : undefined,\n    \"aria-readonly\": isEditable ? undefined : true,\n    \"aria-required\": ariaRequired,\n    autoCapitalize: autoCapitalize,\n    className: className,\n    contentEditable: isEditable,\n    \"data-testid\": testid,\n    id: id,\n    ref: mergedRefs,\n    role: isEditable ? role : undefined,\n    spellCheck: spellCheck,\n    style: style,\n    tabIndex: tabIndex,\n    ...rest\n  });\n}\nconst ContentEditableElement = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(ContentEditableElementImpl);\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction canShowPlaceholderFromCurrentEditorState(editor) {\n  const currentCanShowPlaceholder = editor.getEditorState().read((0,_lexical_text__WEBPACK_IMPORTED_MODULE_2__.$canShowPlaceholderCurry)(editor.isComposing()));\n  return currentCanShowPlaceholder;\n}\nfunction useCanShowPlaceholder(editor) {\n  const [canShowPlaceholder, setCanShowPlaceholder] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => canShowPlaceholderFromCurrentEditorState(editor));\n  useLayoutEffectImpl(() => {\n    function resetCanShowPlaceholder() {\n      const currentCanShowPlaceholder = canShowPlaceholderFromCurrentEditorState(editor);\n      setCanShowPlaceholder(currentCanShowPlaceholder);\n    }\n    resetCanShowPlaceholder();\n    return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_3__.mergeRegister)(editor.registerUpdateListener(() => {\n      resetCanShowPlaceholder();\n    }), editor.registerEditableListener(() => {\n      resetCanShowPlaceholder();\n    }));\n  }, [editor]);\n  return canShowPlaceholder;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst ContentEditable = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(ContentEditableImpl);\nfunction ContentEditableImpl(props, ref) {\n  const {\n    placeholder,\n    ...rest\n  } = props;\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)();\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ContentEditableElement, {\n      editor: editor,\n      ...rest,\n      ref: ref\n    }), placeholder != null && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Placeholder, {\n      editor: editor,\n      content: placeholder\n    })]\n  });\n}\nfunction Placeholder({\n  content,\n  editor\n}) {\n  const showPlaceholder = useCanShowPlaceholder(editor);\n  const [isEditable, setEditable] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(editor.isEditable());\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    setEditable(editor.isEditable());\n    return editor.registerEditableListener(currentIsEditable => {\n      setEditable(currentIsEditable);\n    });\n  }, [editor]);\n  if (!showPlaceholder) {\n    return null;\n  }\n  let placeholder = null;\n  if (typeof content === 'function') {\n    placeholder = content(isEditable);\n  } else if (content !== null) {\n    placeholder = content;\n  }\n  if (placeholder === null) {\n    return null;\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n    \"aria-hidden\": true,\n    children: placeholder\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalContentEditable.dev.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LexicalErrorBoundary: () => (/* binding */ LexicalErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  _setPrototypeOf(subClass, superClass);\n}\n\nvar changedArray = function changedArray(a, b) {\n  if (a === void 0) {\n    a = [];\n  }\n\n  if (b === void 0) {\n    b = [];\n  }\n\n  return a.length !== b.length || a.some(function (item, index) {\n    return !Object.is(item, b[index]);\n  });\n};\n\nvar initialState = {\n  error: null\n};\n\nvar ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ErrorBoundary, _React$Component);\n\n  function ErrorBoundary() {\n    var _this;\n\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(_args)) || this;\n    _this.state = initialState;\n\n    _this.resetErrorBoundary = function () {\n      var _this$props;\n\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      _this.props.onReset == null ? void 0 : (_this$props = _this.props).onReset.apply(_this$props, args);\n\n      _this.reset();\n    };\n\n    return _this;\n  }\n\n  ErrorBoundary.getDerivedStateFromError = function getDerivedStateFromError(error) {\n    return {\n      error: error\n    };\n  };\n\n  var _proto = ErrorBoundary.prototype;\n\n  _proto.reset = function reset() {\n    this.setState(initialState);\n  };\n\n  _proto.componentDidCatch = function componentDidCatch(error, info) {\n    var _this$props$onError, _this$props2;\n\n    (_this$props$onError = (_this$props2 = this.props).onError) == null ? void 0 : _this$props$onError.call(_this$props2, error, info);\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n    var error = this.state.error;\n    var resetKeys = this.props.resetKeys; // There's an edge case where if the thing that triggered the error\n    // happens to *also* be in the resetKeys array, we'd end up resetting\n    // the error boundary immediately. This would likely trigger a second\n    // error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call\n    // of cDU after the error is set\n\n    if (error !== null && prevState.error !== null && changedArray(prevProps.resetKeys, resetKeys)) {\n      var _this$props$onResetKe, _this$props3;\n\n      (_this$props$onResetKe = (_this$props3 = this.props).onResetKeysChange) == null ? void 0 : _this$props$onResetKe.call(_this$props3, prevProps.resetKeys, resetKeys);\n      this.reset();\n    }\n  };\n\n  _proto.render = function render() {\n    var error = this.state.error;\n    var _this$props4 = this.props,\n        fallbackRender = _this$props4.fallbackRender,\n        FallbackComponent = _this$props4.FallbackComponent,\n        fallback = _this$props4.fallback;\n\n    if (error !== null) {\n      var _props = {\n        error: error,\n        resetErrorBoundary: this.resetErrorBoundary\n      };\n\n      if ( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(fallback)) {\n        return fallback;\n      } else if (typeof fallbackRender === 'function') {\n        return fallbackRender(_props);\n      } else if (FallbackComponent) {\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(FallbackComponent, _props);\n      } else {\n        throw new Error('react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop');\n      }\n    }\n\n    return this.props.children;\n  };\n\n  return ErrorBoundary;\n}(react__WEBPACK_IMPORTED_MODULE_0__.Component);\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction LexicalErrorBoundary({\n  children,\n  onError\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ErrorBoundary, {\n    fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n      style: {\n        border: '1px solid #f00',\n        color: '#f00',\n        padding: '8px'\n      },\n      children: \"An error was thrown.\"\n    }),\n    onError: onError,\n    children: children\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HistoryPlugin: () => (/* binding */ HistoryPlugin),\n/* harmony export */   createEmptyHistoryState: () => (/* reexport safe */ _lexical_history__WEBPACK_IMPORTED_MODULE_0__.createEmptyHistoryState)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_history__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lexical/history */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+history@0.28.0/node_modules/@lexical/history/LexicalHistory.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useHistory(editor, externalHistoryState, delay = 1000) {\n  const historyState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => externalHistoryState || (0,_lexical_history__WEBPACK_IMPORTED_MODULE_0__.createEmptyHistoryState)(), [externalHistoryState]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    return (0,_lexical_history__WEBPACK_IMPORTED_MODULE_0__.registerHistory)(editor, historyState, delay);\n  }, [delay, editor, historyState]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction HistoryPlugin({\n  delay,\n  externalHistoryState\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__.useLexicalComposerContext)();\n  useHistory(editor, externalHistoryState, delay);\n  return null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnChangePlugin: () => (/* binding */ OnChangePlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction OnChangePlugin({\n  ignoreHistoryMergeTagChange = true,\n  ignoreSelectionChange = false,\n  onChange\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__.useLexicalComposerContext)();\n  useLayoutEffectImpl(() => {\n    if (onChange) {\n      return editor.registerUpdateListener(({\n        editorState,\n        dirtyElements,\n        dirtyLeaves,\n        prevEditorState,\n        tags\n      }) => {\n        if (ignoreSelectionChange && dirtyElements.size === 0 && dirtyLeaves.size === 0 || ignoreHistoryMergeTagChange && tags.has('history-merge') || prevEditorState.isEmpty()) {\n          return;\n        }\n        onChange(editorState, editor, tags);\n      });\n    }\n  }, [editor, ignoreHistoryMergeTagChange, ignoreSelectionChange, onChange]);\n  return null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RichTextPlugin: () => (/* binding */ RichTextPlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_useLexicalEditable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/react/useLexicalEditable */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/useLexicalEditable.dev.mjs\");\n/* harmony import */ var _lexical_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/text */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+text@0.28.0/node_modules/@lexical/text/LexicalText.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/utils */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var _lexical_dragon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/dragon */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+dragon@0.28.0/node_modules/@lexical/dragon/LexicalDragon.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @lexical/rich-text */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction canShowPlaceholderFromCurrentEditorState(editor) {\n  const currentCanShowPlaceholder = editor.getEditorState().read((0,_lexical_text__WEBPACK_IMPORTED_MODULE_3__.$canShowPlaceholderCurry)(editor.isComposing()));\n  return currentCanShowPlaceholder;\n}\nfunction useCanShowPlaceholder(editor) {\n  const [canShowPlaceholder, setCanShowPlaceholder] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => canShowPlaceholderFromCurrentEditorState(editor));\n  useLayoutEffectImpl(() => {\n    function resetCanShowPlaceholder() {\n      const currentCanShowPlaceholder = canShowPlaceholderFromCurrentEditorState(editor);\n      setCanShowPlaceholder(currentCanShowPlaceholder);\n    }\n    resetCanShowPlaceholder();\n    return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_4__.mergeRegister)(editor.registerUpdateListener(() => {\n      resetCanShowPlaceholder();\n    }), editor.registerEditableListener(() => {\n      resetCanShowPlaceholder();\n    }));\n  }, [editor]);\n  return canShowPlaceholder;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useDecorators(editor, ErrorBoundary) {\n  const [decorators, setDecorators] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => editor.getDecorators());\n\n  // Subscribe to changes\n  useLayoutEffectImpl(() => {\n    return editor.registerDecoratorListener(nextDecorators => {\n      (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => {\n        setDecorators(nextDecorators);\n      });\n    });\n  }, [editor]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // If the content editable mounts before the subscription is added, then\n    // nothing will be rendered on initial pass. We can get around that by\n    // ensuring that we set the value.\n    setDecorators(editor.getDecorators());\n  }, [editor]);\n\n  // Return decorators defined as React Portals\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const decoratedPortals = [];\n    const decoratorKeys = Object.keys(decorators);\n    for (let i = 0; i < decoratorKeys.length; i++) {\n      const nodeKey = decoratorKeys[i];\n      const reactDecorator = /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ErrorBoundary, {\n        onError: e => editor._onError(e),\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n          fallback: null,\n          children: decorators[nodeKey]\n        })\n      });\n      const element = editor.getElementByKey(nodeKey);\n      if (element !== null) {\n        decoratedPortals.push(/*#__PURE__*/(0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(reactDecorator, element, nodeKey));\n      }\n    }\n    return decoratedPortals;\n  }, [ErrorBoundary, decorators, editor]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useRichTextSetup(editor) {\n  useLayoutEffectImpl(() => {\n    return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_4__.mergeRegister)((0,_lexical_rich_text__WEBPACK_IMPORTED_MODULE_5__.registerRichText)(editor), (0,_lexical_dragon__WEBPACK_IMPORTED_MODULE_6__.registerDragonSupport)(editor));\n\n    // We only do this for init\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [editor]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction RichTextPlugin({\n  contentEditable,\n  // TODO Remove. This property is now part of ContentEditable\n  placeholder = null,\n  ErrorBoundary\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_7__.useLexicalComposerContext)();\n  const decorators = useDecorators(editor, ErrorBoundary);\n  useRichTextSetup(editor);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n    children: [contentEditable, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Placeholder, {\n      content: placeholder\n    }), decorators]\n  });\n}\n\n// TODO remove\nfunction Placeholder({\n  content\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_7__.useLexicalComposerContext)();\n  const showPlaceholder = useCanShowPlaceholder(editor);\n  const editable = (0,_lexical_react_useLexicalEditable__WEBPACK_IMPORTED_MODULE_8__.useLexicalEditable)();\n  if (!showPlaceholder) {\n    return null;\n  }\n  if (typeof content === 'function') {\n    return content(editable);\n  } else {\n    return content;\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@lexical+text@0.28.0/node_modules/@lexical/text/LexicalText.dev.mjs":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+text@0.28.0/node_modules/@lexical/text/LexicalText.dev.mjs ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $canShowPlaceholder: () => (/* binding */ $canShowPlaceholder),\n/* harmony export */   $canShowPlaceholderCurry: () => (/* binding */ $canShowPlaceholderCurry),\n/* harmony export */   $findTextIntersectionFromCharacters: () => (/* binding */ $findTextIntersectionFromCharacters),\n/* harmony export */   $isRootTextContentEmpty: () => (/* binding */ $isRootTextContentEmpty),\n/* harmony export */   $isRootTextContentEmptyCurry: () => (/* binding */ $isRootTextContentEmptyCurry),\n/* harmony export */   $rootTextContent: () => (/* binding */ $rootTextContent),\n/* harmony export */   registerLexicalTextEntity: () => (/* binding */ registerLexicalTextEntity)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(app-pages-browser)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Returns the root's text content.\n * @returns The root's text content.\n */\nfunction $rootTextContent() {\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  return root.getTextContent();\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Determines if the root has any text content and can trim any whitespace if it does.\n * @param isEditorComposing - Is the editor in composition mode due to an active Input Method Editor?\n * @param trim - Should the root text have its whitespaced trimmed? Defaults to true.\n * @returns true if text content is empty, false if there is text or isEditorComposing is true.\n */\nfunction $isRootTextContentEmpty(isEditorComposing, trim = true) {\n  if (isEditorComposing) {\n    return false;\n  }\n  let text = $rootTextContent();\n  if (trim) {\n    text = text.trim();\n  }\n  return text === '';\n}\n\n/**\n * Returns a function that executes {@link $isRootTextContentEmpty}\n * @param isEditorComposing - Is the editor in composition mode due to an active Input Method Editor?\n * @param trim - Should the root text have its whitespaced trimmed? Defaults to true.\n * @returns A function that executes $isRootTextContentEmpty based on arguments.\n */\nfunction $isRootTextContentEmptyCurry(isEditorComposing, trim) {\n  return () => $isRootTextContentEmpty(isEditorComposing, trim);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Determines if the input should show the placeholder. If anything is in\n * in the root the placeholder should not be shown.\n * @param isComposing - Is the editor in composition mode due to an active Input Method Editor?\n * @returns true if the input should show the placeholder, false otherwise.\n */\nfunction $canShowPlaceholder(isComposing) {\n  if (!$isRootTextContentEmpty(isComposing, false)) {\n    return false;\n  }\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  const children = root.getChildren();\n  const childrenLength = children.length;\n  if (childrenLength > 1) {\n    return false;\n  }\n  for (let i = 0; i < childrenLength; i++) {\n    const topBlock = children[i];\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(topBlock)) {\n      return false;\n    }\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(topBlock)) {\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isParagraphNode)(topBlock)) {\n        return false;\n      }\n      if (topBlock.__indent !== 0) {\n        return false;\n      }\n      const topBlockChildren = topBlock.getChildren();\n      const topBlockChildrenLength = topBlockChildren.length;\n      for (let s = 0; s < topBlockChildrenLength; s++) {\n        const child = topBlockChildren[i];\n        if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(child)) {\n          return false;\n        }\n      }\n    }\n  }\n  return true;\n}\n\n/**\n * Returns a function that executes {@link $canShowPlaceholder}\n * @param isEditorComposing - Is the editor in composition mode due to an active Input Method Editor?\n * @returns A function that executes $canShowPlaceholder with arguments.\n */\nfunction $canShowPlaceholderCurry(isEditorComposing) {\n  return () => $canShowPlaceholder(isEditorComposing);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Finds a TextNode with a size larger than targetCharacters and returns\n * the node along with the remaining length of the text.\n * @param root - The RootNode.\n * @param targetCharacters - The number of characters whose TextNode must be larger than.\n * @returns The TextNode and the intersections offset, or null if no TextNode is found.\n */\nfunction $findTextIntersectionFromCharacters(root, targetCharacters) {\n  let node = root.getFirstChild();\n  let currentCharacters = 0;\n  mainLoop: while (node !== null) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n      const child = node.getFirstChild();\n      if (child !== null) {\n        node = child;\n        continue;\n      }\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n      const characters = node.getTextContentSize();\n      if (currentCharacters + characters > targetCharacters) {\n        return {\n          node,\n          offset: targetCharacters - currentCharacters\n        };\n      }\n      currentCharacters += characters;\n    }\n    const sibling = node.getNextSibling();\n    if (sibling !== null) {\n      node = sibling;\n      continue;\n    }\n    let parent = node.getParent();\n    while (parent !== null) {\n      const parentSibling = parent.getNextSibling();\n      if (parentSibling !== null) {\n        node = parentSibling;\n        continue mainLoop;\n      }\n      parent = parent.getParent();\n    }\n    break;\n  }\n  return null;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Returns a tuple that can be rested (...) into mergeRegister to clean up\n * node transforms listeners that transforms text into another node, eg. a HashtagNode.\n * @example\n * ```ts\n *   useEffect(() => {\n    return mergeRegister(\n      ...registerLexicalTextEntity(editor, getMatch, targetNode, createNode),\n    );\n  }, [createNode, editor, getMatch, targetNode]);\n * ```\n * Where targetNode is the type of node containing the text you want to transform (like a text input),\n * then getMatch uses a regex to find a matching text and creates the proper node to include the matching text.\n * @param editor - The lexical editor.\n * @param getMatch - Finds a matching string that satisfies a regex expression.\n * @param targetNode - The node type that contains text to match with. eg. HashtagNode\n * @param createNode - A function that creates a new node to contain the matched text. eg createHashtagNode\n * @returns An array containing the plain text and reverse node transform listeners.\n */\nfunction registerLexicalTextEntity(editor, getMatch, targetNode, createNode) {\n  const isTargetNode = node => {\n    return node instanceof targetNode;\n  };\n  const $replaceWithSimpleText = node => {\n    const textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(node.getTextContent());\n    textNode.setFormat(node.getFormat());\n    node.replace(textNode);\n  };\n  const getMode = node => {\n    return node.getLatest().__mode;\n  };\n  const $textNodeTransform = node => {\n    if (!node.isSimpleText()) {\n      return;\n    }\n    let prevSibling = node.getPreviousSibling();\n    let text = node.getTextContent();\n    let currentNode = node;\n    let match;\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevSibling)) {\n      const previousText = prevSibling.getTextContent();\n      const combinedText = previousText + text;\n      const prevMatch = getMatch(combinedText);\n      if (isTargetNode(prevSibling)) {\n        if (prevMatch === null || getMode(prevSibling) !== 0) {\n          $replaceWithSimpleText(prevSibling);\n          return;\n        } else {\n          const diff = prevMatch.end - previousText.length;\n          if (diff > 0) {\n            const concatText = text.slice(0, diff);\n            const newTextContent = previousText + concatText;\n            prevSibling.select();\n            prevSibling.setTextContent(newTextContent);\n            if (diff === text.length) {\n              node.remove();\n            } else {\n              const remainingText = text.slice(diff);\n              node.setTextContent(remainingText);\n            }\n            return;\n          }\n        }\n      } else if (prevMatch === null || prevMatch.start < previousText.length) {\n        return;\n      }\n    }\n    let prevMatchLengthToSkip = 0;\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n      match = getMatch(text);\n      let nextText = match === null ? '' : text.slice(match.end);\n      text = nextText;\n      if (nextText === '') {\n        const nextSibling = currentNode.getNextSibling();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(nextSibling)) {\n          nextText = currentNode.getTextContent() + nextSibling.getTextContent();\n          const nextMatch = getMatch(nextText);\n          if (nextMatch === null) {\n            if (isTargetNode(nextSibling)) {\n              $replaceWithSimpleText(nextSibling);\n            } else {\n              nextSibling.markDirty();\n            }\n            return;\n          } else if (nextMatch.start !== 0) {\n            return;\n          }\n        }\n      }\n      if (match === null) {\n        return;\n      }\n      if (match.start === 0 && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevSibling) && prevSibling.isTextEntity()) {\n        prevMatchLengthToSkip += match.end;\n        continue;\n      }\n      let nodeToReplace;\n      if (match.start === 0) {\n        [nodeToReplace, currentNode] = currentNode.splitText(match.end);\n      } else {\n        [, nodeToReplace, currentNode] = currentNode.splitText(match.start + prevMatchLengthToSkip, match.end + prevMatchLengthToSkip);\n      }\n      if (!(nodeToReplace !== undefined)) {\n        formatDevErrorMessage(`${'nodeToReplace'} should not be undefined. You may want to check splitOffsets passed to the splitText.`);\n      }\n      const replacementNode = createNode(nodeToReplace);\n      replacementNode.setFormat(nodeToReplace.getFormat());\n      nodeToReplace.replace(replacementNode);\n      if (currentNode == null) {\n        return;\n      }\n      prevMatchLengthToSkip = 0;\n      prevSibling = replacementNode;\n    }\n  };\n  const $reverseNodeTransform = node => {\n    const text = node.getTextContent();\n    const match = getMatch(text);\n    if (match === null || match.start !== 0) {\n      $replaceWithSimpleText(node);\n      return;\n    }\n    if (text.length > match.end) {\n      // This will split out the rest of the text as simple text\n      node.splitText(match.end);\n      return;\n    }\n    const prevSibling = node.getPreviousSibling();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevSibling) && prevSibling.isTextEntity()) {\n      $replaceWithSimpleText(prevSibling);\n      $replaceWithSimpleText(node);\n    }\n    const nextSibling = node.getNextSibling();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(nextSibling) && nextSibling.isTextEntity()) {\n      $replaceWithSimpleText(nextSibling);\n\n      // This may have already been converted in the previous block\n      if (isTargetNode(node)) {\n        $replaceWithSimpleText(node);\n      }\n    }\n  };\n  const removePlainTextTransform = editor.registerNodeTransform(lexical__WEBPACK_IMPORTED_MODULE_0__.TextNode, $textNodeTransform);\n  const removeReverseNodeTransform = editor.registerNodeTransform(targetNode, $reverseNodeTransform);\n  return [removePlainTextTransform, removeReverseNodeTransform];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@lexical+text@0.28.0/node_modules/@lexical/text/LexicalText.dev.mjs\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/Field-6OZARKAL.js":
/*!*****************************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/Field-6OZARKAL.js ***!
  \*****************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RichText: () => (/* binding */ cs)\n/* harmony export */ });\n/* harmony import */ var _chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./chunk-KZKGNMS3.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/chunk-KZKGNMS3.js\");\n/* harmony import */ var _chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./chunk-BZZVLW4U.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/chunk-BZZVLW4U.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/jsx-runtime.js\");\n/* harmony import */ var _payloadcms_ui__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! @payloadcms/ui */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+ui@3.43.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_payload@3.43.0_rea_ktz7gjeu6woombyossqsn5mdmm/node_modules/@payloadcms/ui/dist/exports/client/index.js\");\n/* harmony import */ var _payloadcms_ui_shared__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! @payloadcms/ui/shared */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+ui@3.43.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_payload@3.43.0_rea_ktz7gjeu6woombyossqsn5mdmm/node_modules/@payloadcms/ui/dist/exports/shared/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react_error_boundary__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! react-error-boundary */ \"(app-pages-browser)/../../node_modules/.pnpm/react-error-boundary@4.1.2_react@19.1.0/node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js\");\n/* harmony import */ var _bundled_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bundled.css */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/bundled.css\");\n/* harmony import */ var _lexical_react_LexicalComposer_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! @lexical/react/LexicalComposer.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposer.dev.mjs\");\n/* harmony import */ var react_compiler_runtime__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! react/compiler-runtime */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/compiler-runtime.js\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalErrorBoundary_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @lexical/react/LexicalErrorBoundary.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalHistoryPlugin_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! @lexical/react/LexicalHistoryPlugin.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalOnChangePlugin_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! @lexical/react/LexicalOnChangePlugin.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalRichTextPlugin_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @lexical/react/LexicalRichTextPlugin.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! lexical */ \"(app-pages-browser)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @lexical/utils */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! react-dom */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react-dom/index.js\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @lexical/rich-text */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/* harmony import */ var _payloadcms_ui__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @payloadcms/ui */ \"(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+ui@3.43.0_@types+react@19.1.0_monaco-editor@0.52.2_next@15.3.0_payload@3.43.0_rea_ktz7gjeu6woombyossqsn5mdmm/node_modules/@payloadcms/ui/dist/exports/client/chunk-TIQCV7VX.js\");\n/* harmony import */ var _lexical_react_LexicalContentEditable_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @lexical/react/LexicalContentEditable.js */ \"(app-pages-browser)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalContentEditable.dev.mjs\");\n/* __next_internal_client_entry_do_not_use__ RichText auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nvar $ = (param)=>{\n    let { anchorElem: t, clientProps: e, plugin: o } = param;\n    return o.position === \"floatingAnchorElem\" && t ? o.Component && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(o.Component, {\n        anchorElem: t,\n        clientProps: e\n    }) : o.Component && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(o.Component, {\n        clientProps: e\n    });\n};\n\n\n\n\n\nfunction _e() {\n    let t = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_3__.c)(3), [e] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)(), o = Gt, r, n;\n    return t[0] !== e ? (r = ()=>(0,_lexical_utils__WEBPACK_IMPORTED_MODULE_5__.mergeRegister)(e.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_6__.CLICK_COMMAND, Vt, lexical__WEBPACK_IMPORTED_MODULE_6__.COMMAND_PRIORITY_LOW), e.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_6__.KEY_DELETE_COMMAND, o, lexical__WEBPACK_IMPORTED_MODULE_6__.COMMAND_PRIORITY_LOW), e.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_6__.KEY_BACKSPACE_COMMAND, o, lexical__WEBPACK_IMPORTED_MODULE_6__.COMMAND_PRIORITY_LOW), e.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_6__.SELECTION_CHANGE_COMMAND, Yt, lexical__WEBPACK_IMPORTED_MODULE_6__.COMMAND_PRIORITY_LOW), e.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_6__.KEY_ARROW_UP_COMMAND, zt, lexical__WEBPACK_IMPORTED_MODULE_6__.COMMAND_PRIORITY_LOW), e.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_6__.KEY_ARROW_DOWN_COMMAND, Ht, lexical__WEBPACK_IMPORTED_MODULE_6__.COMMAND_PRIORITY_LOW)), n = [\n        e\n    ], t[0] = e, t[1] = r, t[2] = n) : (r = t[1], n = t[2]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(r, n), null;\n}\nfunction Ht(t) {\n    let e = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isNodeSelection)(e)) {\n        var _e_getNodes_, _ne;\n        t.preventDefault();\n        let i = (_e_getNodes_ = e.getNodes()[0]) === null || _e_getNodes_ === void 0 ? void 0 : _e_getNodes_.getNextSibling();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isDecoratorNode)(i)) {\n            let y = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getEditor)().getElementByKey(i.getKey());\n            return y && Z({\n                element: y,\n                node: i\n            }), !0;\n        }\n        if (!(0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isElementNode)(i)) return !0;\n        var _i_getFirstDescendant;\n        let s = (_i_getFirstDescendant = i.getFirstDescendant()) !== null && _i_getFirstDescendant !== void 0 ? _i_getFirstDescendant : i;\n        return s && ((_ne = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_5__.$findMatchingParent)(s, se)) === null || _ne === void 0 ? void 0 : _ne.selectEnd(), t.preventDefault()), !0;\n    }\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isRangeSelection)(e)) return !1;\n    let r = (e.isBackward() ? e.anchor : e.focus).getNode(), n = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_5__.$findMatchingParent)(r, jt), c = n === null || n === void 0 ? void 0 : n.getNextSibling();\n    if (!n || c !== Oe(n)) return !1;\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isDecoratorNode)(c)) {\n        let i = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getEditor)().getElementByKey(c.getKey());\n        if (i) return Z({\n            element: i,\n            node: c\n        }), t.preventDefault(), !0;\n    }\n    return !1;\n}\n_c = Ht;\nfunction jt(t) {\n    return Oe(t) !== null;\n}\nfunction zt(t) {\n    let e = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isNodeSelection)(e)) {\n        var _e_getNodes_, _ne;\n        let i = (_e_getNodes_ = e.getNodes()[0]) === null || _e_getNodes_ === void 0 ? void 0 : _e_getNodes_.getPreviousSibling();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isDecoratorNode)(i)) {\n            let y = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getEditor)().getElementByKey(i.getKey());\n            return y ? (Z({\n                element: y,\n                node: i\n            }), t.preventDefault(), !0) : !1;\n        }\n        if (!(0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isElementNode)(i)) return !1;\n        var _i_getLastDescendant;\n        let s = (_i_getLastDescendant = i.getLastDescendant()) !== null && _i_getLastDescendant !== void 0 ? _i_getLastDescendant : i;\n        return s ? ((_ne = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_5__.$findMatchingParent)(s, se)) === null || _ne === void 0 ? void 0 : _ne.selectStart(), t.preventDefault(), !0) : !1;\n    }\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isRangeSelection)(e)) return !1;\n    let r = (e.isBackward() ? e.anchor : e.focus).getNode(), n = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_5__.$findMatchingParent)(r, Wt), c = n === null || n === void 0 ? void 0 : n.getPreviousSibling();\n    if (!n || c !== Fe(n)) return !1;\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isDecoratorNode)(c)) {\n        let i = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getEditor)().getElementByKey(c.getKey());\n        if (i) return Z({\n            element: i,\n            node: c\n        }), t.preventDefault(), !0;\n    }\n    return !1;\n}\nfunction Wt(t) {\n    return Fe(t) !== null;\n}\n_c1 = Wt;\nfunction Yt() {\n    var _document_querySelector, _t_element;\n    let t = Jt();\n    return (_document_querySelector = document.querySelector(\".decorator-selected\")) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.classList.remove(\"decorator-selected\"), t ? ((_t_element = t.element) === null || _t_element === void 0 ? void 0 : _t_element.classList.add(\"decorator-selected\"), !0) : !1;\n}\n_c2 = Yt;\nfunction Vt(t) {\n    var _document_querySelector;\n    (_document_querySelector = document.querySelector(\".decorator-selected\")) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.classList.remove(\"decorator-selected\");\n    let e = qt(t);\n    if (!e) return !0;\n    let { target: o } = t;\n    return !(o instanceof HTMLElement) || o.isContentEditable || o.closest('button, textarea, input, .react-select, .code-editor, .no-select-decorator, [role=\"button\"]') ? (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$setSelection)(null) : Z(e), !0;\n}\n_c3 = Vt;\nfunction Gt(t) {\n    let e = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getSelection)();\n    return (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isNodeSelection)(e) ? (t.preventDefault(), e.getNodes().forEach(Ut), !0) : !1;\n}\n_c4 = Gt;\nfunction Ut(t) {\n    t.remove();\n}\n_c5 = Ut;\nfunction qt(t) {\n    if (!(t.target instanceof HTMLElement)) return;\n    let e = t.target.closest('[data-lexical-decorator=\"true\"]');\n    if (!(e instanceof HTMLElement)) return;\n    let o = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getNearestNodeFromDOMNode)(e);\n    return (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isDecoratorNode)(o) ? {\n        element: e,\n        node: o\n    } : void 0;\n}\nfunction Jt() {\n    let t = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isNodeSelection)(t)) return;\n    let e = t.getNodes();\n    if (e.length !== 1) return;\n    let o = e[0];\n    return (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isDecoratorNode)(o) ? {\n        decorator: o,\n        element: (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getEditor)().getElementByKey(o.getKey())\n    } : void 0;\n}\n_c6 = Jt;\nfunction Z(param) {\n    let { element: t, node: e } = param;\n    var _document_querySelector;\n    (_document_querySelector = document.querySelector(\".decorator-selected\")) === null || _document_querySelector === void 0 ? void 0 : _document_querySelector.classList.remove(\"decorator-selected\");\n    let o = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$createNodeSelection)();\n    o.add(e.getKey()), (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$setSelection)(o), t.scrollIntoView({\n        behavior: \"smooth\",\n        block: \"nearest\"\n    }), t.classList.add(\"decorator-selected\");\n}\n_c7 = Z;\nfunction se(t) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isDecoratorNode)(t) && !t.isInline()) return !0;\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isElementNode)(t) || (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isRootOrShadowRoot)(t)) return !1;\n    let e = t.getFirstChild(), o = e === null || (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isLineBreakNode)(e) || (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isTextNode)(e) || e.isInline();\n    return !t.isInline() && t.canBeEmpty() !== !1 && o;\n}\nfunction Oe(t) {\n    let e = t.getNextSibling();\n    for(; e !== null;){\n        if (se(e)) return e;\n        e = e.getNextSibling();\n    }\n    return null;\n}\n_c8 = Oe;\nfunction Fe(t) {\n    let e = t.getPreviousSibling();\n    for(; e !== null;){\n        if (se(e)) return e;\n        e = e.getPreviousSibling();\n    }\n    return null;\n}\n_c9 = Fe;\n\n\n\n\n\n\nvar Y = function(t, e, o, r) {\n    let n = arguments.length > 4 && arguments[4] !== void 0 ? arguments[4] : 50, c = arguments.length > 5 && arguments[5] !== void 0 ? arguments[5] : 25;\n    let i = 0;\n    if (t && !t.contains(r)) {\n        let { bottom: s, left: u, right: y, top: g } = t.getBoundingClientRect(), a = g + window.scrollY, l = s + window.scrollY;\n        if (o < a - c || o > l + c || e < u - n || e > y + n) return -1;\n        (e < u || e > y) && (i = e < u ? e - u : e - y);\n    }\n    return i;\n};\n_c10 = Y;\n\nfunction V(t) {\n    let e = t.getBoundingClientRect(), o = getComputedStyle(t).getPropertyValue(\"transform\");\n    if (!o || o === \"none\") return e;\n    let r = o.split(\",\").pop();\n    return e.y = e.y - Number(r === null || r === void 0 ? void 0 : r.replace(\")\", \"\")), e;\n}\n_c11 = V;\nfunction le(t) {\n    let e = (u, y)=>u ? parseFloat(window.getComputedStyle(u)[y]) : 0, { marginBottom: o, marginTop: r } = window.getComputedStyle(t), n = e(t.previousElementSibling, \"marginBottom\"), c = e(t.nextElementSibling, \"marginTop\"), i = Math.max(parseFloat(r), n);\n    return {\n        marginBottom: Math.max(parseFloat(o), c),\n        marginTop: i\n    };\n}\n\nfunction H(t) {\n    return t.getEditorState().read(()=>(0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getRoot)().getChildrenKeys());\n}\n_c12 = H;\nvar Xt = 1, Zt = -1, $e = 0, A = {\n    props: null,\n    result: null\n};\nfunction eo(t, e) {\n    let o = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 20;\n    let r = t.x - e.x, n = t.y - e.y;\n    return r * r + n * n <= o * o;\n}\nfunction G(t) {\n    let { anchorElem: e, cache_threshold: o = 20, editor: r, fuzzy: n = !1, horizontalOffset: c = 0, point: { x: i, y: s }, startIndex: u = 0, useEdgeAsDefault: y = !1 } = t;\n    if (o > 0 && A.props && A.result && A.props.fuzzy === t.fuzzy && A.props.horizontalOffset === t.horizontalOffset && A.props.useEdgeAsDefault === t.useEdgeAsDefault && eo(A.props.point, t.point, o)) return A.result;\n    let g = e.getBoundingClientRect(), a = H(r), l = {\n        blockElem: null,\n        blockNode: null,\n        distance: 1 / 0,\n        foundAtIndex: -1,\n        isFoundNodeEmptyParagraph: !1\n    };\n    return r.getEditorState().read(()=>{\n        if (y) {\n            let m = r.getElementByKey(a[0]), h = r.getElementByKey(a[a.length - 1]);\n            if (m && h) {\n                let [d, f] = [\n                    V(m),\n                    V(h)\n                ];\n                if (s < d.top ? (l.blockElem = m, l.distance = d.top - s, l.blockNode = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getNodeByKey)(a[0]), l.foundAtIndex = 0) : s > f.bottom && (l.distance = s - f.bottom, l.blockNode = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getNodeByKey)(a[a.length - 1]), l.blockElem = h, l.foundAtIndex = a.length - 1), l === null || l === void 0 ? void 0 : l.blockElem) return {\n                    blockElem: null,\n                    isFoundNodeEmptyParagraph: !1\n                };\n            }\n        }\n        let p = u, x = $e;\n        for(; p >= 0 && p < a.length;){\n            let m = a[p], h = r.getElementByKey(m);\n            if (h === null) break;\n            let d = new _chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.f(i + c, s), f = _chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.k.fromDOMRect(V(h)), { marginBottom: E, marginTop: w } = le(h), S = f.generateNewRect({\n                bottom: f.bottom + E,\n                left: g.left,\n                right: g.right,\n                top: f.top - w\n            }), { distance: b, isOnBottomSide: k, isOnTopSide: N } = S.distanceFromPoint(d);\n            if (b === 0) {\n                l.blockElem = h, l.blockNode = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getNodeByKey)(m), l.foundAtIndex = p, l.distance = b, l.blockNode && l.blockNode.getType() === \"paragraph\" && l.blockNode.getTextContent() === \"\" && (!n && !t.returnEmptyParagraphs && (l.blockElem = null, l.blockNode = null), l.isFoundNodeEmptyParagraph = !0);\n                break;\n            } else n && b < l.distance && (l.blockElem = h, l.blockNode = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getNodeByKey)(m), l.distance = b, l.foundAtIndex = p);\n            x === $e && (N ? x = Zt : k ? x = Xt : x = 1 / 0), p += x;\n        }\n    }), A.props = t, A.result = {\n        blockElem: l.blockElem,\n        blockNode: l.blockNode,\n        foundAtIndex: l.foundAtIndex,\n        isFoundNodeEmptyParagraph: l.isFoundNodeEmptyParagraph\n    }, {\n        blockElem: l.blockElem,\n        blockNode: l.blockNode,\n        foundAtIndex: l.foundAtIndex,\n        isFoundNodeEmptyParagraph: l.isFoundNodeEmptyParagraph\n    };\n}\n_c13 = G;\nfunction ce(t, e) {\n    return !!t.closest(\".\".concat(e));\n}\nvar to = [\n    \"IMG\",\n    \"INPUT\",\n    \"TEXTAREA\",\n    \"SELECT\",\n    \"BUTTON\",\n    \"VIDEO\",\n    \"OBJECT\",\n    \"EMBED\",\n    \"IFRAME\",\n    \"HR\"\n];\nfunction Ke(t) {\n    if (!t || to.includes(t.tagName) || t.offsetHeight === 0 || t.offsetWidth === 0) return !1;\n    let e = window.getComputedStyle(t);\n    return !(e.display === \"table-cell\" || e.position === \"absolute\" || e.visibility === \"hidden\" || e.opacity === \"0\");\n}\n_c14 = Ke;\nfunction ue(t, e, o) {\n    let r = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : 0;\n    if (!t) {\n        e.style.opacity = \"0\", e.style.transform = \"translate(-10000px, -10000px)\";\n        return;\n    }\n    let n = t.getBoundingClientRect(), c = window.getComputedStyle(t), i = e.getBoundingClientRect(), s = o.getBoundingClientRect(), u;\n    if ([\n        \"lexical-block\",\n        \"lexical-upload\",\n        \"lexical-relationship\"\n    ].some((a)=>{\n        var _t_firstElementChild;\n        return (_t_firstElementChild = t.firstElementChild) === null || _t_firstElementChild === void 0 ? void 0 : _t_firstElementChild.classList.contains(a);\n    })) u = n.top + 8 - s.top;\n    else {\n        let a = Ke(t) ? parseInt(c.lineHeight, 10) : 0;\n        u = n.top + (a - i.height) / 2 - s.top;\n    }\n    let g = r;\n    e.style.opacity = \"1\", e.style.transform = \"translate(\".concat(g, \"px, \").concat(u, \"px)\");\n}\nvar co = \"add-block-menu\", de = 1 / 0;\nfunction uo(t) {\n    return t === 0 ? 1 / 0 : de >= 0 && de < t ? de : Math.floor(t / 2);\n}\nfunction mo(t, e, o) {\n    var _n_admin;\n    let r = e.parentElement, { editorConfig: n } = (0,_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__.b)(), c = (n === null || n === void 0 ? void 0 : (_n_admin = n.admin) === null || _n_admin === void 0 ? void 0 : _n_admin.hideGutter) ? -24 : 12, i = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), [s, u] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _document;\n        function g(a) {\n            let l = a.target;\n            if (!(0,lexical__WEBPACK_IMPORTED_MODULE_6__.isHTMLElement)(l)) return;\n            let p = Y(r, a.pageX, a.pageY, l);\n            if (p === -1) {\n                u(null);\n                return;\n            }\n            if (ce(l, co)) return;\n            let x = H(t), { blockElem: m, blockNode: h, foundAtIndex: d } = G({\n                anchorElem: e,\n                cache_threshold: 0,\n                editor: t,\n                horizontalOffset: -p,\n                point: new _chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.f(a.x, a.y),\n                returnEmptyParagraphs: !0,\n                startIndex: uo(x.length),\n                useEdgeAsDefault: !1\n            });\n            de = d, m && h && ((s === null || s === void 0 ? void 0 : s.node) !== h || (s === null || s === void 0 ? void 0 : s.elem) !== m) && u({\n                elem: m,\n                node: h\n            });\n        }\n        return (_document = document) === null || _document === void 0 ? void 0 : _document.addEventListener(\"mousemove\", g), ()=>{\n            var _document;\n            (_document = document) === null || _document === void 0 ? void 0 : _document.removeEventListener(\"mousemove\", g);\n        };\n    }, [\n        r,\n        e,\n        t,\n        s\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        i.current && (s === null || s === void 0 ? void 0 : s.node) && ue(s === null || s === void 0 ? void 0 : s.elem, i.current, e, c);\n    }, [\n        e,\n        s,\n        c\n    ]);\n    let y = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((g)=>{\n        let a = s;\n        (a === null || a === void 0 ? void 0 : a.node) && (t.update(()=>{\n            let l = !0;\n            if (((a === null || a === void 0 ? void 0 : a.node.getType()) !== \"paragraph\" || a.node.getTextContent() !== \"\") && (l = !1), !l) {\n                let p = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$createParagraphNode)();\n                a === null || a === void 0 ? void 0 : a.node.insertAfter(p), setTimeout(()=>{\n                    a = {\n                        elem: t.getElementByKey(p.getKey()),\n                        node: p\n                    }, u(a);\n                }, 0);\n            }\n        }), setTimeout(()=>{\n            t.update(()=>{\n                t.focus(), (a === null || a === void 0 ? void 0 : a.node) && \"select\" in a.node && typeof a.node.select == \"function\" && a.node.select();\n            });\n        }, 1), setTimeout(()=>{\n            t.dispatchCommand(_chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.i, {\n                node: a === null || a === void 0 ? void 0 : a.node\n            });\n        }, 2), g.stopPropagation(), g.preventDefault());\n    }, [\n        t,\n        s\n    ]);\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_7__.createPortal)((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"button\", {\n            \"aria-label\": \"Add block\",\n            className: \"icon add-block-menu\",\n            onClick: (g)=>{\n                y(g);\n            },\n            ref: i,\n            type: \"button\",\n            children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: o ? \"icon\" : \"\"\n            })\n        })\n    }), e);\n}\nfunction ze(t) {\n    let { anchorElem: e } = t, o = e === void 0 ? document.body : e, [r] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)();\n    return mo(r, o, r._editable);\n}\n\n\n\n\n\n\n\nvar We = 0, fo = -24;\nvar j = 0;\nfunction Ye(t, e, o, r, n, c, i, s, u) {\n    let y = arguments.length > 9 && arguments[9] !== void 0 ? arguments[9] : !1;\n    let { height: g, top: a } = r.getBoundingClientRect(), { top: l, width: p } = i.getBoundingClientRect(), { marginBottom: x, marginTop: m } = le(r), h = a, d = c >= a + g / 2 + window.scrollY, f = !1;\n    if (n === null || n === void 0 ? void 0 : n.elem) if (r !== (n === null || n === void 0 ? void 0 : n.elem)) (d && (n === null || n === void 0 ? void 0 : n.elem) && (n === null || n === void 0 ? void 0 : n.elem) === r.nextElementSibling || !d && (n === null || n === void 0 ? void 0 : n.elem) && (n === null || n === void 0 ? void 0 : n.elem) === r.previousElementSibling) && (j++, j < 200 && (f = !0));\n    else {\n        var _n_boundingBox;\n        j++;\n        let b = n === null || n === void 0 ? void 0 : (_n_boundingBox = n.boundingBox) === null || _n_boundingBox === void 0 ? void 0 : _n_boundingBox.y, k = r.getBoundingClientRect().y;\n        (d === (n === null || n === void 0 ? void 0 : n.isBelow) && b === k || j < 200) && (f = !1);\n    }\n    if (f) return {\n        isBelow: d,\n        willStayInSamePosition: f\n    };\n    y ? h += g / 2 : d ? h += g + x / 2 : h -= m / 2;\n    let E = 0;\n    y || (d ? E = -We : E = We);\n    let w = h - l + E, S = fo - e;\n    return o.style.width = \"calc(\".concat(p, \"px - \").concat(t, \")\"), o.style.opacity = \".8\", o.style.transform = \"translate(\".concat(S, \"px, calc(\").concat(w, \"px - 2px))\"), (n === null || n === void 0 ? void 0 : n.elem) && (n.elem.style.opacity = \"\", (n === null || n === void 0 ? void 0 : n.elem) === r ? d ? n.elem.style.marginTop = \"\" : n.elem.style.marginBottom = \"\" : (n.elem.style.marginBottom = \"\", n.elem.style.marginTop = \"\")), j = 0, {\n        isBelow: d,\n        willStayInSamePosition: f\n    };\n}\n_c15 = Ye;\nvar xo = \"draggable-block-menu\", qe = \"application/x-lexical-drag-block\", ee = 1 / 0;\nfunction Je(t) {\n    return t === 0 ? 1 / 0 : ee >= 0 && ee < t ? ee : Math.floor(t / 2);\n}\n_c16 = Je;\nfunction Eo(t, e) {\n    let { transform: o } = e.style;\n    t.setDragImage(e, 0, 0), setTimeout(()=>{\n        e.style.transform = o;\n    });\n}\n_c17 = Eo;\nfunction Qe(t, e) {\n    t && (t.style.opacity = \"0\"), e && (e.style.opacity = \"\", e.style.marginBottom = \"\", e.style.marginTop = \"\");\n}\n_c18 = Qe;\nfunction Co(t, e, o) {\n    var _l_admin, _l_admin1;\n    let r = e.parentElement, n = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), c = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), i = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null), s = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(!1), [u, y] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), [g, a] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), { editorConfig: l } = (0,_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__.b)(), p = (l === null || l === void 0 ? void 0 : (_l_admin = l.admin) === null || _l_admin === void 0 ? void 0 : _l_admin.hideGutter) ? -44 : -8;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        var _document;\n        function h(d) {\n            let f = d.target;\n            if (!(0,lexical__WEBPACK_IMPORTED_MODULE_6__.isHTMLElement)(f)) return;\n            let E = Y(r, d.pageX, d.pageY, f);\n            if (E === -1) {\n                y(null);\n                return;\n            }\n            if (ce(f, xo)) return;\n            let w = H(t), { blockElem: S, foundAtIndex: b, isFoundNodeEmptyParagraph: k } = G({\n                anchorElem: e,\n                cache_threshold: 0,\n                editor: t,\n                horizontalOffset: -E,\n                point: new _chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.f(d.x, d.y),\n                startIndex: Je(w.length),\n                useEdgeAsDefault: !1,\n                verbose: !1\n            });\n            ee = b, !(!S && !k) && u !== S && y(S);\n        }\n        return (_document = document) === null || _document === void 0 ? void 0 : _document.addEventListener(\"mousemove\", h), ()=>{\n            var _document;\n            (_document = document) === null || _document === void 0 ? void 0 : _document.removeEventListener(\"mousemove\", h);\n        };\n    }, [\n        r,\n        e,\n        t,\n        u\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        n.current && ue(u, n.current, e, p);\n    }, [\n        e,\n        u,\n        p\n    ]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        function h(f) {\n            if (!s.current) return !1;\n            let [E] = (0,_lexical_rich_text__WEBPACK_IMPORTED_MODULE_10__.eventFiles)(f);\n            if (E) return !1;\n            let { pageY: w, target: S } = f;\n            if (!(0,lexical__WEBPACK_IMPORTED_MODULE_6__.isHTMLElement)(S)) return !1;\n            let b = Y(r, f.pageX, f.pageY, S, 100, 50), k = H(t), { blockElem: N, foundAtIndex: L, isFoundNodeEmptyParagraph: I } = G({\n                anchorElem: e,\n                editor: t,\n                fuzzy: !0,\n                horizontalOffset: -b,\n                point: new _chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.f(f.x, f.y),\n                startIndex: Je(k.length),\n                useEdgeAsDefault: !0,\n                verbose: !0\n            });\n            ee = L;\n            let v = c.current;\n            if (N === null || v === null) return !1;\n            if (u !== N) {\n                var _l_admin, _l_admin1, _n_current_getBoundingClientRect, _n_current, _n_current_getBoundingClientRect1, _n_current1;\n                var _n_current_getBoundingClientRect_width, _n_current_getBoundingClientRect_width1;\n                let { isBelow: T, willStayInSamePosition: J } = Ye((l === null || l === void 0 ? void 0 : (_l_admin = l.admin) === null || _l_admin === void 0 ? void 0 : _l_admin.hideGutter) ? \"0px\" : \"3rem\", p + ((l === null || l === void 0 ? void 0 : (_l_admin1 = l.admin) === null || _l_admin1 === void 0 ? void 0 : _l_admin1.hideGutter) ? (_n_current_getBoundingClientRect_width = n === null || n === void 0 ? void 0 : (_n_current = n.current) === null || _n_current === void 0 ? void 0 : (_n_current_getBoundingClientRect = _n_current.getBoundingClientRect()) === null || _n_current_getBoundingClientRect === void 0 ? void 0 : _n_current_getBoundingClientRect.width) !== null && _n_current_getBoundingClientRect_width !== void 0 ? _n_current_getBoundingClientRect_width : 0 : -((_n_current_getBoundingClientRect_width1 = n === null || n === void 0 ? void 0 : (_n_current1 = n.current) === null || _n_current1 === void 0 ? void 0 : (_n_current_getBoundingClientRect1 = _n_current1.getBoundingClientRect()) === null || _n_current_getBoundingClientRect1 === void 0 ? void 0 : _n_current_getBoundingClientRect1.width) !== null && _n_current_getBoundingClientRect_width1 !== void 0 ? _n_current_getBoundingClientRect_width1 : 0)), v, N, g, w, e, f, i, I);\n                f.preventDefault(), J || a({\n                    boundingBox: N.getBoundingClientRect(),\n                    elem: N,\n                    isBelow: T\n                });\n            } else (g === null || g === void 0 ? void 0 : g.elem) && (Qe(v, g.elem), a({\n                boundingBox: N.getBoundingClientRect(),\n                elem: N,\n                isBelow: !1\n            }));\n            return !0;\n        }\n        function d(f) {\n            if (!s.current) return !1;\n            let [E] = (0,_lexical_rich_text__WEBPACK_IMPORTED_MODULE_10__.eventFiles)(f);\n            if (E) return !1;\n            let { dataTransfer: w, pageY: S, target: b } = f, k = (w === null || w === void 0 ? void 0 : w.getData(qe)) || \"\";\n            return t.update(()=>{\n                let N = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getNodeByKey)(k);\n                if (!N || !(0,lexical__WEBPACK_IMPORTED_MODULE_6__.isHTMLElement)(b)) return !1;\n                let L = Y(r, f.pageX, f.pageY, b, 100, 50), { blockElem: I, isFoundNodeEmptyParagraph: v } = G({\n                    anchorElem: e,\n                    editor: t,\n                    fuzzy: !0,\n                    horizontalOffset: -L,\n                    point: new _chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.f(f.x, f.y),\n                    useEdgeAsDefault: !0\n                });\n                if (!I) return !1;\n                let T = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getNearestNodeFromDOMNode)(I);\n                if (!T) return !1;\n                if (T === N) return !0;\n                let { height: J, top: he } = V(I), z = S >= he + J / 2 + window.scrollY;\n                v ? (T.insertBefore(N), T.remove()) : z ? T.insertAfter(N) : T.insertBefore(N), u !== null && y(null), document.querySelectorAll(\".lexical-block-highlighter\").forEach((D)=>{\n                    D.remove();\n                });\n                let oe = t.getElementByKey(N.getKey());\n                setTimeout(()=>{\n                    let D = oe === null || oe === void 0 ? void 0 : oe.getBoundingClientRect();\n                    if (!D) return;\n                    let P = document.createElement(\"div\");\n                    P.className = \"lexical-block-highlighter\", P.style.backgroundColor = \"var(--theme-elevation-1000\", P.style.transition = \"opacity 0.5s ease-in-out\", P.style.zIndex = \"1\", P.style.pointerEvents = \"none\", P.style.boxSizing = \"border-box\", P.style.borderRadius = \"4px\", P.style.position = \"absolute\", document.body.appendChild(P), P.style.opacity = \"0.1\", P.style.height = \"\".concat(D.height + 8, \"px\"), P.style.width = \"\".concat(D.width + 8, \"px\"), P.style.top = \"\".concat(D.top + window.scrollY - 4, \"px\"), P.style.left = \"\".concat(D.left - 4, \"px\"), setTimeout(()=>{\n                        P.style.opacity = \"0\", setTimeout(()=>{\n                            P.remove();\n                        }, 500);\n                    }, 1e3);\n                }, 120);\n            }), !0;\n        }\n        return document.addEventListener(\"dragover\", h), document.addEventListener(\"drop\", d), ()=>{\n            document.removeEventListener(\"dragover\", h), document.removeEventListener(\"drop\", d);\n        };\n    }, [\n        r,\n        p,\n        e,\n        t,\n        g,\n        u,\n        l === null || l === void 0 ? void 0 : (_l_admin1 = l.admin) === null || _l_admin1 === void 0 ? void 0 : _l_admin1.hideGutter\n    ]);\n    function x(h) {\n        let d = h.dataTransfer;\n        if (!d || !u) return;\n        Eo(d, u);\n        let f = \"\";\n        t.update(()=>{\n            let E = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getNearestNodeFromDOMNode)(u);\n            E && (f = E.getKey());\n        }), s.current = !0, d.setData(qe, f);\n    }\n    function m() {\n        s.current = !1, (g === null || g === void 0 ? void 0 : g.elem) && Qe(c.current, g === null || g === void 0 ? void 0 : g.elem);\n    }\n    return /*#__PURE__*/ (0,react_dom__WEBPACK_IMPORTED_MODULE_7__.createPortal)((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: \"icon draggable-block-menu\",\n                draggable: !0,\n                onDragEnd: m,\n                onDragStart: x,\n                ref: n,\n                children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                    className: o ? \"icon\" : \"\"\n                })\n            }),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: \"draggable-block-target-line\",\n                ref: c\n            }),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: \"debug-highlight\",\n                ref: i\n            })\n        ]\n    }), e);\n}\n_c19 = Co;\nfunction Ze(t) {\n    let { anchorElem: e } = t, o = e === void 0 ? document.body : e, [r] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)();\n    return Co(r, o, r._editable);\n}\n_c20 = Ze;\n\n\n\n\n\nvar et = \"insert-paragraph-at-end\", tt = ()=>{\n    var _o_admin;\n    let t = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_3__.c)(4), [e] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)(), { editorConfig: o } = (0,_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__.b)();\n    if (o === null || o === void 0 ? void 0 : (_o_admin = o.admin) === null || _o_admin === void 0 ? void 0 : _o_admin.hideInsertParagraphAtEnd) return null;\n    let r;\n    t[0] !== e ? (r = ()=>{\n        e.update(Ro);\n    }, t[0] = e, t[1] = r) : r = t[1];\n    let n = r, c;\n    return t[2] !== n ? (c = (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n        \"aria-label\": \"Insert Paragraph\",\n        className: et,\n        onClick: n,\n        role: \"button\",\n        tabIndex: 0,\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n            className: \"\".concat(et, \"-inside\"),\n            children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                children: \"+\"\n            })\n        })\n    }), t[2] = n, t[3] = c) : c = t[3], c;\n};\nfunction Ro() {\n    let t = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$createParagraphNode)();\n    (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getRoot)().append(t), t.select();\n}\n_c21 = Ro;\n\n\n\nvar nt = ()=>{\n    _s();\n    let t = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_3__.c)(4), { editorConfig: e } = (0,_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__.b)(), [o] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)(), r, n;\n    return t[0] !== o || t[1] !== e.features.markdownTransformers ? (r = ()=>{\n        var _e_features_markdownTransformers;\n        return (0,_chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.c)(o, (_e_features_markdownTransformers = e.features.markdownTransformers) !== null && _e_features_markdownTransformers !== void 0 ? _e_features_markdownTransformers : []);\n    }, n = [\n        o,\n        e.features.markdownTransformers\n    ], t[0] = o, t[1] = e.features.markdownTransformers, t[2] = r, t[3] = n) : (r = t[2], n = t[3]), react__WEBPACK_IMPORTED_MODULE_1__.useEffect(r, n), null;\n};\n_s(nt, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n\n\n\nfunction rt() {\n    let [t] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>t.registerNodeTransform(lexical__WEBPACK_IMPORTED_MODULE_6__.RootNode, (e)=>{\n            let o = (0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getSelection)();\n            if ((0,lexical__WEBPACK_IMPORTED_MODULE_6__.$isRangeSelection)(o)) {\n                let r = o.anchor.getNode(), n = o.focus.getNode();\n                (!r.isAttached() || !n.isAttached()) && (e.selectEnd(), console.warn(\"updateEditor: selection has been moved to the end of the editor because the previously selected nodes have been removed and selection wasn't moved to another node. Ensure selection changes after removing/replacing a selected node.\"));\n            }\n            return !1;\n        }), [\n        t\n    ]), null;\n}\n\n\n\nfunction it() {\n    let [t] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)();\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>t.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_6__.SELECT_ALL_COMMAND, ()=>{\n            if ((0,lexical__WEBPACK_IMPORTED_MODULE_6__.$getSelection)()) return !1;\n            let o = document.activeElement;\n            return o instanceof HTMLInputElement && o.select(), !0;\n        }, lexical__WEBPACK_IMPORTED_MODULE_6__.COMMAND_PRIORITY_LOW), [\n        t\n    ]), null;\n}\n\n\n\n\n\n\n\n\nfunction st(t, e) {\n    let o = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_3__.c)(4), { maxLength: r, minLength: n } = e, c = r === void 0 ? 75 : r, i = n === void 0 ? 1 : n, s;\n    return o[0] !== c || o[1] !== i || o[2] !== t ? (s = (u)=>{\n        let { query: y } = u, g = \"[^\" + t + _chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.h + \"\\\\s]\", l = new RegExp(\"(^|\\\\s|\\\\()([\" + t + \"]((?:\" + g + \"){0,\" + c + \"}))$\").exec(y);\n        if (l !== null) {\n            let p = l[1], x = l[3];\n            if (x.length >= i) return {\n                leadOffset: l.index + p.length,\n                matchingString: x,\n                replaceableString: l[2]\n            };\n        }\n        return null;\n    }, o[0] = c, o[1] = i, o[2] = t, o[3] = s) : s = o[3], s;\n}\nvar B = \"slash-menu-popup\";\nfunction zo(param) {\n    let { isSelected: t, item: e, onClick: o, onMouseEnter: r, ref: n } = param;\n    let { fieldProps: { featureClientSchemaMap: c, schemaPath: i } } = (0,_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__.b)(), { i18n: s } = (0,_payloadcms_ui__WEBPACK_IMPORTED_MODULE_11__.d)(), u = \"\".concat(B, \"__item \").concat(B, \"__item-\").concat(e.key);\n    t && (u += \" \".concat(B, \"__item--selected\"));\n    let y = e.key;\n    return e.label && (y = typeof e.label == \"function\" ? e.label({\n        featureClientSchemaMap: c,\n        i18n: s,\n        schemaPath: i\n    }) : e.label), y.length > 25 && (y = y.substring(0, 25) + \"...\"), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"button\", {\n        \"aria-selected\": t,\n        className: u,\n        id: B + \"__item-\" + e.key,\n        onClick: o,\n        onMouseEnter: r,\n        ref: n,\n        role: \"option\",\n        tabIndex: -1,\n        type: \"button\",\n        children: [\n            (e === null || e === void 0 ? void 0 : e.Icon) && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(e.Icon, {}),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"span\", {\n                className: \"\".concat(B, \"__item-text\"),\n                children: y\n            })\n        ]\n    }, e.key);\n}\nfunction ut(param) {\n    let { anchorElem: t = document.body } = param;\n    let [e] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)(), [o, r] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), { editorConfig: n } = (0,_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__.b)(), { i18n: c } = (0,_payloadcms_ui__WEBPACK_IMPORTED_MODULE_11__.d)(), { fieldProps: { featureClientSchemaMap: i, schemaPath: s } } = (0,_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__.b)(), u = st(\"/\", {\n        minLength: 0\n    }), y = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)(()=>{\n        let a = [];\n        for (let l of n.features.slashMenu.dynamicGroups)if (o) {\n            let p = l({\n                editor: e,\n                queryString: o\n            });\n            a = a.concat(p);\n        }\n        return a;\n    }, [\n        e,\n        o,\n        n === null || n === void 0 ? void 0 : n.features\n    ]), g = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        let a = [];\n        var _n_features_slashMenu_groups;\n        for (let l of (_n_features_slashMenu_groups = n === null || n === void 0 ? void 0 : n.features.slashMenu.groups) !== null && _n_features_slashMenu_groups !== void 0 ? _n_features_slashMenu_groups : [])a.push(l);\n        if (o) {\n            a = a.map((p)=>{\n                let x = p.items.filter((m)=>{\n                    let h = m.key;\n                    return m.label && (h = typeof m.label == \"function\" ? m.label({\n                        featureClientSchemaMap: i,\n                        i18n: c,\n                        schemaPath: s\n                    }) : m.label), new RegExp(o, \"gi\").exec(h) ? !0 : m.keywords != null ? m.keywords.some((d)=>new RegExp(o, \"gi\").exec(d)) : !1;\n                });\n                return x.length ? {\n                    ...p,\n                    items: x\n                } : null;\n            }), a = a.filter((p)=>p != null);\n            let l = y();\n            for (let p of l){\n                var _x_items;\n                let x = a.find((m)=>m.key === p.key);\n                x ? a = a.filter((m)=>m.key !== p.key) : x = {\n                    ...p,\n                    items: []\n                }, (x === null || x === void 0 ? void 0 : (_x_items = x.items) === null || _x_items === void 0 ? void 0 : _x_items.length) && (x.items = x.items.concat(x.items)), a.push(x);\n            }\n        }\n        return a;\n    }, [\n        o,\n        n === null || n === void 0 ? void 0 : n.features.slashMenu.groups,\n        y,\n        i,\n        c,\n        s\n    ]);\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.j, {\n        anchorElem: t,\n        groups: g,\n        menuRenderFn: (a, param)=>{\n            let { selectedItemKey: l, selectItemAndCleanUp: p, setSelectedItemKey: x } = param;\n            return a.current && g.length ? /*#__PURE__*/ react_dom__WEBPACK_IMPORTED_MODULE_7__.createPortal((0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                className: B,\n                children: g.map((m)=>{\n                    let h = m.key;\n                    return m.label && i && (h = typeof m.label == \"function\" ? m.label({\n                        featureClientSchemaMap: i,\n                        i18n: c,\n                        schemaPath: s\n                    }) : m.label), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                        className: \"\".concat(B, \"__group \").concat(B, \"__group-\").concat(m.key),\n                        children: [\n                            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: \"\".concat(B, \"__group-title\"),\n                                children: h\n                            }),\n                            m.items.map((d, f)=>(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(zo, {\n                                    index: f,\n                                    isSelected: l === d.key,\n                                    item: d,\n                                    onClick: ()=>{\n                                        x(d.key), p(d);\n                                    },\n                                    onMouseEnter: ()=>{\n                                        x(d.key);\n                                    },\n                                    ref: (E)=>{\n                                        d.ref = {\n                                            current: E\n                                        };\n                                    }\n                                }, d.key))\n                        ]\n                    }, m.key);\n                })\n            }), a.current) : null;\n        },\n        onQueryChange: r,\n        triggerFn: u\n    });\n}\n\n\n\n\nfunction dt(t) {\n    let e = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_3__.c)(6), { features: o } = t, [r] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)(), n;\n    e[0] !== r || e[1] !== o.enabledFormats ? (n = ()=>{\n        let i = qo(o.enabledFormats);\n        if (i.length !== 0) return r.registerNodeTransform(lexical__WEBPACK_IMPORTED_MODULE_6__.TextNode, (s)=>{\n            i.forEach((u)=>{\n                s.hasFormat(u) && s.toggleFormat(u);\n            });\n        });\n    }, e[0] = r, e[1] = o.enabledFormats, e[2] = n) : n = e[2];\n    let c;\n    return e[3] !== r || e[4] !== o ? (c = [\n        r,\n        o\n    ], e[3] = r, e[4] = o, e[5] = c) : c = e[5], (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(n, c), null;\n}\nfunction qo(t) {\n    let e = Object.keys(lexical__WEBPACK_IMPORTED_MODULE_6__.TEXT_TYPE_TO_FORMAT), o = new Set(t);\n    return e.filter((r)=>!o.has(r));\n}\n\n\n\n\n\nfunction ft(t) {\n    var _r_admin;\n    let e = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_3__.c)(7), { className: o, editorConfig: r } = t, { t: n } = (0,_payloadcms_ui__WEBPACK_IMPORTED_MODULE_11__.d)(), c;\n    if (e[0] !== o || e[1] !== (r === null || r === void 0 ? void 0 : (_r_admin = r.admin) === null || _r_admin === void 0 ? void 0 : _r_admin.placeholder) || e[2] !== n) {\n        var _r_admin1, _r_admin2, _r_admin3, _r_admin4;\n        let i;\n        var _r_admin_placeholder;\n        e[4] !== (r === null || r === void 0 ? void 0 : (_r_admin1 = r.admin) === null || _r_admin1 === void 0 ? void 0 : _r_admin1.placeholder) || e[5] !== n ? (i = (_r_admin_placeholder = r === null || r === void 0 ? void 0 : (_r_admin2 = r.admin) === null || _r_admin2 === void 0 ? void 0 : _r_admin2.placeholder) !== null && _r_admin_placeholder !== void 0 ? _r_admin_placeholder : n(\"lexical:general:placeholder\"), e[4] = r === null || r === void 0 ? void 0 : (_r_admin3 = r.admin) === null || _r_admin3 === void 0 ? void 0 : _r_admin3.placeholder, e[5] = n, e[6] = i) : i = e[6], c = (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_lexical_react_LexicalContentEditable_js__WEBPACK_IMPORTED_MODULE_12__.ContentEditable, {\n            \"aria-placeholder\": n(\"lexical:general:placeholder\"),\n            className: o !== null && o !== void 0 ? o : \"ContentEditable__root\",\n            placeholder: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n                className: \"editor-placeholder\",\n                children: i\n            })\n        }), e[0] = o, e[1] = r === null || r === void 0 ? void 0 : (_r_admin4 = r.admin) === null || _r_admin4 === void 0 ? void 0 : _r_admin4.placeholder, e[2] = n, e[3] = c;\n    } else c = e[3];\n    return c;\n}\nvar gt = (t)=>{\n    let e = (0,react_compiler_runtime__WEBPACK_IMPORTED_MODULE_3__.c)(19), { editorConfig: o, editorContainerRef: r, isSmallWidthViewport: n, onChange: c } = t, i = (0,_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__.b)(), [s] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)(), [u, y] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null), g;\n    e[0] === Symbol.for(\"react.memo_cache_sentinel\") ? (g = (m)=>{\n        m !== null && y(m);\n    }, e[0] = g) : g = e[0];\n    let a = g, l, p;\n    e[1] !== s || e[2] !== i ? (l = ()=>{\n        var _i_parentEditor, _i_parentEditor1;\n        if (!(i === null || i === void 0 ? void 0 : i.uuid)) {\n            console.error(\"Lexical Editor must be used within an EditorConfigProvider\");\n            return;\n        }\n        (i === null || i === void 0 ? void 0 : (_i_parentEditor = i.parentEditor) === null || _i_parentEditor === void 0 ? void 0 : _i_parentEditor.uuid) && ((_i_parentEditor1 = i.parentEditor) === null || _i_parentEditor1 === void 0 ? void 0 : _i_parentEditor1.registerChild(i.uuid, i));\n        let m = ()=>{\n            i.focusEditor(i);\n        }, h = ()=>{\n            i.blurEditor(i);\n        }, d = s.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_6__.FOCUS_COMMAND, ()=>(m(), !0), lexical__WEBPACK_IMPORTED_MODULE_6__.COMMAND_PRIORITY_LOW), f = s.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_6__.BLUR_COMMAND, ()=>(h(), !0), lexical__WEBPACK_IMPORTED_MODULE_6__.COMMAND_PRIORITY_LOW);\n        return ()=>{\n            var _i_parentEditor_unregisterChild, _i_parentEditor;\n            d(), f(), (_i_parentEditor = i.parentEditor) === null || _i_parentEditor === void 0 ? void 0 : (_i_parentEditor_unregisterChild = _i_parentEditor.unregisterChild) === null || _i_parentEditor_unregisterChild === void 0 ? void 0 : _i_parentEditor_unregisterChild.call(_i_parentEditor, i.uuid);\n        };\n    }, p = [\n        s,\n        i\n    ], e[1] = s, e[2] = i, e[3] = l, e[4] = p) : (l = e[3], p = e[4]), (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(l, p);\n    let x;\n    if (e[5] !== s || e[6] !== o || e[7] !== r || e[8] !== u || e[9] !== n || e[10] !== c) {\n        var _o_features_plugins, _o_features_plugins1, _o_features_plugins2, _o_features_markdownTransformers, _o_features, _o_features_plugins3, _o_features_plugins4, _o_features_plugins5;\n        let m;\n        e[12] !== c ? (m = (d, f, E)=>{\n            (!E.has(\"focus\") || E.size > 1) && (c === null || c === void 0 ? void 0 : c(d, f, E));\n        }, e[12] = c, e[13] = m) : m = e[13];\n        let h;\n        e[14] !== s || e[15] !== o.features.plugins || e[16] !== u || e[17] !== n ? (h = u && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n            children: [\n                !n && s.isEditable() && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                    children: [\n                        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(Ze, {\n                            anchorElem: u\n                        }),\n                        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ze, {\n                            anchorElem: u\n                        })\n                    ]\n                }),\n                (_o_features_plugins = o.features.plugins) === null || _o_features_plugins === void 0 ? void 0 : _o_features_plugins.map((d)=>{\n                    if (d.position === \"floatingAnchorElem\" && !(d.desktopOnly === !0 && n)) return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)($, {\n                        anchorElem: u,\n                        clientProps: d.clientProps,\n                        plugin: d\n                    }, d.key);\n                }),\n                s.isEditable() && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                    children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ut, {\n                        anchorElem: u\n                    })\n                })\n            ]\n        }), e[14] = s, e[15] = o.features.plugins, e[16] = u, e[17] = n, e[18] = h) : h = e[18], x = (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n            children: [\n                (_o_features_plugins1 = o.features.plugins) === null || _o_features_plugins1 === void 0 ? void 0 : _o_features_plugins1.map(un),\n                (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                    className: \"editor-container\",\n                    ref: r,\n                    children: [\n                        (_o_features_plugins2 = o.features.plugins) === null || _o_features_plugins2 === void 0 ? void 0 : _o_features_plugins2.map(dn),\n                        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_lexical_react_LexicalRichTextPlugin_js__WEBPACK_IMPORTED_MODULE_13__.RichTextPlugin, {\n                            contentEditable: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                className: \"editor-scroller\",\n                                children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"div\", {\n                                    className: \"editor\",\n                                    ref: a,\n                                    children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ft, {\n                                        editorConfig: o\n                                    })\n                                })\n                            }),\n                            ErrorBoundary: _lexical_react_LexicalErrorBoundary_js__WEBPACK_IMPORTED_MODULE_14__.LexicalErrorBoundary\n                        }),\n                        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(rt, {}),\n                        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(tt, {}),\n                        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_e, {}),\n                        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(dt, {\n                            features: o.features\n                        }),\n                        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(it, {}),\n                        (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_lexical_react_LexicalOnChangePlugin_js__WEBPACK_IMPORTED_MODULE_15__.OnChangePlugin, {\n                            ignoreSelectionChange: !0,\n                            onChange: m\n                        }),\n                        h,\n                        s.isEditable() && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n                            children: [\n                                (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_lexical_react_LexicalHistoryPlugin_js__WEBPACK_IMPORTED_MODULE_16__.HistoryPlugin, {}),\n                                (o === null || o === void 0 ? void 0 : (_o_features = o.features) === null || _o_features === void 0 ? void 0 : (_o_features_markdownTransformers = _o_features.markdownTransformers) === null || _o_features_markdownTransformers === void 0 ? void 0 : _o_features_markdownTransformers.length) > 0 && (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(nt, {})\n                            ]\n                        }),\n                        (_o_features_plugins3 = o.features.plugins) === null || _o_features_plugins3 === void 0 ? void 0 : _o_features_plugins3.map(mn),\n                        (_o_features_plugins4 = o.features.plugins) === null || _o_features_plugins4 === void 0 ? void 0 : _o_features_plugins4.map(fn)\n                    ]\n                }),\n                (_o_features_plugins5 = o.features.plugins) === null || _o_features_plugins5 === void 0 ? void 0 : _o_features_plugins5.map(pn)\n            ]\n        }), e[5] = s, e[6] = o, e[7] = r, e[8] = u, e[9] = n, e[10] = c, e[11] = x;\n    } else x = e[11];\n    return x;\n};\nfunction un(t) {\n    if (t.position === \"aboveContainer\") return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)($, {\n        clientProps: t.clientProps,\n        plugin: t\n    }, t.key);\n}\nfunction dn(t) {\n    if (t.position === \"top\") return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)($, {\n        clientProps: t.clientProps,\n        plugin: t\n    }, t.key);\n}\nfunction mn(t) {\n    if (t.position === \"normal\") return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)($, {\n        clientProps: t.clientProps,\n        plugin: t\n    }, t.key);\n}\nfunction fn(t) {\n    if (t.position === \"bottom\") return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)($, {\n        clientProps: t.clientProps,\n        plugin: t\n    }, t.key);\n}\nfunction pn(t) {\n    if (t.position === \"belowContainer\") return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)($, {\n        clientProps: t.clientProps,\n        plugin: t\n    }, t.key);\n}\nvar yt = (param)=>{\n    let { children: t, providers: e } = param;\n    if (!(e === null || e === void 0 ? void 0 : e.length)) return t;\n    let o = e[0];\n    return e.length > 1 ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(o, {\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(yt, {\n            providers: e.slice(1),\n            children: t\n        })\n    }) : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(o, {\n        children: t\n    });\n}, xt = (t)=>{\n    _s1();\n    let { composerKey: e, editorConfig: o, fieldProps: r, isSmallWidthViewport: n, onChange: c, readOnly: i, value: s } = t, u = (0,_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__.b)(), y = (0,_payloadcms_ui__WEBPACK_IMPORTED_MODULE_17__.useEditDepth)(), g = react__WEBPACK_IMPORTED_MODULE_1__.useRef(null), a = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>{\n        if (s && typeof s != \"object\") throw new Error(\"The value passed to the Lexical editor is not an object. This is not supported. Please remove the data from the field and start again. This is the value that was passed in: \" + JSON.stringify(s));\n        if (s && Array.isArray(s) && !(\"root\" in s)) throw new Error(\"You have tried to pass in data from the old Slate editor to the new Lexical editor. The data structure is different, thus you will have to migrate your data. We offer a one-line migration script which migrates all your rich text fields: https://payloadcms.com/docs/lexical/migration#migration-via-migration-script-recommended\");\n        if (s && \"jsonContent\" in s) throw new Error(\"You have tried to pass in data from payload-plugin-lexical. The data structure is different, thus you will have to migrate your data. Migration guide: https://payloadcms.com/docs/lexical/migration#migrating-from-payload-plugin-lexical\");\n        return {\n            editable: i !== !0,\n            editorState: s != null ? JSON.stringify(s) : void 0,\n            namespace: o.lexical.namespace,\n            nodes: (0,_chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.l)({\n                editorConfig: o\n            }),\n            onError: (l)=>{\n                throw l;\n            },\n            theme: o.lexical.theme\n        };\n    }, [\n        o\n    ]);\n    return a ? (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_lexical_react_LexicalComposer_js__WEBPACK_IMPORTED_MODULE_18__.LexicalComposer, {\n        initialConfig: a,\n        children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_chunk_BZZVLW4U_js__WEBPACK_IMPORTED_MODULE_9__.a, {\n            editorConfig: o,\n            editorContainerRef: g,\n            fieldProps: r,\n            parentContext: (u === null || u === void 0 ? void 0 : u.editDepth) === y ? u : void 0,\n            children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(yt, {\n                providers: o.features.providers,\n                children: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(gt, {\n                    editorConfig: o,\n                    editorContainerRef: g,\n                    isSmallWidthViewport: n,\n                    onChange: c\n                })\n            })\n        })\n    }, e + a.editable) : (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n        children: \"Loading...\"\n    });\n};\n_s1(xt, \"9onNMVqjoAdY6r9VTthePjMIitY=\");\nvar pe = \"rich-text-lexical\", wn = (t)=>{\n    var _e_admin;\n    _s2();\n    let { editorConfig: e, field: o, field: { admin: { className: r, description: n, readOnly: c } = {}, label: i, localized: s, required: u }, path: y, readOnly: g, validate: a } = t, l = g || c, p = (0,_payloadcms_ui__WEBPACK_IMPORTED_MODULE_17__.useEditDepth)(), x = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((M, Q)=>typeof a == \"function\" ? a(M, {\n            ...Q,\n            required: u\n        }) : !0, [\n        a,\n        u\n    ]), { customComponents: { AfterInput: m, BeforeInput: h, Description: d, Error: f, Label: E } = {}, disabled: w, initialValue: S, path: b, setValue: k, showError: N, value: L } = (0,_payloadcms_ui__WEBPACK_IMPORTED_MODULE_17__.useField)({\n        potentiallyStalePath: y,\n        validate: x\n    }), I = l || w, [v, T] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(!1), [J, he] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(), ye = react__WEBPACK_IMPORTED_MODULE_1__.useRef(S), z = react__WEBPACK_IMPORTED_MODULE_1__.useRef(L);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        let M = ()=>{\n            let Q = window.matchMedia(\"(max-width: 768px)\").matches;\n            Q !== v && T(Q);\n        };\n        return M(), window.addEventListener(\"resize\", M), ()=>{\n            window.removeEventListener(\"resize\", M);\n        };\n    }, [\n        v\n    ]);\n    let Pe = [\n        pe,\n        \"field-type\",\n        r,\n        N && \"error\",\n        I && \"\".concat(pe, \"--read-only\"),\n        (e === null || e === void 0 ? void 0 : (_e_admin = e.admin) === null || _e_admin === void 0 ? void 0 : _e_admin.hideGutter) !== !0 && !v ? \"\".concat(pe, \"--show-gutter\") : null\n    ].filter(Boolean).join(\" \"), oe = \"\".concat(b, \".\").concat(p), D = (0,_chunk_KZKGNMS3_js__WEBPACK_IMPORTED_MODULE_8__.b)(), P = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)((M)=>{\n        D(()=>{\n            let Se = M.toJSON();\n            z.current = Se, k(Se);\n        });\n    }, [\n        k,\n        D\n    ]), St = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(()=>(0,_payloadcms_ui_shared__WEBPACK_IMPORTED_MODULE_19__.mergeFieldStyles)(o), [\n        o\n    ]), Rt = (0,_payloadcms_ui__WEBPACK_IMPORTED_MODULE_17__.useEffectEvent)((M)=>{\n        z.current !== L && JSON.stringify(z.current) !== JSON.stringify(L) && (ye.current = M, z.current = L, he(new Date));\n    });\n    return (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        Object.is(S, ye.current) || Rt(S);\n    }, [\n        S\n    ]), (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: Pe,\n        style: St,\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_payloadcms_ui__WEBPACK_IMPORTED_MODULE_17__.RenderCustomComponent, {\n                CustomComponent: f,\n                Fallback: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_payloadcms_ui__WEBPACK_IMPORTED_MODULE_17__.FieldError, {\n                    path: b,\n                    showError: N\n                })\n            }),\n            E || (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_payloadcms_ui__WEBPACK_IMPORTED_MODULE_17__.FieldLabel, {\n                label: i,\n                localized: s,\n                path: b,\n                required: u\n            }),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n                className: \"\".concat(pe, \"__wrap\"),\n                children: [\n                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(react_error_boundary__WEBPACK_IMPORTED_MODULE_20__.ErrorBoundary, {\n                        fallbackRender: vn,\n                        onReset: ()=>{},\n                        children: [\n                            h,\n                            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(xt, {\n                                composerKey: oe,\n                                editorConfig: e,\n                                fieldProps: t,\n                                isSmallWidthViewport: v,\n                                onChange: P,\n                                readOnly: I,\n                                value: L\n                            }, JSON.stringify({\n                                path: b,\n                                rerenderProviderKey: J\n                            })),\n                            m\n                        ]\n                    }),\n                    d,\n                    (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_payloadcms_ui__WEBPACK_IMPORTED_MODULE_17__.RenderCustomComponent, {\n                        CustomComponent: d,\n                        Fallback: (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(_payloadcms_ui__WEBPACK_IMPORTED_MODULE_17__.FieldDescription, {\n                            description: n,\n                            path: b\n                        })\n                    })\n                ]\n            })\n        ]\n    }, oe);\n};\n_s2(wn, \"oRKmpJL9BbYivRh7ug8x90GGR8E=\");\nfunction vn(param) {\n    let { error: t } = param;\n    return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxs)(\"div\", {\n        className: \"errorBoundary\",\n        role: \"alert\",\n        children: [\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"p\", {\n                children: \"Something went wrong:\"\n            }),\n            (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(\"pre\", {\n                style: {\n                    color: \"red\"\n                },\n                children: t.message\n            })\n        ]\n    });\n}\nvar cs = wn;\n //# sourceMappingURL=Field-6OZARKAL.js.map\nvar _c, _c1, _c2, _c3, _c4, _c5, _c6, _c7, _c8, _c9, _c10, _c11, _c12, _c13, _c14, _c15, _c16, _c17, _c18, _c19, _c20, _c21;\n$RefreshReg$(_c, \"Ht\");\n$RefreshReg$(_c1, \"Wt\");\n$RefreshReg$(_c2, \"Yt\");\n$RefreshReg$(_c3, \"Vt\");\n$RefreshReg$(_c4, \"Gt\");\n$RefreshReg$(_c5, \"Ut\");\n$RefreshReg$(_c6, \"Jt\");\n$RefreshReg$(_c7, \"Z\");\n$RefreshReg$(_c8, \"Oe\");\n$RefreshReg$(_c9, \"Fe\");\n$RefreshReg$(_c10, \"Y\");\n$RefreshReg$(_c11, \"V\");\n$RefreshReg$(_c12, \"H\");\n$RefreshReg$(_c13, \"G\");\n$RefreshReg$(_c14, \"Ke\");\n$RefreshReg$(_c15, \"Ye\");\n$RefreshReg$(_c16, \"Je\");\n$RefreshReg$(_c17, \"Eo\");\n$RefreshReg$(_c18, \"Qe\");\n$RefreshReg$(_c19, \"Co\");\n$RefreshReg$(_c20, \"Ze\");\n$RefreshReg$(_c21, \"Ro\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/Field-6OZARKAL.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/bundled.css":
/*!***********************************************************************************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/bundled.css ***!
  \***********************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"5173c13edaa1\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vQHBheWxvYWRjbXMrcmljaHRleHQtbGV4aWNhbEAzLjQzLjBfQGZhY2VsZXNzLXVpK21vZGFsQDMuMC4wLWJldGEuMl9AZmFjZWxlc3MtdWkrc2Nyb2xsLWluZm9AX25wcjdkb2s2MzdwMnV4Y2ZqNndtcHg0ZGptL25vZGVfbW9kdWxlcy9AcGF5bG9hZGNtcy9yaWNodGV4dC1sZXhpY2FsL2Rpc3QvZXhwb3J0cy9jbGllbnQvYnVuZGxlZC5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxJQUFVLElBQUksaUJBQWlCIiwic291cmNlcyI6WyJDOlxccHJvamVjdHNcXGxtc1xcbm9kZV9tb2R1bGVzXFwucG5wbVxcQHBheWxvYWRjbXMrcmljaHRleHQtbGV4aWNhbEAzLjQzLjBfQGZhY2VsZXNzLXVpK21vZGFsQDMuMC4wLWJldGEuMl9AZmFjZWxlc3MtdWkrc2Nyb2xsLWluZm9AX25wcjdkb2s2MzdwMnV4Y2ZqNndtcHg0ZGptXFxub2RlX21vZHVsZXNcXEBwYXlsb2FkY21zXFxyaWNodGV4dC1sZXhpY2FsXFxkaXN0XFxleHBvcnRzXFxjbGllbnRcXGJ1bmRsZWQuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTE3M2MxM2VkYWExXCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/@payloadcms+richtext-lexical@3.43.0_@faceless-ui+modal@3.0.0-beta.2_@faceless-ui+scroll-info@_npr7dok637p2uxcfj6wmpx4djm/node_modules/@payloadcms/richtext-lexical/dist/exports/client/bundled.css\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/react-error-boundary@4.1.2_react@19.1.0/node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/react-error-boundary@4.1.2_react@19.1.0/node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ErrorBoundary: () => (/* binding */ ErrorBoundary),\n/* harmony export */   ErrorBoundaryContext: () => (/* binding */ ErrorBoundaryContext),\n/* harmony export */   useErrorBoundary: () => (/* binding */ useErrorBoundary),\n/* harmony export */   withErrorBoundary: () => (/* binding */ withErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ ErrorBoundary,ErrorBoundaryContext,useErrorBoundary,withErrorBoundary auto */ var _s = $RefreshSig$();\n\nconst ErrorBoundaryContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nconst initialState = {\n    didCatch: false,\n    error: null\n};\nclass ErrorBoundary extends react__WEBPACK_IMPORTED_MODULE_0__.Component {\n    static getDerivedStateFromError(error) {\n        return {\n            didCatch: true,\n            error\n        };\n    }\n    resetErrorBoundary() {\n        const { error } = this.state;\n        if (error !== null) {\n            var _this$props$onReset, _this$props;\n            for(var _len = arguments.length, args = new Array(_len), _key = 0; _key < _len; _key++){\n                args[_key] = arguments[_key];\n            }\n            (_this$props$onReset = (_this$props = this.props).onReset) === null || _this$props$onReset === void 0 ? void 0 : _this$props$onReset.call(_this$props, {\n                args,\n                reason: \"imperative-api\"\n            });\n            this.setState(initialState);\n        }\n    }\n    componentDidCatch(error, info) {\n        var _this$props$onError, _this$props2;\n        (_this$props$onError = (_this$props2 = this.props).onError) === null || _this$props$onError === void 0 ? void 0 : _this$props$onError.call(_this$props2, error, info);\n    }\n    componentDidUpdate(prevProps, prevState) {\n        const { didCatch } = this.state;\n        const { resetKeys } = this.props;\n        // There's an edge case where if the thing that triggered the error happens to *also* be in the resetKeys array,\n        // we'd end up resetting the error boundary immediately.\n        // This would likely trigger a second error to be thrown.\n        // So we make sure that we don't check the resetKeys on the first call of cDU after the error is set.\n        if (didCatch && prevState.error !== null && hasArrayChanged(prevProps.resetKeys, resetKeys)) {\n            var _this$props$onReset2, _this$props3;\n            (_this$props$onReset2 = (_this$props3 = this.props).onReset) === null || _this$props$onReset2 === void 0 ? void 0 : _this$props$onReset2.call(_this$props3, {\n                next: resetKeys,\n                prev: prevProps.resetKeys,\n                reason: \"keys\"\n            });\n            this.setState(initialState);\n        }\n    }\n    render() {\n        const { children, fallbackRender, FallbackComponent, fallback } = this.props;\n        const { didCatch, error } = this.state;\n        let childToRender = children;\n        if (didCatch) {\n            const props = {\n                error,\n                resetErrorBoundary: this.resetErrorBoundary\n            };\n            if (typeof fallbackRender === \"function\") {\n                childToRender = fallbackRender(props);\n            } else if (FallbackComponent) {\n                childToRender = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(FallbackComponent, props);\n            } else if (fallback !== undefined) {\n                childToRender = fallback;\n            } else {\n                {\n                    console.error(\"react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop\");\n                }\n                throw error;\n            }\n        }\n        return /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ErrorBoundaryContext.Provider, {\n            value: {\n                didCatch,\n                error,\n                resetErrorBoundary: this.resetErrorBoundary\n            }\n        }, childToRender);\n    }\n    constructor(props){\n        super(props);\n        this.resetErrorBoundary = this.resetErrorBoundary.bind(this);\n        this.state = initialState;\n    }\n}\nfunction hasArrayChanged() {\n    let a = arguments.length > 0 && arguments[0] !== undefined ? arguments[0] : [];\n    let b = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : [];\n    return a.length !== b.length || a.some((item, index)=>!Object.is(item, b[index]));\n}\nfunction assertErrorBoundaryContext(value) {\n    if (value == null || typeof value.didCatch !== \"boolean\" || typeof value.resetErrorBoundary !== \"function\") {\n        throw new Error(\"ErrorBoundaryContext not found\");\n    }\n}\nfunction useErrorBoundary() {\n    _s();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(ErrorBoundaryContext);\n    assertErrorBoundaryContext(context);\n    const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)({\n        error: null,\n        hasError: false\n    });\n    const memoized = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)({\n        \"useErrorBoundary.useMemo[memoized]\": ()=>({\n                resetBoundary: ({\n                    \"useErrorBoundary.useMemo[memoized]\": ()=>{\n                        context.resetErrorBoundary();\n                        setState({\n                            error: null,\n                            hasError: false\n                        });\n                    }\n                })[\"useErrorBoundary.useMemo[memoized]\"],\n                showBoundary: ({\n                    \"useErrorBoundary.useMemo[memoized]\": (error)=>setState({\n                            error,\n                            hasError: true\n                        })\n                })[\"useErrorBoundary.useMemo[memoized]\"]\n            })\n    }[\"useErrorBoundary.useMemo[memoized]\"], [\n        context.resetErrorBoundary\n    ]);\n    if (state.hasError) {\n        throw state.error;\n    }\n    return memoized;\n}\n_s(useErrorBoundary, \"+pKi6m5l0SpCZXu8kma/1W0pdXE=\");\nfunction withErrorBoundary(component, errorBoundaryProps) {\n    const Wrapped = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref)=>/*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(ErrorBoundary, errorBoundaryProps, /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(component, {\n            ...props,\n            ref\n        })));\n    // Format for display in DevTools\n    const name = component.displayName || component.name || \"Unknown\";\n    Wrapped.displayName = \"withErrorBoundary(\".concat(name, \")\");\n    return Wrapped;\n}\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/react-error-boundary@4.1.2_react@19.1.0/node_modules/react-error-boundary/dist/react-error-boundary.development.esm.js\n"));

/***/ })

}]);