"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+utils@0.28.0";
exports.ids = ["vendor-chunks/@lexical+utils@0.28.0"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $descendantsMatching: () => (/* binding */ $descendantsMatching),\n/* harmony export */   $dfs: () => (/* binding */ $dfs),\n/* harmony export */   $dfsIterator: () => (/* binding */ $dfsIterator),\n/* harmony export */   $filter: () => (/* binding */ $filter),\n/* harmony export */   $findMatchingParent: () => (/* binding */ $findMatchingParent),\n/* harmony export */   $firstToLastIterator: () => (/* binding */ $firstToLastIterator),\n/* harmony export */   $getAdjacentCaret: () => (/* binding */ $getAdjacentCaret),\n/* harmony export */   $getAdjacentSiblingOrParentSiblingCaret: () => (/* binding */ $getAdjacentSiblingOrParentSiblingCaret),\n/* harmony export */   $getDepth: () => (/* binding */ $getDepth),\n/* harmony export */   $getNearestBlockElementAncestorOrThrow: () => (/* binding */ $getNearestBlockElementAncestorOrThrow),\n/* harmony export */   $getNearestNodeOfType: () => (/* binding */ $getNearestNodeOfType),\n/* harmony export */   $getNextRightPreorderNode: () => (/* binding */ $getNextRightPreorderNode),\n/* harmony export */   $getNextSiblingOrParentSibling: () => (/* binding */ $getNextSiblingOrParentSibling),\n/* harmony export */   $insertFirst: () => (/* binding */ $insertFirst),\n/* harmony export */   $insertNodeToNearestRoot: () => (/* binding */ $insertNodeToNearestRoot),\n/* harmony export */   $insertNodeToNearestRootAtCaret: () => (/* binding */ $insertNodeToNearestRootAtCaret),\n/* harmony export */   $isEditorIsNestedEditor: () => (/* binding */ $isEditorIsNestedEditor),\n/* harmony export */   $lastToFirstIterator: () => (/* binding */ $lastToFirstIterator),\n/* harmony export */   $restoreEditorState: () => (/* binding */ $restoreEditorState),\n/* harmony export */   $reverseDfs: () => (/* binding */ $reverseDfs),\n/* harmony export */   $reverseDfsIterator: () => (/* binding */ $reverseDfsIterator),\n/* harmony export */   $splitNode: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.$splitNode),\n/* harmony export */   $unwrapAndFilterDescendants: () => (/* binding */ $unwrapAndFilterDescendants),\n/* harmony export */   $unwrapNode: () => (/* binding */ $unwrapNode),\n/* harmony export */   $wrapNodeInElement: () => (/* binding */ $wrapNodeInElement),\n/* harmony export */   CAN_USE_BEFORE_INPUT: () => (/* binding */ CAN_USE_BEFORE_INPUT),\n/* harmony export */   CAN_USE_DOM: () => (/* binding */ CAN_USE_DOM),\n/* harmony export */   IS_ANDROID: () => (/* binding */ IS_ANDROID),\n/* harmony export */   IS_ANDROID_CHROME: () => (/* binding */ IS_ANDROID_CHROME),\n/* harmony export */   IS_APPLE: () => (/* binding */ IS_APPLE),\n/* harmony export */   IS_APPLE_WEBKIT: () => (/* binding */ IS_APPLE_WEBKIT),\n/* harmony export */   IS_CHROME: () => (/* binding */ IS_CHROME),\n/* harmony export */   IS_FIREFOX: () => (/* binding */ IS_FIREFOX),\n/* harmony export */   IS_IOS: () => (/* binding */ IS_IOS),\n/* harmony export */   IS_SAFARI: () => (/* binding */ IS_SAFARI),\n/* harmony export */   addClassNamesToElement: () => (/* binding */ addClassNamesToElement),\n/* harmony export */   calculateZoomLevel: () => (/* binding */ calculateZoomLevel),\n/* harmony export */   isBlockDomNode: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isBlockDomNode),\n/* harmony export */   isHTMLAnchorElement: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLAnchorElement),\n/* harmony export */   isHTMLElement: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement),\n/* harmony export */   isInlineDomNode: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isInlineDomNode),\n/* harmony export */   isMimeType: () => (/* binding */ isMimeType),\n/* harmony export */   makeStateWrapper: () => (/* binding */ makeStateWrapper),\n/* harmony export */   markSelection: () => (/* binding */ markSelection),\n/* harmony export */   mediaFileReader: () => (/* binding */ mediaFileReader),\n/* harmony export */   mergeRegister: () => (/* binding */ mergeRegister),\n/* harmony export */   objectKlassEquals: () => (/* binding */ objectKlassEquals),\n/* harmony export */   positionNodeOnRange: () => (/* binding */ mlcPositionNodeOnRange),\n/* harmony export */   registerNestedElementResolver: () => (/* binding */ registerNestedElementResolver),\n/* harmony export */   removeClassNamesFromElement: () => (/* binding */ removeClassNamesFromElement),\n/* harmony export */   selectionAlwaysOnDisplay: () => (/* binding */ selectionAlwaysOnDisplay)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/selection */ \"(rsc)/../../node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM$1 = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst documentMode = CAN_USE_DOM$1 && 'documentMode' in document ? document.documentMode : null;\nconst IS_APPLE$1 = CAN_USE_DOM$1 && /Mac|iPod|iPhone|iPad/.test(navigator.platform);\nconst IS_FIREFOX$1 = CAN_USE_DOM$1 && /^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);\nconst CAN_USE_BEFORE_INPUT$1 = CAN_USE_DOM$1 && 'InputEvent' in window && !documentMode ? 'getTargetRanges' in new window.InputEvent('input') : false;\nconst IS_SAFARI$1 = CAN_USE_DOM$1 && /Version\\/[\\d.]+.*Safari/.test(navigator.userAgent);\nconst IS_IOS$1 = CAN_USE_DOM$1 && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\nconst IS_ANDROID$1 = CAN_USE_DOM$1 && /Android/.test(navigator.userAgent);\n\n// Keep these in case we need to use them in the future.\n// export const IS_WINDOWS: boolean = CAN_USE_DOM && /Win/.test(navigator.platform);\nconst IS_CHROME$1 = CAN_USE_DOM$1 && /^(?=.*Chrome).*/i.test(navigator.userAgent);\n// export const canUseTextInputEvent: boolean = CAN_USE_DOM && 'TextEvent' in window && !documentMode;\n\nconst IS_ANDROID_CHROME$1 = CAN_USE_DOM$1 && IS_ANDROID$1 && IS_CHROME$1;\nconst IS_APPLE_WEBKIT$1 = CAN_USE_DOM$1 && /AppleWebKit\\/[\\d.]+/.test(navigator.userAgent) && !IS_CHROME$1;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction normalizeClassNames(...classNames) {\n  const rval = [];\n  for (const className of classNames) {\n    if (className && typeof className === 'string') {\n      for (const [s] of className.matchAll(/\\S+/g)) {\n        rval.push(s);\n      }\n    }\n  }\n  return rval;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Returns a function that will execute all functions passed when called. It is generally used\n * to register multiple lexical listeners and then tear them down with a single function call, such\n * as React's useEffect hook.\n * @example\n * ```ts\n * useEffect(() => {\n *   return mergeRegister(\n *     editor.registerCommand(...registerCommand1 logic),\n *     editor.registerCommand(...registerCommand2 logic),\n *     editor.registerCommand(...registerCommand3 logic)\n *   )\n * }, [editor])\n * ```\n * In this case, useEffect is returning the function returned by mergeRegister as a cleanup\n * function to be executed after either the useEffect runs again (due to one of its dependencies\n * updating) or the component it resides in unmounts.\n * Note the functions don't neccesarily need to be in an array as all arguments\n * are considered to be the func argument and spread from there.\n * The order of cleanup is the reverse of the argument order. Generally it is\n * expected that the first \"acquire\" will be \"released\" last (LIFO order),\n * because a later step may have some dependency on an earlier one.\n * @param func - An array of cleanup functions meant to be executed by the returned function.\n * @returns the function which executes all the passed cleanup functions.\n */\nfunction mergeRegister(...func) {\n  return () => {\n    for (let i = func.length - 1; i >= 0; i--) {\n      func[i]();\n    }\n    // Clean up the references and make future calls a no-op\n    func.length = 0;\n  };\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction px(value) {\n  return `${value}px`;\n}\n\nconst mutationObserverConfig = {\n  attributes: true,\n  characterData: true,\n  childList: true,\n  subtree: true\n};\nfunction prependDOMNode(parent, node) {\n  parent.insertBefore(node, parent.firstChild);\n}\n\n/**\n * Place one or multiple newly created Nodes at the passed Range's position.\n * Multiple nodes will only be created when the Range spans multiple lines (aka\n * client rects).\n *\n * This function can come particularly useful to highlight particular parts of\n * the text without interfering with the EditorState, that will often replicate\n * the state across collab and clipboard.\n *\n * This function accounts for DOM updates which can modify the passed Range.\n * Hence, the function return to remove the listener.\n */\nfunction mlcPositionNodeOnRange(editor, range, onReposition) {\n  let rootDOMNode = null;\n  let parentDOMNode = null;\n  let observer = null;\n  let lastNodes = [];\n  const wrapperNode = document.createElement('div');\n  wrapperNode.style.position = 'relative';\n  function position() {\n    if (!(rootDOMNode !== null)) {\n      formatDevErrorMessage(`Unexpected null rootDOMNode`);\n    }\n    if (!(parentDOMNode !== null)) {\n      formatDevErrorMessage(`Unexpected null parentDOMNode`);\n    }\n    const {\n      left: parentLeft,\n      top: parentTop\n    } = parentDOMNode.getBoundingClientRect();\n    const rects = (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_1__.createRectsFromDOMRange)(editor, range);\n    if (!wrapperNode.isConnected) {\n      prependDOMNode(parentDOMNode, wrapperNode);\n    }\n    let hasRepositioned = false;\n    for (let i = 0; i < rects.length; i++) {\n      const rect = rects[i];\n      // Try to reuse the previously created Node when possible, no need to\n      // remove/create on the most common case reposition case\n      const rectNode = lastNodes[i] || document.createElement('div');\n      const rectNodeStyle = rectNode.style;\n      if (rectNodeStyle.position !== 'absolute') {\n        rectNodeStyle.position = 'absolute';\n        hasRepositioned = true;\n      }\n      const left = px(rect.left - parentLeft);\n      if (rectNodeStyle.left !== left) {\n        rectNodeStyle.left = left;\n        hasRepositioned = true;\n      }\n      const top = px(rect.top - parentTop);\n      if (rectNodeStyle.top !== top) {\n        rectNode.style.top = top;\n        hasRepositioned = true;\n      }\n      const width = px(rect.width);\n      if (rectNodeStyle.width !== width) {\n        rectNode.style.width = width;\n        hasRepositioned = true;\n      }\n      const height = px(rect.height);\n      if (rectNodeStyle.height !== height) {\n        rectNode.style.height = height;\n        hasRepositioned = true;\n      }\n      if (rectNode.parentNode !== wrapperNode) {\n        wrapperNode.append(rectNode);\n        hasRepositioned = true;\n      }\n      lastNodes[i] = rectNode;\n    }\n    while (lastNodes.length > rects.length) {\n      lastNodes.pop();\n    }\n    if (hasRepositioned) {\n      onReposition(lastNodes);\n    }\n  }\n  function stop() {\n    parentDOMNode = null;\n    rootDOMNode = null;\n    if (observer !== null) {\n      observer.disconnect();\n    }\n    observer = null;\n    wrapperNode.remove();\n    for (const node of lastNodes) {\n      node.remove();\n    }\n    lastNodes = [];\n  }\n  function restart() {\n    const currentRootDOMNode = editor.getRootElement();\n    if (currentRootDOMNode === null) {\n      return stop();\n    }\n    const currentParentDOMNode = currentRootDOMNode.parentElement;\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(currentParentDOMNode)) {\n      return stop();\n    }\n    stop();\n    rootDOMNode = currentRootDOMNode;\n    parentDOMNode = currentParentDOMNode;\n    observer = new MutationObserver(mutations => {\n      const nextRootDOMNode = editor.getRootElement();\n      const nextParentDOMNode = nextRootDOMNode && nextRootDOMNode.parentElement;\n      if (nextRootDOMNode !== rootDOMNode || nextParentDOMNode !== parentDOMNode) {\n        return restart();\n      }\n      for (const mutation of mutations) {\n        if (!wrapperNode.contains(mutation.target)) {\n          // TODO throttle\n          return position();\n        }\n      }\n    });\n    observer.observe(currentParentDOMNode, mutationObserverConfig);\n    position();\n  }\n  const removeRootListener = editor.registerRootListener(restart);\n  return () => {\n    removeRootListener();\n    stop();\n  };\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction rangeTargetFromPoint(point, node, dom) {\n  if (point.type === 'text' || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n    const textDOM = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMTextNode)(dom) || dom;\n    return [textDOM, point.offset];\n  } else {\n    const slot = node.getDOMSlot(dom);\n    return [slot.element, slot.getFirstChildOffset() + point.offset];\n  }\n}\nfunction rangeFromPoints(editor, anchor, anchorNode, anchorDOM, focus, focusNode, focusDOM) {\n  const editorDocument = editor._window ? editor._window.document : document;\n  const range = editorDocument.createRange();\n  if (focusNode.isBefore(anchorNode)) {\n    range.setStart(...rangeTargetFromPoint(focus, focusNode, focusDOM));\n    range.setEnd(...rangeTargetFromPoint(anchor, anchorNode, anchorDOM));\n  } else {\n    range.setStart(...rangeTargetFromPoint(anchor, anchorNode, anchorDOM));\n    range.setEnd(...rangeTargetFromPoint(focus, focusNode, focusDOM));\n  }\n  return range;\n}\n/**\n * Place one or multiple newly created Nodes at the current selection. Multiple\n * nodes will only be created when the selection spans multiple lines (aka\n * client rects).\n *\n * This function can come useful when you want to show the selection but the\n * editor has been focused away.\n */\nfunction markSelection(editor, onReposition) {\n  let previousAnchorNode = null;\n  let previousAnchorNodeDOM = null;\n  let previousAnchorOffset = null;\n  let previousFocusNode = null;\n  let previousFocusNodeDOM = null;\n  let previousFocusOffset = null;\n  let removeRangeListener = () => {};\n  function compute(editorState) {\n    editorState.read(() => {\n      const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n        // TODO\n        previousAnchorNode = null;\n        previousAnchorOffset = null;\n        previousFocusNode = null;\n        previousFocusOffset = null;\n        removeRangeListener();\n        removeRangeListener = () => {};\n        return;\n      }\n      const {\n        anchor,\n        focus\n      } = selection;\n      const currentAnchorNode = anchor.getNode();\n      const currentAnchorNodeKey = currentAnchorNode.getKey();\n      const currentAnchorOffset = anchor.offset;\n      const currentFocusNode = focus.getNode();\n      const currentFocusNodeKey = currentFocusNode.getKey();\n      const currentFocusOffset = focus.offset;\n      const currentAnchorNodeDOM = editor.getElementByKey(currentAnchorNodeKey);\n      const currentFocusNodeDOM = editor.getElementByKey(currentFocusNodeKey);\n      const differentAnchorDOM = previousAnchorNode === null || currentAnchorNodeDOM !== previousAnchorNodeDOM || currentAnchorOffset !== previousAnchorOffset || currentAnchorNodeKey !== previousAnchorNode.getKey();\n      const differentFocusDOM = previousFocusNode === null || currentFocusNodeDOM !== previousFocusNodeDOM || currentFocusOffset !== previousFocusOffset || currentFocusNodeKey !== previousFocusNode.getKey();\n      if ((differentAnchorDOM || differentFocusDOM) && currentAnchorNodeDOM !== null && currentFocusNodeDOM !== null) {\n        const range = rangeFromPoints(editor, anchor, currentAnchorNode, currentAnchorNodeDOM, focus, currentFocusNode, currentFocusNodeDOM);\n        removeRangeListener();\n        removeRangeListener = mlcPositionNodeOnRange(editor, range, domNodes => {\n          if (onReposition === undefined) {\n            for (const domNode of domNodes) {\n              const domNodeStyle = domNode.style;\n              if (domNodeStyle.background !== 'Highlight') {\n                domNodeStyle.background = 'Highlight';\n              }\n              if (domNodeStyle.color !== 'HighlightText') {\n                domNodeStyle.color = 'HighlightText';\n              }\n              if (domNodeStyle.marginTop !== px(-1.5)) {\n                domNodeStyle.marginTop = px(-1.5);\n              }\n              if (domNodeStyle.paddingTop !== px(4)) {\n                domNodeStyle.paddingTop = px(4);\n              }\n              if (domNodeStyle.paddingBottom !== px(0)) {\n                domNodeStyle.paddingBottom = px(0);\n              }\n            }\n          } else {\n            onReposition(domNodes);\n          }\n        });\n      }\n      previousAnchorNode = currentAnchorNode;\n      previousAnchorNodeDOM = currentAnchorNodeDOM;\n      previousAnchorOffset = currentAnchorOffset;\n      previousFocusNode = currentFocusNode;\n      previousFocusNodeDOM = currentFocusNodeDOM;\n      previousFocusOffset = currentFocusOffset;\n    });\n  }\n  compute(editor.getEditorState());\n  return mergeRegister(editor.registerUpdateListener(({\n    editorState\n  }) => compute(editorState)), () => {\n    removeRangeListener();\n  });\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction selectionAlwaysOnDisplay(editor) {\n  let removeSelectionMark = null;\n  const onSelectionChange = () => {\n    const domSelection = getSelection();\n    const domAnchorNode = domSelection && domSelection.anchorNode;\n    const editorRootElement = editor.getRootElement();\n    const isSelectionInsideEditor = domAnchorNode !== null && editorRootElement !== null && editorRootElement.contains(domAnchorNode);\n    if (isSelectionInsideEditor) {\n      if (removeSelectionMark !== null) {\n        removeSelectionMark();\n        removeSelectionMark = null;\n      }\n    } else {\n      if (removeSelectionMark === null) {\n        removeSelectionMark = markSelection(editor);\n      }\n    }\n  };\n  document.addEventListener('selectionchange', onSelectionChange);\n  return () => {\n    if (removeSelectionMark !== null) {\n      removeSelectionMark();\n    }\n    document.removeEventListener('selectionchange', onSelectionChange);\n  };\n}\n\n// Hotfix to export these with inlined types #5918\nconst CAN_USE_BEFORE_INPUT = CAN_USE_BEFORE_INPUT$1;\nconst CAN_USE_DOM = CAN_USE_DOM$1;\nconst IS_ANDROID = IS_ANDROID$1;\nconst IS_ANDROID_CHROME = IS_ANDROID_CHROME$1;\nconst IS_APPLE = IS_APPLE$1;\nconst IS_APPLE_WEBKIT = IS_APPLE_WEBKIT$1;\nconst IS_CHROME = IS_CHROME$1;\nconst IS_FIREFOX = IS_FIREFOX$1;\nconst IS_IOS = IS_IOS$1;\nconst IS_SAFARI = IS_SAFARI$1;\n\n/**\n * Takes an HTML element and adds the classNames passed within an array,\n * ignoring any non-string types. A space can be used to add multiple classes\n * eg. addClassNamesToElement(element, ['element-inner active', true, null])\n * will add both 'element-inner' and 'active' as classes to that element.\n * @param element - The element in which the classes are added\n * @param classNames - An array defining the class names to add to the element\n */\nfunction addClassNamesToElement(element, ...classNames) {\n  const classesToAdd = normalizeClassNames(...classNames);\n  if (classesToAdd.length > 0) {\n    element.classList.add(...classesToAdd);\n  }\n}\n\n/**\n * Takes an HTML element and removes the classNames passed within an array,\n * ignoring any non-string types. A space can be used to remove multiple classes\n * eg. removeClassNamesFromElement(element, ['active small', true, null])\n * will remove both the 'active' and 'small' classes from that element.\n * @param element - The element in which the classes are removed\n * @param classNames - An array defining the class names to remove from the element\n */\nfunction removeClassNamesFromElement(element, ...classNames) {\n  const classesToRemove = normalizeClassNames(...classNames);\n  if (classesToRemove.length > 0) {\n    element.classList.remove(...classesToRemove);\n  }\n}\n\n/**\n * Returns true if the file type matches the types passed within the acceptableMimeTypes array, false otherwise.\n * The types passed must be strings and are CASE-SENSITIVE.\n * eg. if file is of type 'text' and acceptableMimeTypes = ['TEXT', 'IMAGE'] the function will return false.\n * @param file - The file you want to type check.\n * @param acceptableMimeTypes - An array of strings of types which the file is checked against.\n * @returns true if the file is an acceptable mime type, false otherwise.\n */\nfunction isMimeType(file, acceptableMimeTypes) {\n  for (const acceptableType of acceptableMimeTypes) {\n    if (file.type.startsWith(acceptableType)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Lexical File Reader with:\n *  1. MIME type support\n *  2. batched results (HistoryPlugin compatibility)\n *  3. Order aware (respects the order when multiple Files are passed)\n *\n * const filesResult = await mediaFileReader(files, ['image/']);\n * filesResult.forEach(file => editor.dispatchCommand('INSERT_IMAGE', \\\\{\n *   src: file.result,\n * \\\\}));\n */\nfunction mediaFileReader(files, acceptableMimeTypes) {\n  const filesIterator = files[Symbol.iterator]();\n  return new Promise((resolve, reject) => {\n    const processed = [];\n    const handleNextFile = () => {\n      const {\n        done,\n        value: file\n      } = filesIterator.next();\n      if (done) {\n        return resolve(processed);\n      }\n      const fileReader = new FileReader();\n      fileReader.addEventListener('error', reject);\n      fileReader.addEventListener('load', () => {\n        const result = fileReader.result;\n        if (typeof result === 'string') {\n          processed.push({\n            file,\n            result\n          });\n        }\n        handleNextFile();\n      });\n      if (isMimeType(file, acceptableMimeTypes)) {\n        fileReader.readAsDataURL(file);\n      } else {\n        handleNextFile();\n      }\n    };\n    handleNextFile();\n  });\n}\n/**\n * \"Depth-First Search\" starts at the root/top node of a tree and goes as far as it can down a branch end\n * before backtracking and finding a new path. Consider solving a maze by hugging either wall, moving down a\n * branch until you hit a dead-end (leaf) and backtracking to find the nearest branching path and repeat.\n * It will then return all the nodes found in the search in an array of objects.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An array of objects of all the nodes found by the search, including their depth into the tree.\n * \\\\{depth: number, node: LexicalNode\\\\} It will always return at least 1 node (the start node).\n */\nfunction $dfs(startNode, endNode) {\n  return Array.from($dfsIterator(startNode, endNode));\n}\n\n/**\n * Get the adjacent caret in the same direction\n *\n * @param caret A caret or null\n * @returns `caret.getAdjacentCaret()` or `null`\n */\nfunction $getAdjacentCaret(caret) {\n  return caret ? caret.getAdjacentCaret() : null;\n}\n\n/**\n * $dfs iterator (right to left). Tree traversal is done on the fly as new values are requested with O(1) memory.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).\n */\nfunction $reverseDfs(startNode, endNode) {\n  return Array.from($reverseDfsIterator(startNode, endNode));\n}\n\n/**\n * $dfs iterator (left to right). Tree traversal is done on the fly as new values are requested with O(1) memory.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).\n */\nfunction $dfsIterator(startNode, endNode) {\n  return $dfsCaretIterator('next', startNode, endNode);\n}\nfunction $getEndCaret(startNode, direction) {\n  const rval = $getAdjacentSiblingOrParentSiblingCaret((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(startNode, direction));\n  return rval && rval[0];\n}\nfunction $dfsCaretIterator(direction, startNode, endNode) {\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  const start = startNode || root;\n  const startCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(start) ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(start, direction) : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(start, direction);\n  const startDepth = $getDepth(start);\n  const endCaret = endNode ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaretOrSelf)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(endNode, direction))) : $getEndCaret(start, direction);\n  let depth = startDepth;\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.makeStepwiseIterator)({\n    hasNext: state => state !== null,\n    initial: startCaret,\n    map: state => ({\n      depth,\n      node: state.origin\n    }),\n    step: state => {\n      if (state.isSameNodeCaret(endCaret)) {\n        return null;\n      }\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(state)) {\n        depth++;\n      }\n      const rval = $getAdjacentSiblingOrParentSiblingCaret(state);\n      if (!rval || rval[0].isSameNodeCaret(endCaret)) {\n        return null;\n      }\n      depth += rval[1];\n      return rval[0];\n    }\n  });\n}\n\n/**\n * Returns the Node sibling when this exists, otherwise the closest parent sibling. For example\n * R -> P -> T1, T2\n *   -> P2\n * returns T2 for node T1, P2 for node T2, and null for node P2.\n * @param node LexicalNode.\n * @returns An array (tuple) containing the found Lexical node and the depth difference, or null, if this node doesn't exist.\n */\nfunction $getNextSiblingOrParentSibling(node) {\n  const rval = $getAdjacentSiblingOrParentSiblingCaret((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(node, 'next'));\n  return rval && [rval[0].origin, rval[1]];\n}\nfunction $getDepth(node) {\n  let depth = -1;\n  for (let innerNode = node; innerNode !== null; innerNode = innerNode.getParent()) {\n    depth++;\n  }\n  return depth;\n}\n\n/**\n * Performs a right-to-left preorder tree traversal.\n * From the starting node it goes to the rightmost child, than backtracks to parent and finds new rightmost path.\n * It will return the next node in traversal sequence after the startingNode.\n * The traversal is similar to $dfs functions above, but the nodes are visited right-to-left, not left-to-right.\n * @param startingNode - The node to start the search.\n * @returns The next node in pre-order right to left traversal sequence or `null`, if the node does not exist\n */\nfunction $getNextRightPreorderNode(startingNode) {\n  const startCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaretOrSelf)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(startingNode, 'previous'));\n  const next = $getAdjacentSiblingOrParentSiblingCaret(startCaret, 'root');\n  return next && next[0].origin;\n}\n\n/**\n * $dfs iterator (right to left). Tree traversal is done on the fly as new values are requested with O(1) memory.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).\n */\nfunction $reverseDfsIterator(startNode, endNode) {\n  return $dfsCaretIterator('previous', startNode, endNode);\n}\n\n/**\n * Takes a node and traverses up its ancestors (toward the root node)\n * in order to find a specific type of node.\n * @param node - the node to begin searching.\n * @param klass - an instance of the type of node to look for.\n * @returns the node of type klass that was passed, or null if none exist.\n */\nfunction $getNearestNodeOfType(node, klass) {\n  let parent = node;\n  while (parent != null) {\n    if (parent instanceof klass) {\n      return parent;\n    }\n    parent = parent.getParent();\n  }\n  return null;\n}\n\n/**\n * Returns the element node of the nearest ancestor, otherwise throws an error.\n * @param startNode - The starting node of the search\n * @returns The ancestor node found\n */\nfunction $getNearestBlockElementAncestorOrThrow(startNode) {\n  const blockNode = $findMatchingParent(startNode, node => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && !node.isInline());\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(blockNode)) {\n    {\n      formatDevErrorMessage(`Expected node ${startNode.__key} to have closest block element node.`);\n    }\n  }\n  return blockNode;\n}\n/**\n * Starts with a node and moves up the tree (toward the root node) to find a matching node based on\n * the search parameters of the findFn. (Consider JavaScripts' .find() function where a testing function must be\n * passed as an argument. eg. if( (node) => node.__type === 'div') ) return true; otherwise return false\n * @param startingNode - The node where the search starts.\n * @param findFn - A testing function that returns true if the current node satisfies the testing parameters.\n * @returns A parent node that matches the findFn parameters, or null if one wasn't found.\n */\nconst $findMatchingParent = (startingNode, findFn) => {\n  let curr = startingNode;\n  while (curr !== (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)() && curr != null) {\n    if (findFn(curr)) {\n      return curr;\n    }\n    curr = curr.getParent();\n  }\n  return null;\n};\n\n/**\n * Attempts to resolve nested element nodes of the same type into a single node of that type.\n * It is generally used for marks/commenting\n * @param editor - The lexical editor\n * @param targetNode - The target for the nested element to be extracted from.\n * @param cloneNode - See {@link $createMarkNode}\n * @param handleOverlap - Handles any overlap between the node to extract and the targetNode\n * @returns The lexical editor\n */\nfunction registerNestedElementResolver(editor, targetNode, cloneNode, handleOverlap) {\n  const $isTargetNode = node => {\n    return node instanceof targetNode;\n  };\n  const $findMatch = node => {\n    // First validate we don't have any children that are of the target,\n    // as we need to handle them first.\n    const children = node.getChildren();\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if ($isTargetNode(child)) {\n        return null;\n      }\n    }\n    let parentNode = node;\n    let childNode = node;\n    while (parentNode !== null) {\n      childNode = parentNode;\n      parentNode = parentNode.getParent();\n      if ($isTargetNode(parentNode)) {\n        return {\n          child: childNode,\n          parent: parentNode\n        };\n      }\n    }\n    return null;\n  };\n  const $elementNodeTransform = node => {\n    const match = $findMatch(node);\n    if (match !== null) {\n      const {\n        child,\n        parent\n      } = match;\n\n      // Simple path, we can move child out and siblings into a new parent.\n\n      if (child.is(node)) {\n        handleOverlap(parent, node);\n        const nextSiblings = child.getNextSiblings();\n        const nextSiblingsLength = nextSiblings.length;\n        parent.insertAfter(child);\n        if (nextSiblingsLength !== 0) {\n          const newParent = cloneNode(parent);\n          child.insertAfter(newParent);\n          for (let i = 0; i < nextSiblingsLength; i++) {\n            newParent.append(nextSiblings[i]);\n          }\n        }\n        if (!parent.canBeEmpty() && parent.getChildrenSize() === 0) {\n          parent.remove();\n        }\n      }\n    }\n  };\n  return editor.registerNodeTransform(targetNode, $elementNodeTransform);\n}\n\n/**\n * Clones the editor and marks it as dirty to be reconciled. If there was a selection,\n * it would be set back to its previous state, or null otherwise.\n * @param editor - The lexical editor\n * @param editorState - The editor's state\n */\nfunction $restoreEditorState(editor, editorState) {\n  const FULL_RECONCILE = 2;\n  const nodeMap = new Map();\n  const activeEditorState = editor._pendingEditorState;\n  for (const [key, node] of editorState._nodeMap) {\n    nodeMap.set(key, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$cloneWithProperties)(node));\n  }\n  if (activeEditorState) {\n    activeEditorState._nodeMap = nodeMap;\n  }\n  editor._dirtyType = FULL_RECONCILE;\n  const selection = editorState._selection;\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(selection === null ? null : selection.clone());\n}\n\n/**\n * If the selected insertion area is the root/shadow root node (see {@link lexical!$isRootOrShadowRoot}),\n * the node will be appended there, otherwise, it will be inserted before the insertion area.\n * If there is no selection where the node is to be inserted, it will be appended after any current nodes\n * within the tree, as a child of the root node. A paragraph will then be added after the inserted node and selected.\n * @param node - The node to be inserted\n * @returns The node after its insertion\n */\nfunction $insertNodeToNearestRoot(node) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)() || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n  let initialCaret;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    initialCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretFromPoint)(selection.focus, 'next');\n  } else {\n    if (selection != null) {\n      const nodes = selection.getNodes();\n      const lastNode = nodes[nodes.length - 1];\n      if (lastNode) {\n        initialCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(lastNode, 'next');\n      }\n    }\n    initialCaret = initialCaret || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)(), 'previous').getFlipped().insert((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n  }\n  const insertCaret = $insertNodeToNearestRootAtCaret(node, initialCaret);\n  const adjacent = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(insertCaret);\n  const selectionCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(adjacent) ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)(adjacent) : insertCaret;\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelectionFromCaretRange)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCollapsedCaretRange)(selectionCaret));\n  return node.getLatest();\n}\n\n/**\n * If the insertion caret is the root/shadow root node (see {@link lexical!$isRootOrShadowRoot}),\n * the node will be inserted there, otherwise the parent nodes will be split according to the\n * given options.\n * @param node - The node to be inserted\n * @param caret - The location to insert or split from\n * @returns The node after its insertion\n */\nfunction $insertNodeToNearestRootAtCaret(node, caret, options) {\n  let insertCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCaretInDirection)(caret, 'next');\n  for (let nextCaret = insertCaret; nextCaret; nextCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$splitAtPointCaretNext)(nextCaret, options)) {\n    insertCaret = nextCaret;\n  }\n  if (!!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextPointCaret)(insertCaret)) {\n    formatDevErrorMessage(`$insertNodeToNearestRootAtCaret: An unattached TextNode can not be split`);\n  }\n  insertCaret.insert(node.isInline() ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)().append(node) : node);\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCaretInDirection)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(node.getLatest(), 'next'), caret.direction);\n}\n\n/**\n * Wraps the node into another node created from a createElementNode function, eg. $createParagraphNode\n * @param node - Node to be wrapped.\n * @param createElementNode - Creates a new lexical element to wrap the to-be-wrapped node and returns it.\n * @returns A new lexical element with the previous node appended within (as a child, including its children).\n */\nfunction $wrapNodeInElement(node, createElementNode) {\n  const elementNode = createElementNode();\n  node.replace(elementNode);\n  elementNode.append(node);\n  return elementNode;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n/**\n * @param object = The instance of the type\n * @param objectClass = The class of the type\n * @returns Whether the object is has the same Klass of the objectClass, ignoring the difference across window (e.g. different iframs)\n */\nfunction objectKlassEquals(object, objectClass) {\n  return object !== null ? Object.getPrototypeOf(object).constructor.name === objectClass.name : false;\n}\n\n/**\n * Filter the nodes\n * @param nodes Array of nodes that needs to be filtered\n * @param filterFn A filter function that returns node if the current node satisfies the condition otherwise null\n * @returns Array of filtered nodes\n */\n\nfunction $filter(nodes, filterFn) {\n  const result = [];\n  for (let i = 0; i < nodes.length; i++) {\n    const node = filterFn(nodes[i]);\n    if (node !== null) {\n      result.push(node);\n    }\n  }\n  return result;\n}\n/**\n * Appends the node before the first child of the parent node\n * @param parent A parent node\n * @param node Node that needs to be appended\n */\nfunction $insertFirst(parent, node) {\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(parent, 'next').insert(node);\n}\nlet NEEDS_MANUAL_ZOOM = IS_FIREFOX || !CAN_USE_DOM ? false : undefined;\nfunction needsManualZoom() {\n  if (NEEDS_MANUAL_ZOOM === undefined) {\n    // If the browser implements standardized CSS zoom, then the client rect\n    // will be wider after zoom is applied\n    // https://chromestatus.com/feature/5198254868529152\n    // https://github.com/facebook/lexical/issues/6863\n    const div = document.createElement('div');\n    div.style.cssText = 'position: absolute; opacity: 0; width: 100px; left: -1000px;';\n    document.body.appendChild(div);\n    const noZoom = div.getBoundingClientRect();\n    div.style.setProperty('zoom', '2');\n    NEEDS_MANUAL_ZOOM = div.getBoundingClientRect().width === noZoom.width;\n    document.body.removeChild(div);\n  }\n  return NEEDS_MANUAL_ZOOM;\n}\n\n/**\n * Calculates the zoom level of an element as a result of using\n * css zoom property. For browsers that implement standardized CSS\n * zoom (Firefox, Chrome >= 128), this will always return 1.\n * @param element\n */\nfunction calculateZoomLevel(element) {\n  let zoom = 1;\n  if (needsManualZoom()) {\n    while (element) {\n      zoom *= Number(window.getComputedStyle(element).getPropertyValue('zoom'));\n      element = element.parentElement;\n    }\n  }\n  return zoom;\n}\n\n/**\n * Checks if the editor is a nested editor created by LexicalNestedComposer\n */\nfunction $isEditorIsNestedEditor(editor) {\n  return editor._parentEditor !== null;\n}\n\n/**\n * A depth first last-to-first traversal of root that stops at each node that matches\n * $predicate and ensures that its parent is root. This is typically used to discard\n * invalid or unsupported wrapping nodes. For example, a TableNode must only have\n * TableRowNode as children, but an importer might add invalid nodes based on\n * caption, tbody, thead, etc. and this will unwrap and discard those.\n *\n * @param root The root to start the traversal\n * @param $predicate Should return true for nodes that are permitted to be children of root\n * @returns true if this unwrapped or removed any nodes\n */\nfunction $unwrapAndFilterDescendants(root, $predicate) {\n  return $unwrapAndFilterDescendantsImpl(root, $predicate, null);\n}\nfunction $unwrapAndFilterDescendantsImpl(root, $predicate, $onSuccess) {\n  let didMutate = false;\n  for (const node of $lastToFirstIterator(root)) {\n    if ($predicate(node)) {\n      if ($onSuccess !== null) {\n        $onSuccess(node);\n      }\n      continue;\n    }\n    didMutate = true;\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n      $unwrapAndFilterDescendantsImpl(node, $predicate, $onSuccess || (child => node.insertAfter(child)));\n    }\n    node.remove();\n  }\n  return didMutate;\n}\n\n/**\n * A depth first traversal of the children array that stops at and collects\n * each node that `$predicate` matches. This is typically used to discard\n * invalid or unsupported wrapping nodes on a children array in the `after`\n * of an {@link lexical!DOMConversionOutput}. For example, a TableNode must only have\n * TableRowNode as children, but an importer might add invalid nodes based on\n * caption, tbody, thead, etc. and this will unwrap and discard those.\n *\n * This function is read-only and performs no mutation operations, which makes\n * it suitable for import and export purposes but likely not for any in-place\n * mutation. You should use {@link $unwrapAndFilterDescendants} for in-place\n * mutations such as node transforms.\n *\n * @param children The children to traverse\n * @param $predicate Should return true for nodes that are permitted to be children of root\n * @returns The children or their descendants that match $predicate\n */\n\nfunction $descendantsMatching(children, $predicate) {\n  const result = [];\n  const stack = Array.from(children).reverse();\n  for (let child = stack.pop(); child !== undefined; child = stack.pop()) {\n    if ($predicate(child)) {\n      result.push(child);\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(child)) {\n      for (const grandchild of $lastToFirstIterator(child)) {\n        stack.push(grandchild);\n      }\n    }\n  }\n  return result;\n}\n\n/**\n * Return an iterator that yields each child of node from first to last, taking\n * care to preserve the next sibling before yielding the value in case the caller\n * removes the yielded node.\n *\n * @param node The node whose children to iterate\n * @returns An iterator of the node's children\n */\nfunction $firstToLastIterator(node) {\n  return $childIterator((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(node, 'next'));\n}\n\n/**\n * Return an iterator that yields each child of node from last to first, taking\n * care to preserve the previous sibling before yielding the value in case the caller\n * removes the yielded node.\n *\n * @param node The node whose children to iterate\n * @returns An iterator of the node's children\n */\nfunction $lastToFirstIterator(node) {\n  return $childIterator((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(node, 'previous'));\n}\nfunction $childIterator(startCaret) {\n  const seen = new Set() ;\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.makeStepwiseIterator)({\n    hasNext: lexical__WEBPACK_IMPORTED_MODULE_0__.$isSiblingCaret,\n    initial: startCaret.getAdjacentCaret(),\n    map: caret => {\n      const origin = caret.origin.getLatest();\n      if (seen !== null) {\n        const key = origin.getKey();\n        if (!!seen.has(key)) {\n          formatDevErrorMessage(`$childIterator: Cycle detected, node with key ${String(key)} has already been traversed`);\n        }\n        seen.add(key);\n      }\n      return origin;\n    },\n    step: caret => caret.getAdjacentCaret()\n  });\n}\n\n/**\n * Replace this node with its children\n *\n * @param node The ElementNode to unwrap and remove\n */\nfunction $unwrapNode(node) {\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$rewindSiblingCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(node, 'next')).splice(1, node.getChildren());\n}\n\n/**\n * Returns the Node sibling when this exists, otherwise the closest parent sibling. For example\n * R -> P -> T1, T2\n *   -> P2\n * returns T2 for node T1, P2 for node T2, and null for node P2.\n * @param node LexicalNode.\n * @returns An array (tuple) containing the found Lexical node and the depth difference, or null, if this node doesn't exist.\n */\nfunction $getAdjacentSiblingOrParentSiblingCaret(startCaret, rootMode = 'root') {\n  let depthDiff = 0;\n  let caret = startCaret;\n  let nextCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(caret);\n  while (nextCaret === null) {\n    depthDiff--;\n    nextCaret = caret.getParentCaret(rootMode);\n    if (!nextCaret) {\n      return null;\n    }\n    caret = nextCaret;\n    nextCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(caret);\n  }\n  return nextCaret && [nextCaret, depthDiff];\n}\n\n/**\n * A wrapper that creates bound functions and methods for the\n * StateConfig to save some boilerplate when defining methods\n * or exporting only the accessors from your modules rather\n * than exposing the StateConfig directly.\n */\n\n/**\n * EXPERIMENTAL\n *\n * A convenience interface for working with {@link $getState} and\n * {@link $setState}.\n *\n * @param stateConfig The stateConfig to wrap with convenience functionality\n * @returns a StateWrapper\n */\nfunction makeStateWrapper(stateConfig) {\n  const $get = node => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getState)(node, stateConfig);\n  const $set = (node, valueOrUpdater) => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setState)(node, stateConfig, valueOrUpdater);\n  return {\n    $get,\n    $set,\n    accessors: [$get, $set],\n    makeGetterMethod: () => function $getter() {\n      return $get(this);\n    },\n    makeSetterMethod: () => function $setter(valueOrUpdater) {\n      return $set(this, valueOrUpdater);\n    },\n    stateConfig\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs":
/*!*******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs ***!
  \*******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $descendantsMatching: () => (/* binding */ $descendantsMatching),\n/* harmony export */   $dfs: () => (/* binding */ $dfs),\n/* harmony export */   $dfsIterator: () => (/* binding */ $dfsIterator),\n/* harmony export */   $filter: () => (/* binding */ $filter),\n/* harmony export */   $findMatchingParent: () => (/* binding */ $findMatchingParent),\n/* harmony export */   $firstToLastIterator: () => (/* binding */ $firstToLastIterator),\n/* harmony export */   $getAdjacentCaret: () => (/* binding */ $getAdjacentCaret),\n/* harmony export */   $getAdjacentSiblingOrParentSiblingCaret: () => (/* binding */ $getAdjacentSiblingOrParentSiblingCaret),\n/* harmony export */   $getDepth: () => (/* binding */ $getDepth),\n/* harmony export */   $getNearestBlockElementAncestorOrThrow: () => (/* binding */ $getNearestBlockElementAncestorOrThrow),\n/* harmony export */   $getNearestNodeOfType: () => (/* binding */ $getNearestNodeOfType),\n/* harmony export */   $getNextRightPreorderNode: () => (/* binding */ $getNextRightPreorderNode),\n/* harmony export */   $getNextSiblingOrParentSibling: () => (/* binding */ $getNextSiblingOrParentSibling),\n/* harmony export */   $insertFirst: () => (/* binding */ $insertFirst),\n/* harmony export */   $insertNodeToNearestRoot: () => (/* binding */ $insertNodeToNearestRoot),\n/* harmony export */   $insertNodeToNearestRootAtCaret: () => (/* binding */ $insertNodeToNearestRootAtCaret),\n/* harmony export */   $isEditorIsNestedEditor: () => (/* binding */ $isEditorIsNestedEditor),\n/* harmony export */   $lastToFirstIterator: () => (/* binding */ $lastToFirstIterator),\n/* harmony export */   $restoreEditorState: () => (/* binding */ $restoreEditorState),\n/* harmony export */   $reverseDfs: () => (/* binding */ $reverseDfs),\n/* harmony export */   $reverseDfsIterator: () => (/* binding */ $reverseDfsIterator),\n/* harmony export */   $splitNode: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.$splitNode),\n/* harmony export */   $unwrapAndFilterDescendants: () => (/* binding */ $unwrapAndFilterDescendants),\n/* harmony export */   $unwrapNode: () => (/* binding */ $unwrapNode),\n/* harmony export */   $wrapNodeInElement: () => (/* binding */ $wrapNodeInElement),\n/* harmony export */   CAN_USE_BEFORE_INPUT: () => (/* binding */ CAN_USE_BEFORE_INPUT),\n/* harmony export */   CAN_USE_DOM: () => (/* binding */ CAN_USE_DOM),\n/* harmony export */   IS_ANDROID: () => (/* binding */ IS_ANDROID),\n/* harmony export */   IS_ANDROID_CHROME: () => (/* binding */ IS_ANDROID_CHROME),\n/* harmony export */   IS_APPLE: () => (/* binding */ IS_APPLE),\n/* harmony export */   IS_APPLE_WEBKIT: () => (/* binding */ IS_APPLE_WEBKIT),\n/* harmony export */   IS_CHROME: () => (/* binding */ IS_CHROME),\n/* harmony export */   IS_FIREFOX: () => (/* binding */ IS_FIREFOX),\n/* harmony export */   IS_IOS: () => (/* binding */ IS_IOS),\n/* harmony export */   IS_SAFARI: () => (/* binding */ IS_SAFARI),\n/* harmony export */   addClassNamesToElement: () => (/* binding */ addClassNamesToElement),\n/* harmony export */   calculateZoomLevel: () => (/* binding */ calculateZoomLevel),\n/* harmony export */   isBlockDomNode: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isBlockDomNode),\n/* harmony export */   isHTMLAnchorElement: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLAnchorElement),\n/* harmony export */   isHTMLElement: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement),\n/* harmony export */   isInlineDomNode: () => (/* reexport safe */ lexical__WEBPACK_IMPORTED_MODULE_0__.isInlineDomNode),\n/* harmony export */   isMimeType: () => (/* binding */ isMimeType),\n/* harmony export */   makeStateWrapper: () => (/* binding */ makeStateWrapper),\n/* harmony export */   markSelection: () => (/* binding */ markSelection),\n/* harmony export */   mediaFileReader: () => (/* binding */ mediaFileReader),\n/* harmony export */   mergeRegister: () => (/* binding */ mergeRegister),\n/* harmony export */   objectKlassEquals: () => (/* binding */ objectKlassEquals),\n/* harmony export */   positionNodeOnRange: () => (/* binding */ mlcPositionNodeOnRange),\n/* harmony export */   registerNestedElementResolver: () => (/* binding */ registerNestedElementResolver),\n/* harmony export */   removeClassNamesFromElement: () => (/* binding */ removeClassNamesFromElement),\n/* harmony export */   selectionAlwaysOnDisplay: () => (/* binding */ selectionAlwaysOnDisplay)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/selection */ \"(ssr)/../../node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM$1 = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst documentMode = CAN_USE_DOM$1 && 'documentMode' in document ? document.documentMode : null;\nconst IS_APPLE$1 = CAN_USE_DOM$1 && /Mac|iPod|iPhone|iPad/.test(navigator.platform);\nconst IS_FIREFOX$1 = CAN_USE_DOM$1 && /^(?!.*Seamonkey)(?=.*Firefox).*/i.test(navigator.userAgent);\nconst CAN_USE_BEFORE_INPUT$1 = CAN_USE_DOM$1 && 'InputEvent' in window && !documentMode ? 'getTargetRanges' in new window.InputEvent('input') : false;\nconst IS_SAFARI$1 = CAN_USE_DOM$1 && /Version\\/[\\d.]+.*Safari/.test(navigator.userAgent);\nconst IS_IOS$1 = CAN_USE_DOM$1 && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\nconst IS_ANDROID$1 = CAN_USE_DOM$1 && /Android/.test(navigator.userAgent);\n\n// Keep these in case we need to use them in the future.\n// export const IS_WINDOWS: boolean = CAN_USE_DOM && /Win/.test(navigator.platform);\nconst IS_CHROME$1 = CAN_USE_DOM$1 && /^(?=.*Chrome).*/i.test(navigator.userAgent);\n// export const canUseTextInputEvent: boolean = CAN_USE_DOM && 'TextEvent' in window && !documentMode;\n\nconst IS_ANDROID_CHROME$1 = CAN_USE_DOM$1 && IS_ANDROID$1 && IS_CHROME$1;\nconst IS_APPLE_WEBKIT$1 = CAN_USE_DOM$1 && /AppleWebKit\\/[\\d.]+/.test(navigator.userAgent) && !IS_CHROME$1;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction normalizeClassNames(...classNames) {\n  const rval = [];\n  for (const className of classNames) {\n    if (className && typeof className === 'string') {\n      for (const [s] of className.matchAll(/\\S+/g)) {\n        rval.push(s);\n      }\n    }\n  }\n  return rval;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Returns a function that will execute all functions passed when called. It is generally used\n * to register multiple lexical listeners and then tear them down with a single function call, such\n * as React's useEffect hook.\n * @example\n * ```ts\n * useEffect(() => {\n *   return mergeRegister(\n *     editor.registerCommand(...registerCommand1 logic),\n *     editor.registerCommand(...registerCommand2 logic),\n *     editor.registerCommand(...registerCommand3 logic)\n *   )\n * }, [editor])\n * ```\n * In this case, useEffect is returning the function returned by mergeRegister as a cleanup\n * function to be executed after either the useEffect runs again (due to one of its dependencies\n * updating) or the component it resides in unmounts.\n * Note the functions don't neccesarily need to be in an array as all arguments\n * are considered to be the func argument and spread from there.\n * The order of cleanup is the reverse of the argument order. Generally it is\n * expected that the first \"acquire\" will be \"released\" last (LIFO order),\n * because a later step may have some dependency on an earlier one.\n * @param func - An array of cleanup functions meant to be executed by the returned function.\n * @returns the function which executes all the passed cleanup functions.\n */\nfunction mergeRegister(...func) {\n  return () => {\n    for (let i = func.length - 1; i >= 0; i--) {\n      func[i]();\n    }\n    // Clean up the references and make future calls a no-op\n    func.length = 0;\n  };\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction px(value) {\n  return `${value}px`;\n}\n\nconst mutationObserverConfig = {\n  attributes: true,\n  characterData: true,\n  childList: true,\n  subtree: true\n};\nfunction prependDOMNode(parent, node) {\n  parent.insertBefore(node, parent.firstChild);\n}\n\n/**\n * Place one or multiple newly created Nodes at the passed Range's position.\n * Multiple nodes will only be created when the Range spans multiple lines (aka\n * client rects).\n *\n * This function can come particularly useful to highlight particular parts of\n * the text without interfering with the EditorState, that will often replicate\n * the state across collab and clipboard.\n *\n * This function accounts for DOM updates which can modify the passed Range.\n * Hence, the function return to remove the listener.\n */\nfunction mlcPositionNodeOnRange(editor, range, onReposition) {\n  let rootDOMNode = null;\n  let parentDOMNode = null;\n  let observer = null;\n  let lastNodes = [];\n  const wrapperNode = document.createElement('div');\n  wrapperNode.style.position = 'relative';\n  function position() {\n    if (!(rootDOMNode !== null)) {\n      formatDevErrorMessage(`Unexpected null rootDOMNode`);\n    }\n    if (!(parentDOMNode !== null)) {\n      formatDevErrorMessage(`Unexpected null parentDOMNode`);\n    }\n    const {\n      left: parentLeft,\n      top: parentTop\n    } = parentDOMNode.getBoundingClientRect();\n    const rects = (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_1__.createRectsFromDOMRange)(editor, range);\n    if (!wrapperNode.isConnected) {\n      prependDOMNode(parentDOMNode, wrapperNode);\n    }\n    let hasRepositioned = false;\n    for (let i = 0; i < rects.length; i++) {\n      const rect = rects[i];\n      // Try to reuse the previously created Node when possible, no need to\n      // remove/create on the most common case reposition case\n      const rectNode = lastNodes[i] || document.createElement('div');\n      const rectNodeStyle = rectNode.style;\n      if (rectNodeStyle.position !== 'absolute') {\n        rectNodeStyle.position = 'absolute';\n        hasRepositioned = true;\n      }\n      const left = px(rect.left - parentLeft);\n      if (rectNodeStyle.left !== left) {\n        rectNodeStyle.left = left;\n        hasRepositioned = true;\n      }\n      const top = px(rect.top - parentTop);\n      if (rectNodeStyle.top !== top) {\n        rectNode.style.top = top;\n        hasRepositioned = true;\n      }\n      const width = px(rect.width);\n      if (rectNodeStyle.width !== width) {\n        rectNode.style.width = width;\n        hasRepositioned = true;\n      }\n      const height = px(rect.height);\n      if (rectNodeStyle.height !== height) {\n        rectNode.style.height = height;\n        hasRepositioned = true;\n      }\n      if (rectNode.parentNode !== wrapperNode) {\n        wrapperNode.append(rectNode);\n        hasRepositioned = true;\n      }\n      lastNodes[i] = rectNode;\n    }\n    while (lastNodes.length > rects.length) {\n      lastNodes.pop();\n    }\n    if (hasRepositioned) {\n      onReposition(lastNodes);\n    }\n  }\n  function stop() {\n    parentDOMNode = null;\n    rootDOMNode = null;\n    if (observer !== null) {\n      observer.disconnect();\n    }\n    observer = null;\n    wrapperNode.remove();\n    for (const node of lastNodes) {\n      node.remove();\n    }\n    lastNodes = [];\n  }\n  function restart() {\n    const currentRootDOMNode = editor.getRootElement();\n    if (currentRootDOMNode === null) {\n      return stop();\n    }\n    const currentParentDOMNode = currentRootDOMNode.parentElement;\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(currentParentDOMNode)) {\n      return stop();\n    }\n    stop();\n    rootDOMNode = currentRootDOMNode;\n    parentDOMNode = currentParentDOMNode;\n    observer = new MutationObserver(mutations => {\n      const nextRootDOMNode = editor.getRootElement();\n      const nextParentDOMNode = nextRootDOMNode && nextRootDOMNode.parentElement;\n      if (nextRootDOMNode !== rootDOMNode || nextParentDOMNode !== parentDOMNode) {\n        return restart();\n      }\n      for (const mutation of mutations) {\n        if (!wrapperNode.contains(mutation.target)) {\n          // TODO throttle\n          return position();\n        }\n      }\n    });\n    observer.observe(currentParentDOMNode, mutationObserverConfig);\n    position();\n  }\n  const removeRootListener = editor.registerRootListener(restart);\n  return () => {\n    removeRootListener();\n    stop();\n  };\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction rangeTargetFromPoint(point, node, dom) {\n  if (point.type === 'text' || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n    const textDOM = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.getDOMTextNode)(dom) || dom;\n    return [textDOM, point.offset];\n  } else {\n    const slot = node.getDOMSlot(dom);\n    return [slot.element, slot.getFirstChildOffset() + point.offset];\n  }\n}\nfunction rangeFromPoints(editor, anchor, anchorNode, anchorDOM, focus, focusNode, focusDOM) {\n  const editorDocument = editor._window ? editor._window.document : document;\n  const range = editorDocument.createRange();\n  if (focusNode.isBefore(anchorNode)) {\n    range.setStart(...rangeTargetFromPoint(focus, focusNode, focusDOM));\n    range.setEnd(...rangeTargetFromPoint(anchor, anchorNode, anchorDOM));\n  } else {\n    range.setStart(...rangeTargetFromPoint(anchor, anchorNode, anchorDOM));\n    range.setEnd(...rangeTargetFromPoint(focus, focusNode, focusDOM));\n  }\n  return range;\n}\n/**\n * Place one or multiple newly created Nodes at the current selection. Multiple\n * nodes will only be created when the selection spans multiple lines (aka\n * client rects).\n *\n * This function can come useful when you want to show the selection but the\n * editor has been focused away.\n */\nfunction markSelection(editor, onReposition) {\n  let previousAnchorNode = null;\n  let previousAnchorNodeDOM = null;\n  let previousAnchorOffset = null;\n  let previousFocusNode = null;\n  let previousFocusNodeDOM = null;\n  let previousFocusOffset = null;\n  let removeRangeListener = () => {};\n  function compute(editorState) {\n    editorState.read(() => {\n      const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n        // TODO\n        previousAnchorNode = null;\n        previousAnchorOffset = null;\n        previousFocusNode = null;\n        previousFocusOffset = null;\n        removeRangeListener();\n        removeRangeListener = () => {};\n        return;\n      }\n      const {\n        anchor,\n        focus\n      } = selection;\n      const currentAnchorNode = anchor.getNode();\n      const currentAnchorNodeKey = currentAnchorNode.getKey();\n      const currentAnchorOffset = anchor.offset;\n      const currentFocusNode = focus.getNode();\n      const currentFocusNodeKey = currentFocusNode.getKey();\n      const currentFocusOffset = focus.offset;\n      const currentAnchorNodeDOM = editor.getElementByKey(currentAnchorNodeKey);\n      const currentFocusNodeDOM = editor.getElementByKey(currentFocusNodeKey);\n      const differentAnchorDOM = previousAnchorNode === null || currentAnchorNodeDOM !== previousAnchorNodeDOM || currentAnchorOffset !== previousAnchorOffset || currentAnchorNodeKey !== previousAnchorNode.getKey();\n      const differentFocusDOM = previousFocusNode === null || currentFocusNodeDOM !== previousFocusNodeDOM || currentFocusOffset !== previousFocusOffset || currentFocusNodeKey !== previousFocusNode.getKey();\n      if ((differentAnchorDOM || differentFocusDOM) && currentAnchorNodeDOM !== null && currentFocusNodeDOM !== null) {\n        const range = rangeFromPoints(editor, anchor, currentAnchorNode, currentAnchorNodeDOM, focus, currentFocusNode, currentFocusNodeDOM);\n        removeRangeListener();\n        removeRangeListener = mlcPositionNodeOnRange(editor, range, domNodes => {\n          if (onReposition === undefined) {\n            for (const domNode of domNodes) {\n              const domNodeStyle = domNode.style;\n              if (domNodeStyle.background !== 'Highlight') {\n                domNodeStyle.background = 'Highlight';\n              }\n              if (domNodeStyle.color !== 'HighlightText') {\n                domNodeStyle.color = 'HighlightText';\n              }\n              if (domNodeStyle.marginTop !== px(-1.5)) {\n                domNodeStyle.marginTop = px(-1.5);\n              }\n              if (domNodeStyle.paddingTop !== px(4)) {\n                domNodeStyle.paddingTop = px(4);\n              }\n              if (domNodeStyle.paddingBottom !== px(0)) {\n                domNodeStyle.paddingBottom = px(0);\n              }\n            }\n          } else {\n            onReposition(domNodes);\n          }\n        });\n      }\n      previousAnchorNode = currentAnchorNode;\n      previousAnchorNodeDOM = currentAnchorNodeDOM;\n      previousAnchorOffset = currentAnchorOffset;\n      previousFocusNode = currentFocusNode;\n      previousFocusNodeDOM = currentFocusNodeDOM;\n      previousFocusOffset = currentFocusOffset;\n    });\n  }\n  compute(editor.getEditorState());\n  return mergeRegister(editor.registerUpdateListener(({\n    editorState\n  }) => compute(editorState)), () => {\n    removeRangeListener();\n  });\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction selectionAlwaysOnDisplay(editor) {\n  let removeSelectionMark = null;\n  const onSelectionChange = () => {\n    const domSelection = getSelection();\n    const domAnchorNode = domSelection && domSelection.anchorNode;\n    const editorRootElement = editor.getRootElement();\n    const isSelectionInsideEditor = domAnchorNode !== null && editorRootElement !== null && editorRootElement.contains(domAnchorNode);\n    if (isSelectionInsideEditor) {\n      if (removeSelectionMark !== null) {\n        removeSelectionMark();\n        removeSelectionMark = null;\n      }\n    } else {\n      if (removeSelectionMark === null) {\n        removeSelectionMark = markSelection(editor);\n      }\n    }\n  };\n  document.addEventListener('selectionchange', onSelectionChange);\n  return () => {\n    if (removeSelectionMark !== null) {\n      removeSelectionMark();\n    }\n    document.removeEventListener('selectionchange', onSelectionChange);\n  };\n}\n\n// Hotfix to export these with inlined types #5918\nconst CAN_USE_BEFORE_INPUT = CAN_USE_BEFORE_INPUT$1;\nconst CAN_USE_DOM = CAN_USE_DOM$1;\nconst IS_ANDROID = IS_ANDROID$1;\nconst IS_ANDROID_CHROME = IS_ANDROID_CHROME$1;\nconst IS_APPLE = IS_APPLE$1;\nconst IS_APPLE_WEBKIT = IS_APPLE_WEBKIT$1;\nconst IS_CHROME = IS_CHROME$1;\nconst IS_FIREFOX = IS_FIREFOX$1;\nconst IS_IOS = IS_IOS$1;\nconst IS_SAFARI = IS_SAFARI$1;\n\n/**\n * Takes an HTML element and adds the classNames passed within an array,\n * ignoring any non-string types. A space can be used to add multiple classes\n * eg. addClassNamesToElement(element, ['element-inner active', true, null])\n * will add both 'element-inner' and 'active' as classes to that element.\n * @param element - The element in which the classes are added\n * @param classNames - An array defining the class names to add to the element\n */\nfunction addClassNamesToElement(element, ...classNames) {\n  const classesToAdd = normalizeClassNames(...classNames);\n  if (classesToAdd.length > 0) {\n    element.classList.add(...classesToAdd);\n  }\n}\n\n/**\n * Takes an HTML element and removes the classNames passed within an array,\n * ignoring any non-string types. A space can be used to remove multiple classes\n * eg. removeClassNamesFromElement(element, ['active small', true, null])\n * will remove both the 'active' and 'small' classes from that element.\n * @param element - The element in which the classes are removed\n * @param classNames - An array defining the class names to remove from the element\n */\nfunction removeClassNamesFromElement(element, ...classNames) {\n  const classesToRemove = normalizeClassNames(...classNames);\n  if (classesToRemove.length > 0) {\n    element.classList.remove(...classesToRemove);\n  }\n}\n\n/**\n * Returns true if the file type matches the types passed within the acceptableMimeTypes array, false otherwise.\n * The types passed must be strings and are CASE-SENSITIVE.\n * eg. if file is of type 'text' and acceptableMimeTypes = ['TEXT', 'IMAGE'] the function will return false.\n * @param file - The file you want to type check.\n * @param acceptableMimeTypes - An array of strings of types which the file is checked against.\n * @returns true if the file is an acceptable mime type, false otherwise.\n */\nfunction isMimeType(file, acceptableMimeTypes) {\n  for (const acceptableType of acceptableMimeTypes) {\n    if (file.type.startsWith(acceptableType)) {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\n * Lexical File Reader with:\n *  1. MIME type support\n *  2. batched results (HistoryPlugin compatibility)\n *  3. Order aware (respects the order when multiple Files are passed)\n *\n * const filesResult = await mediaFileReader(files, ['image/']);\n * filesResult.forEach(file => editor.dispatchCommand('INSERT_IMAGE', \\\\{\n *   src: file.result,\n * \\\\}));\n */\nfunction mediaFileReader(files, acceptableMimeTypes) {\n  const filesIterator = files[Symbol.iterator]();\n  return new Promise((resolve, reject) => {\n    const processed = [];\n    const handleNextFile = () => {\n      const {\n        done,\n        value: file\n      } = filesIterator.next();\n      if (done) {\n        return resolve(processed);\n      }\n      const fileReader = new FileReader();\n      fileReader.addEventListener('error', reject);\n      fileReader.addEventListener('load', () => {\n        const result = fileReader.result;\n        if (typeof result === 'string') {\n          processed.push({\n            file,\n            result\n          });\n        }\n        handleNextFile();\n      });\n      if (isMimeType(file, acceptableMimeTypes)) {\n        fileReader.readAsDataURL(file);\n      } else {\n        handleNextFile();\n      }\n    };\n    handleNextFile();\n  });\n}\n/**\n * \"Depth-First Search\" starts at the root/top node of a tree and goes as far as it can down a branch end\n * before backtracking and finding a new path. Consider solving a maze by hugging either wall, moving down a\n * branch until you hit a dead-end (leaf) and backtracking to find the nearest branching path and repeat.\n * It will then return all the nodes found in the search in an array of objects.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An array of objects of all the nodes found by the search, including their depth into the tree.\n * \\\\{depth: number, node: LexicalNode\\\\} It will always return at least 1 node (the start node).\n */\nfunction $dfs(startNode, endNode) {\n  return Array.from($dfsIterator(startNode, endNode));\n}\n\n/**\n * Get the adjacent caret in the same direction\n *\n * @param caret A caret or null\n * @returns `caret.getAdjacentCaret()` or `null`\n */\nfunction $getAdjacentCaret(caret) {\n  return caret ? caret.getAdjacentCaret() : null;\n}\n\n/**\n * $dfs iterator (right to left). Tree traversal is done on the fly as new values are requested with O(1) memory.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).\n */\nfunction $reverseDfs(startNode, endNode) {\n  return Array.from($reverseDfsIterator(startNode, endNode));\n}\n\n/**\n * $dfs iterator (left to right). Tree traversal is done on the fly as new values are requested with O(1) memory.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).\n */\nfunction $dfsIterator(startNode, endNode) {\n  return $dfsCaretIterator('next', startNode, endNode);\n}\nfunction $getEndCaret(startNode, direction) {\n  const rval = $getAdjacentSiblingOrParentSiblingCaret((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(startNode, direction));\n  return rval && rval[0];\n}\nfunction $dfsCaretIterator(direction, startNode, endNode) {\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  const start = startNode || root;\n  const startCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(start) ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(start, direction) : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(start, direction);\n  const startDepth = $getDepth(start);\n  const endCaret = endNode ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaretOrSelf)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(endNode, direction))) : $getEndCaret(start, direction);\n  let depth = startDepth;\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.makeStepwiseIterator)({\n    hasNext: state => state !== null,\n    initial: startCaret,\n    map: state => ({\n      depth,\n      node: state.origin\n    }),\n    step: state => {\n      if (state.isSameNodeCaret(endCaret)) {\n        return null;\n      }\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(state)) {\n        depth++;\n      }\n      const rval = $getAdjacentSiblingOrParentSiblingCaret(state);\n      if (!rval || rval[0].isSameNodeCaret(endCaret)) {\n        return null;\n      }\n      depth += rval[1];\n      return rval[0];\n    }\n  });\n}\n\n/**\n * Returns the Node sibling when this exists, otherwise the closest parent sibling. For example\n * R -> P -> T1, T2\n *   -> P2\n * returns T2 for node T1, P2 for node T2, and null for node P2.\n * @param node LexicalNode.\n * @returns An array (tuple) containing the found Lexical node and the depth difference, or null, if this node doesn't exist.\n */\nfunction $getNextSiblingOrParentSibling(node) {\n  const rval = $getAdjacentSiblingOrParentSiblingCaret((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(node, 'next'));\n  return rval && [rval[0].origin, rval[1]];\n}\nfunction $getDepth(node) {\n  let depth = -1;\n  for (let innerNode = node; innerNode !== null; innerNode = innerNode.getParent()) {\n    depth++;\n  }\n  return depth;\n}\n\n/**\n * Performs a right-to-left preorder tree traversal.\n * From the starting node it goes to the rightmost child, than backtracks to parent and finds new rightmost path.\n * It will return the next node in traversal sequence after the startingNode.\n * The traversal is similar to $dfs functions above, but the nodes are visited right-to-left, not left-to-right.\n * @param startingNode - The node to start the search.\n * @returns The next node in pre-order right to left traversal sequence or `null`, if the node does not exist\n */\nfunction $getNextRightPreorderNode(startingNode) {\n  const startCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaretOrSelf)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(startingNode, 'previous'));\n  const next = $getAdjacentSiblingOrParentSiblingCaret(startCaret, 'root');\n  return next && next[0].origin;\n}\n\n/**\n * $dfs iterator (right to left). Tree traversal is done on the fly as new values are requested with O(1) memory.\n * @param startNode - The node to start the search, if omitted, it will start at the root node.\n * @param endNode - The node to end the search, if omitted, it will find all descendants of the startingNode.\n * @returns An iterator, each yielded value is a DFSNode. It will always return at least 1 node (the start node).\n */\nfunction $reverseDfsIterator(startNode, endNode) {\n  return $dfsCaretIterator('previous', startNode, endNode);\n}\n\n/**\n * Takes a node and traverses up its ancestors (toward the root node)\n * in order to find a specific type of node.\n * @param node - the node to begin searching.\n * @param klass - an instance of the type of node to look for.\n * @returns the node of type klass that was passed, or null if none exist.\n */\nfunction $getNearestNodeOfType(node, klass) {\n  let parent = node;\n  while (parent != null) {\n    if (parent instanceof klass) {\n      return parent;\n    }\n    parent = parent.getParent();\n  }\n  return null;\n}\n\n/**\n * Returns the element node of the nearest ancestor, otherwise throws an error.\n * @param startNode - The starting node of the search\n * @returns The ancestor node found\n */\nfunction $getNearestBlockElementAncestorOrThrow(startNode) {\n  const blockNode = $findMatchingParent(startNode, node => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node) && !node.isInline());\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(blockNode)) {\n    {\n      formatDevErrorMessage(`Expected node ${startNode.__key} to have closest block element node.`);\n    }\n  }\n  return blockNode;\n}\n/**\n * Starts with a node and moves up the tree (toward the root node) to find a matching node based on\n * the search parameters of the findFn. (Consider JavaScripts' .find() function where a testing function must be\n * passed as an argument. eg. if( (node) => node.__type === 'div') ) return true; otherwise return false\n * @param startingNode - The node where the search starts.\n * @param findFn - A testing function that returns true if the current node satisfies the testing parameters.\n * @returns A parent node that matches the findFn parameters, or null if one wasn't found.\n */\nconst $findMatchingParent = (startingNode, findFn) => {\n  let curr = startingNode;\n  while (curr !== (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)() && curr != null) {\n    if (findFn(curr)) {\n      return curr;\n    }\n    curr = curr.getParent();\n  }\n  return null;\n};\n\n/**\n * Attempts to resolve nested element nodes of the same type into a single node of that type.\n * It is generally used for marks/commenting\n * @param editor - The lexical editor\n * @param targetNode - The target for the nested element to be extracted from.\n * @param cloneNode - See {@link $createMarkNode}\n * @param handleOverlap - Handles any overlap between the node to extract and the targetNode\n * @returns The lexical editor\n */\nfunction registerNestedElementResolver(editor, targetNode, cloneNode, handleOverlap) {\n  const $isTargetNode = node => {\n    return node instanceof targetNode;\n  };\n  const $findMatch = node => {\n    // First validate we don't have any children that are of the target,\n    // as we need to handle them first.\n    const children = node.getChildren();\n    for (let i = 0; i < children.length; i++) {\n      const child = children[i];\n      if ($isTargetNode(child)) {\n        return null;\n      }\n    }\n    let parentNode = node;\n    let childNode = node;\n    while (parentNode !== null) {\n      childNode = parentNode;\n      parentNode = parentNode.getParent();\n      if ($isTargetNode(parentNode)) {\n        return {\n          child: childNode,\n          parent: parentNode\n        };\n      }\n    }\n    return null;\n  };\n  const $elementNodeTransform = node => {\n    const match = $findMatch(node);\n    if (match !== null) {\n      const {\n        child,\n        parent\n      } = match;\n\n      // Simple path, we can move child out and siblings into a new parent.\n\n      if (child.is(node)) {\n        handleOverlap(parent, node);\n        const nextSiblings = child.getNextSiblings();\n        const nextSiblingsLength = nextSiblings.length;\n        parent.insertAfter(child);\n        if (nextSiblingsLength !== 0) {\n          const newParent = cloneNode(parent);\n          child.insertAfter(newParent);\n          for (let i = 0; i < nextSiblingsLength; i++) {\n            newParent.append(nextSiblings[i]);\n          }\n        }\n        if (!parent.canBeEmpty() && parent.getChildrenSize() === 0) {\n          parent.remove();\n        }\n      }\n    }\n  };\n  return editor.registerNodeTransform(targetNode, $elementNodeTransform);\n}\n\n/**\n * Clones the editor and marks it as dirty to be reconciled. If there was a selection,\n * it would be set back to its previous state, or null otherwise.\n * @param editor - The lexical editor\n * @param editorState - The editor's state\n */\nfunction $restoreEditorState(editor, editorState) {\n  const FULL_RECONCILE = 2;\n  const nodeMap = new Map();\n  const activeEditorState = editor._pendingEditorState;\n  for (const [key, node] of editorState._nodeMap) {\n    nodeMap.set(key, (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$cloneWithProperties)(node));\n  }\n  if (activeEditorState) {\n    activeEditorState._nodeMap = nodeMap;\n  }\n  editor._dirtyType = FULL_RECONCILE;\n  const selection = editorState._selection;\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(selection === null ? null : selection.clone());\n}\n\n/**\n * If the selected insertion area is the root/shadow root node (see {@link lexical!$isRootOrShadowRoot}),\n * the node will be appended there, otherwise, it will be inserted before the insertion area.\n * If there is no selection where the node is to be inserted, it will be appended after any current nodes\n * within the tree, as a child of the root node. A paragraph will then be added after the inserted node and selected.\n * @param node - The node to be inserted\n * @returns The node after its insertion\n */\nfunction $insertNodeToNearestRoot(node) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)() || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getPreviousSelection)();\n  let initialCaret;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    initialCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$caretFromPoint)(selection.focus, 'next');\n  } else {\n    if (selection != null) {\n      const nodes = selection.getNodes();\n      const lastNode = nodes[nodes.length - 1];\n      if (lastNode) {\n        initialCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(lastNode, 'next');\n      }\n    }\n    initialCaret = initialCaret || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)(), 'previous').getFlipped().insert((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)());\n  }\n  const insertCaret = $insertNodeToNearestRootAtCaret(node, initialCaret);\n  const adjacent = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(insertCaret);\n  const selectionCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isChildCaret)(adjacent) ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeCaret)(adjacent) : insertCaret;\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelectionFromCaretRange)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCollapsedCaretRange)(selectionCaret));\n  return node.getLatest();\n}\n\n/**\n * If the insertion caret is the root/shadow root node (see {@link lexical!$isRootOrShadowRoot}),\n * the node will be inserted there, otherwise the parent nodes will be split according to the\n * given options.\n * @param node - The node to be inserted\n * @param caret - The location to insert or split from\n * @returns The node after its insertion\n */\nfunction $insertNodeToNearestRootAtCaret(node, caret, options) {\n  let insertCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCaretInDirection)(caret, 'next');\n  for (let nextCaret = insertCaret; nextCaret; nextCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$splitAtPointCaretNext)(nextCaret, options)) {\n    insertCaret = nextCaret;\n  }\n  if (!!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextPointCaret)(insertCaret)) {\n    formatDevErrorMessage(`$insertNodeToNearestRootAtCaret: An unattached TextNode can not be split`);\n  }\n  insertCaret.insert(node.isInline() ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)().append(node) : node);\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getCaretInDirection)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(node.getLatest(), 'next'), caret.direction);\n}\n\n/**\n * Wraps the node into another node created from a createElementNode function, eg. $createParagraphNode\n * @param node - Node to be wrapped.\n * @param createElementNode - Creates a new lexical element to wrap the to-be-wrapped node and returns it.\n * @returns A new lexical element with the previous node appended within (as a child, including its children).\n */\nfunction $wrapNodeInElement(node, createElementNode) {\n  const elementNode = createElementNode();\n  node.replace(elementNode);\n  elementNode.append(node);\n  return elementNode;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\n\n/**\n * @param object = The instance of the type\n * @param objectClass = The class of the type\n * @returns Whether the object is has the same Klass of the objectClass, ignoring the difference across window (e.g. different iframs)\n */\nfunction objectKlassEquals(object, objectClass) {\n  return object !== null ? Object.getPrototypeOf(object).constructor.name === objectClass.name : false;\n}\n\n/**\n * Filter the nodes\n * @param nodes Array of nodes that needs to be filtered\n * @param filterFn A filter function that returns node if the current node satisfies the condition otherwise null\n * @returns Array of filtered nodes\n */\n\nfunction $filter(nodes, filterFn) {\n  const result = [];\n  for (let i = 0; i < nodes.length; i++) {\n    const node = filterFn(nodes[i]);\n    if (node !== null) {\n      result.push(node);\n    }\n  }\n  return result;\n}\n/**\n * Appends the node before the first child of the parent node\n * @param parent A parent node\n * @param node Node that needs to be appended\n */\nfunction $insertFirst(parent, node) {\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(parent, 'next').insert(node);\n}\nlet NEEDS_MANUAL_ZOOM = IS_FIREFOX || !CAN_USE_DOM ? false : undefined;\nfunction needsManualZoom() {\n  if (NEEDS_MANUAL_ZOOM === undefined) {\n    // If the browser implements standardized CSS zoom, then the client rect\n    // will be wider after zoom is applied\n    // https://chromestatus.com/feature/5198254868529152\n    // https://github.com/facebook/lexical/issues/6863\n    const div = document.createElement('div');\n    div.style.cssText = 'position: absolute; opacity: 0; width: 100px; left: -1000px;';\n    document.body.appendChild(div);\n    const noZoom = div.getBoundingClientRect();\n    div.style.setProperty('zoom', '2');\n    NEEDS_MANUAL_ZOOM = div.getBoundingClientRect().width === noZoom.width;\n    document.body.removeChild(div);\n  }\n  return NEEDS_MANUAL_ZOOM;\n}\n\n/**\n * Calculates the zoom level of an element as a result of using\n * css zoom property. For browsers that implement standardized CSS\n * zoom (Firefox, Chrome >= 128), this will always return 1.\n * @param element\n */\nfunction calculateZoomLevel(element) {\n  let zoom = 1;\n  if (needsManualZoom()) {\n    while (element) {\n      zoom *= Number(window.getComputedStyle(element).getPropertyValue('zoom'));\n      element = element.parentElement;\n    }\n  }\n  return zoom;\n}\n\n/**\n * Checks if the editor is a nested editor created by LexicalNestedComposer\n */\nfunction $isEditorIsNestedEditor(editor) {\n  return editor._parentEditor !== null;\n}\n\n/**\n * A depth first last-to-first traversal of root that stops at each node that matches\n * $predicate and ensures that its parent is root. This is typically used to discard\n * invalid or unsupported wrapping nodes. For example, a TableNode must only have\n * TableRowNode as children, but an importer might add invalid nodes based on\n * caption, tbody, thead, etc. and this will unwrap and discard those.\n *\n * @param root The root to start the traversal\n * @param $predicate Should return true for nodes that are permitted to be children of root\n * @returns true if this unwrapped or removed any nodes\n */\nfunction $unwrapAndFilterDescendants(root, $predicate) {\n  return $unwrapAndFilterDescendantsImpl(root, $predicate, null);\n}\nfunction $unwrapAndFilterDescendantsImpl(root, $predicate, $onSuccess) {\n  let didMutate = false;\n  for (const node of $lastToFirstIterator(root)) {\n    if ($predicate(node)) {\n      if ($onSuccess !== null) {\n        $onSuccess(node);\n      }\n      continue;\n    }\n    didMutate = true;\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n      $unwrapAndFilterDescendantsImpl(node, $predicate, $onSuccess || (child => node.insertAfter(child)));\n    }\n    node.remove();\n  }\n  return didMutate;\n}\n\n/**\n * A depth first traversal of the children array that stops at and collects\n * each node that `$predicate` matches. This is typically used to discard\n * invalid or unsupported wrapping nodes on a children array in the `after`\n * of an {@link lexical!DOMConversionOutput}. For example, a TableNode must only have\n * TableRowNode as children, but an importer might add invalid nodes based on\n * caption, tbody, thead, etc. and this will unwrap and discard those.\n *\n * This function is read-only and performs no mutation operations, which makes\n * it suitable for import and export purposes but likely not for any in-place\n * mutation. You should use {@link $unwrapAndFilterDescendants} for in-place\n * mutations such as node transforms.\n *\n * @param children The children to traverse\n * @param $predicate Should return true for nodes that are permitted to be children of root\n * @returns The children or their descendants that match $predicate\n */\n\nfunction $descendantsMatching(children, $predicate) {\n  const result = [];\n  const stack = Array.from(children).reverse();\n  for (let child = stack.pop(); child !== undefined; child = stack.pop()) {\n    if ($predicate(child)) {\n      result.push(child);\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(child)) {\n      for (const grandchild of $lastToFirstIterator(child)) {\n        stack.push(grandchild);\n      }\n    }\n  }\n  return result;\n}\n\n/**\n * Return an iterator that yields each child of node from first to last, taking\n * care to preserve the next sibling before yielding the value in case the caller\n * removes the yielded node.\n *\n * @param node The node whose children to iterate\n * @returns An iterator of the node's children\n */\nfunction $firstToLastIterator(node) {\n  return $childIterator((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(node, 'next'));\n}\n\n/**\n * Return an iterator that yields each child of node from last to first, taking\n * care to preserve the previous sibling before yielding the value in case the caller\n * removes the yielded node.\n *\n * @param node The node whose children to iterate\n * @returns An iterator of the node's children\n */\nfunction $lastToFirstIterator(node) {\n  return $childIterator((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getChildCaret)(node, 'previous'));\n}\nfunction $childIterator(startCaret) {\n  const seen = new Set() ;\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.makeStepwiseIterator)({\n    hasNext: lexical__WEBPACK_IMPORTED_MODULE_0__.$isSiblingCaret,\n    initial: startCaret.getAdjacentCaret(),\n    map: caret => {\n      const origin = caret.origin.getLatest();\n      if (seen !== null) {\n        const key = origin.getKey();\n        if (!!seen.has(key)) {\n          formatDevErrorMessage(`$childIterator: Cycle detected, node with key ${String(key)} has already been traversed`);\n        }\n        seen.add(key);\n      }\n      return origin;\n    },\n    step: caret => caret.getAdjacentCaret()\n  });\n}\n\n/**\n * Replace this node with its children\n *\n * @param node The ElementNode to unwrap and remove\n */\nfunction $unwrapNode(node) {\n  (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$rewindSiblingCaret)((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSiblingCaret)(node, 'next')).splice(1, node.getChildren());\n}\n\n/**\n * Returns the Node sibling when this exists, otherwise the closest parent sibling. For example\n * R -> P -> T1, T2\n *   -> P2\n * returns T2 for node T1, P2 for node T2, and null for node P2.\n * @param node LexicalNode.\n * @returns An array (tuple) containing the found Lexical node and the depth difference, or null, if this node doesn't exist.\n */\nfunction $getAdjacentSiblingOrParentSiblingCaret(startCaret, rootMode = 'root') {\n  let depthDiff = 0;\n  let caret = startCaret;\n  let nextCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(caret);\n  while (nextCaret === null) {\n    depthDiff--;\n    nextCaret = caret.getParentCaret(rootMode);\n    if (!nextCaret) {\n      return null;\n    }\n    caret = nextCaret;\n    nextCaret = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentChildCaret)(caret);\n  }\n  return nextCaret && [nextCaret, depthDiff];\n}\n\n/**\n * A wrapper that creates bound functions and methods for the\n * StateConfig to save some boilerplate when defining methods\n * or exporting only the accessors from your modules rather\n * than exposing the StateConfig directly.\n */\n\n/**\n * EXPERIMENTAL\n *\n * A convenience interface for working with {@link $getState} and\n * {@link $setState}.\n *\n * @param stateConfig The stateConfig to wrap with convenience functionality\n * @returns a StateWrapper\n */\nfunction makeStateWrapper(stateConfig) {\n  const $get = node => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getState)(node, stateConfig);\n  const $set = (node, valueOrUpdater) => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setState)(node, stateConfig, valueOrUpdater);\n  return {\n    $get,\n    $set,\n    accessors: [$get, $set],\n    makeGetterMethod: () => function $getter() {\n      return $get(this);\n    },\n    makeSetterMethod: () => function $setter(valueOrUpdater) {\n      return $set(this, valueOrUpdater);\n    },\n    stateConfig\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\n");

/***/ })

};
;