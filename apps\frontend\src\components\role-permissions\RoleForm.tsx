'use client'

import { useState } from 'react'
import { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Plus, Edit, Save, X } from 'lucide-react'
import { Formik, Field, ErrorMessage } from 'formik'
import * as Yup from 'yup'
import { toast } from 'sonner'

// Validation schema using Yup - Following Location Management patterns
const validationSchema = Yup.object({
  name: Yup.string()
    .required('Role name is required')
    .min(2, 'Role name must be at least 2 characters')
    .max(100, 'Role name must be less than 100 characters'),
  code: Yup.string()
    .required('Role code is required')
    .min(2, 'Role code must be at least 2 characters')
    .max(50, 'Role code must be at most 50 characters')
    .matches(/^[a-z_]+$/, 'Role code must contain only lowercase letters and underscores'),
  description: Yup.string()
    .max(500, 'Description must be less than 500 characters'),
  level: Yup.string()
    .required('Role level is required'),
  scope: Yup.string()
    .max(100, 'Scope must be less than 100 characters'),
  isActive: Yup.boolean(),
  priority: Yup.number()
    .min(0, 'Priority must be 0 or greater')
    .integer('Priority must be a whole number')
})

interface RoleFormProps {
  role?: any
  mode: 'create' | 'edit'
  trigger?: React.ReactNode
  onSuccess?: () => void
  open?: boolean
  onOpenChange?: (open: boolean) => void
}

export function RoleForm({ role, mode, trigger, onSuccess, open: externalOpen, onOpenChange }: RoleFormProps) {
  const [internalOpen, setInternalOpen] = useState(false)
  const open = externalOpen !== undefined ? externalOpen : internalOpen
  const setOpen = onOpenChange || setInternalOpen
  const { createRole, updateRole, isLoading } = useRolePermissionsStore()

  // Initial values for Formik - Following Location Management patterns
  const initialValues = {
    name: role?.name || '',
    code: role?.code || '',
    description: role?.description || '',
    level: role?.level || '3',
    scope: role?.scope || '',
    isActive: role?.isActive ?? true,
    priority: role?.priority || 0
  }

  const handleSubmit = async (values: any, { setSubmitting, resetForm }: any) => {
    try {
      // Prepare data for submission - Following Location Management patterns
      const submitData = {
        name: values.name,
        code: values.code.toLowerCase(),
        description: values.description,
        level: values.level,
        scope: values.scope,
        isActive: values.isActive,
        priority: values.priority,
        isSystemRole: false // New roles are not system roles
      }

      console.log('Submitting role data:', submitData)

      if (mode === 'create') {
        await createRole(submitData)
        toast.success('Role created successfully')
        resetForm()
      } else {
        await updateRole(role.id, submitData)
        toast.success('Role updated successfully')
      }

      // Only close dialog on successful submission
      setOpen(false)
      onSuccess?.()
    } catch (error) {
      console.error('Form submission error:', error)
      toast.error('Failed to save role')
      // Don't close dialog on error - let user fix the issues
    } finally {
      setSubmitting(false)
    }
  }

  const defaultTrigger = (
    <Button size="sm" className="gap-2">
      {mode === 'create' ? <Plus className="h-4 w-4" /> : <Edit className="h-4 w-4" />}
      {mode === 'create' ? 'Add Role' : 'Edit Role'}
    </Button>
  )

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {mode === 'create' ? <Plus className="h-5 w-5" /> : <Edit className="h-5 w-5" />}
            {mode === 'create' ? 'Create New Role' : 'Edit Role'}
          </DialogTitle>
        </DialogHeader>

        <Formik
          initialValues={initialValues}
          validationSchema={validationSchema}
          onSubmit={handleSubmit}
          enableReinitialize={true}
        >
          {({ values, errors, touched, handleChange, isSubmitting, setSubmitting, resetForm }) => (
            <div className="space-y-6">
              {/* Basic Information */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Basic Information</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="name">Role Name *</Label>
                    <Field name="name">
                      {({ field }: any) => (
                        <Input
                          {...field}
                          id="name"
                          placeholder="Enter role name"
                          className={errors.name && touched.name ? 'border-red-500' : ''}
                        />
                      )}
                    </Field>
                    <ErrorMessage name="name" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div>
                    <Label htmlFor="code">Role Code *</Label>
                    <Field name="code">
                      {({ field }: any) => (
                        <Input
                          {...field}
                          id="code"
                          placeholder="Enter role code (lowercase, underscores allowed)"
                          className={errors.code && touched.code ? 'border-red-500' : ''}
                        />
                      )}
                    </Field>
                    <ErrorMessage name="code" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Field name="description">
                      {({ field }: any) => (
                        <Textarea
                          {...field}
                          id="description"
                          placeholder="Enter role description"
                          rows={3}
                          className={errors.description && touched.description ? 'border-red-500' : ''}
                        />
                      )}
                    </Field>
                    <ErrorMessage name="description" component="div" className="text-red-500 text-sm mt-1" />
                  </div>
                </CardContent>
              </Card>

              {/* Role Configuration */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Role Configuration</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div>
                    <Label htmlFor="level">Role Level *</Label>
                    <Field name="level">
                      {({ field, form }: any) => (
                        <Select
                          value={field.value}
                          onValueChange={(value) => form.setFieldValue('level', value)}
                        >
                          <SelectTrigger className={errors.level && touched.level ? 'border-red-500' : ''}>
                            <SelectValue placeholder="Select role level" />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="1">Level 1 - Super Admin</SelectItem>
                            <SelectItem value="2">Level 2 - Institute Admin</SelectItem>
                            <SelectItem value="3">Level 3 - User Level</SelectItem>
                          </SelectContent>
                        </Select>
                      )}
                    </Field>
                    <ErrorMessage name="level" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div>
                    <Label htmlFor="scope">Scope</Label>
                    <Field name="scope">
                      {({ field }: any) => (
                        <Input
                          {...field}
                          id="scope"
                          placeholder="Enter role scope (optional)"
                          className={errors.scope && touched.scope ? 'border-red-500' : ''}
                        />
                      )}
                    </Field>
                    <ErrorMessage name="scope" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div>
                    <Label htmlFor="priority">Priority</Label>
                    <Field name="priority">
                      {({ field }: any) => (
                        <Input
                          {...field}
                          id="priority"
                          type="number"
                          min="0"
                          placeholder="Enter priority (0 = highest)"
                          className={errors.priority && touched.priority ? 'border-red-500' : ''}
                        />
                      )}
                    </Field>
                    <ErrorMessage name="priority" component="div" className="text-red-500 text-sm mt-1" />
                  </div>

                  <div className="flex items-center space-x-2">
                    <Field name="isActive">
                      {({ field, form }: any) => (
                        <Switch
                          id="isActive"
                          checked={field.value}
                          onCheckedChange={(checked) => form.setFieldValue('isActive', checked)}
                        />
                      )}
                    </Field>
                    <Label htmlFor="isActive">Active Role</Label>
                  </div>
                </CardContent>
              </Card>

              {/* Form Actions */}
              <div className="flex justify-end gap-3 pt-4 border-t">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setOpen(false)}
                  disabled={isSubmitting}
                >
                  <X className="h-4 w-4 mr-2" />
                  Cancel
                </Button>
                <Button
                  type="submit"
                  onClick={() => handleSubmit(values, { setSubmitting, resetForm })}
                  disabled={isSubmitting}
                  className="gap-2"
                >
                  <Save className="h-4 w-4" />
                  {isSubmitting ? 'Saving...' : (mode === 'create' ? 'Create Role' : 'Update Role')}
                </Button>
              </div>
            </div>
          )}
        </Formik>
      </DialogContent>
    </Dialog>
  )
}
