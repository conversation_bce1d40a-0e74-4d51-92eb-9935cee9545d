import { Endpoint } from 'payload/config'
import { PermissionService } from '../../services/permissionService'

const rolesEndpoints: Endpoint[] = [
  // Get user permissions
  {
    path: '/roles/user-permissions/:userId',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { userId } = req.params
        const currentUser = req.user

        // Check if user can view these permissions
        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        if (currentUser.role !== 'super_admin' &&
            currentUser.role !== 'institute_admin' &&
            currentUser.id !== userId) {
          return res.status(403).json({
            error: 'Access denied'
          })
        }

        const permissionService = new PermissionService(req.payload)
        const permissions = await permissionService.getUserPermissionSummary(userId)

        res.json({
          success: true,
          permissions
        })

      } catch (error) {
        console.error('Get user permissions error:', error)
        res.status(500).json({
          error: error.message || 'Internal server error'
        })
      }
    }
  },

  // Check specific permission
  {
    path: '/roles/check-permission',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { userId, permission, scope, targetId } = req.body
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        // Only allow checking own permissions or if admin
        if (currentUser.role !== 'super_admin' &&
            currentUser.role !== 'institute_admin' &&
            currentUser.id !== userId) {
          return res.status(403).json({
            error: 'Access denied'
          })
        }

        const permissionService = new PermissionService(req.payload)
        const hasPermission = await permissionService.checkPermission({
          userId,
          permission,
          scope,
          targetId
        })

        res.json({
          success: true,
          hasPermission
        })

      } catch (error) {
        console.error('Check permission error:', error)
        res.status(500).json({
          error: error.message || 'Internal server error'
        })
      }
    }
  },

  // Get permissions by category
  {
    path: '/roles/permissions-by-category',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { level } = req.query
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const where: any = { isActive: { equals: true } }

        if (level) {
          where.level = { greater_than_equal: parseInt(level as string) }
        }

        // Level restrictions for institute admins
        if (currentUser.role === 'institute_admin') {
          where.level = { greater_than_equal: 2 }
        }

        const permissions = await req.payload.find({
          collection: 'permissions',
          where,
          sort: 'category'
        })

        // Group by category
        const permissionsByCategory = permissions.docs.reduce((acc: Record<string, any[]>, permission: any) => {
          if (!acc[permission.category]) {
            acc[permission.category] = []
          }
          acc[permission.category].push(permission)
          return acc
        }, {})

        res.json({
          success: true,
          permissions: permissionsByCategory
        })

      } catch (error) {
        console.error('Get permissions by category error:', error)
        res.status(500).json({
          error: error.message || 'Internal server error'
        })
      }
    }
  },

  // Assign role to user
  {
    path: '/roles/assign-role',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { userId, roleId } = req.body
        const currentUser = req.user

        if (!currentUser || (currentUser.role !== 'super_admin' && currentUser.role !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can assign roles'
          })
        }

        // Get target user
        const targetUser = await req.payload.findByID({
          collection: 'users',
          id: userId
        })

        if (!targetUser) {
          return res.status(404).json({
            error: 'User not found'
          })
        }

        // Get role
        const role = await req.payload.findByID({
          collection: 'roles',
          id: roleId
        })

        if (!role) {
          return res.status(404).json({
            error: 'Role not found'
          })
        }

        // Check permissions for institute admins
        if (currentUser.role === 'institute_admin') {
          if (role.level === 1) {
            return res.status(403).json({
              error: 'Cannot assign Level 1 roles'
            })
          }

          if (targetUser.institute !== currentUser.institute) {
            return res.status(403).json({
              error: 'Can only assign roles to users in your institute'
            })
          }
        }

        const permissionService = new PermissionService(req.payload)
        await permissionService.assignRoleToUser(userId, roleId, currentUser.id)

        res.json({
          success: true,
          message: 'Role assigned successfully'
        })

      } catch (error) {
        console.error('Assign role error:', error)
        res.status(500).json({
          error: error.message || 'Failed to assign role'
        })
      }
    }
  },

  // Request custom permission
  {
    path: '/roles/request-custom-permission',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { userId, permissionId, type, scope, scopeTarget, reason } = req.body
        const currentUser = req.user

        if (!currentUser || (currentUser.role !== 'super_admin' && currentUser.role !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can request custom permissions'
          })
        }

        const permissionService = new PermissionService(req.payload)
        const requestId = await permissionService.requestCustomPermission(
          userId,
          permissionId,
          type,
          scope,
          scopeTarget,
          currentUser.id,
          reason
        )

        res.json({
          success: true,
          requestId,
          message: 'Custom permission requested successfully'
        })

      } catch (error) {
        console.error('Request custom permission error:', error)
        res.status(500).json({
          error: error.message || 'Failed to request custom permission'
        })
      }
    }
  },

  // Approve/Reject custom permission
  {
    path: '/roles/approve-custom-permission',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { permissionId, action, reason } = req.body
        const currentUser = req.user

        if (!currentUser || (currentUser.role !== 'super_admin' && currentUser.role !== 'institute_admin')) {
          return res.status(403).json({
            error: 'Only admins can approve/reject permissions'
          })
        }

        if (!['approve', 'reject'].includes(action)) {
          return res.status(400).json({
            error: 'Invalid action'
          })
        }

        const permissionService = new PermissionService(req.payload)

        if (action === 'approve') {
          await permissionService.approveCustomPermission(permissionId, currentUser.id, reason)
        } else {
          await permissionService.rejectCustomPermission(permissionId, currentUser.id, reason)
        }

        res.json({
          success: true,
          message: `Permission ${action}d successfully`
        })

      } catch (error) {
        console.error('Approve/reject permission error:', error)
        res.status(500).json({
          error: error.message || 'Failed to process permission request'
        })
      }
    }
  },

  // Get available roles for user level
  {
    path: '/roles/available-roles',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { level, instituteId } = req.query
        const currentUser = req.user

        if (!currentUser) {
          return res.status(401).json({
            error: 'Authentication required'
          })
        }

        const where: any = {
          isActive: { equals: true }
        }

        // Filter by level
        if (level) {
          where.level = { equals: parseInt(level as string) }
        }

        // Filter by institute for institute admins
        if (currentUser.role === 'institute_admin') {
          where.and = [
            { level: { greater_than_equal: 2 } },
            {
              or: [
                { 'scope.institute': { equals: currentUser.institute } },
                { 'scope.institute': { exists: false } }
              ]
            }
          ]
        }

        const roles = await req.payload.find({
          collection: 'roles',
          where,
          populate: ['permissions.permission'],
          sort: 'level'
        })

        res.json({
          success: true,
          roles: roles.docs
        })

      } catch (error) {
        console.error('Get available roles error:', error)
        res.status(500).json({
          error: error.message || 'Internal server error'
        })
      }
    }
  }
]

export default rolesEndpoints
