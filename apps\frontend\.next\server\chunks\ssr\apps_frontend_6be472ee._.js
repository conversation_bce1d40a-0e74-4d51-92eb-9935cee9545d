module.exports = {

"[project]/apps/frontend/.next-internal/server/app/super-admin/role-permissions/page/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/apps/frontend/src/app/favicon.ico.mjs { IMAGE => \"[project]/apps/frontend/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/apps/frontend/src/app/favicon.ico.mjs { IMAGE => \"[project]/apps/frontend/src/app/favicon.ico (static in ecmascript)\" } [app-rsc] (structured image object, ecmascript)"));
}}),
"[project]/apps/frontend/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/apps/frontend/src/app/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/apps/frontend/src/app/super-admin/layout.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/apps/frontend/src/app/super-admin/layout.tsx [app-rsc] (ecmascript)"));
}}),
"[project]/apps/frontend/src/app/super-admin/role-permissions/page.tsx [app-rsc] (ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[project]/apps/frontend/src/app/super-admin/role-permissions/page.tsx [app-rsc] (ecmascript, Next.js server component)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.n(__turbopack_context__.i("[project]/apps/frontend/src/app/super-admin/role-permissions/page.tsx [app-rsc] (ecmascript)"));
}}),

};

//# sourceMappingURL=apps_frontend_6be472ee._.js.map