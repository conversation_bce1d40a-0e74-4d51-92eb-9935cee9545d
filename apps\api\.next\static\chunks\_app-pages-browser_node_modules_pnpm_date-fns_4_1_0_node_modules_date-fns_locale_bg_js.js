"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_bg_js"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   bg: () => (/* binding */ bg),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _bg_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./bg/_lib/formatDistance.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatDistance.js\");\n/* harmony import */ var _bg_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./bg/_lib/formatLong.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatLong.js\");\n/* harmony import */ var _bg_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./bg/_lib/formatRelative.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatRelative.js\");\n/* harmony import */ var _bg_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./bg/_lib/localize.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/localize.js\");\n/* harmony import */ var _bg_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./bg/_lib/match.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Bulgarian locale.\n * @language Bulgarian\n * @iso-639-2 bul\n * <AUTHOR> Stoynov [@arvigeus](https://github.com/arvigeus)\n * <AUTHOR> Ovedenski [@fintara](https://github.com/fintara)\n */ const bg = {\n    code: \"bg\",\n    formatDistance: _bg_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _bg_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _bg_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _bg_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _bg_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (bg);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatDistance.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatDistance.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"по-малко от секунда\",\n        other: \"по-малко от {{count}} секунди\"\n    },\n    xSeconds: {\n        one: \"1 секунда\",\n        other: \"{{count}} секунди\"\n    },\n    halfAMinute: \"половин минута\",\n    lessThanXMinutes: {\n        one: \"по-малко от минута\",\n        other: \"по-малко от {{count}} минути\"\n    },\n    xMinutes: {\n        one: \"1 минута\",\n        other: \"{{count}} минути\"\n    },\n    aboutXHours: {\n        one: \"около час\",\n        other: \"около {{count}} часа\"\n    },\n    xHours: {\n        one: \"1 час\",\n        other: \"{{count}} часа\"\n    },\n    xDays: {\n        one: \"1 ден\",\n        other: \"{{count}} дни\"\n    },\n    aboutXWeeks: {\n        one: \"около седмица\",\n        other: \"около {{count}} седмици\"\n    },\n    xWeeks: {\n        one: \"1 седмица\",\n        other: \"{{count}} седмици\"\n    },\n    aboutXMonths: {\n        one: \"около месец\",\n        other: \"около {{count}} месеца\"\n    },\n    xMonths: {\n        one: \"1 месец\",\n        other: \"{{count}} месеца\"\n    },\n    aboutXYears: {\n        one: \"около година\",\n        other: \"около {{count}} години\"\n    },\n    xYears: {\n        one: \"1 година\",\n        other: \"{{count}} години\"\n    },\n    overXYears: {\n        one: \"над година\",\n        other: \"над {{count}} години\"\n    },\n    almostXYears: {\n        one: \"почти година\",\n        other: \"почти {{count}} години\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"след \" + result;\n        } else {\n            return \"преди \" + result;\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatLong.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatLong.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, dd MMMM yyyy\",\n    long: \"dd MMMM yyyy\",\n    medium: \"dd MMM yyyy\",\n    short: \"dd.MM.yyyy\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    any: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatRelative.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatRelative.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\");\n/* harmony import */ var _toDate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../toDate.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/toDate.js\");\n\n\n// Adapted from the `ru` translation\nconst weekdays = [\n    \"неделя\",\n    \"понеделник\",\n    \"вторник\",\n    \"сряда\",\n    \"четвъртък\",\n    \"петък\",\n    \"събота\"\n];\nfunction lastWeek(day) {\n    const weekday = weekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 6:\n            return \"'миналата \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n        case 5:\n            return \"'миналия \" + weekday + \" в' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = weekdays[day];\n    if (day === 2 /* Tue */ ) {\n        return \"'във \" + weekday + \" в' p\";\n    } else {\n        return \"'в \" + weekday + \" в' p\";\n    }\n}\nfunction nextWeek(day) {\n    const weekday = weekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 6:\n            return \"'следващата \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n        case 5:\n            return \"'следващия \" + weekday + \" в' p\";\n    }\n}\nconst lastWeekFormatToken = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return lastWeek(day);\n    }\n};\nconst nextWeekFormatToken = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return nextWeek(day);\n    }\n};\nconst formatRelativeLocale = {\n    lastWeek: lastWeekFormatToken,\n    yesterday: \"'вчера в' p\",\n    today: \"'днес в' p\",\n    tomorrow: \"'утре в' p\",\n    nextWeek: nextWeekFormatToken,\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS9iZy9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFvRDtBQUNSO0FBRTVDLG9DQUFvQztBQUVwQyxNQUFNRSxXQUFXO0lBQ2Y7SUFDQTtJQUNBO0lBQ0E7SUFDQTtJQUNBO0lBQ0E7Q0FDRDtBQUVELFNBQVNDLFNBQVNDLEdBQUc7SUFDbkIsTUFBTUMsVUFBVUgsUUFBUSxDQUFDRSxJQUFJO0lBRTdCLE9BQVFBO1FBQ04sS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1lBQ0gsT0FBTyxlQUFlQyxVQUFVO1FBQ2xDLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztRQUNMLEtBQUs7WUFDSCxPQUFPLGNBQWNBLFVBQVU7SUFDbkM7QUFDRjtBQUVBLFNBQVNDLFNBQVNGLEdBQUc7SUFDbkIsTUFBTUMsVUFBVUgsUUFBUSxDQUFDRSxJQUFJO0lBRTdCLElBQUlBLFFBQVEsRUFBRSxPQUFPLEtBQUk7UUFDdkIsT0FBTyxVQUFVQyxVQUFVO0lBQzdCLE9BQU87UUFDTCxPQUFPLFFBQVFBLFVBQVU7SUFDM0I7QUFDRjtBQUVBLFNBQVNFLFNBQVNILEdBQUc7SUFDbkIsTUFBTUMsVUFBVUgsUUFBUSxDQUFDRSxJQUFJO0lBRTdCLE9BQVFBO1FBQ04sS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1lBQ0gsT0FBTyxpQkFBaUJDLFVBQVU7UUFDcEMsS0FBSztRQUNMLEtBQUs7UUFDTCxLQUFLO1FBQ0wsS0FBSztZQUNILE9BQU8sZ0JBQWdCQSxVQUFVO0lBQ3JDO0FBQ0Y7QUFFQSxNQUFNRyxzQkFBc0IsQ0FBQ0MsV0FBV0MsVUFBVUM7SUFDaEQsTUFBTUMsT0FBT1gsa0RBQU1BLENBQUNRO0lBQ3BCLE1BQU1MLE1BQU1RLEtBQUtDLE1BQU07SUFDdkIsSUFBSWIsMERBQVVBLENBQUNZLE1BQU1GLFVBQVVDLFVBQVU7UUFDdkMsT0FBT0wsU0FBU0Y7SUFDbEIsT0FBTztRQUNMLE9BQU9ELFNBQVNDO0lBQ2xCO0FBQ0Y7QUFFQSxNQUFNVSxzQkFBc0IsQ0FBQ0wsV0FBV0MsVUFBVUM7SUFDaEQsTUFBTUMsT0FBT1gsa0RBQU1BLENBQUNRO0lBQ3BCLE1BQU1MLE1BQU1RLEtBQUtDLE1BQU07SUFDdkIsSUFBSWIsMERBQVVBLENBQUNZLE1BQU1GLFVBQVVDLFVBQVU7UUFDdkMsT0FBT0wsU0FBU0Y7SUFDbEIsT0FBTztRQUNMLE9BQU9HLFNBQVNIO0lBQ2xCO0FBQ0Y7QUFFQSxNQUFNVyx1QkFBdUI7SUFDM0JaLFVBQVVLO0lBQ1ZRLFdBQVc7SUFDWEMsT0FBTztJQUNQQyxVQUFVO0lBQ1ZYLFVBQVVPO0lBQ1ZLLE9BQU87QUFDVDtBQUVPLE1BQU1DLGlCQUFpQixDQUFDQyxPQUFPVCxNQUFNRixVQUFVQztJQUNwRCxNQUFNVyxTQUFTUCxvQkFBb0IsQ0FBQ00sTUFBTTtJQUUxQyxJQUFJLE9BQU9DLFdBQVcsWUFBWTtRQUNoQyxPQUFPQSxPQUFPVixNQUFNRixVQUFVQztJQUNoQztJQUVBLE9BQU9XO0FBQ1QsRUFBRSIsInNvdXJjZXMiOlsiQzpcXHByb2plY3RzXFxsbXNcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGRhdGUtZm5zQDQuMS4wXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxsb2NhbGVcXGJnXFxfbGliXFxmb3JtYXRSZWxhdGl2ZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBpc1NhbWVXZWVrIH0gZnJvbSBcIi4uLy4uLy4uL2lzU2FtZVdlZWsuanNcIjtcbmltcG9ydCB7IHRvRGF0ZSB9IGZyb20gXCIuLi8uLi8uLi90b0RhdGUuanNcIjtcblxuLy8gQWRhcHRlZCBmcm9tIHRoZSBgcnVgIHRyYW5zbGF0aW9uXG5cbmNvbnN0IHdlZWtkYXlzID0gW1xuICBcItC90LXQtNC10LvRj1wiLFxuICBcItC/0L7QvdC10LTQtdC70L3QuNC6XCIsXG4gIFwi0LLRgtC+0YDQvdC40LpcIixcbiAgXCLRgdGA0Y/QtNCwXCIsXG4gIFwi0YfQtdGC0LLRitGA0YLRitC6XCIsXG4gIFwi0L/QtdGC0YrQulwiLFxuICBcItGB0YrQsdC+0YLQsFwiLFxuXTtcblxuZnVuY3Rpb24gbGFzdFdlZWsoZGF5KSB7XG4gIGNvbnN0IHdlZWtkYXkgPSB3ZWVrZGF5c1tkYXldO1xuXG4gIHN3aXRjaCAoZGF5KSB7XG4gICAgY2FzZSAwOlxuICAgIGNhc2UgMzpcbiAgICBjYXNlIDY6XG4gICAgICByZXR1cm4gXCIn0LzQuNC90LDQu9Cw0YLQsCBcIiArIHdlZWtkYXkgKyBcIiDQsicgcFwiO1xuICAgIGNhc2UgMTpcbiAgICBjYXNlIDI6XG4gICAgY2FzZSA0OlxuICAgIGNhc2UgNTpcbiAgICAgIHJldHVybiBcIifQvNC40L3QsNC70LjRjyBcIiArIHdlZWtkYXkgKyBcIiDQsicgcFwiO1xuICB9XG59XG5cbmZ1bmN0aW9uIHRoaXNXZWVrKGRheSkge1xuICBjb25zdCB3ZWVrZGF5ID0gd2Vla2RheXNbZGF5XTtcblxuICBpZiAoZGF5ID09PSAyIC8qIFR1ZSAqLykge1xuICAgIHJldHVybiBcIifQstGK0LIgXCIgKyB3ZWVrZGF5ICsgXCIg0LInIHBcIjtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gXCIn0LIgXCIgKyB3ZWVrZGF5ICsgXCIg0LInIHBcIjtcbiAgfVxufVxuXG5mdW5jdGlvbiBuZXh0V2VlayhkYXkpIHtcbiAgY29uc3Qgd2Vla2RheSA9IHdlZWtkYXlzW2RheV07XG5cbiAgc3dpdGNoIChkYXkpIHtcbiAgICBjYXNlIDA6XG4gICAgY2FzZSAzOlxuICAgIGNhc2UgNjpcbiAgICAgIHJldHVybiBcIifRgdC70LXQtNCy0LDRidCw0YLQsCBcIiArIHdlZWtkYXkgKyBcIiDQsicgcFwiO1xuICAgIGNhc2UgMTpcbiAgICBjYXNlIDI6XG4gICAgY2FzZSA0OlxuICAgIGNhc2UgNTpcbiAgICAgIHJldHVybiBcIifRgdC70LXQtNCy0LDRidC40Y8gXCIgKyB3ZWVrZGF5ICsgXCIg0LInIHBcIjtcbiAgfVxufVxuXG5jb25zdCBsYXN0V2Vla0Zvcm1hdFRva2VuID0gKGRpcnR5RGF0ZSwgYmFzZURhdGUsIG9wdGlvbnMpID0+IHtcbiAgY29uc3QgZGF0ZSA9IHRvRGF0ZShkaXJ0eURhdGUpO1xuICBjb25zdCBkYXkgPSBkYXRlLmdldERheSgpO1xuICBpZiAoaXNTYW1lV2VlayhkYXRlLCBiYXNlRGF0ZSwgb3B0aW9ucykpIHtcbiAgICByZXR1cm4gdGhpc1dlZWsoZGF5KTtcbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gbGFzdFdlZWsoZGF5KTtcbiAgfVxufTtcblxuY29uc3QgbmV4dFdlZWtGb3JtYXRUb2tlbiA9IChkaXJ0eURhdGUsIGJhc2VEYXRlLCBvcHRpb25zKSA9PiB7XG4gIGNvbnN0IGRhdGUgPSB0b0RhdGUoZGlydHlEYXRlKTtcbiAgY29uc3QgZGF5ID0gZGF0ZS5nZXREYXkoKTtcbiAgaWYgKGlzU2FtZVdlZWsoZGF0ZSwgYmFzZURhdGUsIG9wdGlvbnMpKSB7XG4gICAgcmV0dXJuIHRoaXNXZWVrKGRheSk7XG4gIH0gZWxzZSB7XG4gICAgcmV0dXJuIG5leHRXZWVrKGRheSk7XG4gIH1cbn07XG5cbmNvbnN0IGZvcm1hdFJlbGF0aXZlTG9jYWxlID0ge1xuICBsYXN0V2VlazogbGFzdFdlZWtGb3JtYXRUb2tlbixcbiAgeWVzdGVyZGF5OiBcIifQstGH0LXRgNCwINCyJyBwXCIsXG4gIHRvZGF5OiBcIifQtNC90LXRgSDQsicgcFwiLFxuICB0b21vcnJvdzogXCIn0YPRgtGA0LUg0LInIHBcIixcbiAgbmV4dFdlZWs6IG5leHRXZWVrRm9ybWF0VG9rZW4sXG4gIG90aGVyOiBcIlBcIixcbn07XG5cbmV4cG9ydCBjb25zdCBmb3JtYXRSZWxhdGl2ZSA9ICh0b2tlbiwgZGF0ZSwgYmFzZURhdGUsIG9wdGlvbnMpID0+IHtcbiAgY29uc3QgZm9ybWF0ID0gZm9ybWF0UmVsYXRpdmVMb2NhbGVbdG9rZW5dO1xuXG4gIGlmICh0eXBlb2YgZm9ybWF0ID09PSBcImZ1bmN0aW9uXCIpIHtcbiAgICByZXR1cm4gZm9ybWF0KGRhdGUsIGJhc2VEYXRlLCBvcHRpb25zKTtcbiAgfVxuXG4gIHJldHVybiBmb3JtYXQ7XG59O1xuIl0sIm5hbWVzIjpbImlzU2FtZVdlZWsiLCJ0b0RhdGUiLCJ3ZWVrZGF5cyIsImxhc3RXZWVrIiwiZGF5Iiwid2Vla2RheSIsInRoaXNXZWVrIiwibmV4dFdlZWsiLCJsYXN0V2Vla0Zvcm1hdFRva2VuIiwiZGlydHlEYXRlIiwiYmFzZURhdGUiLCJvcHRpb25zIiwiZGF0ZSIsImdldERheSIsIm5leHRXZWVrRm9ybWF0VG9rZW4iLCJmb3JtYXRSZWxhdGl2ZUxvY2FsZSIsInllc3RlcmRheSIsInRvZGF5IiwidG9tb3Jyb3ciLCJvdGhlciIsImZvcm1hdFJlbGF0aXZlIiwidG9rZW4iLCJmb3JtYXQiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/localize.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/localize.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"пр.н.е.\",\n        \"н.е.\"\n    ],\n    abbreviated: [\n        \"преди н. е.\",\n        \"н. е.\"\n    ],\n    wide: [\n        \"преди новата ера\",\n        \"новата ера\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1-во тримес.\",\n        \"2-ро тримес.\",\n        \"3-то тримес.\",\n        \"4-то тримес.\"\n    ],\n    wide: [\n        \"1-во тримесечие\",\n        \"2-ро тримесечие\",\n        \"3-то тримесечие\",\n        \"4-то тримесечие\"\n    ]\n};\nconst monthValues = {\n    abbreviated: [\n        \"яну\",\n        \"фев\",\n        \"мар\",\n        \"апр\",\n        \"май\",\n        \"юни\",\n        \"юли\",\n        \"авг\",\n        \"сеп\",\n        \"окт\",\n        \"ное\",\n        \"дек\"\n    ],\n    wide: [\n        \"януари\",\n        \"февруари\",\n        \"март\",\n        \"април\",\n        \"май\",\n        \"юни\",\n        \"юли\",\n        \"август\",\n        \"септември\",\n        \"октомври\",\n        \"ноември\",\n        \"декември\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"Н\",\n        \"П\",\n        \"В\",\n        \"С\",\n        \"Ч\",\n        \"П\",\n        \"С\"\n    ],\n    short: [\n        \"нд\",\n        \"пн\",\n        \"вт\",\n        \"ср\",\n        \"чт\",\n        \"пт\",\n        \"сб\"\n    ],\n    abbreviated: [\n        \"нед\",\n        \"пон\",\n        \"вто\",\n        \"сря\",\n        \"чет\",\n        \"пет\",\n        \"съб\"\n    ],\n    wide: [\n        \"неделя\",\n        \"понеделник\",\n        \"вторник\",\n        \"сряда\",\n        \"четвъртък\",\n        \"петък\",\n        \"събота\"\n    ]\n};\nconst dayPeriodValues = {\n    wide: {\n        am: \"преди обяд\",\n        pm: \"след обяд\",\n        midnight: \"в полунощ\",\n        noon: \"на обяд\",\n        morning: \"сутринта\",\n        afternoon: \"следобед\",\n        evening: \"вечерта\",\n        night: \"през нощта\"\n    }\n};\nfunction isFeminine(unit) {\n    return unit === \"year\" || unit === \"week\" || unit === \"minute\" || unit === \"second\";\n}\nfunction isNeuter(unit) {\n    return unit === \"quarter\";\n}\nfunction numberWithSuffix(number, unit, masculine, feminine, neuter) {\n    const suffix = isNeuter(unit) ? neuter : isFeminine(unit) ? feminine : masculine;\n    return number + \"-\" + suffix;\n}\nconst ordinalNumber = (dirtyNumber, options)=>{\n    const number = Number(dirtyNumber);\n    const unit = options === null || options === void 0 ? void 0 : options.unit;\n    if (number === 0) {\n        return numberWithSuffix(0, unit, \"ев\", \"ева\", \"ево\");\n    } else if (number % 1000 === 0) {\n        return numberWithSuffix(number, unit, \"ен\", \"на\", \"но\");\n    } else if (number % 100 === 0) {\n        return numberWithSuffix(number, unit, \"тен\", \"тна\", \"тно\");\n    }\n    const rem100 = number % 100;\n    if (rem100 > 20 || rem100 < 10) {\n        switch(rem100 % 10){\n            case 1:\n                return numberWithSuffix(number, unit, \"ви\", \"ва\", \"во\");\n            case 2:\n                return numberWithSuffix(number, unit, \"ри\", \"ра\", \"ро\");\n            case 7:\n            case 8:\n                return numberWithSuffix(number, unit, \"ми\", \"ма\", \"мо\");\n        }\n    }\n    return numberWithSuffix(number, unit, \"ти\", \"та\", \"то\");\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/match.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/match.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?[врмт][аи]|-?т?(ен|на)|-?(ев|ева))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^((пр)?н\\.?\\s?е\\.?)/i,\n    abbreviated: /^((пр)?н\\.?\\s?е\\.?)/i,\n    wide: /^(преди новата ера|новата ера|нова ера)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^п/i,\n        /^н/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^[1234](-?[врт]?o?)? тримес.?/i,\n    wide: /^[1234](-?[врт]?о?)? тримесечие/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[нпвсч]/i,\n    short: /^(нд|пн|вт|ср|чт|пт|сб)/i,\n    abbreviated: /^(нед|пон|вто|сря|чет|пет|съб)/i,\n    wide: /^(неделя|понеделник|вторник|сряда|четвъртък|петък|събота)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^н/i,\n        /^п/i,\n        /^в/i,\n        /^с/i,\n        /^ч/i,\n        /^п/i,\n        /^с/i\n    ],\n    any: [\n        /^н[ед]/i,\n        /^п[он]/i,\n        /^вт/i,\n        /^ср/i,\n        /^ч[ет]/i,\n        /^п[ет]/i,\n        /^с[ъб]/i\n    ]\n};\nconst matchMonthPatterns = {\n    abbreviated: /^(яну|фев|мар|апр|май|юни|юли|авг|сеп|окт|ное|дек)/i,\n    wide: /^(януари|февруари|март|април|май|юни|юли|август|септември|октомври|ноември|декември)/i\n};\nconst parseMonthPatterns = {\n    any: [\n        /^я/i,\n        /^ф/i,\n        /^мар/i,\n        /^ап/i,\n        /^май/i,\n        /^юн/i,\n        /^юл/i,\n        /^ав/i,\n        /^се/i,\n        /^окт/i,\n        /^но/i,\n        /^де/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    any: /^(преди о|след о|в по|на о|през|веч|сут|следо)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^преди о/i,\n        pm: /^след о/i,\n        midnight: /^в пол/i,\n        noon: /^на об/i,\n        morning: /^сут/i,\n        afternoon: /^следо/i,\n        evening: /^веч/i,\n        night: /^през н/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/bg/_lib/match.js\n"));

/***/ })

}]);