"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+text@0.28.0";
exports.ids = ["vendor-chunks/@lexical+text@0.28.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+text@0.28.0/node_modules/@lexical/text/LexicalText.dev.mjs":
/*!****************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+text@0.28.0/node_modules/@lexical/text/LexicalText.dev.mjs ***!
  \****************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $canShowPlaceholder: () => (/* binding */ $canShowPlaceholder),\n/* harmony export */   $canShowPlaceholderCurry: () => (/* binding */ $canShowPlaceholderCurry),\n/* harmony export */   $findTextIntersectionFromCharacters: () => (/* binding */ $findTextIntersectionFromCharacters),\n/* harmony export */   $isRootTextContentEmpty: () => (/* binding */ $isRootTextContentEmpty),\n/* harmony export */   $isRootTextContentEmptyCurry: () => (/* binding */ $isRootTextContentEmptyCurry),\n/* harmony export */   $rootTextContent: () => (/* binding */ $rootTextContent),\n/* harmony export */   registerLexicalTextEntity: () => (/* binding */ registerLexicalTextEntity)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Returns the root's text content.\n * @returns The root's text content.\n */\nfunction $rootTextContent() {\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  return root.getTextContent();\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Determines if the root has any text content and can trim any whitespace if it does.\n * @param isEditorComposing - Is the editor in composition mode due to an active Input Method Editor?\n * @param trim - Should the root text have its whitespaced trimmed? Defaults to true.\n * @returns true if text content is empty, false if there is text or isEditorComposing is true.\n */\nfunction $isRootTextContentEmpty(isEditorComposing, trim = true) {\n  if (isEditorComposing) {\n    return false;\n  }\n  let text = $rootTextContent();\n  if (trim) {\n    text = text.trim();\n  }\n  return text === '';\n}\n\n/**\n * Returns a function that executes {@link $isRootTextContentEmpty}\n * @param isEditorComposing - Is the editor in composition mode due to an active Input Method Editor?\n * @param trim - Should the root text have its whitespaced trimmed? Defaults to true.\n * @returns A function that executes $isRootTextContentEmpty based on arguments.\n */\nfunction $isRootTextContentEmptyCurry(isEditorComposing, trim) {\n  return () => $isRootTextContentEmpty(isEditorComposing, trim);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Determines if the input should show the placeholder. If anything is in\n * in the root the placeholder should not be shown.\n * @param isComposing - Is the editor in composition mode due to an active Input Method Editor?\n * @returns true if the input should show the placeholder, false otherwise.\n */\nfunction $canShowPlaceholder(isComposing) {\n  if (!$isRootTextContentEmpty(isComposing, false)) {\n    return false;\n  }\n  const root = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)();\n  const children = root.getChildren();\n  const childrenLength = children.length;\n  if (childrenLength > 1) {\n    return false;\n  }\n  for (let i = 0; i < childrenLength; i++) {\n    const topBlock = children[i];\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(topBlock)) {\n      return false;\n    }\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(topBlock)) {\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isParagraphNode)(topBlock)) {\n        return false;\n      }\n      if (topBlock.__indent !== 0) {\n        return false;\n      }\n      const topBlockChildren = topBlock.getChildren();\n      const topBlockChildrenLength = topBlockChildren.length;\n      for (let s = 0; s < topBlockChildrenLength; s++) {\n        const child = topBlockChildren[i];\n        if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(child)) {\n          return false;\n        }\n      }\n    }\n  }\n  return true;\n}\n\n/**\n * Returns a function that executes {@link $canShowPlaceholder}\n * @param isEditorComposing - Is the editor in composition mode due to an active Input Method Editor?\n * @returns A function that executes $canShowPlaceholder with arguments.\n */\nfunction $canShowPlaceholderCurry(isEditorComposing) {\n  return () => $canShowPlaceholder(isEditorComposing);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Finds a TextNode with a size larger than targetCharacters and returns\n * the node along with the remaining length of the text.\n * @param root - The RootNode.\n * @param targetCharacters - The number of characters whose TextNode must be larger than.\n * @returns The TextNode and the intersections offset, or null if no TextNode is found.\n */\nfunction $findTextIntersectionFromCharacters(root, targetCharacters) {\n  let node = root.getFirstChild();\n  let currentCharacters = 0;\n  mainLoop: while (node !== null) {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(node)) {\n      const child = node.getFirstChild();\n      if (child !== null) {\n        node = child;\n        continue;\n      }\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n      const characters = node.getTextContentSize();\n      if (currentCharacters + characters > targetCharacters) {\n        return {\n          node,\n          offset: targetCharacters - currentCharacters\n        };\n      }\n      currentCharacters += characters;\n    }\n    const sibling = node.getNextSibling();\n    if (sibling !== null) {\n      node = sibling;\n      continue;\n    }\n    let parent = node.getParent();\n    while (parent !== null) {\n      const parentSibling = parent.getNextSibling();\n      if (parentSibling !== null) {\n        node = parentSibling;\n        continue mainLoop;\n      }\n      parent = parent.getParent();\n    }\n    break;\n  }\n  return null;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\n/**\n * Returns a tuple that can be rested (...) into mergeRegister to clean up\n * node transforms listeners that transforms text into another node, eg. a HashtagNode.\n * @example\n * ```ts\n *   useEffect(() => {\n    return mergeRegister(\n      ...registerLexicalTextEntity(editor, getMatch, targetNode, createNode),\n    );\n  }, [createNode, editor, getMatch, targetNode]);\n * ```\n * Where targetNode is the type of node containing the text you want to transform (like a text input),\n * then getMatch uses a regex to find a matching text and creates the proper node to include the matching text.\n * @param editor - The lexical editor.\n * @param getMatch - Finds a matching string that satisfies a regex expression.\n * @param targetNode - The node type that contains text to match with. eg. HashtagNode\n * @param createNode - A function that creates a new node to contain the matched text. eg createHashtagNode\n * @returns An array containing the plain text and reverse node transform listeners.\n */\nfunction registerLexicalTextEntity(editor, getMatch, targetNode, createNode) {\n  const isTargetNode = node => {\n    return node instanceof targetNode;\n  };\n  const $replaceWithSimpleText = node => {\n    const textNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTextNode)(node.getTextContent());\n    textNode.setFormat(node.getFormat());\n    node.replace(textNode);\n  };\n  const getMode = node => {\n    return node.getLatest().__mode;\n  };\n  const $textNodeTransform = node => {\n    if (!node.isSimpleText()) {\n      return;\n    }\n    let prevSibling = node.getPreviousSibling();\n    let text = node.getTextContent();\n    let currentNode = node;\n    let match;\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevSibling)) {\n      const previousText = prevSibling.getTextContent();\n      const combinedText = previousText + text;\n      const prevMatch = getMatch(combinedText);\n      if (isTargetNode(prevSibling)) {\n        if (prevMatch === null || getMode(prevSibling) !== 0) {\n          $replaceWithSimpleText(prevSibling);\n          return;\n        } else {\n          const diff = prevMatch.end - previousText.length;\n          if (diff > 0) {\n            const concatText = text.slice(0, diff);\n            const newTextContent = previousText + concatText;\n            prevSibling.select();\n            prevSibling.setTextContent(newTextContent);\n            if (diff === text.length) {\n              node.remove();\n            } else {\n              const remainingText = text.slice(diff);\n              node.setTextContent(remainingText);\n            }\n            return;\n          }\n        }\n      } else if (prevMatch === null || prevMatch.start < previousText.length) {\n        return;\n      }\n    }\n    let prevMatchLengthToSkip = 0;\n    // eslint-disable-next-line no-constant-condition\n    while (true) {\n      match = getMatch(text);\n      let nextText = match === null ? '' : text.slice(match.end);\n      text = nextText;\n      if (nextText === '') {\n        const nextSibling = currentNode.getNextSibling();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(nextSibling)) {\n          nextText = currentNode.getTextContent() + nextSibling.getTextContent();\n          const nextMatch = getMatch(nextText);\n          if (nextMatch === null) {\n            if (isTargetNode(nextSibling)) {\n              $replaceWithSimpleText(nextSibling);\n            } else {\n              nextSibling.markDirty();\n            }\n            return;\n          } else if (nextMatch.start !== 0) {\n            return;\n          }\n        }\n      }\n      if (match === null) {\n        return;\n      }\n      if (match.start === 0 && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevSibling) && prevSibling.isTextEntity()) {\n        prevMatchLengthToSkip += match.end;\n        continue;\n      }\n      let nodeToReplace;\n      if (match.start === 0) {\n        [nodeToReplace, currentNode] = currentNode.splitText(match.end);\n      } else {\n        [, nodeToReplace, currentNode] = currentNode.splitText(match.start + prevMatchLengthToSkip, match.end + prevMatchLengthToSkip);\n      }\n      if (!(nodeToReplace !== undefined)) {\n        formatDevErrorMessage(`${'nodeToReplace'} should not be undefined. You may want to check splitOffsets passed to the splitText.`);\n      }\n      const replacementNode = createNode(nodeToReplace);\n      replacementNode.setFormat(nodeToReplace.getFormat());\n      nodeToReplace.replace(replacementNode);\n      if (currentNode == null) {\n        return;\n      }\n      prevMatchLengthToSkip = 0;\n      prevSibling = replacementNode;\n    }\n  };\n  const $reverseNodeTransform = node => {\n    const text = node.getTextContent();\n    const match = getMatch(text);\n    if (match === null || match.start !== 0) {\n      $replaceWithSimpleText(node);\n      return;\n    }\n    if (text.length > match.end) {\n      // This will split out the rest of the text as simple text\n      node.splitText(match.end);\n      return;\n    }\n    const prevSibling = node.getPreviousSibling();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevSibling) && prevSibling.isTextEntity()) {\n      $replaceWithSimpleText(prevSibling);\n      $replaceWithSimpleText(node);\n    }\n    const nextSibling = node.getNextSibling();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(nextSibling) && nextSibling.isTextEntity()) {\n      $replaceWithSimpleText(nextSibling);\n\n      // This may have already been converted in the previous block\n      if (isTargetNode(node)) {\n        $replaceWithSimpleText(node);\n      }\n    }\n  };\n  const removePlainTextTransform = editor.registerNodeTransform(lexical__WEBPACK_IMPORTED_MODULE_0__.TextNode, $textNodeTransform);\n  const removeReverseNodeTransform = editor.registerNodeTransform(targetNode, $reverseNodeTransform);\n  return [removePlainTextTransform, removeReverseNodeTransform];\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+text@0.28.0/node_modules/@lexical/text/LexicalText.dev.mjs\n");

/***/ })

};
;