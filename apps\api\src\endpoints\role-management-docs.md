# Role Management API Endpoints

Comprehensive role management endpoints following the tax management pattern.

## 📋 **Available Endpoints**

### **1. Get Roles** 
`GET /api/roles`

**Query Parameters:**
- `search` - Search in name, code, description
- `level` - Filter by role level (1, 2, 3)
- `isActive` - Filter by active status (true, false, all)
- `category` - Filter by category (system, custom, all)
- `scope` - Filter by scope (global, institute, branch)
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20)

**Response:**
```json
{
  "success": true,
  "data": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "totalPages": 5,
    "totalDocs": 100,
    "hasNextPage": true,
    "hasPrevPage": false
  }
}
```

### **2. Get Permissions**
`GET /api/permissions`

**Query Parameters:**
- `search` - Search in name, code, description, resource, action
- `category` - Filter by category
- `resource` - Filter by resource
- `action` - Filter by action
- `level` - Filter by level
- `isActive` - Filter by active status (true, false, all)
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 20)

### **3. Get Role-Permission Assignments**
`GET /api/role-permissions`

**Query Parameters:**
- `roleId` - Filter by specific role
- `permissionId` - Filter by specific permission
- `isActive` - Filter by active status (true, false, all)
- `page` - Page number (default: 1)
- `limit` - Items per page (default: 50)

### **4. Get Roles with Permissions**
`GET /api/roles-with-permissions`

**Query Parameters:**
- `includeInactive` - Include inactive roles (default: false)
- `level` - Filter by role level
- `category` - Filter by category (system, custom)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "1",
      "name": "Super Admin",
      "code": "super_admin",
      "level": 1,
      "permissions": [...],
      "permissionCount": 98
    }
  ],
  "totalDocs": 5
}
```

### **5. Get Permissions by Category**
`GET /api/permissions-by-category`

**Query Parameters:**
- `level` - Minimum level filter
- `includeInactive` - Include inactive permissions (default: false)

**Response:**
```json
{
  "success": true,
  "data": {
    "platform": [...],
    "courses": [...],
    "users": [...]
  },
  "totalCategories": 8,
  "totalPermissions": 98
}
```

### **6. Assign Permissions to Role**
`POST /api/roles/:roleId/permissions`

**Body:**
```json
{
  "permissionIds": ["perm1", "perm2", "perm3"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Permissions assigned successfully",
  "assignedCount": 3
}
```

### **7. Remove Permission from Role**
`DELETE /api/roles/:roleId/permissions/:permissionId`

**Response:**
```json
{
  "success": true,
  "message": "Permission removed successfully"
}
```

### **8. Get Role Statistics**
`GET /api/roles/statistics`

**Response:**
```json
{
  "success": true,
  "data": {
    "roles": {
      "total": 15,
      "active": 12,
      "inactive": 3,
      "system": 5,
      "custom": 10,
      "byLevel": {
        "level1": 2,
        "level2": 8,
        "level3": 5
      }
    },
    "permissions": {
      "total": 98,
      "active": 95,
      "inactive": 3
    },
    "assignments": {
      "total": 245
    }
  }
}
```

### **9. Get Specific Role Permissions**
`GET /api/roles/:roleId/permissions`

**Response:**
```json
{
  "success": true,
  "data": {
    "role": {...},
    "permissions": [...],
    "permissionCount": 25
  }
}
```

### **10. Bulk Role Operations**
`POST /api/roles/bulk`

**Body:**
```json
{
  "operation": "activate|deactivate|delete",
  "roleIds": ["role1", "role2", "role3"],
  "data": {} // Optional additional data
}
```

**Response:**
```json
{
  "success": true,
  "message": "Bulk operation completed: 2 successful, 1 failed",
  "results": [
    {"id": "role1", "success": true},
    {"id": "role2", "success": false, "error": "Role is assigned to users"}
  ],
  "summary": {
    "total": 3,
    "successful": 2,
    "failed": 1
  }
}
```

## 🔐 **Authentication & Authorization**

All endpoints require authentication. Access levels:

- **Super Admin**: Full access to all endpoints
- **Institute Admin**: Limited access (Level 2+ roles only)
- **Other roles**: Read-only access to own permissions

## 🚀 **Usage Examples**

### **Frontend Integration:**

```typescript
// Get roles with filtering
const roles = await api.get('/api/roles', {
  search: 'admin',
  level: '2',
  isActive: 'true',
  page: '1',
  limit: '10'
})

// Assign permissions to role
const result = await api.post('/api/roles/123/permissions', {
  permissionIds: ['perm1', 'perm2', 'perm3']
})

// Get role statistics
const stats = await api.get('/api/roles/statistics')
```

## 📊 **Features**

✅ **Comprehensive filtering** - Search, category, level, scope, status
✅ **Pagination support** - Efficient data loading
✅ **Role-Permission management** - Assign/remove permissions
✅ **Statistics & analytics** - Role usage insights
✅ **Bulk operations** - Efficient mass updates
✅ **Access control** - Role-based endpoint access
✅ **Error handling** - Consistent error responses
✅ **Type safety** - Full TypeScript support

## 🔄 **Integration with Frontend**

These endpoints are designed to work seamlessly with the existing frontend role-permissions store and components, providing all the functionality needed for comprehensive role and permission management.
