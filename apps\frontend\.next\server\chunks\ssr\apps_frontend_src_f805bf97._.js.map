{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,yNAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sLAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/tabs.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface TabsProps {\n  defaultValue?: string\n  value?: string\n  onValueChange?: (value: string) => void\n  children: React.ReactNode\n  className?: string\n}\n\ninterface TabsContextType {\n  value: string\n  onValueChange: (value: string) => void\n}\n\nconst TabsContext = React.createContext<TabsContextType | undefined>(undefined)\n\nconst Tabs = React.forwardRef<HTMLDivElement, TabsProps>(\n  ({ defaultValue, value, onValueChange, children, className, ...props }, ref) => {\n    const [internalValue, setInternalValue] = React.useState(defaultValue || \"\")\n\n    const currentValue = value !== undefined ? value : internalValue\n    const handleValueChange = onValueChange || setInternalValue\n\n    return (\n      <TabsContext.Provider value={{ value: currentValue, onValueChange: handleValueChange }}>\n        <div ref={ref} className={cn(\"w-full\", className)} {...props}>\n          {children}\n        </div>\n      </TabsContext.Provider>\n    )\n  }\n)\nTabs.displayName = \"Tabs\"\n\nconst TabsList = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"inline-flex h-10 items-center justify-center rounded-md bg-gray-100 p-1 text-gray-500\",\n      className\n    )}\n    {...props}\n  />\n))\nTabsList.displayName = \"TabsList\"\n\nconst TabsTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & { value: string }\n>(({ className, value, children, ...props }, ref) => {\n  const context = React.useContext(TabsContext)\n  if (!context) throw new Error(\"TabsTrigger must be used within Tabs\")\n\n  const isActive = context.value === value\n\n  return (\n    <button\n      ref={ref}\n      className={cn(\n        \"inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n        isActive\n          ? \"bg-white text-gray-900 shadow-sm\"\n          : \"text-gray-600 hover:text-gray-900\",\n        className\n      )}\n      onClick={() => context.onValueChange(value)}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n})\nTabsTrigger.displayName = \"TabsTrigger\"\n\nconst TabsContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & { value: string }\n>(({ className, value, children, ...props }, ref) => {\n  const context = React.useContext(TabsContext)\n  if (!context) throw new Error(\"TabsContent must be used within Tabs\")\n\n  if (context.value !== value) return null\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"mt-2 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:ring-offset-2\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n    </div>\n  )\n})\nTabsContent.displayName = \"TabsContent\"\n\nexport { Tabs, TabsList, TabsTrigger, TabsContent }\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AAHA;;;;AAkBA,MAAM,4BAAc,CAAA,GAAA,0SAAA,CAAA,gBAAmB,AAAD,EAA+B;AAErE,MAAM,qBAAO,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAC1B,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,aAAa,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACtE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAc,AAAD,EAAE,gBAAgB;IAEzE,MAAM,eAAe,UAAU,YAAY,QAAQ;IACnD,MAAM,oBAAoB,iBAAiB;IAE3C,qBACE,mVAAC,YAAY,QAAQ;QAAC,OAAO;YAAE,OAAO;YAAc,eAAe;QAAkB;kBACnF,cAAA,mVAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,UAAU;YAAa,GAAG,KAAK;sBACzD;;;;;;;;;;;AAIT;AAEF,KAAK,WAAW,GAAG;AAEnB,MAAM,yBAAW,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,yFACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,4BAAc,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,WAAW,QAAQ,KAAK,KAAK;IAEnC,qBACE,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,gRACA,WACI,qCACA,qCACJ;QAEF,SAAS,IAAM,QAAQ,aAAa,CAAC;QACpC,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,4BAAc,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC3C,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,IAAI,QAAQ,KAAK,KAAK,OAAO,OAAO;IAEpC,qBACE,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,gHACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 113, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 210, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground hover:bg-primary/80\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        destructive:\n          \"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80\",\n        outline: \"text-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nexport interface BadgeProps\n  extends React.HTMLAttributes<HTMLDivElement>,\n    VariantProps<typeof badgeVariants> {}\n\nfunction Badge({ className, variant, ...props }: BadgeProps) {\n  return (\n    <div className={cn(badgeVariants({ variant }), className)} {...props} />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AAEA;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACtB,0KACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SAAS;QACX;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAOF,SAAS,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAmB;IACzD,qBACE,mVAAC;QAAI,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QAAa,GAAG,KAAK;;;;;;AAExE", "debugId": null}}, {"offset": {"line": 252, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,2OAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,oSAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Input({ className, type, ...props }: React.ComponentProps<\"input\">) {\n  return (\n    <input\n      type={type}\n      data-slot=\"input\"\n      className={cn(\n        \"file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        \"focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]\",\n        \"aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Input }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,MAAM,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAsC;IACzE,qBACE,mVAAC;QACC,MAAM;QACN,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,mcACA,iFACA,0GACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/table.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst Table = React.forwardRef<\n  HTMLTableElement,\n  React.HTMLAttributes<HTMLTableElement>\n>(({ className, ...props }, ref) => (\n  <div className=\"relative w-full overflow-auto\">\n    <table\n      ref={ref}\n      className={cn(\"w-full caption-bottom text-sm\", className)}\n      {...props}\n    />\n  </div>\n))\nTable.displayName = \"Table\"\n\nconst TableHeader = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <thead ref={ref} className={cn(\"[&_tr]:border-b\", className)} {...props} />\n))\nTableHeader.displayName = \"TableHeader\"\n\nconst TableBody = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tbody\n    ref={ref}\n    className={cn(\"[&_tr:last-child]:border-0\", className)}\n    {...props}\n  />\n))\nTableBody.displayName = \"TableBody\"\n\nconst TableFooter = React.forwardRef<\n  HTMLTableSectionElement,\n  React.HTMLAttributes<HTMLTableSectionElement>\n>(({ className, ...props }, ref) => (\n  <tfoot\n    ref={ref}\n    className={cn(\n      \"border-t bg-muted/50 font-medium [&>tr]:last:border-b-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableFooter.displayName = \"TableFooter\"\n\nconst TableRow = React.forwardRef<\n  HTMLTableRowElement,\n  React.HTMLAttributes<HTMLTableRowElement>\n>(({ className, ...props }, ref) => (\n  <tr\n    ref={ref}\n    className={cn(\n      \"border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted\",\n      className\n    )}\n    {...props}\n  />\n))\nTableRow.displayName = \"TableRow\"\n\nconst TableHead = React.forwardRef<\n  HTMLTableCellElement,\n  React.ThHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <th\n    ref={ref}\n    className={cn(\n      \"h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0\",\n      className\n    )}\n    {...props}\n  />\n))\nTableHead.displayName = \"TableHead\"\n\nconst TableCell = React.forwardRef<\n  HTMLTableCellElement,\n  React.TdHTMLAttributes<HTMLTableCellElement>\n>(({ className, ...props }, ref) => (\n  <td\n    ref={ref}\n    className={cn(\"p-4 align-middle [&:has([role=checkbox])]:pr-0\", className)}\n    {...props}\n  />\n))\nTableCell.displayName = \"TableCell\"\n\nconst TableCaption = React.forwardRef<\n  HTMLTableCaptionElement,\n  React.HTMLAttributes<HTMLTableCaptionElement>\n>(({ className, ...props }, ref) => (\n  <caption\n    ref={ref}\n    className={cn(\"mt-4 text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nTableCaption.displayName = \"TableCaption\"\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QAAI,WAAU;kBACb,cAAA,mVAAC;YACC,KAAK;YACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIf,MAAM,WAAW,GAAG;AAEpB,MAAM,4BAAc,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QAAM,KAAK;QAAK,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAAa,GAAG,KAAK;;;;;;AAEzE,YAAY,WAAW,GAAG;AAE1B,MAAM,0BAAY,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGb,YAAY,WAAW,GAAG;AAE1B,MAAM,yBAAW,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGb,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,oGACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,0BAAY,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,kDAAkD;QAC/D,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,6BAAe,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGb,aAAa,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 445, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface DropdownMenuProps {\n  children: React.ReactNode\n}\n\ninterface DropdownMenuContextType {\n  open: boolean\n  setOpen: (open: boolean) => void\n}\n\nconst DropdownMenuContext = React.createContext<DropdownMenuContextType | undefined>(undefined)\n\nconst DropdownMenu = ({ children }: DropdownMenuProps) => {\n  const [open, setOpen] = React.useState(false)\n\n  return (\n    <DropdownMenuContext.Provider value={{ open, setOpen }}>\n      <div className=\"relative inline-block text-left\">\n        {children}\n      </div>\n    </DropdownMenuContext.Provider>\n  )\n}\n\nconst DropdownMenuTrigger = React.forwardRef<\n  HTMLButtonElement,\n  React.ButtonHTMLAttributes<HTMLButtonElement> & {\n    asChild?: boolean\n  }\n>(({ className, children, asChild = false, ...props }, ref) => {\n  const context = React.useContext(DropdownMenuContext)\n  if (!context) throw new Error(\"DropdownMenuTrigger must be used within DropdownMenu\")\n\n  const handleClick = () => {\n    context.setOpen(!context.open)\n  }\n\n  if (asChild) {\n    return React.cloneElement(children as React.ReactElement, {\n      onClick: handleClick,\n      ref,\n    })\n  }\n\n  return (\n    <button\n      ref={ref}\n      className={cn(\n        \"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none\",\n        className\n      )}\n      onClick={handleClick}\n      {...props}\n    >\n      {children}\n    </button>\n  )\n})\nDropdownMenuTrigger.displayName = \"DropdownMenuTrigger\"\n\nconst DropdownMenuContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    align?: \"start\" | \"center\" | \"end\"\n    sideOffset?: number\n  }\n>(({ className, align = \"center\", sideOffset = 4, ...props }, ref) => {\n  const context = React.useContext(DropdownMenuContext)\n  if (!context) throw new Error(\"DropdownMenuContent must be used within DropdownMenu\")\n\n  const contentRef = React.useRef<HTMLDivElement>(null)\n\n  React.useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (contentRef.current && !contentRef.current.contains(event.target as Node)) {\n        context.setOpen(false)\n      }\n    }\n\n    if (context.open) {\n      document.addEventListener(\"mousedown\", handleClickOutside)\n    }\n\n    return () => {\n      document.removeEventListener(\"mousedown\", handleClickOutside)\n    }\n  }, [context.open, context.setOpen])\n\n  if (!context.open) return null\n\n  return (\n    <div\n      ref={contentRef}\n      className={cn(\n        \"absolute z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md\",\n        align === \"start\" && \"left-0\",\n        align === \"center\" && \"left-1/2 transform -translate-x-1/2\",\n        align === \"end\" && \"right-0\",\n        className\n      )}\n      style={{ top: `calc(100% + ${sideOffset}px)` }}\n      {...props}\n    />\n  )\n})\nDropdownMenuContent.displayName = \"DropdownMenuContent\"\n\nconst DropdownMenuItem = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => {\n  const context = React.useContext(DropdownMenuContext)\n\n  const handleClick = (event: React.MouseEvent) => {\n    if (props.onClick) {\n      props.onClick(event)\n    }\n    context?.setOpen(false)\n  }\n\n  return (\n    <div\n      ref={ref}\n      className={cn(\n        \"relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 hover:bg-accent hover:text-accent-foreground\",\n        inset && \"pl-8\",\n        className\n      )}\n      onClick={handleClick}\n      {...props}\n    />\n  )\n})\nDropdownMenuItem.displayName = \"DropdownMenuItem\"\n\nconst DropdownMenuSeparator = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"-mx-1 my-1 h-px bg-muted\", className)}\n    {...props}\n  />\n))\nDropdownMenuSeparator.displayName = \"DropdownMenuSeparator\"\n\nconst DropdownMenuLabel = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    inset?: boolean\n  }\n>(({ className, inset, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\n      \"px-2 py-1.5 text-sm font-semibold\",\n      inset && \"pl-8\",\n      className\n    )}\n    {...props}\n  />\n))\nDropdownMenuLabel.displayName = \"DropdownMenuLabel\"\n\nexport {\n  DropdownMenu,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuSeparator,\n  DropdownMenuLabel,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,oCAAsB,CAAA,GAAA,0SAAA,CAAA,gBAAmB,AAAD,EAAuC;AAErF,MAAM,eAAe,CAAC,EAAE,QAAQ,EAAqB;IACnD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAc,AAAD,EAAE;IAEvC,qBACE,mVAAC,oBAAoB,QAAQ;QAAC,OAAO;YAAE;YAAM;QAAQ;kBACnD,cAAA,mVAAC;YAAI,WAAU;sBACZ;;;;;;;;;;;AAIT;AAEA,MAAM,oCAAsB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAKzC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACrD,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,cAAc;QAClB,QAAQ,OAAO,CAAC,CAAC,QAAQ,IAAI;IAC/B;IAEA,IAAI,SAAS;QACX,qBAAO,CAAA,GAAA,0SAAA,CAAA,eAAkB,AAAD,EAAE,UAAgC;YACxD,SAAS;YACT;QACF;IACF;IAEA,qBACE,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,iPACA;QAEF,SAAS;QACR,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,oCAAsB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAMzC,CAAC,EAAE,SAAS,EAAE,QAAQ,QAAQ,EAAE,aAAa,CAAC,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IACjC,IAAI,CAAC,SAAS,MAAM,IAAI,MAAM;IAE9B,MAAM,aAAa,CAAA,GAAA,0SAAA,CAAA,SAAY,AAAD,EAAkB;IAEhD,CAAA,GAAA,0SAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,qBAAqB,CAAC;YAC1B,IAAI,WAAW,OAAO,IAAI,CAAC,WAAW,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;gBAC5E,QAAQ,OAAO,CAAC;YAClB;QACF;QAEA,IAAI,QAAQ,IAAI,EAAE;YAChB,SAAS,gBAAgB,CAAC,aAAa;QACzC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,aAAa;QAC5C;IACF,GAAG;QAAC,QAAQ,IAAI;QAAE,QAAQ,OAAO;KAAC;IAElC,IAAI,CAAC,QAAQ,IAAI,EAAE,OAAO;IAE1B,qBACE,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,iHACA,UAAU,WAAW,UACrB,UAAU,YAAY,uCACtB,UAAU,SAAS,WACnB;QAEF,OAAO;YAAE,KAAK,CAAC,YAAY,EAAE,WAAW,GAAG,CAAC;QAAC;QAC5C,GAAG,KAAK;;;;;;AAGf;AACA,oBAAoB,WAAW,GAAG;AAElC,MAAM,iCAAmB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAKtC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE;IACjC,MAAM,UAAU,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAAE;IAEjC,MAAM,cAAc,CAAC;QACnB,IAAI,MAAM,OAAO,EAAE;YACjB,MAAM,OAAO,CAAC;QAChB;QACA,SAAS,QAAQ;IACnB;IAEA,qBACE,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,gRACA,SAAS,QACT;QAEF,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,iBAAiB,WAAW,GAAG;AAE/B,MAAM,sCAAwB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG3C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;;;;;AAGb,sBAAsB,WAAW,GAAG;AAEpC,MAAM,kCAAoB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAKvC,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,mVAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,qCACA,SAAS,QACT;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/alert-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAEA;AACA;AAEA;AACA;AANA;;;;;;AAQA,MAAM,cAAc,kYAAA,CAAA,OAAyB;AAE7C,MAAM,qBAAqB,kYAAA,CAAA,UAA4B;AAEvD,MAAM,oBAAoB,kYAAA,CAAA,SAA2B;AAErD,MAAM,mCAAqB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC,kYAAA,CAAA,UAA4B;QAC3B,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;QACT,KAAK;;;;;;AAGT,mBAAmB,WAAW,GAAG,kYAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,mCAAqB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGxC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC;;0BACC,mVAAC;;;;;0BACD,mVAAC,kYAAA,CAAA,UAA4B;gBAC3B,KAAK;gBACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,uVACA;gBAED,GAAG,KAAK;;;;;;;;;;;;AAIf,mBAAmB,WAAW,GAAG,kYAAA,CAAA,UAA4B,CAAC,WAAW;AAEzE,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,mVAAC;QACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,oDACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,oBAAoB,CAAC,EACzB,SAAS,EACT,GAAG,OACkC,iBACrC,mVAAC;QACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG;AAEhC,MAAM,iCAAmB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC,kYAAA,CAAA,QAA0B;QACzB,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG,kYAAA,CAAA,QAA0B,CAAC,WAAW;AAErE,MAAM,uCAAyB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG5C,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC,kYAAA,CAAA,cAAgC;QAC/B,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,uBAAuB,WAAW,GAChC,kYAAA,CAAA,cAAgC,CAAC,WAAW;AAE9C,MAAM,kCAAoB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC,kYAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD,KAAK;QAC/B,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kYAAA,CAAA,SAA2B,CAAC,WAAW;AAEvE,MAAM,kCAAoB,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGvC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,mVAAC,kYAAA,CAAA,SAA2B;QAC1B,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,CAAA,GAAA,sJAAA,CAAA,iBAAc,AAAD,EAAE;YAAE,SAAS;QAAU,IACpC,gBACA;QAED,GAAG,KAAK;;;;;;AAGb,kBAAkB,WAAW,GAAG,kYAAA,CAAA,SAA2B,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 716, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/super-admin/role-permissions/RoleList.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'\nimport { \n  Search, \n  Plus, \n  MoreHorizontal, \n  Edit, \n  Trash2, \n  Shield, \n  Users, \n  Eye,\n  Filter,\n  Grid,\n  List,\n  CheckCircle,\n  XCircle,\n  AlertTriangle\n} from 'lucide-react'\nimport { Role, useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'\n\ninterface RoleListProps {\n  onCreateRole: () => void\n  onEditRole: (role: Role) => void\n  onAssignPermissions: (role: Role) => void\n  viewMode: 'list' | 'cards'\n  onViewModeChange: (mode: 'list' | 'cards') => void\n}\n\nexport default function RoleList({ \n  onCreateRole, \n  onEditRole, \n  onAssignPermissions,\n  viewMode,\n  onViewModeChange \n}: RoleListProps) {\n  const {\n    roles,\n    isLoading,\n    filters,\n    setFilters,\n    deleteRole,\n  } = useRolePermissionsStore()\n\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)\n  const [roleToDelete, setRoleToDelete] = useState<Role | null>(null)\n\n  const handleDeleteRole = async () => {\n    if (roleToDelete) {\n      await deleteRole(roleToDelete.id)\n      setDeleteDialogOpen(false)\n      setRoleToDelete(null)\n    }\n  }\n\n  const openDeleteDialog = (role: Role) => {\n    setRoleToDelete(role)\n    setDeleteDialogOpen(true)\n  }\n\n  const getLevelBadgeColor = (level: number) => {\n    switch (level) {\n      case 1: return 'bg-red-100 text-red-800'\n      case 2: return 'bg-orange-100 text-orange-800'\n      case 3: return 'bg-yellow-100 text-yellow-800'\n      case 4: return 'bg-blue-100 text-blue-800'\n      case 5: return 'bg-green-100 text-green-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getLevelLabel = (level: number) => {\n    switch (level) {\n      case 1: return 'Executive'\n      case 2: return 'Manager'\n      case 3: return 'Senior'\n      case 4: return 'Staff'\n      case 5: return 'Junior'\n      default: return 'Unknown'\n    }\n  }\n\n  const RoleCard = ({ role }: { role: Role }) => (\n    <Card className=\"hover:shadow-md transition-shadow\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"space-y-1\">\n            <CardTitle className=\"text-lg flex items-center gap-2\">\n              <Shield className=\"h-4 w-4\" />\n              {role.name}\n            </CardTitle>\n            <div className=\"flex items-center gap-2\">\n              <Badge className={getLevelBadgeColor(role.level)}>\n                Level {role.level} - {getLevelLabel(role.level)}\n              </Badge>\n              {role.isActive ? (\n                <Badge variant=\"default\" className=\"bg-green-100 text-green-800\">\n                  <CheckCircle className=\"h-3 w-3 mr-1\" />\n                  Active\n                </Badge>\n              ) : (\n                <Badge variant=\"secondary\" className=\"bg-red-100 text-red-800\">\n                  <XCircle className=\"h-3 w-3 mr-1\" />\n                  Inactive\n                </Badge>\n              )}\n              {role.isSystemRole && (\n                <Badge variant=\"outline\" className=\"border-orange-300 text-orange-700\">\n                  <AlertTriangle className=\"h-3 w-3 mr-1\" />\n                  System\n                </Badge>\n              )}\n            </div>\n          </div>\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"sm\">\n                <MoreHorizontal className=\"h-4 w-4\" />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\">\n              <DropdownMenuItem onClick={() => onAssignPermissions(role)}>\n                <Shield className=\"h-4 w-4 mr-2\" />\n                Manage Permissions\n              </DropdownMenuItem>\n              <DropdownMenuItem onClick={() => onEditRole(role)}>\n                <Edit className=\"h-4 w-4 mr-2\" />\n                Edit Role\n              </DropdownMenuItem>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem \n                onClick={() => openDeleteDialog(role)}\n                className=\"text-red-600\"\n                disabled={role.isSystemRole}\n              >\n                <Trash2 className=\"h-4 w-4 mr-2\" />\n                Delete Role\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </CardHeader>\n      <CardContent>\n        <CardDescription className=\"mb-3\">\n          {role.description || 'No description provided'}\n        </CardDescription>\n        <div className=\"flex items-center justify-between text-sm text-gray-500\">\n          <div className=\"flex items-center gap-1\">\n            <Users className=\"h-4 w-4\" />\n            <span>{role.permissionCount || 0} permissions</span>\n          </div>\n          <span>Created {new Date(role.createdAt).toLocaleDateString()}</span>\n        </div>\n      </CardContent>\n    </Card>\n  )\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n        <div>\n          <h2 className=\"text-2xl font-bold\">Role Management</h2>\n          <p className=\"text-gray-600\">Manage user roles and their permission assignments</p>\n        </div>\n        <Button onClick={onCreateRole} className=\"flex items-center gap-2\">\n          <Plus className=\"h-4 w-4\" />\n          Create Role\n        </Button>\n      </div>\n\n      {/* Filters and Controls */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Search roles...\"\n                  value={filters.search || ''}\n                  onChange={(e) => setFilters({ search: e.target.value })}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant={viewMode === 'list' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => onViewModeChange('list')}\n              >\n                <List className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant={viewMode === 'cards' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => onViewModeChange('cards')}\n              >\n                <Grid className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Content */}\n      {isLoading ? (\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-2 text-gray-600\">Loading roles...</p>\n        </div>\n      ) : roles.length === 0 ? (\n        <Card>\n          <CardContent className=\"text-center py-8\">\n            <Shield className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No roles found</h3>\n            <p className=\"text-gray-600 mb-4\">Get started by creating your first role.</p>\n            <Button onClick={onCreateRole}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Create Role\n            </Button>\n          </CardContent>\n        </Card>\n      ) : viewMode === 'cards' ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {roles.map((role) => (\n            <RoleCard key={role.id} role={role} />\n          ))}\n        </div>\n      ) : (\n        <Card>\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Role Name</TableHead>\n                <TableHead>Level</TableHead>\n                <TableHead>Status</TableHead>\n                <TableHead>Permissions</TableHead>\n                <TableHead>Created</TableHead>\n                <TableHead className=\"w-[70px]\">Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {roles.map((role) => (\n                <TableRow key={role.id}>\n                  <TableCell>\n                    <div className=\"space-y-1\">\n                      <div className=\"font-medium flex items-center gap-2\">\n                        <Shield className=\"h-4 w-4\" />\n                        {role.name}\n                        {role.isSystemRole && (\n                          <Badge variant=\"outline\" className=\"border-orange-300 text-orange-700\">\n                            System\n                          </Badge>\n                        )}\n                      </div>\n                      {role.description && (\n                        <div className=\"text-sm text-gray-500 truncate max-w-xs\">\n                          {role.description}\n                        </div>\n                      )}\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <Badge className={getLevelBadgeColor(role.level)}>\n                      Level {role.level}\n                    </Badge>\n                  </TableCell>\n                  <TableCell>\n                    {role.isActive ? (\n                      <Badge variant=\"default\" className=\"bg-green-100 text-green-800\">\n                        <CheckCircle className=\"h-3 w-3 mr-1\" />\n                        Active\n                      </Badge>\n                    ) : (\n                      <Badge variant=\"secondary\" className=\"bg-red-100 text-red-800\">\n                        <XCircle className=\"h-3 w-3 mr-1\" />\n                        Inactive\n                      </Badge>\n                    )}\n                  </TableCell>\n                  <TableCell>\n                    <div className=\"flex items-center gap-1\">\n                      <Users className=\"h-4 w-4 text-gray-400\" />\n                      <span>{role.permissionCount || 0}</span>\n                    </div>\n                  </TableCell>\n                  <TableCell className=\"text-sm text-gray-500\">\n                    {new Date(role.createdAt).toLocaleDateString()}\n                  </TableCell>\n                  <TableCell>\n                    <DropdownMenu>\n                      <DropdownMenuTrigger asChild>\n                        <Button variant=\"ghost\" size=\"sm\">\n                          <MoreHorizontal className=\"h-4 w-4\" />\n                        </Button>\n                      </DropdownMenuTrigger>\n                      <DropdownMenuContent align=\"end\">\n                        <DropdownMenuItem onClick={() => onAssignPermissions(role)}>\n                          <Shield className=\"h-4 w-4 mr-2\" />\n                          Manage Permissions\n                        </DropdownMenuItem>\n                        <DropdownMenuItem onClick={() => onEditRole(role)}>\n                          <Edit className=\"h-4 w-4 mr-2\" />\n                          Edit Role\n                        </DropdownMenuItem>\n                        <DropdownMenuSeparator />\n                        <DropdownMenuItem \n                          onClick={() => openDeleteDialog(role)}\n                          className=\"text-red-600\"\n                          disabled={role.isSystemRole}\n                        >\n                          <Trash2 className=\"h-4 w-4 mr-2\" />\n                          Delete Role\n                        </DropdownMenuItem>\n                      </DropdownMenuContent>\n                    </DropdownMenu>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </Card>\n      )}\n\n      {/* Delete Confirmation Dialog */}\n      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>Delete Role</AlertDialogTitle>\n            <AlertDialogDescription>\n              Are you sure you want to delete the role \"{roleToDelete?.name}\"? \n              This action cannot be undone and will remove all permission assignments for this role.\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\n            <AlertDialogAction onClick={handleDeleteRole} className=\"bg-red-600 hover:bg-red-700\">\n              Delete Role\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAVA;;;;;;;;;;;;AAoCe,SAAS,SAAS,EAC/B,YAAY,EACZ,UAAU,EACV,mBAAmB,EACnB,QAAQ,EACR,gBAAgB,EACF;IACd,MAAM,EACJ,KAAK,EACL,SAAS,EACT,OAAO,EACP,UAAU,EACV,UAAU,EACX,GAAG;IAEJ,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,MAAM,mBAAmB;QACvB,IAAI,cAAc;YAChB,MAAM,WAAW,aAAa,EAAE;YAChC,oBAAoB;YACpB,gBAAgB;QAClB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,gBAAgB;QAChB,oBAAoB;IACtB;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAkB,iBACxC,mVAAC,oJAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,mVAAC,oJAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;;kDACb,mVAAC,oJAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,mVAAC,0RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CACjB,KAAK,IAAI;;;;;;;kDAEZ,mVAAC;wCAAI,WAAU;;0DACb,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,mBAAmB,KAAK,KAAK;;oDAAG;oDACzC,KAAK,KAAK;oDAAC;oDAAI,cAAc,KAAK,KAAK;;;;;;;4CAE/C,KAAK,QAAQ,iBACZ,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;kEACjC,mVAAC,+SAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;qEAI1C,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;;kEACnC,mVAAC,gSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;4CAIvC,KAAK,YAAY,kBAChB,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;kEACjC,mVAAC,4SAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAMlD,mVAAC,gKAAA,CAAA,eAAY;;kDACX,mVAAC,gKAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,mVAAC,sJAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;sDAC3B,cAAA,mVAAC,oSAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG9B,mVAAC,gKAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,mVAAC,gKAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,oBAAoB;;kEACnD,mVAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGrC,mVAAC,gKAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,WAAW;;kEAC1C,mVAAC,+RAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,mVAAC,gKAAA,CAAA,wBAAqB;;;;;0DACtB,mVAAC,gKAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,iBAAiB;gDAChC,WAAU;gDACV,UAAU,KAAK,YAAY;;kEAE3B,mVAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7C,mVAAC,oJAAA,CAAA,cAAW;;sCACV,mVAAC,oJAAA,CAAA,kBAAe;4BAAC,WAAU;sCACxB,KAAK,WAAW,IAAI;;;;;;sCAEvB,mVAAC;4BAAI,WAAU;;8CACb,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,wRAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,mVAAC;;gDAAM,KAAK,eAAe,IAAI;gDAAE;;;;;;;;;;;;;8CAEnC,mVAAC;;wCAAK;wCAAS,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;IAMlE,qBACE,mVAAC;QAAI,WAAU;;0BAEb,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;;0CACC,mVAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,mVAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,mVAAC,sJAAA,CAAA,SAAM;wBAAC,SAAS;wBAAc,WAAU;;0CACvC,mVAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAMhC,mVAAC,oJAAA,CAAA,OAAI;0BACH,cAAA,mVAAC,oJAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;0CACb,cAAA,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,mVAAC,qJAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO,QAAQ,MAAM,IAAI;4CACzB,UAAU,CAAC,IAAM,WAAW;oDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrD,WAAU;;;;;;;;;;;;;;;;;0CAIhB,mVAAC;gCAAI,WAAU;;kDACb,mVAAC,sJAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,iBAAiB;kDAEhC,cAAA,mVAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,mVAAC,sJAAA,CAAA,SAAM;wCACL,SAAS,aAAa,UAAU,YAAY;wCAC5C,MAAK;wCACL,SAAS,IAAM,iBAAiB;kDAEhC,cAAA,mVAAC,6RAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzB,0BACC,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAI,WAAU;;;;;;kCACf,mVAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;uBAElC,MAAM,MAAM,KAAK,kBACnB,mVAAC,oJAAA,CAAA,OAAI;0BACH,cAAA,mVAAC,oJAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,mVAAC,0RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,mVAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,mVAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,mVAAC,sJAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,mVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;uBAKrC,aAAa,wBACf,mVAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,mVAAC;wBAAuB,MAAM;uBAAf,KAAK,EAAE;;;;;;;;;qCAI1B,mVAAC,oJAAA,CAAA,OAAI;0BACH,cAAA,mVAAC,qJAAA,CAAA,QAAK;;sCACJ,mVAAC,qJAAA,CAAA,cAAW;sCACV,cAAA,mVAAC,qJAAA,CAAA,WAAQ;;kDACP,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;;;;;;;;;;;;sCAGpC,mVAAC,qJAAA,CAAA,YAAS;sCACP,MAAM,GAAG,CAAC,CAAC,qBACV,mVAAC,qJAAA,CAAA,WAAQ;;sDACP,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC;gDAAI,WAAU;;kEACb,mVAAC;wDAAI,WAAU;;0EACb,mVAAC,0RAAA,CAAA,SAAM;gEAAC,WAAU;;;;;;4DACjB,KAAK,IAAI;4DACT,KAAK,YAAY,kBAChB,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAoC;;;;;;;;;;;;oDAK1E,KAAK,WAAW,kBACf,mVAAC;wDAAI,WAAU;kEACZ,KAAK,WAAW;;;;;;;;;;;;;;;;;sDAKzB,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,mBAAmB,KAAK,KAAK;;oDAAG;oDACzC,KAAK,KAAK;;;;;;;;;;;;sDAGrB,mVAAC,qJAAA,CAAA,YAAS;sDACP,KAAK,QAAQ,iBACZ,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;kEACjC,mVAAC,+SAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;oDAAiB;;;;;;qEAI1C,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAY,WAAU;;kEACnC,mVAAC,gSAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAK1C,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC;gDAAI,WAAU;;kEACb,mVAAC,wRAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;kEACjB,mVAAC;kEAAM,KAAK,eAAe,IAAI;;;;;;;;;;;;;;;;;sDAGnC,mVAAC,qJAAA,CAAA,YAAS;4CAAC,WAAU;sDAClB,IAAI,KAAK,KAAK,SAAS,EAAE,kBAAkB;;;;;;sDAE9C,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC,gKAAA,CAAA,eAAY;;kEACX,mVAAC,gKAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,mVAAC,sJAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,mVAAC,oSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,mVAAC,gKAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,mVAAC,gKAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,oBAAoB;;kFACnD,mVAAC,0RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGrC,mVAAC,gKAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,WAAW;;kFAC1C,mVAAC,+RAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,mVAAC,gKAAA,CAAA,wBAAqB;;;;;0EACtB,mVAAC,gKAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,iBAAiB;gEAChC,WAAU;gEACV,UAAU,KAAK,YAAY;;kFAE3B,mVAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;mCApE9B,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;0BAkFhC,mVAAC,+JAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,mVAAC,+JAAA,CAAA,qBAAkB;;sCACjB,mVAAC,+JAAA,CAAA,oBAAiB;;8CAChB,mVAAC,+JAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,mVAAC,+JAAA,CAAA,yBAAsB;;wCAAC;wCACqB,cAAc;wCAAK;;;;;;;;;;;;;sCAIlE,mVAAC,+JAAA,CAAA,oBAAiB;;8CAChB,mVAAC,+JAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,mVAAC,+JAAA,CAAA,oBAAiB;oCAAC,SAAS;oCAAkB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQlG", "debugId": null}}, {"offset": {"line": 1741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/super-admin/role-permissions/PermissionList.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport { <PERSON><PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Badge } from '@/components/ui/badge'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'\nimport { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'\nimport { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from '@/components/ui/alert-dialog'\nimport { \n  Search, \n  Plus, \n  MoreHorizontal, \n  Edit, \n  Trash2, \n  Key, \n  Filter,\n  Grid,\n  List,\n  AlertTriangle,\n  Shield\n} from 'lucide-react'\nimport { Permission, useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'\n\ninterface PermissionListProps {\n  onCreatePermission: () => void\n  onEditPermission: (permission: Permission) => void\n  viewMode: 'list' | 'cards'\n  onViewModeChange: (mode: 'list' | 'cards') => void\n}\n\nexport default function PermissionList({ \n  onCreatePermission, \n  onEditPermission,\n  viewMode,\n  onViewModeChange \n}: PermissionListProps) {\n  const {\n    permissions,\n    isLoading,\n    filters,\n    setFilters,\n    deletePermission,\n  } = useRolePermissionsStore()\n\n  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)\n  const [permissionToDelete, setPermissionToDelete] = useState<Permission | null>(null)\n\n  const handleDeletePermission = async () => {\n    if (permissionToDelete) {\n      await deletePermission(permissionToDelete.id)\n      setDeleteDialogOpen(false)\n      setPermissionToDelete(null)\n    }\n  }\n\n  const openDeleteDialog = (permission: Permission) => {\n    setPermissionToDelete(permission)\n    setDeleteDialogOpen(true)\n  }\n\n  const getActionBadgeColor = (action: string) => {\n    switch (action.toLowerCase()) {\n      case 'create': return 'bg-green-100 text-green-800'\n      case 'read': case 'view': return 'bg-blue-100 text-blue-800'\n      case 'update': case 'edit': return 'bg-yellow-100 text-yellow-800'\n      case 'delete': return 'bg-red-100 text-red-800'\n      case 'manage': return 'bg-purple-100 text-purple-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getScopeBadgeColor = (scope: string) => {\n    switch (scope.toLowerCase()) {\n      case 'global': return 'bg-red-100 text-red-800'\n      case 'institute': return 'bg-blue-100 text-blue-800'\n      case 'department': return 'bg-yellow-100 text-yellow-800'\n      case 'branch': return 'bg-green-100 text-green-800'\n      case 'own': return 'bg-gray-100 text-gray-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  const getLevelBadgeColor = (level: number) => {\n    switch (level) {\n      case 1: return 'bg-red-100 text-red-800'\n      case 2: return 'bg-orange-100 text-orange-800'\n      case 3: return 'bg-yellow-100 text-yellow-800'\n      case 4: return 'bg-blue-100 text-blue-800'\n      case 5: return 'bg-green-100 text-green-800'\n      default: return 'bg-gray-100 text-gray-800'\n    }\n  }\n\n  // Get unique categories for filter\n  const categories = Array.from(new Set(permissions.map(p => p.category)))\n\n  const PermissionCard = ({ permission }: { permission: Permission }) => (\n    <Card className=\"hover:shadow-md transition-shadow\">\n      <CardHeader className=\"pb-3\">\n        <div className=\"flex items-start justify-between\">\n          <div className=\"space-y-1\">\n            <CardTitle className=\"text-lg flex items-center gap-2\">\n              <Key className=\"h-4 w-4\" />\n              {permission.name}\n            </CardTitle>\n            <div className=\"flex items-center gap-2 flex-wrap\">\n              <Badge className={getActionBadgeColor(permission.action)}>\n                {permission.action}\n              </Badge>\n              <Badge variant=\"outline\">\n                {permission.resource}\n              </Badge>\n              <Badge className={getScopeBadgeColor(permission.scope)}>\n                {permission.scope}\n              </Badge>\n              <Badge className={getLevelBadgeColor(permission.requiredLevel)}>\n                Level {permission.requiredLevel}+\n              </Badge>\n              {permission.isSystemPermission && (\n                <Badge variant=\"outline\" className=\"border-orange-300 text-orange-700\">\n                  <AlertTriangle className=\"h-3 w-3 mr-1\" />\n                  System\n                </Badge>\n              )}\n            </div>\n          </div>\n          <DropdownMenu>\n            <DropdownMenuTrigger asChild>\n              <Button variant=\"ghost\" size=\"sm\">\n                <MoreHorizontal className=\"h-4 w-4\" />\n              </Button>\n            </DropdownMenuTrigger>\n            <DropdownMenuContent align=\"end\">\n              <DropdownMenuItem onClick={() => onEditPermission(permission)}>\n                <Edit className=\"h-4 w-4 mr-2\" />\n                Edit Permission\n              </DropdownMenuItem>\n              <DropdownMenuSeparator />\n              <DropdownMenuItem \n                onClick={() => openDeleteDialog(permission)}\n                className=\"text-red-600\"\n                disabled={permission.isSystemPermission}\n              >\n                <Trash2 className=\"h-4 w-4 mr-2\" />\n                Delete Permission\n              </DropdownMenuItem>\n            </DropdownMenuContent>\n          </DropdownMenu>\n        </div>\n      </CardHeader>\n      <CardContent>\n        <CardDescription className=\"mb-3\">\n          {permission.description || 'No description provided'}\n        </CardDescription>\n        <div className=\"flex items-center justify-between text-sm text-gray-500\">\n          <Badge variant=\"secondary\">\n            {permission.category.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n          </Badge>\n          <span>Created {new Date(permission.createdAt).toLocaleDateString()}</span>\n        </div>\n      </CardContent>\n    </Card>\n  )\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\">\n        <div>\n          <h2 className=\"text-2xl font-bold\">Permission Management</h2>\n          <p className=\"text-gray-600\">Manage system permissions that can be assigned to roles</p>\n        </div>\n        <Button onClick={onCreatePermission} className=\"flex items-center gap-2\">\n          <Plus className=\"h-4 w-4\" />\n          Create Permission\n        </Button>\n      </div>\n\n      {/* Filters and Controls */}\n      <Card>\n        <CardContent className=\"pt-6\">\n          <div className=\"flex flex-col sm:flex-row gap-4\">\n            <div className=\"flex-1\">\n              <div className=\"relative\">\n                <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                <Input\n                  placeholder=\"Search permissions...\"\n                  value={filters.search || ''}\n                  onChange={(e) => setFilters({ search: e.target.value })}\n                  className=\"pl-10\"\n                />\n              </div>\n            </div>\n            <div className=\"sm:w-48\">\n              <select\n                value={filters.category || ''}\n                onChange={(e) => setFilters({ category: e.target.value || undefined })}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              >\n                <option value=\"\">All Categories</option>\n                {categories.map(category => (\n                  <option key={category} value={category}>\n                    {category.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                  </option>\n                ))}\n              </select>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Button\n                variant={viewMode === 'list' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => onViewModeChange('list')}\n              >\n                <List className=\"h-4 w-4\" />\n              </Button>\n              <Button\n                variant={viewMode === 'cards' ? 'default' : 'outline'}\n                size=\"sm\"\n                onClick={() => onViewModeChange('cards')}\n              >\n                <Grid className=\"h-4 w-4\" />\n              </Button>\n            </div>\n          </div>\n        </CardContent>\n      </Card>\n\n      {/* Content */}\n      {isLoading ? (\n        <div className=\"text-center py-8\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-2 text-gray-600\">Loading permissions...</p>\n        </div>\n      ) : permissions.length === 0 ? (\n        <Card>\n          <CardContent className=\"text-center py-8\">\n            <Key className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No permissions found</h3>\n            <p className=\"text-gray-600 mb-4\">Get started by creating your first permission.</p>\n            <Button onClick={onCreatePermission}>\n              <Plus className=\"h-4 w-4 mr-2\" />\n              Create Permission\n            </Button>\n          </CardContent>\n        </Card>\n      ) : viewMode === 'cards' ? (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {permissions.map((permission) => (\n            <PermissionCard key={permission.id} permission={permission} />\n          ))}\n        </div>\n      ) : (\n        <Card>\n          <Table>\n            <TableHeader>\n              <TableRow>\n                <TableHead>Permission Name</TableHead>\n                <TableHead>Category</TableHead>\n                <TableHead>Action</TableHead>\n                <TableHead>Resource</TableHead>\n                <TableHead>Scope</TableHead>\n                <TableHead>Level</TableHead>\n                <TableHead className=\"w-[70px]\">Actions</TableHead>\n              </TableRow>\n            </TableHeader>\n            <TableBody>\n              {permissions.map((permission) => (\n                <TableRow key={permission.id}>\n                  <TableCell>\n                    <div className=\"space-y-1\">\n                      <div className=\"font-medium flex items-center gap-2\">\n                        <Key className=\"h-4 w-4\" />\n                        {permission.name}\n                        {permission.isSystemPermission && (\n                          <Badge variant=\"outline\" className=\"border-orange-300 text-orange-700\">\n                            System\n                          </Badge>\n                        )}\n                      </div>\n                      {permission.description && (\n                        <div className=\"text-sm text-gray-500 truncate max-w-xs\">\n                          {permission.description}\n                        </div>\n                      )}\n                    </div>\n                  </TableCell>\n                  <TableCell>\n                    <Badge variant=\"secondary\">\n                      {permission.category.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                    </Badge>\n                  </TableCell>\n                  <TableCell>\n                    <Badge className={getActionBadgeColor(permission.action)}>\n                      {permission.action}\n                    </Badge>\n                  </TableCell>\n                  <TableCell>\n                    <Badge variant=\"outline\">\n                      {permission.resource}\n                    </Badge>\n                  </TableCell>\n                  <TableCell>\n                    <Badge className={getScopeBadgeColor(permission.scope)}>\n                      {permission.scope}\n                    </Badge>\n                  </TableCell>\n                  <TableCell>\n                    <Badge className={getLevelBadgeColor(permission.requiredLevel)}>\n                      Level {permission.requiredLevel}+\n                    </Badge>\n                  </TableCell>\n                  <TableCell>\n                    <DropdownMenu>\n                      <DropdownMenuTrigger asChild>\n                        <Button variant=\"ghost\" size=\"sm\">\n                          <MoreHorizontal className=\"h-4 w-4\" />\n                        </Button>\n                      </DropdownMenuTrigger>\n                      <DropdownMenuContent align=\"end\">\n                        <DropdownMenuItem onClick={() => onEditPermission(permission)}>\n                          <Edit className=\"h-4 w-4 mr-2\" />\n                          Edit Permission\n                        </DropdownMenuItem>\n                        <DropdownMenuSeparator />\n                        <DropdownMenuItem \n                          onClick={() => openDeleteDialog(permission)}\n                          className=\"text-red-600\"\n                          disabled={permission.isSystemPermission}\n                        >\n                          <Trash2 className=\"h-4 w-4 mr-2\" />\n                          Delete Permission\n                        </DropdownMenuItem>\n                      </DropdownMenuContent>\n                    </DropdownMenu>\n                  </TableCell>\n                </TableRow>\n              ))}\n            </TableBody>\n          </Table>\n        </Card>\n      )}\n\n      {/* Delete Confirmation Dialog */}\n      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>\n        <AlertDialogContent>\n          <AlertDialogHeader>\n            <AlertDialogTitle>Delete Permission</AlertDialogTitle>\n            <AlertDialogDescription>\n              Are you sure you want to delete the permission \"{permissionToDelete?.name}\"? \n              This action cannot be undone and will remove this permission from all roles.\n            </AlertDialogDescription>\n          </AlertDialogHeader>\n          <AlertDialogFooter>\n            <AlertDialogCancel>Cancel</AlertDialogCancel>\n            <AlertDialogAction onClick={handleDeletePermission} className=\"bg-red-600 hover:bg-red-700\">\n              Delete Permission\n            </AlertDialogAction>\n          </AlertDialogFooter>\n        </AlertDialogContent>\n      </AlertDialog>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAVA;;;;;;;;;;;;AAgCe,SAAS,eAAe,EACrC,kBAAkB,EAClB,gBAAgB,EAChB,QAAQ,EACR,gBAAgB,EACI;IACpB,MAAM,EACJ,WAAW,EACX,SAAS,EACT,OAAO,EACP,UAAU,EACV,gBAAgB,EACjB,GAAG;IAEJ,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAqB;IAEhF,MAAM,yBAAyB;QAC7B,IAAI,oBAAoB;YACtB,MAAM,iBAAiB,mBAAmB,EAAE;YAC5C,oBAAoB;YACpB,sBAAsB;QACxB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,sBAAsB;QACtB,oBAAoB;IACtB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ,OAAO,WAAW;YACxB,KAAK;gBAAU,OAAO;YACtB,KAAK;YAAQ,KAAK;gBAAQ,OAAO;YACjC,KAAK;YAAU,KAAK;gBAAQ,OAAO;YACnC,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAU,OAAO;YACtB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ,MAAM,WAAW;YACvB,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAa,OAAO;YACzB,KAAK;gBAAc,OAAO;YAC1B,KAAK;gBAAU,OAAO;YACtB,KAAK;gBAAO,OAAO;YACnB;gBAAS,OAAO;QAClB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf,KAAK;gBAAG,OAAO;YACf;gBAAS,OAAO;QAClB;IACF;IAEA,mCAAmC;IACnC,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IAErE,MAAM,iBAAiB,CAAC,EAAE,UAAU,EAA8B,iBAChE,mVAAC,oJAAA,CAAA,OAAI;YAAC,WAAU;;8BACd,mVAAC,oJAAA,CAAA,aAAU;oBAAC,WAAU;8BACpB,cAAA,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;;kDACb,mVAAC,oJAAA,CAAA,YAAS;wCAAC,WAAU;;0DACnB,mVAAC,oRAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CACd,WAAW,IAAI;;;;;;;kDAElB,mVAAC;wCAAI,WAAU;;0DACb,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,oBAAoB,WAAW,MAAM;0DACpD,WAAW,MAAM;;;;;;0DAEpB,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,WAAW,QAAQ;;;;;;0DAEtB,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,mBAAmB,WAAW,KAAK;0DAClD,WAAW,KAAK;;;;;;0DAEnB,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,mBAAmB,WAAW,aAAa;;oDAAG;oDACvD,WAAW,aAAa;oDAAC;;;;;;;4CAEjC,WAAW,kBAAkB,kBAC5B,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;gDAAU,WAAU;;kEACjC,mVAAC,4SAAA,CAAA,gBAAa;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;0CAMlD,mVAAC,gKAAA,CAAA,eAAY;;kDACX,mVAAC,gKAAA,CAAA,sBAAmB;wCAAC,OAAO;kDAC1B,cAAA,mVAAC,sJAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAQ,MAAK;sDAC3B,cAAA,mVAAC,oSAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAG9B,mVAAC,gKAAA,CAAA,sBAAmB;wCAAC,OAAM;;0DACzB,mVAAC,gKAAA,CAAA,mBAAgB;gDAAC,SAAS,IAAM,iBAAiB;;kEAChD,mVAAC,+RAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;0DAGnC,mVAAC,gKAAA,CAAA,wBAAqB;;;;;0DACtB,mVAAC,gKAAA,CAAA,mBAAgB;gDACf,SAAS,IAAM,iBAAiB;gDAChC,WAAU;gDACV,UAAU,WAAW,kBAAkB;;kEAEvC,mVAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAO7C,mVAAC,oJAAA,CAAA,cAAW;;sCACV,mVAAC,oJAAA,CAAA,kBAAe;4BAAC,WAAU;sCACxB,WAAW,WAAW,IAAI;;;;;;sCAE7B,mVAAC;4BAAI,WAAU;;8CACb,mVAAC,qJAAA,CAAA,QAAK;oCAAC,SAAQ;8CACZ,WAAW,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;8CAE7E,mVAAC;;wCAAK;wCAAS,IAAI,KAAK,WAAW,SAAS,EAAE,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;IAMxE,qBACE,mVAAC;QAAI,WAAU;;0BAEb,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;;0CACC,mVAAC;gCAAG,WAAU;0CAAqB;;;;;;0CACnC,mVAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;kCAE/B,mVAAC,sJAAA,CAAA,SAAM;wBAAC,SAAS;wBAAoB,WAAU;;0CAC7C,mVAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;0BAMhC,mVAAC,oJAAA,CAAA,OAAI;0BACH,cAAA,mVAAC,oJAAA,CAAA,cAAW;oBAAC,WAAU;8BACrB,cAAA,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;0CACb,cAAA,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,mVAAC,qJAAA,CAAA,QAAK;4CACJ,aAAY;4CACZ,OAAO,QAAQ,MAAM,IAAI;4CACzB,UAAU,CAAC,IAAM,WAAW;oDAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gDAAC;4CACrD,WAAU;;;;;;;;;;;;;;;;;0CAIhB,mVAAC;gCAAI,WAAU;0CACb,cAAA,mVAAC;oCACC,OAAO,QAAQ,QAAQ,IAAI;oCAC3B,UAAU,CAAC,IAAM,WAAW;4CAAE,UAAU,EAAE,MAAM,CAAC,KAAK,IAAI;wCAAU;oCACpE,WAAU;;sDAEV,mVAAC;4CAAO,OAAM;sDAAG;;;;;;wCAChB,WAAW,GAAG,CAAC,CAAA,yBACd,mVAAC;gDAAsB,OAAO;0DAC3B,SAAS,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;+CADrD;;;;;;;;;;;;;;;;0CAMnB,mVAAC;gCAAI,WAAU;;kDACb,mVAAC,sJAAA,CAAA,SAAM;wCACL,SAAS,aAAa,SAAS,YAAY;wCAC3C,MAAK;wCACL,SAAS,IAAM,iBAAiB;kDAEhC,cAAA,mVAAC,sRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;kDAElB,mVAAC,sJAAA,CAAA,SAAM;wCACL,SAAS,aAAa,UAAU,YAAY;wCAC5C,MAAK;wCACL,SAAS,IAAM,iBAAiB;kDAEhC,cAAA,mVAAC,6RAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAQzB,0BACC,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAI,WAAU;;;;;;kCACf,mVAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;uBAElC,YAAY,MAAM,KAAK,kBACzB,mVAAC,oJAAA,CAAA,OAAI;0BACH,cAAA,mVAAC,oJAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,mVAAC,oRAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,mVAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,mVAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAClC,mVAAC,sJAAA,CAAA,SAAM;4BAAC,SAAS;;8CACf,mVAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;uBAKrC,aAAa,wBACf,mVAAC;gBAAI,WAAU;0BACZ,YAAY,GAAG,CAAC,CAAC,2BAChB,mVAAC;wBAAmC,YAAY;uBAA3B,WAAW,EAAE;;;;;;;;;qCAItC,mVAAC,oJAAA,CAAA,OAAI;0BACH,cAAA,mVAAC,qJAAA,CAAA,QAAK;;sCACJ,mVAAC,qJAAA,CAAA,cAAW;sCACV,cAAA,mVAAC,qJAAA,CAAA,WAAQ;;kDACP,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,mVAAC,qJAAA,CAAA,YAAS;wCAAC,WAAU;kDAAW;;;;;;;;;;;;;;;;;sCAGpC,mVAAC,qJAAA,CAAA,YAAS;sCACP,YAAY,GAAG,CAAC,CAAC,2BAChB,mVAAC,qJAAA,CAAA,WAAQ;;sDACP,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC;gDAAI,WAAU;;kEACb,mVAAC;wDAAI,WAAU;;0EACb,mVAAC,oRAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DACd,WAAW,IAAI;4DACf,WAAW,kBAAkB,kBAC5B,mVAAC,qJAAA,CAAA,QAAK;gEAAC,SAAQ;gEAAU,WAAU;0EAAoC;;;;;;;;;;;;oDAK1E,WAAW,WAAW,kBACrB,mVAAC;wDAAI,WAAU;kEACZ,WAAW,WAAW;;;;;;;;;;;;;;;;;sDAK/B,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,WAAW,QAAQ,CAAC,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;;;;;;sDAG/E,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,oBAAoB,WAAW,MAAM;0DACpD,WAAW,MAAM;;;;;;;;;;;sDAGtB,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC,qJAAA,CAAA,QAAK;gDAAC,SAAQ;0DACZ,WAAW,QAAQ;;;;;;;;;;;sDAGxB,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,mBAAmB,WAAW,KAAK;0DAClD,WAAW,KAAK;;;;;;;;;;;sDAGrB,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC,qJAAA,CAAA,QAAK;gDAAC,WAAW,mBAAmB,WAAW,aAAa;;oDAAG;oDACvD,WAAW,aAAa;oDAAC;;;;;;;;;;;;sDAGpC,mVAAC,qJAAA,CAAA,YAAS;sDACR,cAAA,mVAAC,gKAAA,CAAA,eAAY;;kEACX,mVAAC,gKAAA,CAAA,sBAAmB;wDAAC,OAAO;kEAC1B,cAAA,mVAAC,sJAAA,CAAA,SAAM;4DAAC,SAAQ;4DAAQ,MAAK;sEAC3B,cAAA,mVAAC,oSAAA,CAAA,iBAAc;gEAAC,WAAU;;;;;;;;;;;;;;;;kEAG9B,mVAAC,gKAAA,CAAA,sBAAmB;wDAAC,OAAM;;0EACzB,mVAAC,gKAAA,CAAA,mBAAgB;gEAAC,SAAS,IAAM,iBAAiB;;kFAChD,mVAAC,+RAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;0EAGnC,mVAAC,gKAAA,CAAA,wBAAqB;;;;;0EACtB,mVAAC,gKAAA,CAAA,mBAAgB;gEACf,SAAS,IAAM,iBAAiB;gEAChC,WAAU;gEACV,UAAU,WAAW,kBAAkB;;kFAEvC,mVAAC,8RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;oEAAiB;;;;;;;;;;;;;;;;;;;;;;;;;mCA9D9B,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;0BA4EtC,mVAAC,+JAAA,CAAA,cAAW;gBAAC,MAAM;gBAAkB,cAAc;0BACjD,cAAA,mVAAC,+JAAA,CAAA,qBAAkB;;sCACjB,mVAAC,+JAAA,CAAA,oBAAiB;;8CAChB,mVAAC,+JAAA,CAAA,mBAAgB;8CAAC;;;;;;8CAClB,mVAAC,+JAAA,CAAA,yBAAsB;;wCAAC;wCAC2B,oBAAoB;wCAAK;;;;;;;;;;;;;sCAI9E,mVAAC,+JAAA,CAAA,oBAAiB;;8CAChB,mVAAC,+JAAA,CAAA,oBAAiB;8CAAC;;;;;;8CACnB,mVAAC,+JAAA,CAAA,oBAAiB;oCAAC,SAAS;oCAAwB,WAAU;8CAA8B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQxG", "debugId": null}}, {"offset": {"line": 2741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/textarea.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Textarea({ className, ...props }: React.ComponentProps<\"textarea\">) {\n  return (\n    <textarea\n      data-slot=\"textarea\"\n      className={cn(\n        \"border-input placeholder:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 flex field-sizing-content min-h-16 w-full rounded-md border bg-transparent px-3 py-2 text-base shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Textarea }\n"], "names": [], "mappings": ";;;;AAEA;;;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAyC;IACzE,qBACE,mVAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,ucACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2766, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Label({\n  className,\n  ...props\n}: React.ComponentProps<typeof LabelPrimitive.Root>) {\n  return (\n    <LabelPrimitive.Root\n      data-slot=\"label\"\n      className={cn(\n        \"flex items-center gap-2 text-sm leading-none font-medium select-none group-data-[disabled=true]:pointer-events-none group-data-[disabled=true]:opacity-50 peer-disabled:cursor-not-allowed peer-disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Label }\n"], "names": [], "mappings": ";;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,MAAM,EACb,SAAS,EACT,GAAG,OAC8C;IACjD,qBACE,mVAAC,6WAAA,CAAA,OAAmB;QAClB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,uNACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2794, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/switch.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface SwitchProps extends Omit<React.InputHTMLAttributes<HTMLInputElement>, 'type'> {\n  checked?: boolean\n  onCheckedChange?: (checked: boolean) => void\n}\n\nconst Switch = React.forwardRef<HTMLInputElement, SwitchProps>(\n  ({ className, checked, onCheckedChange, onChange, ...props }, ref) => {\n    const [internalChecked, setInternalChecked] = React.useState(checked || false)\n    const isChecked = checked !== undefined ? checked : internalChecked\n\n    const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {\n      const newChecked = event.target.checked\n      if (checked === undefined) {\n        setInternalChecked(newChecked)\n      }\n      onCheckedChange?.(newChecked)\n      onChange?.(event)\n    }\n\n    return (\n      <label className=\"relative inline-flex items-center cursor-pointer\">\n        <input\n          type=\"checkbox\"\n          ref={ref}\n          className=\"sr-only peer\"\n          checked={isChecked}\n          onChange={handleChange}\n          {...props}\n        />\n        <div\n          className={cn(\n            \"relative w-11 h-6 rounded-full transition-colors duration-200 ease-in-out\",\n            \"focus-within:ring-2 focus-within:ring-blue-500 focus-within:ring-offset-2\",\n            isChecked\n              ? \"bg-blue-600\"\n              : \"bg-gray-200\",\n            className\n          )}\n        >\n          <div\n            className={cn(\n              \"absolute top-0.5 left-0.5 w-5 h-5 bg-white rounded-full shadow-md transition-transform duration-200 ease-in-out\",\n              isChecked ? \"translate-x-5\" : \"translate-x-0\"\n            )}\n          />\n        </div>\n      </label>\n    )\n  }\n)\nSwitch.displayName = \"Switch\"\n\nexport { Switch }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAUA,MAAM,uBAAS,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,eAAe,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAc,AAAD,EAAE,WAAW;IACxE,MAAM,YAAY,YAAY,YAAY,UAAU;IAEpD,MAAM,eAAe,CAAC;QACpB,MAAM,aAAa,MAAM,MAAM,CAAC,OAAO;QACvC,IAAI,YAAY,WAAW;YACzB,mBAAmB;QACrB;QACA,kBAAkB;QAClB,WAAW;IACb;IAEA,qBACE,mVAAC;QAAM,WAAU;;0BACf,mVAAC;gBACC,MAAK;gBACL,KAAK;gBACL,WAAU;gBACV,SAAS;gBACT,UAAU;gBACT,GAAG,KAAK;;;;;;0BAEX,mVAAC;gBACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,6EACA,6EACA,YACI,gBACA,eACJ;0BAGF,cAAA,mVAAC;oBACC,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,mHACA,YAAY,kBAAkB;;;;;;;;;;;;;;;;;AAM1C;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2859, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,mVAAC,+WAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,mVAAC,+WAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,mVAAC,+WAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,mVAAC,+WAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,mVAAC,+WAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,mVAAC,4SAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,mVAAC,+WAAA,CAAA,SAAsB;kBACrB,cAAA,mVAAC,+WAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,mVAAC;;;;;8BACD,mVAAC,+WAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,mVAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,mVAAC,+WAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,mVAAC,+WAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,mVAAC;gBAAK,WAAU;0BACd,cAAA,mVAAC,+WAAA,CAAA,gBAA6B;8BAC5B,cAAA,mVAAC,4RAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,mVAAC,+WAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,mVAAC,+WAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,mVAAC,+WAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,mVAAC,wSAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,mVAAC,+WAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,mVAAC,4SAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 3084, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/super-admin/role-permissions/RoleForm.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Formik, Form, Field, ErrorMessage } from 'formik'\nimport * as Yup from 'yup'\nimport { But<PERSON> } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Label } from '@/components/ui/label'\nimport { Switch } from '@/components/ui/switch'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AlertCircle, Save, X } from 'lucide-react'\nimport { Role } from '@/stores/super-admin/useRolePermissionsStore'\n\ninterface RoleFormProps {\n  role?: Role | null\n  onSubmit: (values: Partial<Role>) => Promise<boolean>\n  onCancel: () => void\n  isLoading?: boolean\n}\n\nconst roleValidationSchema = Yup.object({\n  name: Yup.string()\n    .required('Role name is required')\n    .min(2, 'Role name must be at least 2 characters')\n    .max(50, 'Role name must not exceed 50 characters')\n    .matches(/^[a-zA-Z0-9\\s_-]+$/, 'Role name can only contain letters, numbers, spaces, hyphens, and underscores'),\n  \n  description: Yup.string()\n    .max(500, 'Description must not exceed 500 characters'),\n  \n  level: Yup.number()\n    .required('Level is required')\n    .min(1, 'Level must be between 1 and 5')\n    .max(5, 'Level must be between 1 and 5')\n    .integer('Level must be a whole number'),\n  \n  isActive: Yup.boolean(),\n  isSystemRole: Yup.boolean(),\n})\n\nconst levelOptions = [\n  { value: 1, label: 'Level 1 - Executive/Director', description: 'Highest level with full access' },\n  { value: 2, label: 'Level 2 - Manager/Head', description: 'Management level with departmental access' },\n  { value: 3, label: 'Level 3 - Senior Staff', description: 'Senior level with extended permissions' },\n  { value: 4, label: 'Level 4 - Staff', description: 'Standard staff level' },\n  { value: 5, label: 'Level 5 - Junior/Trainee', description: 'Entry level with basic permissions' },\n]\n\nexport default function RoleForm({ role, onSubmit, onCancel, isLoading = false }: RoleFormProps) {\n  const initialValues: Partial<Role> = {\n    name: role?.name || '',\n    description: role?.description || '',\n    level: role?.level || 4,\n    isActive: role?.isActive ?? true,\n    isSystemRole: role?.isSystemRole || false,\n  }\n\n  const handleSubmit = async (values: Partial<Role>) => {\n    const success = await onSubmit(values)\n    if (success) {\n      onCancel() // Close form on success\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          {role ? 'Edit Role' : 'Create New Role'}\n        </CardTitle>\n        <CardDescription>\n          {role \n            ? 'Update the role information and settings below.'\n            : 'Create a new role with specific permissions and access levels.'\n          }\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent>\n        <Formik\n          initialValues={initialValues}\n          validationSchema={roleValidationSchema}\n          onSubmit={handleSubmit}\n          enableReinitialize\n        >\n          {({ values, setFieldValue, errors, touched, isSubmitting }) => (\n            <Form className=\"space-y-6\">\n              {/* Role Name */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"name\" className=\"text-sm font-medium\">\n                  Role Name *\n                </Label>\n                <Field\n                  as={Input}\n                  id=\"name\"\n                  name=\"name\"\n                  placeholder=\"Enter role name (e.g., Academic Coordinator)\"\n                  className={errors.name && touched.name ? 'border-red-500' : ''}\n                />\n                <ErrorMessage name=\"name\" component=\"div\" className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span>{errors.name}</span>\n                </ErrorMessage>\n              </div>\n\n              {/* Description */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\" className=\"text-sm font-medium\">\n                  Description\n                </Label>\n                <Field\n                  as={Textarea}\n                  id=\"description\"\n                  name=\"description\"\n                  placeholder=\"Describe the role's responsibilities and purpose...\"\n                  rows={3}\n                  className={errors.description && touched.description ? 'border-red-500' : ''}\n                />\n                <ErrorMessage name=\"description\" component=\"div\" className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span>{errors.description}</span>\n                </ErrorMessage>\n              </div>\n\n              {/* Level */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"level\" className=\"text-sm font-medium\">\n                  Role Level *\n                </Label>\n                <Select\n                  value={values.level?.toString()}\n                  onValueChange={(value) => setFieldValue('level', parseInt(value))}\n                >\n                  <SelectTrigger className={errors.level && touched.level ? 'border-red-500' : ''}>\n                    <SelectValue placeholder=\"Select role level\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {levelOptions.map((option) => (\n                      <SelectItem key={option.value} value={option.value.toString()}>\n                        <div className=\"flex flex-col\">\n                          <span className=\"font-medium\">{option.label}</span>\n                          <span className=\"text-sm text-gray-500\">{option.description}</span>\n                        </div>\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                <ErrorMessage name=\"level\" component=\"div\" className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span>{errors.level}</span>\n                </ErrorMessage>\n              </div>\n\n              {/* Status Switches */}\n              <div className=\"space-y-4\">\n                <div className=\"flex items-center justify-between p-4 border rounded-lg\">\n                  <div className=\"space-y-1\">\n                    <Label htmlFor=\"isActive\" className=\"text-sm font-medium\">\n                      Active Status\n                    </Label>\n                    <p className=\"text-sm text-gray-500\">\n                      Whether this role is currently active and can be assigned to users\n                    </p>\n                  </div>\n                  <Switch\n                    id=\"isActive\"\n                    checked={values.isActive}\n                    onCheckedChange={(checked) => setFieldValue('isActive', checked)}\n                  />\n                </div>\n\n                <div className=\"flex items-center justify-between p-4 border rounded-lg\">\n                  <div className=\"space-y-1\">\n                    <Label htmlFor=\"isSystemRole\" className=\"text-sm font-medium\">\n                      System Role\n                    </Label>\n                    <p className=\"text-sm text-gray-500\">\n                      System roles are protected and cannot be deleted by users\n                    </p>\n                  </div>\n                  <Switch\n                    id=\"isSystemRole\"\n                    checked={values.isSystemRole}\n                    onCheckedChange={(checked) => setFieldValue('isSystemRole', checked)}\n                  />\n                </div>\n              </div>\n\n              {/* Form Actions */}\n              <div className=\"flex justify-end gap-3 pt-6 border-t\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={onCancel}\n                  disabled={isLoading || isSubmitting}\n                  className=\"flex items-center gap-2\"\n                >\n                  <X className=\"h-4 w-4\" />\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  disabled={isLoading || isSubmitting}\n                  className=\"flex items-center gap-2\"\n                >\n                  <Save className=\"h-4 w-4\" />\n                  {isLoading || isSubmitting ? 'Saving...' : (role ? 'Update Role' : 'Create Role')}\n                </Button>\n              </div>\n            </Form>\n          )}\n        </Formik>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAZA;;;;;;;;;;;;AAsBA,MAAM,uBAAuB,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;IACtC,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACZ,QAAQ,CAAC,yBACT,GAAG,CAAC,GAAG,2CACP,GAAG,CAAC,IAAI,2CACR,OAAO,CAAC,sBAAsB;IAEjC,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACnB,GAAG,CAAC,KAAK;IAEZ,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACb,QAAQ,CAAC,qBACT,GAAG,CAAC,GAAG,iCACP,GAAG,CAAC,GAAG,iCACP,OAAO,CAAC;IAEX,UAAU,CAAA,GAAA,mLAAA,CAAA,UAAW,AAAD;IACpB,cAAc,CAAA,GAAA,mLAAA,CAAA,UAAW,AAAD;AAC1B;AAEA,MAAM,eAAe;IACnB;QAAE,OAAO;QAAG,OAAO;QAAgC,aAAa;IAAiC;IACjG;QAAE,OAAO;QAAG,OAAO;QAA0B,aAAa;IAA4C;IACtG;QAAE,OAAO;QAAG,OAAO;QAA0B,aAAa;IAAyC;IACnG;QAAE,OAAO;QAAG,OAAO;QAAmB,aAAa;IAAuB;IAC1E;QAAE,OAAO;QAAG,OAAO;QAA4B,aAAa;IAAqC;CAClG;AAEc,SAAS,SAAS,EAAE,IAAI,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,KAAK,EAAiB;IAC7F,MAAM,gBAA+B;QACnC,MAAM,MAAM,QAAQ;QACpB,aAAa,MAAM,eAAe;QAClC,OAAO,MAAM,SAAS;QACtB,UAAU,MAAM,YAAY;QAC5B,cAAc,MAAM,gBAAgB;IACtC;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,UAAU,MAAM,SAAS;QAC/B,IAAI,SAAS;YACX,WAAW,wBAAwB;;QACrC;IACF;IAEA,qBACE,mVAAC,oJAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,mVAAC,oJAAA,CAAA,aAAU;;kCACT,mVAAC,oJAAA,CAAA,YAAS;wBAAC,WAAU;kCAClB,OAAO,cAAc;;;;;;kCAExB,mVAAC,oJAAA,CAAA,kBAAe;kCACb,OACG,oDACA;;;;;;;;;;;;0BAKR,mVAAC,oJAAA,CAAA,cAAW;0BACV,cAAA,mVAAC,wNAAA,CAAA,SAAM;oBACL,eAAe;oBACf,kBAAkB;oBAClB,UAAU;oBACV,kBAAkB;8BAEjB,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,iBACxD,mVAAC,wNAAA,CAAA,OAAI;4BAAC,WAAU;;8CAEd,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAO,WAAU;sDAAsB;;;;;;sDAGtD,mVAAC,wNAAA,CAAA,QAAK;4CACJ,IAAI,qJAAA,CAAA,QAAK;4CACT,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,WAAW,OAAO,IAAI,IAAI,QAAQ,IAAI,GAAG,mBAAmB;;;;;;sDAE9D,mVAAC,wNAAA,CAAA,eAAY;4CAAC,MAAK;4CAAO,WAAU;4CAAM,WAAU;;8DAClD,mVAAC,wSAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,mVAAC;8DAAM,OAAO,IAAI;;;;;;;;;;;;;;;;;;8CAKtB,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;sDAAsB;;;;;;sDAG7D,mVAAC,wNAAA,CAAA,QAAK;4CACJ,IAAI,wJAAA,CAAA,WAAQ;4CACZ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,MAAM;4CACN,WAAW,OAAO,WAAW,IAAI,QAAQ,WAAW,GAAG,mBAAmB;;;;;;sDAE5E,mVAAC,wNAAA,CAAA,eAAY;4CAAC,MAAK;4CAAc,WAAU;4CAAM,WAAU;;8DACzD,mVAAC,wSAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,mVAAC;8DAAM,OAAO,WAAW;;;;;;;;;;;;;;;;;;8CAK7B,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAQ,WAAU;sDAAsB;;;;;;sDAGvD,mVAAC,sJAAA,CAAA,SAAM;4CACL,OAAO,OAAO,KAAK,EAAE;4CACrB,eAAe,CAAC,QAAU,cAAc,SAAS,SAAS;;8DAE1D,mVAAC,sJAAA,CAAA,gBAAa;oDAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,GAAG,mBAAmB;8DAC3E,cAAA,mVAAC,sJAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,mVAAC,sJAAA,CAAA,gBAAa;8DACX,aAAa,GAAG,CAAC,CAAC,uBACjB,mVAAC,sJAAA,CAAA,aAAU;4DAAoB,OAAO,OAAO,KAAK,CAAC,QAAQ;sEACzD,cAAA,mVAAC;gEAAI,WAAU;;kFACb,mVAAC;wEAAK,WAAU;kFAAe,OAAO,KAAK;;;;;;kFAC3C,mVAAC;wEAAK,WAAU;kFAAyB,OAAO,WAAW;;;;;;;;;;;;2DAH9C,OAAO,KAAK;;;;;;;;;;;;;;;;sDASnC,mVAAC,wNAAA,CAAA,eAAY;4CAAC,MAAK;4CAAQ,WAAU;4CAAM,WAAU;;8DACnD,mVAAC,wSAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,mVAAC;8DAAM,OAAO,KAAK;;;;;;;;;;;;;;;;;;8CAKvB,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;;8DACb,mVAAC;oDAAI,WAAU;;sEACb,mVAAC,qJAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAW,WAAU;sEAAsB;;;;;;sEAG1D,mVAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,mVAAC,sJAAA,CAAA,SAAM;oDACL,IAAG;oDACH,SAAS,OAAO,QAAQ;oDACxB,iBAAiB,CAAC,UAAY,cAAc,YAAY;;;;;;;;;;;;sDAI5D,mVAAC;4CAAI,WAAU;;8DACb,mVAAC;oDAAI,WAAU;;sEACb,mVAAC,qJAAA,CAAA,QAAK;4DAAC,SAAQ;4DAAe,WAAU;sEAAsB;;;;;;sEAG9D,mVAAC;4DAAE,WAAU;sEAAwB;;;;;;;;;;;;8DAIvC,mVAAC,sJAAA,CAAA,SAAM;oDACL,IAAG;oDACH,SAAS,OAAO,YAAY;oDAC5B,iBAAiB,CAAC,UAAY,cAAc,gBAAgB;;;;;;;;;;;;;;;;;;8CAMlE,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,sJAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU,aAAa;4CACvB,WAAU;;8DAEV,mVAAC,gRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG3B,mVAAC,sJAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU,aAAa;4CACvB,WAAU;;8DAEV,mVAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,aAAa,eAAe,cAAe,OAAO,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASrF", "debugId": null}}, {"offset": {"line": 3585, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/super-admin/role-permissions/PermissionForm.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { Formik, Form, Field, ErrorMessage } from 'formik'\nimport * as Yup from 'yup'\nimport { Button } from '@/components/ui/button'\nimport { Input } from '@/components/ui/input'\nimport { Textarea } from '@/components/ui/textarea'\nimport { Label } from '@/components/ui/label'\nimport { Switch } from '@/components/ui/switch'\nimport { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { AlertCircle, Save, X } from 'lucide-react'\nimport { Permission } from '@/stores/super-admin/useRolePermissionsStore'\n\ninterface PermissionFormProps {\n  permission?: Permission | null\n  onSubmit: (values: Partial<Permission>) => Promise<boolean>\n  onCancel: () => void\n  isLoading?: boolean\n}\n\nconst permissionValidationSchema = Yup.object({\n  name: Yup.string()\n    .required('Permission name is required')\n    .min(2, 'Permission name must be at least 2 characters')\n    .max(100, 'Permission name must not exceed 100 characters'),\n  \n  description: Yup.string()\n    .max(500, 'Description must not exceed 500 characters'),\n  \n  category: Yup.string()\n    .required('Category is required'),\n  \n  resource: Yup.string()\n    .required('Resource is required')\n    .matches(/^[a-z_]+$/, 'Resource must be lowercase with underscores only'),\n  \n  action: Yup.string()\n    .required('Action is required')\n    .matches(/^[a-z_]+$/, 'Action must be lowercase with underscores only'),\n  \n  scope: Yup.string()\n    .required('Scope is required'),\n  \n  requiredLevel: Yup.number()\n    .required('Required level is required')\n    .min(1, 'Level must be between 1 and 5')\n    .max(5, 'Level must be between 1 and 5')\n    .integer('Level must be a whole number'),\n  \n  isSystemPermission: Yup.boolean(),\n})\n\nconst categoryOptions = [\n  'user_management',\n  'institute_management',\n  'course_management',\n  'billing_management',\n  'system_administration',\n  'analytics_reporting',\n  'content_management',\n  'communication',\n]\n\nconst actionOptions = [\n  'create',\n  'read',\n  'update',\n  'delete',\n  'manage',\n  'view',\n  'edit',\n  'approve',\n  'reject',\n  'export',\n  'import',\n]\n\nconst scopeOptions = [\n  'global',\n  'institute',\n  'department',\n  'branch',\n  'own',\n  'assigned',\n]\n\nconst levelOptions = [\n  { value: 1, label: 'Level 1 - Executive/Director' },\n  { value: 2, label: 'Level 2 - Manager/Head' },\n  { value: 3, label: 'Level 3 - Senior Staff' },\n  { value: 4, label: 'Level 4 - Staff' },\n  { value: 5, label: 'Level 5 - Junior/Trainee' },\n]\n\nexport default function PermissionForm({ permission, onSubmit, onCancel, isLoading = false }: PermissionFormProps) {\n  const initialValues: Partial<Permission> = {\n    name: permission?.name || '',\n    description: permission?.description || '',\n    category: permission?.category || '',\n    resource: permission?.resource || '',\n    action: permission?.action || '',\n    scope: permission?.scope || '',\n    requiredLevel: permission?.requiredLevel || 4,\n    isSystemPermission: permission?.isSystemPermission || false,\n  }\n\n  const handleSubmit = async (values: Partial<Permission>) => {\n    const success = await onSubmit(values)\n    if (success) {\n      onCancel() // Close form on success\n    }\n  }\n\n  return (\n    <Card className=\"w-full max-w-2xl mx-auto\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          {permission ? 'Edit Permission' : 'Create New Permission'}\n        </CardTitle>\n        <CardDescription>\n          {permission \n            ? 'Update the permission details and settings below.'\n            : 'Create a new permission that can be assigned to roles.'\n          }\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent>\n        <Formik\n          initialValues={initialValues}\n          validationSchema={permissionValidationSchema}\n          onSubmit={handleSubmit}\n          enableReinitialize\n        >\n          {({ values, setFieldValue, errors, touched, isSubmitting }) => (\n            <Form className=\"space-y-6\">\n              {/* Permission Name */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"name\" className=\"text-sm font-medium\">\n                  Permission Name *\n                </Label>\n                <Field\n                  as={Input}\n                  id=\"name\"\n                  name=\"name\"\n                  placeholder=\"Enter permission name (e.g., Manage Users)\"\n                  className={errors.name && touched.name ? 'border-red-500' : ''}\n                />\n                <ErrorMessage name=\"name\" component=\"div\" className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span>{errors.name}</span>\n                </ErrorMessage>\n              </div>\n\n              {/* Description */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"description\" className=\"text-sm font-medium\">\n                  Description\n                </Label>\n                <Field\n                  as={Textarea}\n                  id=\"description\"\n                  name=\"description\"\n                  placeholder=\"Describe what this permission allows...\"\n                  rows={3}\n                  className={errors.description && touched.description ? 'border-red-500' : ''}\n                />\n                <ErrorMessage name=\"description\" component=\"div\" className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span>{errors.description}</span>\n                </ErrorMessage>\n              </div>\n\n              {/* Category */}\n              <div className=\"space-y-2\">\n                <Label htmlFor=\"category\" className=\"text-sm font-medium\">\n                  Category *\n                </Label>\n                <Select\n                  value={values.category}\n                  onValueChange={(value) => setFieldValue('category', value)}\n                >\n                  <SelectTrigger className={errors.category && touched.category ? 'border-red-500' : ''}>\n                    <SelectValue placeholder=\"Select category\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    {categoryOptions.map((category) => (\n                      <SelectItem key={category} value={category}>\n                        {category.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                      </SelectItem>\n                    ))}\n                  </SelectContent>\n                </Select>\n                <ErrorMessage name=\"category\" component=\"div\" className=\"text-sm text-red-500 flex items-center gap-1\">\n                  <AlertCircle className=\"h-4 w-4\" />\n                  <span>{errors.category}</span>\n                </ErrorMessage>\n              </div>\n\n              {/* Resource and Action */}\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"resource\" className=\"text-sm font-medium\">\n                    Resource *\n                  </Label>\n                  <Field\n                    as={Input}\n                    id=\"resource\"\n                    name=\"resource\"\n                    placeholder=\"e.g., users, courses, institutes\"\n                    className={errors.resource && touched.resource ? 'border-red-500' : ''}\n                  />\n                  <ErrorMessage name=\"resource\" component=\"div\" className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    <span>{errors.resource}</span>\n                  </ErrorMessage>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"action\" className=\"text-sm font-medium\">\n                    Action *\n                  </Label>\n                  <Select\n                    value={values.action}\n                    onValueChange={(value) => setFieldValue('action', value)}\n                  >\n                    <SelectTrigger className={errors.action && touched.action ? 'border-red-500' : ''}>\n                      <SelectValue placeholder=\"Select action\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {actionOptions.map((action) => (\n                        <SelectItem key={action} value={action}>\n                          {action.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <ErrorMessage name=\"action\" component=\"div\" className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    <span>{errors.action}</span>\n                  </ErrorMessage>\n                </div>\n              </div>\n\n              {/* Scope and Required Level */}\n              <div className=\"grid grid-cols-2 gap-4\">\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"scope\" className=\"text-sm font-medium\">\n                    Scope *\n                  </Label>\n                  <Select\n                    value={values.scope}\n                    onValueChange={(value) => setFieldValue('scope', value)}\n                  >\n                    <SelectTrigger className={errors.scope && touched.scope ? 'border-red-500' : ''}>\n                      <SelectValue placeholder=\"Select scope\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {scopeOptions.map((scope) => (\n                        <SelectItem key={scope} value={scope}>\n                          {scope.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <ErrorMessage name=\"scope\" component=\"div\" className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    <span>{errors.scope}</span>\n                  </ErrorMessage>\n                </div>\n\n                <div className=\"space-y-2\">\n                  <Label htmlFor=\"requiredLevel\" className=\"text-sm font-medium\">\n                    Required Level *\n                  </Label>\n                  <Select\n                    value={values.requiredLevel?.toString()}\n                    onValueChange={(value) => setFieldValue('requiredLevel', parseInt(value))}\n                  >\n                    <SelectTrigger className={errors.requiredLevel && touched.requiredLevel ? 'border-red-500' : ''}>\n                      <SelectValue placeholder=\"Select level\" />\n                    </SelectTrigger>\n                    <SelectContent>\n                      {levelOptions.map((option) => (\n                        <SelectItem key={option.value} value={option.value.toString()}>\n                          {option.label}\n                        </SelectItem>\n                      ))}\n                    </SelectContent>\n                  </Select>\n                  <ErrorMessage name=\"requiredLevel\" component=\"div\" className=\"text-sm text-red-500 flex items-center gap-1\">\n                    <AlertCircle className=\"h-4 w-4\" />\n                    <span>{errors.requiredLevel}</span>\n                  </ErrorMessage>\n                </div>\n              </div>\n\n              {/* System Permission Switch */}\n              <div className=\"flex items-center justify-between p-4 border rounded-lg\">\n                <div className=\"space-y-1\">\n                  <Label htmlFor=\"isSystemPermission\" className=\"text-sm font-medium\">\n                    System Permission\n                  </Label>\n                  <p className=\"text-sm text-gray-500\">\n                    System permissions are protected and cannot be deleted by users\n                  </p>\n                </div>\n                <Switch\n                  id=\"isSystemPermission\"\n                  checked={values.isSystemPermission}\n                  onCheckedChange={(checked) => setFieldValue('isSystemPermission', checked)}\n                />\n              </div>\n\n              {/* Form Actions */}\n              <div className=\"flex justify-end gap-3 pt-6 border-t\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={onCancel}\n                  disabled={isLoading || isSubmitting}\n                  className=\"flex items-center gap-2\"\n                >\n                  <X className=\"h-4 w-4\" />\n                  Cancel\n                </Button>\n                <Button\n                  type=\"submit\"\n                  disabled={isLoading || isSubmitting}\n                  className=\"flex items-center gap-2\"\n                >\n                  <Save className=\"h-4 w-4\" />\n                  {isLoading || isSubmitting ? 'Saving...' : (permission ? 'Update Permission' : 'Create Permission')}\n                </Button>\n              </div>\n            </Form>\n          )}\n        </Formik>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAZA;;;;;;;;;;;;AAsBA,MAAM,6BAA6B,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;IAC5C,MAAM,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACZ,QAAQ,CAAC,+BACT,GAAG,CAAC,GAAG,iDACP,GAAG,CAAC,KAAK;IAEZ,aAAa,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACnB,GAAG,CAAC,KAAK;IAEZ,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IAChB,QAAQ,CAAC;IAEZ,UAAU,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IAChB,QAAQ,CAAC,wBACT,OAAO,CAAC,aAAa;IAExB,QAAQ,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACd,QAAQ,CAAC,sBACT,OAAO,CAAC,aAAa;IAExB,OAAO,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACb,QAAQ,CAAC;IAEZ,eAAe,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,IACrB,QAAQ,CAAC,8BACT,GAAG,CAAC,GAAG,iCACP,GAAG,CAAC,GAAG,iCACP,OAAO,CAAC;IAEX,oBAAoB,CAAA,GAAA,mLAAA,CAAA,UAAW,AAAD;AAChC;AAEA,MAAM,kBAAkB;IACtB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,gBAAgB;IACpB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,eAAe;IACnB;IACA;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,eAAe;IACnB;QAAE,OAAO;QAAG,OAAO;IAA+B;IAClD;QAAE,OAAO;QAAG,OAAO;IAAyB;IAC5C;QAAE,OAAO;QAAG,OAAO;IAAyB;IAC5C;QAAE,OAAO;QAAG,OAAO;IAAkB;IACrC;QAAE,OAAO;QAAG,OAAO;IAA2B;CAC/C;AAEc,SAAS,eAAe,EAAE,UAAU,EAAE,QAAQ,EAAE,QAAQ,EAAE,YAAY,KAAK,EAAuB;IAC/G,MAAM,gBAAqC;QACzC,MAAM,YAAY,QAAQ;QAC1B,aAAa,YAAY,eAAe;QACxC,UAAU,YAAY,YAAY;QAClC,UAAU,YAAY,YAAY;QAClC,QAAQ,YAAY,UAAU;QAC9B,OAAO,YAAY,SAAS;QAC5B,eAAe,YAAY,iBAAiB;QAC5C,oBAAoB,YAAY,sBAAsB;IACxD;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,UAAU,MAAM,SAAS;QAC/B,IAAI,SAAS;YACX,WAAW,wBAAwB;;QACrC;IACF;IAEA,qBACE,mVAAC,oJAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,mVAAC,oJAAA,CAAA,aAAU;;kCACT,mVAAC,oJAAA,CAAA,YAAS;wBAAC,WAAU;kCAClB,aAAa,oBAAoB;;;;;;kCAEpC,mVAAC,oJAAA,CAAA,kBAAe;kCACb,aACG,sDACA;;;;;;;;;;;;0BAKR,mVAAC,oJAAA,CAAA,cAAW;0BACV,cAAA,mVAAC,wNAAA,CAAA,SAAM;oBACL,eAAe;oBACf,kBAAkB;oBAClB,UAAU;oBACV,kBAAkB;8BAEjB,CAAC,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,OAAO,EAAE,YAAY,EAAE,iBACxD,mVAAC,wNAAA,CAAA,OAAI;4BAAC,WAAU;;8CAEd,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAO,WAAU;sDAAsB;;;;;;sDAGtD,mVAAC,wNAAA,CAAA,QAAK;4CACJ,IAAI,qJAAA,CAAA,QAAK;4CACT,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,WAAW,OAAO,IAAI,IAAI,QAAQ,IAAI,GAAG,mBAAmB;;;;;;sDAE9D,mVAAC,wNAAA,CAAA,eAAY;4CAAC,MAAK;4CAAO,WAAU;4CAAM,WAAU;;8DAClD,mVAAC,wSAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,mVAAC;8DAAM,OAAO,IAAI;;;;;;;;;;;;;;;;;;8CAKtB,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAc,WAAU;sDAAsB;;;;;;sDAG7D,mVAAC,wNAAA,CAAA,QAAK;4CACJ,IAAI,wJAAA,CAAA,WAAQ;4CACZ,IAAG;4CACH,MAAK;4CACL,aAAY;4CACZ,MAAM;4CACN,WAAW,OAAO,WAAW,IAAI,QAAQ,WAAW,GAAG,mBAAmB;;;;;;sDAE5E,mVAAC,wNAAA,CAAA,eAAY;4CAAC,MAAK;4CAAc,WAAU;4CAAM,WAAU;;8DACzD,mVAAC,wSAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,mVAAC;8DAAM,OAAO,WAAW;;;;;;;;;;;;;;;;;;8CAK7B,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAW,WAAU;sDAAsB;;;;;;sDAG1D,mVAAC,sJAAA,CAAA,SAAM;4CACL,OAAO,OAAO,QAAQ;4CACtB,eAAe,CAAC,QAAU,cAAc,YAAY;;8DAEpD,mVAAC,sJAAA,CAAA,gBAAa;oDAAC,WAAW,OAAO,QAAQ,IAAI,QAAQ,QAAQ,GAAG,mBAAmB;8DACjF,cAAA,mVAAC,sJAAA,CAAA,cAAW;wDAAC,aAAY;;;;;;;;;;;8DAE3B,mVAAC,sJAAA,CAAA,gBAAa;8DACX,gBAAgB,GAAG,CAAC,CAAC,yBACpB,mVAAC,sJAAA,CAAA,aAAU;4DAAgB,OAAO;sEAC/B,SAAS,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;2DADjD;;;;;;;;;;;;;;;;sDAMvB,mVAAC,wNAAA,CAAA,eAAY;4CAAC,MAAK;4CAAW,WAAU;4CAAM,WAAU;;8DACtD,mVAAC,wSAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;8DACvB,mVAAC;8DAAM,OAAO,QAAQ;;;;;;;;;;;;;;;;;;8CAK1B,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,qJAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAW,WAAU;8DAAsB;;;;;;8DAG1D,mVAAC,wNAAA,CAAA,QAAK;oDACJ,IAAI,qJAAA,CAAA,QAAK;oDACT,IAAG;oDACH,MAAK;oDACL,aAAY;oDACZ,WAAW,OAAO,QAAQ,IAAI,QAAQ,QAAQ,GAAG,mBAAmB;;;;;;8DAEtE,mVAAC,wNAAA,CAAA,eAAY;oDAAC,MAAK;oDAAW,WAAU;oDAAM,WAAU;;sEACtD,mVAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,mVAAC;sEAAM,OAAO,QAAQ;;;;;;;;;;;;;;;;;;sDAI1B,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,qJAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAS,WAAU;8DAAsB;;;;;;8DAGxD,mVAAC,sJAAA,CAAA,SAAM;oDACL,OAAO,OAAO,MAAM;oDACpB,eAAe,CAAC,QAAU,cAAc,UAAU;;sEAElD,mVAAC,sJAAA,CAAA,gBAAa;4DAAC,WAAW,OAAO,MAAM,IAAI,QAAQ,MAAM,GAAG,mBAAmB;sEAC7E,cAAA,mVAAC,sJAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,mVAAC,sJAAA,CAAA,gBAAa;sEACX,cAAc,GAAG,CAAC,CAAC,uBAClB,mVAAC,sJAAA,CAAA,aAAU;oEAAc,OAAO;8EAC7B,OAAO,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;mEAD/C;;;;;;;;;;;;;;;;8DAMvB,mVAAC,wNAAA,CAAA,eAAY;oDAAC,MAAK;oDAAS,WAAU;oDAAM,WAAU;;sEACpD,mVAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,mVAAC;sEAAM,OAAO,MAAM;;;;;;;;;;;;;;;;;;;;;;;;8CAM1B,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,qJAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAQ,WAAU;8DAAsB;;;;;;8DAGvD,mVAAC,sJAAA,CAAA,SAAM;oDACL,OAAO,OAAO,KAAK;oDACnB,eAAe,CAAC,QAAU,cAAc,SAAS;;sEAEjD,mVAAC,sJAAA,CAAA,gBAAa;4DAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,GAAG,mBAAmB;sEAC3E,cAAA,mVAAC,sJAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,mVAAC,sJAAA,CAAA,gBAAa;sEACX,aAAa,GAAG,CAAC,CAAC,sBACjB,mVAAC,sJAAA,CAAA,aAAU;oEAAa,OAAO;8EAC5B,MAAM,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;mEAD9C;;;;;;;;;;;;;;;;8DAMvB,mVAAC,wNAAA,CAAA,eAAY;oDAAC,MAAK;oDAAQ,WAAU;oDAAM,WAAU;;sEACnD,mVAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,mVAAC;sEAAM,OAAO,KAAK;;;;;;;;;;;;;;;;;;sDAIvB,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,qJAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAgB,WAAU;8DAAsB;;;;;;8DAG/D,mVAAC,sJAAA,CAAA,SAAM;oDACL,OAAO,OAAO,aAAa,EAAE;oDAC7B,eAAe,CAAC,QAAU,cAAc,iBAAiB,SAAS;;sEAElE,mVAAC,sJAAA,CAAA,gBAAa;4DAAC,WAAW,OAAO,aAAa,IAAI,QAAQ,aAAa,GAAG,mBAAmB;sEAC3F,cAAA,mVAAC,sJAAA,CAAA,cAAW;gEAAC,aAAY;;;;;;;;;;;sEAE3B,mVAAC,sJAAA,CAAA,gBAAa;sEACX,aAAa,GAAG,CAAC,CAAC,uBACjB,mVAAC,sJAAA,CAAA,aAAU;oEAAoB,OAAO,OAAO,KAAK,CAAC,QAAQ;8EACxD,OAAO,KAAK;mEADE,OAAO,KAAK;;;;;;;;;;;;;;;;8DAMnC,mVAAC,wNAAA,CAAA,eAAY;oDAAC,MAAK;oDAAgB,WAAU;oDAAM,WAAU;;sEAC3D,mVAAC,wSAAA,CAAA,cAAW;4DAAC,WAAU;;;;;;sEACvB,mVAAC;sEAAM,OAAO,aAAa;;;;;;;;;;;;;;;;;;;;;;;;8CAMjC,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,qJAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAqB,WAAU;8DAAsB;;;;;;8DAGpE,mVAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAIvC,mVAAC,sJAAA,CAAA,SAAM;4CACL,IAAG;4CACH,SAAS,OAAO,kBAAkB;4CAClC,iBAAiB,CAAC,UAAY,cAAc,sBAAsB;;;;;;;;;;;;8CAKtE,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,sJAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU,aAAa;4CACvB,WAAU;;8DAEV,mVAAC,gRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG3B,mVAAC,sJAAA,CAAA,SAAM;4CACL,MAAK;4CACL,UAAU,aAAa;4CACvB,WAAU;;8DAEV,mVAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,aAAa,eAAe,cAAe,aAAa,sBAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASjG", "debugId": null}}, {"offset": {"line": 4356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\nimport { CheckIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Checkbox({\n  className,\n  ...props\n}: React.ComponentProps<typeof CheckboxPrimitive.Root>) {\n  return (\n    <CheckboxPrimitive.Root\n      data-slot=\"checkbox\"\n      className={cn(\n        \"peer border-input dark:bg-input/30 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground dark:data-[state=checked]:bg-primary data-[state=checked]:border-primary focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive size-4 shrink-0 rounded-[4px] border shadow-xs transition-shadow outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50\",\n        className\n      )}\n      {...props}\n    >\n      <CheckboxPrimitive.Indicator\n        data-slot=\"checkbox-indicator\"\n        className=\"flex items-center justify-center text-current transition-none\"\n      >\n        <CheckIcon className=\"size-3.5\" />\n      </CheckboxPrimitive.Indicator>\n    </CheckboxPrimitive.Root>\n  )\n}\n\nexport { Checkbox }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,SAAS,EAChB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,mVAAC,mXAAA,CAAA,OAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,+eACA;QAED,GAAG,KAAK;kBAET,cAAA,mVAAC,mXAAA,CAAA,YAA2B;YAC1B,aAAU;YACV,WAAU;sBAEV,cAAA,mVAAC,4RAAA,CAAA,YAAS;gBAAC,WAAU;;;;;;;;;;;;;;;;AAI7B", "debugId": null}}, {"offset": {"line": 4401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/separator.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\n\nconst Separator = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement> & {\n    orientation?: \"horizontal\" | \"vertical\"\n    decorative?: boolean\n  }\n>(\n  (\n    { className, orientation = \"horizontal\", decorative = true, ...props },\n    ref\n  ) => (\n    <div\n      ref={ref}\n      role={decorative ? \"none\" : \"separator\"}\n      aria-orientation={orientation}\n      className={cn(\n        \"shrink-0 bg-border\",\n        orientation === \"horizontal\" ? \"h-[1px] w-full\" : \"h-full w-[1px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n)\nSeparator.displayName = \"Separator\"\n\nexport { Separator }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKA,MAAM,0BAAY,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAO/B,CACE,EAAE,SAAS,EAAE,cAAc,YAAY,EAAE,aAAa,IAAI,EAAE,GAAG,OAAO,EACtE,oBAEA,mVAAC;QACC,KAAK;QACL,MAAM,aAAa,SAAS;QAC5B,oBAAkB;QAClB,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,sBACA,gBAAgB,eAAe,mBAAmB,kBAClD;QAED,GAAG,KAAK;;;;;;AAIf,UAAU,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 4430, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/ui/scroll-area.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as ScrollAreaPrimitive from \"@radix-ui/react-scroll-area\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst ScrollArea = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.Root>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.Root>\n>(({ className, children, ...props }, ref) => (\n  <ScrollAreaPrimitive.Root\n    ref={ref}\n    className={cn(\"relative overflow-hidden\", className)}\n    {...props}\n  >\n    <ScrollAreaPrimitive.Viewport className=\"h-full w-full rounded-[inherit]\">\n      {children}\n    </ScrollAreaPrimitive.Viewport>\n    <ScrollBar />\n    <ScrollAreaPrimitive.Corner />\n  </ScrollAreaPrimitive.Root>\n))\nScrollArea.displayName = ScrollAreaPrimitive.Root.displayName\n\nconst ScrollBar = React.forwardRef<\n  React.ElementRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>,\n  React.ComponentPropsWithoutRef<typeof ScrollAreaPrimitive.ScrollAreaScrollbar>\n>(({ className, orientation = \"vertical\", ...props }, ref) => (\n  <ScrollAreaPrimitive.ScrollAreaScrollbar\n    ref={ref}\n    orientation={orientation}\n    className={cn(\n      \"flex touch-none select-none transition-colors\",\n      orientation === \"vertical\" &&\n        \"h-full w-2.5 border-l border-l-transparent p-[1px]\",\n      orientation === \"horizontal\" &&\n        \"h-2.5 flex-col border-t border-t-transparent p-[1px]\",\n      className\n    )}\n    {...props}\n  >\n    <ScrollAreaPrimitive.ScrollAreaThumb className=\"relative flex-1 rounded-full bg-border\" />\n  </ScrollAreaPrimitive.ScrollAreaScrollbar>\n))\nScrollBar.displayName = ScrollAreaPrimitive.ScrollAreaScrollbar.displayName\n\nexport { ScrollArea, ScrollBar }\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,2BAAa,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE,oBACpC,mVAAC,+XAAA,CAAA,OAAwB;QACvB,KAAK;QACL,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;QACzC,GAAG,KAAK;;0BAET,mVAAC,+XAAA,CAAA,WAA4B;gBAAC,WAAU;0BACrC;;;;;;0BAEH,mVAAC;;;;;0BACD,mVAAC,+XAAA,CAAA,SAA0B;;;;;;;;;;;AAG/B,WAAW,WAAW,GAAG,+XAAA,CAAA,OAAwB,CAAC,WAAW;AAE7D,MAAM,0BAAY,CAAA,GAAA,0SAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,cAAc,UAAU,EAAE,GAAG,OAAO,EAAE,oBACpD,mVAAC,+XAAA,CAAA,sBAAuC;QACtC,KAAK;QACL,aAAa;QACb,WAAW,CAAA,GAAA,uIAAA,CAAA,KAAE,AAAD,EACV,iDACA,gBAAgB,cACd,sDACF,gBAAgB,gBACd,wDACF;QAED,GAAG,KAAK;kBAET,cAAA,mVAAC,+XAAA,CAAA,kBAAmC;YAAC,WAAU;;;;;;;;;;;AAGnD,UAAU,WAAW,GAAG,+XAAA,CAAA,sBAAuC,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 4498, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/components/super-admin/role-permissions/RolePermissionAssignment.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect } from 'react'\nimport { Formik, Form } from 'formik'\nimport * as Yup from 'yup'\nimport { Button } from '@/components/ui/button'\nimport { Label } from '@/components/ui/label'\nimport { Checkbox } from '@/components/ui/checkbox'\nimport { Input } from '@/components/ui/input'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Separator } from '@/components/ui/separator'\nimport { ScrollArea } from '@/components/ui/scroll-area'\nimport { Search, Save, X, Shield, Users, AlertCircle } from 'lucide-react'\nimport { Role, Permission, useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'\n\ninterface RolePermissionAssignmentProps {\n  role: Role\n  onCancel: () => void\n}\n\nconst assignmentValidationSchema = Yup.object({\n  selectedPermissions: Yup.array()\n    .min(0, 'At least one permission must be selected')\n})\n\nexport default function RolePermissionAssignment({ role, onCancel }: RolePermissionAssignmentProps) {\n  const {\n    permissions,\n    selectedPermissions,\n    isLoading,\n    fetchPermissions,\n    fetchRolePermissions,\n    assignPermissionsToRole,\n    setSelectedPermissions,\n  } = useRolePermissionsStore()\n\n  const [searchQuery, setSearchQuery] = useState('')\n  const [selectedCategory, setSelectedCategory] = useState<string>('')\n\n  useEffect(() => {\n    fetchPermissions()\n    fetchRolePermissions(role.id)\n  }, [role.id, fetchPermissions, fetchRolePermissions])\n\n  // Filter permissions based on search and category\n  const filteredPermissions = permissions.filter(permission => {\n    const matchesSearch = permission.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         permission.description?.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         permission.resource.toLowerCase().includes(searchQuery.toLowerCase())\n    \n    const matchesCategory = !selectedCategory || permission.category === selectedCategory\n    \n    return matchesSearch && matchesCategory\n  })\n\n  // Group permissions by category\n  const permissionsByCategory = filteredPermissions.reduce((acc, permission) => {\n    const category = permission.category\n    if (!acc[category]) {\n      acc[category] = []\n    }\n    acc[category].push(permission)\n    return acc\n  }, {} as Record<string, Permission[]>)\n\n  // Get unique categories\n  const categories = Array.from(new Set(permissions.map(p => p.category)))\n\n  const handlePermissionToggle = (permissionId: string, checked: boolean) => {\n    const newSelectedPermissions = checked\n      ? [...selectedPermissions, permissionId]\n      : selectedPermissions.filter(id => id !== permissionId)\n    \n    setSelectedPermissions(newSelectedPermissions)\n  }\n\n  const handleSelectAll = (categoryPermissions: Permission[], checked: boolean) => {\n    const categoryPermissionIds = categoryPermissions.map(p => p.id)\n    \n    if (checked) {\n      const newSelected = [...new Set([...selectedPermissions, ...categoryPermissionIds])]\n      setSelectedPermissions(newSelected)\n    } else {\n      const newSelected = selectedPermissions.filter(id => !categoryPermissionIds.includes(id))\n      setSelectedPermissions(newSelected)\n    }\n  }\n\n  const handleSubmit = async () => {\n    const success = await assignPermissionsToRole(role.id, selectedPermissions)\n    if (success) {\n      onCancel()\n    }\n  }\n\n  const getCategoryPermissionCount = (categoryPermissions: Permission[]) => {\n    const selectedCount = categoryPermissions.filter(p => selectedPermissions.includes(p.id)).length\n    return `${selectedCount}/${categoryPermissions.length}`\n  }\n\n  const isAllCategorySelected = (categoryPermissions: Permission[]) => {\n    return categoryPermissions.every(p => selectedPermissions.includes(p.id))\n  }\n\n  const isSomeCategorySelected = (categoryPermissions: Permission[]) => {\n    return categoryPermissions.some(p => selectedPermissions.includes(p.id))\n  }\n\n  return (\n    <Card className=\"w-full max-w-4xl mx-auto\">\n      <CardHeader>\n        <CardTitle className=\"flex items-center gap-2\">\n          <Shield className=\"h-5 w-5\" />\n          Assign Permissions to Role: {role.name}\n        </CardTitle>\n        <CardDescription>\n          Select the permissions that should be granted to users with the \"{role.name}\" role.\n          Users with Level {role.level} access will inherit these permissions.\n        </CardDescription>\n      </CardHeader>\n      \n      <CardContent>\n        <Formik\n          initialValues={{ selectedPermissions }}\n          validationSchema={assignmentValidationSchema}\n          onSubmit={handleSubmit}\n          enableReinitialize\n        >\n          {({ isSubmitting }) => (\n            <Form className=\"space-y-6\">\n              {/* Search and Filter Controls */}\n              <div className=\"flex flex-col sm:flex-row gap-4\">\n                <div className=\"flex-1\">\n                  <div className=\"relative\">\n                    <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\" />\n                    <Input\n                      placeholder=\"Search permissions...\"\n                      value={searchQuery}\n                      onChange={(e) => setSearchQuery(e.target.value)}\n                      className=\"pl-10\"\n                    />\n                  </div>\n                </div>\n                <div className=\"sm:w-48\">\n                  <select\n                    value={selectedCategory}\n                    onChange={(e) => setSelectedCategory(e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\n                  >\n                    <option value=\"\">All Categories</option>\n                    {categories.map(category => (\n                      <option key={category} value={category}>\n                        {category.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                      </option>\n                    ))}\n                  </select>\n                </div>\n              </div>\n\n              {/* Summary */}\n              <div className=\"flex items-center justify-between p-4 bg-blue-50 rounded-lg\">\n                <div className=\"flex items-center gap-2\">\n                  <Users className=\"h-5 w-5 text-blue-600\" />\n                  <span className=\"font-medium text-blue-900\">\n                    {selectedPermissions.length} permission{selectedPermissions.length !== 1 ? 's' : ''} selected\n                  </span>\n                </div>\n                <Badge variant=\"secondary\">\n                  Level {role.level} Role\n                </Badge>\n              </div>\n\n              {/* Permissions List */}\n              <ScrollArea className=\"h-96 border rounded-lg\">\n                <div className=\"p-4 space-y-6\">\n                  {Object.entries(permissionsByCategory).map(([category, categoryPermissions]) => (\n                    <div key={category} className=\"space-y-3\">\n                      {/* Category Header */}\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center gap-3\">\n                          <Checkbox\n                            id={`category-${category}`}\n                            checked={isAllCategorySelected(categoryPermissions)}\n                            onCheckedChange={(checked) => handleSelectAll(categoryPermissions, checked as boolean)}\n                            className={isSomeCategorySelected(categoryPermissions) && !isAllCategorySelected(categoryPermissions) ? 'data-[state=checked]:bg-blue-600' : ''}\n                          />\n                          <Label htmlFor={`category-${category}`} className=\"text-lg font-semibold cursor-pointer\">\n                            {category.replace(/_/g, ' ').replace(/\\b\\w/g, l => l.toUpperCase())}\n                          </Label>\n                          <Badge variant=\"outline\">\n                            {getCategoryPermissionCount(categoryPermissions)}\n                          </Badge>\n                        </div>\n                      </div>\n\n                      {/* Category Permissions */}\n                      <div className=\"ml-6 space-y-2\">\n                        {categoryPermissions.map((permission) => (\n                          <div key={permission.id} className=\"flex items-start gap-3 p-3 border rounded-lg hover:bg-gray-50\">\n                            <Checkbox\n                              id={permission.id}\n                              checked={selectedPermissions.includes(permission.id)}\n                              onCheckedChange={(checked) => handlePermissionToggle(permission.id, checked as boolean)}\n                            />\n                            <div className=\"flex-1 min-w-0\">\n                              <Label htmlFor={permission.id} className=\"font-medium cursor-pointer\">\n                                {permission.name}\n                              </Label>\n                              {permission.description && (\n                                <p className=\"text-sm text-gray-600 mt-1\">\n                                  {permission.description}\n                                </p>\n                              )}\n                              <div className=\"flex items-center gap-2 mt-2\">\n                                <Badge variant=\"secondary\" className=\"text-xs\">\n                                  {permission.action}\n                                </Badge>\n                                <Badge variant=\"outline\" className=\"text-xs\">\n                                  {permission.resource}\n                                </Badge>\n                                <Badge variant=\"outline\" className=\"text-xs\">\n                                  {permission.scope}\n                                </Badge>\n                                <Badge variant=\"outline\" className=\"text-xs\">\n                                  Level {permission.requiredLevel}+\n                                </Badge>\n                                {permission.isSystemPermission && (\n                                  <Badge variant=\"destructive\" className=\"text-xs\">\n                                    System\n                                  </Badge>\n                                )}\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n\n                      {category !== Object.keys(permissionsByCategory)[Object.keys(permissionsByCategory).length - 1] && (\n                        <Separator className=\"my-4\" />\n                      )}\n                    </div>\n                  ))}\n\n                  {Object.keys(permissionsByCategory).length === 0 && (\n                    <div className=\"text-center py-8 text-gray-500\">\n                      <AlertCircle className=\"h-8 w-8 mx-auto mb-2\" />\n                      <p>No permissions found matching your search criteria.</p>\n                    </div>\n                  )}\n                </div>\n              </ScrollArea>\n\n              {/* Form Actions */}\n              <div className=\"flex justify-end gap-3 pt-6 border-t\">\n                <Button\n                  type=\"button\"\n                  variant=\"outline\"\n                  onClick={onCancel}\n                  disabled={isLoading || isSubmitting}\n                  className=\"flex items-center gap-2\"\n                >\n                  <X className=\"h-4 w-4\" />\n                  Cancel\n                </Button>\n                <Button\n                  type=\"button\"\n                  onClick={handleSubmit}\n                  disabled={isLoading || isSubmitting}\n                  className=\"flex items-center gap-2\"\n                >\n                  <Save className=\"h-4 w-4\" />\n                  {isLoading || isSubmitting ? 'Saving...' : 'Save Permissions'}\n                </Button>\n              </div>\n            </Form>\n          )}\n        </Formik>\n      </CardContent>\n    </Card>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;AAbA;;;;;;;;;;;;;;;AAqBA,MAAM,6BAA6B,CAAA,GAAA,mLAAA,CAAA,SAAU,AAAD,EAAE;IAC5C,qBAAqB,CAAA,GAAA,mLAAA,CAAA,QAAS,AAAD,IAC1B,GAAG,CAAC,GAAG;AACZ;AAEe,SAAS,yBAAyB,EAAE,IAAI,EAAE,QAAQ,EAAiC;IAChG,MAAM,EACJ,WAAW,EACX,mBAAmB,EACnB,SAAS,EACT,gBAAgB,EAChB,oBAAoB,EACpB,uBAAuB,EACvB,sBAAsB,EACvB,GAAG;IAEJ,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR;QACA,qBAAqB,KAAK,EAAE;IAC9B,GAAG;QAAC,KAAK,EAAE;QAAE;QAAkB;KAAqB;IAEpD,kDAAkD;IAClD,MAAM,sBAAsB,YAAY,MAAM,CAAC,CAAA;QAC7C,MAAM,gBAAgB,WAAW,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW,OAC/D,WAAW,WAAW,EAAE,cAAc,SAAS,YAAY,WAAW,OACtE,WAAW,QAAQ,CAAC,WAAW,GAAG,QAAQ,CAAC,YAAY,WAAW;QAEvF,MAAM,kBAAkB,CAAC,oBAAoB,WAAW,QAAQ,KAAK;QAErE,OAAO,iBAAiB;IAC1B;IAEA,gCAAgC;IAChC,MAAM,wBAAwB,oBAAoB,MAAM,CAAC,CAAC,KAAK;QAC7D,MAAM,WAAW,WAAW,QAAQ;QACpC,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE;YAClB,GAAG,CAAC,SAAS,GAAG,EAAE;QACpB;QACA,GAAG,CAAC,SAAS,CAAC,IAAI,CAAC;QACnB,OAAO;IACT,GAAG,CAAC;IAEJ,wBAAwB;IACxB,MAAM,aAAa,MAAM,IAAI,CAAC,IAAI,IAAI,YAAY,GAAG,CAAC,CAAA,IAAK,EAAE,QAAQ;IAErE,MAAM,yBAAyB,CAAC,cAAsB;QACpD,MAAM,yBAAyB,UAC3B;eAAI;YAAqB;SAAa,GACtC,oBAAoB,MAAM,CAAC,CAAA,KAAM,OAAO;QAE5C,uBAAuB;IACzB;IAEA,MAAM,kBAAkB,CAAC,qBAAmC;QAC1D,MAAM,wBAAwB,oBAAoB,GAAG,CAAC,CAAA,IAAK,EAAE,EAAE;QAE/D,IAAI,SAAS;YACX,MAAM,cAAc;mBAAI,IAAI,IAAI;uBAAI;uBAAwB;iBAAsB;aAAE;YACpF,uBAAuB;QACzB,OAAO;YACL,MAAM,cAAc,oBAAoB,MAAM,CAAC,CAAA,KAAM,CAAC,sBAAsB,QAAQ,CAAC;YACrF,uBAAuB;QACzB;IACF;IAEA,MAAM,eAAe;QACnB,MAAM,UAAU,MAAM,wBAAwB,KAAK,EAAE,EAAE;QACvD,IAAI,SAAS;YACX;QACF;IACF;IAEA,MAAM,6BAA6B,CAAC;QAClC,MAAM,gBAAgB,oBAAoB,MAAM,CAAC,CAAA,IAAK,oBAAoB,QAAQ,CAAC,EAAE,EAAE,GAAG,MAAM;QAChG,OAAO,GAAG,cAAc,CAAC,EAAE,oBAAoB,MAAM,EAAE;IACzD;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAO,oBAAoB,KAAK,CAAC,CAAA,IAAK,oBAAoB,QAAQ,CAAC,EAAE,EAAE;IACzE;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAO,oBAAoB,IAAI,CAAC,CAAA,IAAK,oBAAoB,QAAQ,CAAC,EAAE,EAAE;IACxE;IAEA,qBACE,mVAAC,oJAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,mVAAC,oJAAA,CAAA,aAAU;;kCACT,mVAAC,oJAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,mVAAC,0RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;4BACD,KAAK,IAAI;;;;;;;kCAExC,mVAAC,oJAAA,CAAA,kBAAe;;4BAAC;4BACmD,KAAK,IAAI;4BAAC;4BAC1D,KAAK,KAAK;4BAAC;;;;;;;;;;;;;0BAIjC,mVAAC,oJAAA,CAAA,cAAW;0BACV,cAAA,mVAAC,wNAAA,CAAA,SAAM;oBACL,eAAe;wBAAE;oBAAoB;oBACrC,kBAAkB;oBAClB,UAAU;oBACV,kBAAkB;8BAEjB,CAAC,EAAE,YAAY,EAAE,iBAChB,mVAAC,wNAAA,CAAA,OAAI;4BAAC,WAAU;;8CAEd,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;sDACb,cAAA,mVAAC;gDAAI,WAAU;;kEACb,mVAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,mVAAC,qJAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,eAAe,EAAE,MAAM,CAAC,KAAK;wDAC9C,WAAU;;;;;;;;;;;;;;;;;sDAIhB,mVAAC;4CAAI,WAAU;sDACb,cAAA,mVAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,oBAAoB,EAAE,MAAM,CAAC,KAAK;gDACnD,WAAU;;kEAEV,mVAAC;wDAAO,OAAM;kEAAG;;;;;;oDAChB,WAAW,GAAG,CAAC,CAAA,yBACd,mVAAC;4DAAsB,OAAO;sEAC3B,SAAS,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;2DADrD;;;;;;;;;;;;;;;;;;;;;;8CASrB,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;;8DACb,mVAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,mVAAC;oDAAK,WAAU;;wDACb,oBAAoB,MAAM;wDAAC;wDAAY,oBAAoB,MAAM,KAAK,IAAI,MAAM;wDAAG;;;;;;;;;;;;;sDAGxF,mVAAC,qJAAA,CAAA,QAAK;4CAAC,SAAQ;;gDAAY;gDAClB,KAAK,KAAK;gDAAC;;;;;;;;;;;;;8CAKtB,mVAAC,8JAAA,CAAA,aAAU;oCAAC,WAAU;8CACpB,cAAA,mVAAC;wCAAI,WAAU;;4CACZ,OAAO,OAAO,CAAC,uBAAuB,GAAG,CAAC,CAAC,CAAC,UAAU,oBAAoB,iBACzE,mVAAC;oDAAmB,WAAU;;sEAE5B,mVAAC;4DAAI,WAAU;sEACb,cAAA,mVAAC;gEAAI,WAAU;;kFACb,mVAAC,wJAAA,CAAA,WAAQ;wEACP,IAAI,CAAC,SAAS,EAAE,UAAU;wEAC1B,SAAS,sBAAsB;wEAC/B,iBAAiB,CAAC,UAAY,gBAAgB,qBAAqB;wEACnE,WAAW,uBAAuB,wBAAwB,CAAC,sBAAsB,uBAAuB,qCAAqC;;;;;;kFAE/I,mVAAC,qJAAA,CAAA,QAAK;wEAAC,SAAS,CAAC,SAAS,EAAE,UAAU;wEAAE,WAAU;kFAC/C,SAAS,OAAO,CAAC,MAAM,KAAK,OAAO,CAAC,SAAS,CAAA,IAAK,EAAE,WAAW;;;;;;kFAElE,mVAAC,qJAAA,CAAA,QAAK;wEAAC,SAAQ;kFACZ,2BAA2B;;;;;;;;;;;;;;;;;sEAMlC,mVAAC;4DAAI,WAAU;sEACZ,oBAAoB,GAAG,CAAC,CAAC,2BACxB,mVAAC;oEAAwB,WAAU;;sFACjC,mVAAC,wJAAA,CAAA,WAAQ;4EACP,IAAI,WAAW,EAAE;4EACjB,SAAS,oBAAoB,QAAQ,CAAC,WAAW,EAAE;4EACnD,iBAAiB,CAAC,UAAY,uBAAuB,WAAW,EAAE,EAAE;;;;;;sFAEtE,mVAAC;4EAAI,WAAU;;8FACb,mVAAC,qJAAA,CAAA,QAAK;oFAAC,SAAS,WAAW,EAAE;oFAAE,WAAU;8FACtC,WAAW,IAAI;;;;;;gFAEjB,WAAW,WAAW,kBACrB,mVAAC;oFAAE,WAAU;8FACV,WAAW,WAAW;;;;;;8FAG3B,mVAAC;oFAAI,WAAU;;sGACb,mVAAC,qJAAA,CAAA,QAAK;4FAAC,SAAQ;4FAAY,WAAU;sGAClC,WAAW,MAAM;;;;;;sGAEpB,mVAAC,qJAAA,CAAA,QAAK;4FAAC,SAAQ;4FAAU,WAAU;sGAChC,WAAW,QAAQ;;;;;;sGAEtB,mVAAC,qJAAA,CAAA,QAAK;4FAAC,SAAQ;4FAAU,WAAU;sGAChC,WAAW,KAAK;;;;;;sGAEnB,mVAAC,qJAAA,CAAA,QAAK;4FAAC,SAAQ;4FAAU,WAAU;;gGAAU;gGACpC,WAAW,aAAa;gGAAC;;;;;;;wFAEjC,WAAW,kBAAkB,kBAC5B,mVAAC,qJAAA,CAAA,QAAK;4FAAC,SAAQ;4FAAc,WAAU;sGAAU;;;;;;;;;;;;;;;;;;;mEA7B/C,WAAW,EAAE;;;;;;;;;;wDAuC1B,aAAa,OAAO,IAAI,CAAC,sBAAsB,CAAC,OAAO,IAAI,CAAC,uBAAuB,MAAM,GAAG,EAAE,kBAC7F,mVAAC,yJAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;;mDA9Df;;;;;4CAmEX,OAAO,IAAI,CAAC,uBAAuB,MAAM,KAAK,mBAC7C,mVAAC;gDAAI,WAAU;;kEACb,mVAAC,wSAAA,CAAA,cAAW;wDAAC,WAAU;;;;;;kEACvB,mVAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;8CAOX,mVAAC;oCAAI,WAAU;;sDACb,mVAAC,sJAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAQ;4CACR,SAAS;4CACT,UAAU,aAAa;4CACvB,WAAU;;8DAEV,mVAAC,gRAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG3B,mVAAC,sJAAA,CAAA,SAAM;4CACL,MAAK;4CACL,SAAS;4CACT,UAAU,aAAa;4CACvB,WAAU;;8DAEV,mVAAC,sRAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;gDACf,aAAa,eAAe,cAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS7D", "debugId": null}}, {"offset": {"line": 5074, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/app/super-admin/role-permissions/page.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState, useEffect, useCallback } from 'react'\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport { useRouter } from 'next/navigation'\nimport { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'\nimport { Badge } from '@/components/ui/badge'\nimport { Shield, Key, Users, Settings } from 'lucide-react'\nimport { Role, Permission, useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'\n\n// Components\nimport RoleList from '@/components/super-admin/role-permissions/RoleList'\nimport PermissionList from '@/components/super-admin/role-permissions/PermissionList'\nimport RoleForm from '@/components/super-admin/role-permissions/RoleForm'\nimport PermissionForm from '@/components/super-admin/role-permissions/PermissionForm'\nimport RolePermissionAssignment from '@/components/super-admin/role-permissions/RolePermissionAssignment'\n\ntype ViewState = 'list' | 'create-role' | 'edit-role' | 'create-permission' | 'edit-permission' | 'assign-permissions'\n\nexport default function RolePermissionsPage() {\n  const { user, isAuthenticated, isLoading, initialize } = useAuthStore()\n  const router = useRouter()\n\n  const {\n    roles,\n    permissions,\n    viewMode,\n    setViewMode,\n    isLoading: storeLoading,\n    fetchRoles,\n    fetchPermissions,\n    fetchRolesWithPermissions,\n    createRole,\n    updateRole,\n    createPermission,\n    updatePermission,\n  } = useRolePermissionsStore()\n\n  const [activeTab, setActiveTab] = useState('roles')\n  const [viewState, setViewState] = useState<ViewState>('list')\n  const [selectedRole, setSelectedRole] = useState<Role | null>(null)\n  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null)\n  const [authChecked, setAuthChecked] = useState(false)\n\n  // Initialize auth on component mount\n  useEffect(() => {\n    initialize()\n  }, [initialize])\n\n  // Authentication check - more robust\n  useEffect(() => {\n    if (!isLoading) {\n      setAuthChecked(true)\n      if (!isAuthenticated || !user) {\n        router.push('/auth/admin/login')\n        return\n      }\n      if (user.role !== 'super_admin') {\n        // User is authenticated but not super admin\n        setAuthChecked(true)\n        return\n      }\n    }\n  }, [user, isAuthenticated, isLoading, router])\n\n  // Load data only after auth is confirmed\n  useEffect(() => {\n    if (authChecked && user && user.role === 'super_admin') {\n      fetchRolesWithPermissions()\n      fetchPermissions()\n    }\n  }, [authChecked, user]) // Remove function dependencies to prevent infinite re-renders\n\n  // Reset view state when changing tabs\n  useEffect(() => {\n    setViewState('list')\n    setSelectedRole(null)\n    setSelectedPermission(null)\n  }, [activeTab])\n\n  // Role handlers\n  const handleCreateRole = () => {\n    setSelectedRole(null)\n    setViewState('create-role')\n  }\n\n  const handleEditRole = (role: Role) => {\n    setSelectedRole(role)\n    setViewState('edit-role')\n  }\n\n  const handleAssignPermissions = (role: Role) => {\n    setSelectedRole(role)\n    setViewState('assign-permissions')\n  }\n\n  const handleRoleSubmit = async (roleData: Partial<Role>) => {\n    if (viewState === 'create-role') {\n      return await createRole(roleData)\n    } else if (viewState === 'edit-role' && selectedRole) {\n      return await updateRole(selectedRole.id, roleData)\n    }\n    return false\n  }\n\n  // Permission handlers\n  const handleCreatePermission = () => {\n    setSelectedPermission(null)\n    setViewState('create-permission')\n  }\n\n  const handleEditPermission = (permission: Permission) => {\n    setSelectedPermission(permission)\n    setViewState('edit-permission')\n  }\n\n  const handlePermissionSubmit = async (permissionData: Partial<Permission>) => {\n    if (viewState === 'create-permission') {\n      return await createPermission(permissionData)\n    } else if (viewState === 'edit-permission' && selectedPermission) {\n      return await updatePermission(selectedPermission.id, permissionData)\n    }\n    return false\n  }\n\n  const handleCancel = () => {\n    setViewState('list')\n    setSelectedRole(null)\n    setSelectedPermission(null)\n  }\n\n  // Show loading state\n  if (isLoading || !authChecked) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Loading...</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Show unauthorized if not authenticated or not super admin\n  if (!isAuthenticated || !user || user.role !== 'super_admin') {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <h1 className=\"text-2xl font-bold text-gray-900 mb-2\">Access Denied</h1>\n          <p className=\"text-gray-600\">You don't have permission to access this area.</p>\n          <p className=\"text-sm text-gray-500 mt-2\">Super Admin access required.</p>\n        </div>\n      </div>\n    )\n  }\n\n  // Render form views\n  if (viewState === 'create-role' || viewState === 'edit-role') {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <RoleForm\n          role={selectedRole}\n          onSubmit={handleRoleSubmit}\n          onCancel={handleCancel}\n          isLoading={storeLoading}\n        />\n      </div>\n    )\n  }\n\n  if (viewState === 'create-permission' || viewState === 'edit-permission') {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <PermissionForm\n          permission={selectedPermission}\n          onSubmit={handlePermissionSubmit}\n          onCancel={handleCancel}\n          isLoading={storeLoading}\n        />\n      </div>\n    )\n  }\n\n  if (viewState === 'assign-permissions' && selectedRole) {\n    return (\n      <div className=\"container mx-auto px-4 py-8\">\n        <RolePermissionAssignment\n          role={selectedRole}\n          onCancel={handleCancel}\n        />\n      </div>\n    )\n  }\n\n  // Main dashboard view\n  return (\n    <div className=\"container mx-auto px-4 py-8\">\n      {/* Header */}\n      <div className=\"mb-8\">\n        <h1 className=\"text-3xl font-bold text-gray-900 mb-2\">Role & Permission Management</h1>\n        <p className=\"text-gray-600\">\n          Manage user roles and permissions for the LMS platform. Control access to features and functionality.\n        </p>\n      </div>\n\n      {/* Statistics Cards */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\">\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-blue-100 rounded-lg\">\n                <Shield className=\"h-6 w-6 text-blue-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Roles</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{roles.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-green-100 rounded-lg\">\n                <Key className=\"h-6 w-6 text-green-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Total Permissions</p>\n                <p className=\"text-2xl font-bold text-gray-900\">{permissions.length}</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-yellow-100 rounded-lg\">\n                <Users className=\"h-6 w-6 text-yellow-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">Active Roles</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {roles.filter(role => role.isActive).length}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"pt-6\">\n            <div className=\"flex items-center\">\n              <div className=\"p-2 bg-purple-100 rounded-lg\">\n                <Settings className=\"h-6 w-6 text-purple-600\" />\n              </div>\n              <div className=\"ml-4\">\n                <p className=\"text-sm font-medium text-gray-600\">System Roles</p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {roles.filter(role => role.isSystemRole).length}\n                </p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Main Content */}\n      <Tabs value={activeTab} onValueChange={setActiveTab} className=\"space-y-6\">\n        <TabsList className=\"grid w-full grid-cols-3\">\n          <TabsTrigger value=\"roles\" className=\"flex items-center gap-2\">\n            <Shield className=\"h-4 w-4\" />\n            Roles\n          </TabsTrigger>\n          <TabsTrigger value=\"permissions\" className=\"flex items-center gap-2\">\n            <Key className=\"h-4 w-4\" />\n            Permissions\n          </TabsTrigger>\n          <TabsTrigger value=\"role-permissions\" className=\"flex items-center gap-2\">\n            <Users className=\"h-4 w-4\" />\n            Role Permissions\n          </TabsTrigger>\n        </TabsList>\n\n        <TabsContent value=\"roles\" className=\"space-y-6\">\n          <RoleList\n            onCreateRole={handleCreateRole}\n            onEditRole={handleEditRole}\n            onAssignPermissions={handleAssignPermissions}\n            viewMode={viewMode}\n            onViewModeChange={setViewMode}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"permissions\" className=\"space-y-6\">\n          <PermissionList\n            onCreatePermission={handleCreatePermission}\n            onEditPermission={handleEditPermission}\n            viewMode={viewMode}\n            onViewModeChange={setViewMode}\n          />\n        </TabsContent>\n\n        <TabsContent value=\"role-permissions\" className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center gap-2\">\n                <Users className=\"h-5 w-5\" />\n                Role Permission Assignment\n              </CardTitle>\n              <CardDescription>\n                Assign and manage permissions for each role. Control what actions each role can perform.\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-6\">\n                {roles.map((role) => (\n                  <div key={role.id} className=\"border rounded-lg p-4\">\n                    <div className=\"flex items-center justify-between mb-4\">\n                      <div className=\"flex items-center gap-3\">\n                        <div className=\"p-2 bg-blue-100 rounded-lg\">\n                          <Shield className=\"h-4 w-4 text-blue-600\" />\n                        </div>\n                        <div>\n                          <h3 className=\"font-semibold text-gray-900\">{role.name}</h3>\n                          <p className=\"text-sm text-gray-600\">{role.description}</p>\n                        </div>\n                      </div>\n                      <div className=\"flex items-center gap-2\">\n                        <Badge variant={role.isActive ? \"default\" : \"secondary\"}>\n                          {role.isActive ? \"Active\" : \"Inactive\"}\n                        </Badge>\n                        <button\n                          onClick={() => handleAssignPermissions(role)}\n                          className=\"px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\n                        >\n                          Manage Permissions\n                        </button>\n                      </div>\n                    </div>\n\n                    {/* Show current permissions for this role */}\n                    <div className=\"flex flex-wrap gap-2\">\n                      {role.permissions && role.permissions.length > 0 ? (\n                        role.permissions.map((permission) => (\n                          <Badge key={permission.id} variant=\"outline\" className=\"text-xs\">\n                            {permission.name}\n                          </Badge>\n                        ))\n                      ) : (\n                        <span className=\"text-sm text-gray-500 italic\">No permissions assigned</span>\n                      )}\n                    </div>\n                  </div>\n                ))}\n\n                {roles.length === 0 && (\n                  <div className=\"text-center py-8\">\n                    <Shield className=\"h-12 w-12 text-gray-400 mx-auto mb-4\" />\n                    <p className=\"text-gray-600\">No roles found. Create a role first to assign permissions.</p>\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </TabsContent>\n      </Tabs>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;AAGA,aAAa;AACb;AACA;AACA;AACA;AACA;AAhBA;;;;;;;;;;;;;;;AAoBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;IACpE,MAAM,SAAS,CAAA,GAAA,uOAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,EACJ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,WAAW,EACX,WAAW,YAAY,EACvB,UAAU,EACV,gBAAgB,EAChB,yBAAyB,EACzB,UAAU,EACV,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EACjB,GAAG;IAEJ,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAa;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAe;IAC9D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAqB;IAChF,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,0SAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,qCAAqC;IACrC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAW;IAEf,qCAAqC;IACrC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,eAAe;YACf,IAAI,CAAC,mBAAmB,CAAC,MAAM;gBAC7B,OAAO,IAAI,CAAC;gBACZ;YACF;YACA,IAAI,KAAK,IAAI,KAAK,eAAe;gBAC/B,4CAA4C;gBAC5C,eAAe;gBACf;YACF;QACF;IACF,GAAG;QAAC;QAAM;QAAiB;QAAW;KAAO;IAE7C,yCAAyC;IACzC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,eAAe,QAAQ,KAAK,IAAI,KAAK,eAAe;YACtD;YACA;QACF;IACF,GAAG;QAAC;QAAa;KAAK,EAAE,8DAA8D;;IAEtF,sCAAsC;IACtC,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,aAAa;QACb,gBAAgB;QAChB,sBAAsB;IACxB,GAAG;QAAC;KAAU;IAEd,gBAAgB;IAChB,MAAM,mBAAmB;QACvB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,iBAAiB,CAAC;QACtB,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,0BAA0B,CAAC;QAC/B,gBAAgB;QAChB,aAAa;IACf;IAEA,MAAM,mBAAmB,OAAO;QAC9B,IAAI,cAAc,eAAe;YAC/B,OAAO,MAAM,WAAW;QAC1B,OAAO,IAAI,cAAc,eAAe,cAAc;YACpD,OAAO,MAAM,WAAW,aAAa,EAAE,EAAE;QAC3C;QACA,OAAO;IACT;IAEA,sBAAsB;IACtB,MAAM,yBAAyB;QAC7B,sBAAsB;QACtB,aAAa;IACf;IAEA,MAAM,uBAAuB,CAAC;QAC5B,sBAAsB;QACtB,aAAa;IACf;IAEA,MAAM,yBAAyB,OAAO;QACpC,IAAI,cAAc,qBAAqB;YACrC,OAAO,MAAM,iBAAiB;QAChC,OAAO,IAAI,cAAc,qBAAqB,oBAAoB;YAChE,OAAO,MAAM,iBAAiB,mBAAmB,EAAE,EAAE;QACvD;QACA,OAAO;IACT;IAEA,MAAM,eAAe;QACnB,aAAa;QACb,gBAAgB;QAChB,sBAAsB;IACxB;IAEA,qBAAqB;IACrB,IAAI,aAAa,CAAC,aAAa;QAC7B,qBACE,mVAAC;YAAI,WAAU;sBACb,cAAA,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAI,WAAU;;;;;;kCACf,mVAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,4DAA4D;IAC5D,IAAI,CAAC,mBAAmB,CAAC,QAAQ,KAAK,IAAI,KAAK,eAAe;QAC5D,qBACE,mVAAC;YAAI,WAAU;sBACb,cAAA,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,mVAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,mVAAC;wBAAE,WAAU;kCAA6B;;;;;;;;;;;;;;;;;IAIlD;IAEA,oBAAoB;IACpB,IAAI,cAAc,iBAAiB,cAAc,aAAa;QAC5D,qBACE,mVAAC;YAAI,WAAU;sBACb,cAAA,mVAAC,2LAAA,CAAA,UAAQ;gBACP,MAAM;gBACN,UAAU;gBACV,UAAU;gBACV,WAAW;;;;;;;;;;;IAInB;IAEA,IAAI,cAAc,uBAAuB,cAAc,mBAAmB;QACxE,qBACE,mVAAC;YAAI,WAAU;sBACb,cAAA,mVAAC,iMAAA,CAAA,UAAc;gBACb,YAAY;gBACZ,UAAU;gBACV,UAAU;gBACV,WAAW;;;;;;;;;;;IAInB;IAEA,IAAI,cAAc,wBAAwB,cAAc;QACtD,qBACE,mVAAC;YAAI,WAAU;sBACb,cAAA,mVAAC,2MAAA,CAAA,UAAwB;gBACvB,MAAM;gBACN,UAAU;;;;;;;;;;;IAIlB;IAEA,sBAAsB;IACtB,qBACE,mVAAC;QAAI,WAAU;;0BAEb,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,mVAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,mVAAC;gBAAI,WAAU;;kCACb,mVAAC,oJAAA,CAAA,OAAI;kCACH,cAAA,mVAAC,oJAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC,0RAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;;;;;;kDAEpB,mVAAC;wCAAI,WAAU;;0DACb,mVAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,mVAAC;gDAAE,WAAU;0DAAoC,MAAM,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMrE,mVAAC,oJAAA,CAAA,OAAI;kCACH,cAAA,mVAAC,oJAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC,oRAAA,CAAA,MAAG;4CAAC,WAAU;;;;;;;;;;;kDAEjB,mVAAC;wCAAI,WAAU;;0DACb,mVAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,mVAAC;gDAAE,WAAU;0DAAoC,YAAY,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAM3E,mVAAC,oJAAA,CAAA,OAAI;kCACH,cAAA,mVAAC,oJAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC,wRAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;kDAEnB,mVAAC;wCAAI,WAAU;;0DACb,mVAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,mVAAC;gDAAE,WAAU;0DACV,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrD,mVAAC,oJAAA,CAAA,OAAI;kCACH,cAAA,mVAAC,oJAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC,8RAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;;;;;;kDAEtB,mVAAC;wCAAI,WAAU;;0DACb,mVAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,mVAAC;gDAAE,WAAU;0DACV,MAAM,MAAM,CAAC,CAAA,OAAQ,KAAK,YAAY,EAAE,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3D,mVAAC,oJAAA,CAAA,OAAI;gBAAC,OAAO;gBAAW,eAAe;gBAAc,WAAU;;kCAC7D,mVAAC,oJAAA,CAAA,WAAQ;wBAAC,WAAU;;0CAClB,mVAAC,oJAAA,CAAA,cAAW;gCAAC,OAAM;gCAAQ,WAAU;;kDACnC,mVAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAGhC,mVAAC,oJAAA,CAAA,cAAW;gCAAC,OAAM;gCAAc,WAAU;;kDACzC,mVAAC,oRAAA,CAAA,MAAG;wCAAC,WAAU;;;;;;oCAAY;;;;;;;0CAG7B,mVAAC,oJAAA,CAAA,cAAW;gCAAC,OAAM;gCAAmB,WAAU;;kDAC9C,mVAAC,wRAAA,CAAA,QAAK;wCAAC,WAAU;;;;;;oCAAY;;;;;;;;;;;;;kCAKjC,mVAAC,oJAAA,CAAA,cAAW;wBAAC,OAAM;wBAAQ,WAAU;kCACnC,cAAA,mVAAC,2LAAA,CAAA,UAAQ;4BACP,cAAc;4BACd,YAAY;4BACZ,qBAAqB;4BACrB,UAAU;4BACV,kBAAkB;;;;;;;;;;;kCAItB,mVAAC,oJAAA,CAAA,cAAW;wBAAC,OAAM;wBAAc,WAAU;kCACzC,cAAA,mVAAC,iMAAA,CAAA,UAAc;4BACb,oBAAoB;4BACpB,kBAAkB;4BAClB,UAAU;4BACV,kBAAkB;;;;;;;;;;;kCAItB,mVAAC,oJAAA,CAAA,cAAW;wBAAC,OAAM;wBAAmB,WAAU;kCAC9C,cAAA,mVAAC,oJAAA,CAAA,OAAI;;8CACH,mVAAC,oJAAA,CAAA,aAAU;;sDACT,mVAAC,oJAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,mVAAC,wRAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;gDAAY;;;;;;;sDAG/B,mVAAC,oJAAA,CAAA,kBAAe;sDAAC;;;;;;;;;;;;8CAInB,mVAAC,oJAAA,CAAA,cAAW;8CACV,cAAA,mVAAC;wCAAI,WAAU;;4CACZ,MAAM,GAAG,CAAC,CAAC,qBACV,mVAAC;oDAAkB,WAAU;;sEAC3B,mVAAC;4DAAI,WAAU;;8EACb,mVAAC;oEAAI,WAAU;;sFACb,mVAAC;4EAAI,WAAU;sFACb,cAAA,mVAAC,0RAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;;;;;;sFAEpB,mVAAC;;8FACC,mVAAC;oFAAG,WAAU;8FAA+B,KAAK,IAAI;;;;;;8FACtD,mVAAC;oFAAE,WAAU;8FAAyB,KAAK,WAAW;;;;;;;;;;;;;;;;;;8EAG1D,mVAAC;oEAAI,WAAU;;sFACb,mVAAC,qJAAA,CAAA,QAAK;4EAAC,SAAS,KAAK,QAAQ,GAAG,YAAY;sFACzC,KAAK,QAAQ,GAAG,WAAW;;;;;;sFAE9B,mVAAC;4EACC,SAAS,IAAM,wBAAwB;4EACvC,WAAU;sFACX;;;;;;;;;;;;;;;;;;sEAOL,mVAAC;4DAAI,WAAU;sEACZ,KAAK,WAAW,IAAI,KAAK,WAAW,CAAC,MAAM,GAAG,IAC7C,KAAK,WAAW,CAAC,GAAG,CAAC,CAAC,2BACpB,mVAAC,qJAAA,CAAA,QAAK;oEAAqB,SAAQ;oEAAU,WAAU;8EACpD,WAAW,IAAI;mEADN,WAAW,EAAE;;;;0FAK3B,mVAAC;gEAAK,WAAU;0EAA+B;;;;;;;;;;;;mDAjC3C,KAAK,EAAE;;;;;4CAuClB,MAAM,MAAM,KAAK,mBAChB,mVAAC;gDAAI,WAAU;;kEACb,mVAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,mVAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjD", "debugId": null}}]}