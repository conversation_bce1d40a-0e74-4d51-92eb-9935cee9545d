'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { useAuthStore } from '@/stores/auth/useAuthStore'
import { useRouter } from 'next/navigation'
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Shield, Key, Users, Settings } from 'lucide-react'
import { Role, Permission, useRolePermissionsStore } from '@/stores/super-admin/useRolePermissionsStore'

// Components
import RoleList from '@/components/super-admin/role-permissions/RoleList'
import PermissionList from '@/components/super-admin/role-permissions/PermissionList'
import RoleForm from '@/components/super-admin/role-permissions/RoleForm'
import PermissionForm from '@/components/super-admin/role-permissions/PermissionForm'
import RolePermissionAssignment from '@/components/super-admin/role-permissions/RolePermissionAssignment'

type ViewState = 'list' | 'create-role' | 'edit-role' | 'create-permission' | 'edit-permission' | 'assign-permissions'

export default function RolePermissionsPage() {
  const { user, isAuthenticated, isLoading } = useAuthStore()
  const router = useRouter()
  
  const {
    roles,
    permissions,
    viewMode,
    setViewMode,
    isLoading: storeLoading,
    fetchRoles,
    fetchPermissions,
    fetchRolesWithPermissions,
    createRole,
    updateRole,
    createPermission,
    updatePermission,
  } = useRolePermissionsStore()

  const [activeTab, setActiveTab] = useState('roles')
  const [viewState, setViewState] = useState<ViewState>('list')
  const [selectedRole, setSelectedRole] = useState<Role | null>(null)
  const [selectedPermission, setSelectedPermission] = useState<Permission | null>(null)

  // Authentication check
  useEffect(() => {
    if (!isLoading && (!isAuthenticated || !user || user.role !== 'super_admin')) {
      router.push('/auth/admin/login')
    }
  }, [user, isAuthenticated, isLoading, router])

  // Load data
  useEffect(() => {
    if (user && user.role === 'super_admin') {
      fetchRolesWithPermissions()
      fetchPermissions()
    }
  }, [user]) // Remove function dependencies to prevent infinite re-renders

  // Reset view state when changing tabs
  useEffect(() => {
    setViewState('list')
    setSelectedRole(null)
    setSelectedPermission(null)
  }, [activeTab])

  // Role handlers
  const handleCreateRole = () => {
    setSelectedRole(null)
    setViewState('create-role')
  }

  const handleEditRole = (role: Role) => {
    setSelectedRole(role)
    setViewState('edit-role')
  }

  const handleAssignPermissions = (role: Role) => {
    setSelectedRole(role)
    setViewState('assign-permissions')
  }

  const handleRoleSubmit = async (roleData: Partial<Role>) => {
    if (viewState === 'create-role') {
      return await createRole(roleData)
    } else if (viewState === 'edit-role' && selectedRole) {
      return await updateRole(selectedRole.id, roleData)
    }
    return false
  }

  // Permission handlers
  const handleCreatePermission = () => {
    setSelectedPermission(null)
    setViewState('create-permission')
  }

  const handleEditPermission = (permission: Permission) => {
    setSelectedPermission(permission)
    setViewState('edit-permission')
  }

  const handlePermissionSubmit = async (permissionData: Partial<Permission>) => {
    if (viewState === 'create-permission') {
      return await createPermission(permissionData)
    } else if (viewState === 'edit-permission' && selectedPermission) {
      return await updatePermission(selectedPermission.id, permissionData)
    }
    return false
  }

  const handleCancel = () => {
    setViewState('list')
    setSelectedRole(null)
    setSelectedPermission(null)
  }

  // Show loading state
  if (isLoading || !user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  // Show unauthorized if not super admin
  if (user.role !== 'super_admin') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Access Denied</h1>
          <p className="text-gray-600">You don't have permission to access this page.</p>
        </div>
      </div>
    )
  }

  // Render form views
  if (viewState === 'create-role' || viewState === 'edit-role') {
    return (
      <div className="container mx-auto px-4 py-8">
        <RoleForm
          role={selectedRole}
          onSubmit={handleRoleSubmit}
          onCancel={handleCancel}
          isLoading={storeLoading}
        />
      </div>
    )
  }

  if (viewState === 'create-permission' || viewState === 'edit-permission') {
    return (
      <div className="container mx-auto px-4 py-8">
        <PermissionForm
          permission={selectedPermission}
          onSubmit={handlePermissionSubmit}
          onCancel={handleCancel}
          isLoading={storeLoading}
        />
      </div>
    )
  }

  if (viewState === 'assign-permissions' && selectedRole) {
    return (
      <div className="container mx-auto px-4 py-8">
        <RolePermissionAssignment
          role={selectedRole}
          onCancel={handleCancel}
        />
      </div>
    )
  }

  // Main dashboard view
  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Role & Permission Management</h1>
        <p className="text-gray-600">
          Manage user roles and permissions for the LMS platform. Control access to features and functionality.
        </p>
      </div>

      {/* Statistics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Shield className="h-6 w-6 text-blue-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Roles</p>
                <p className="text-2xl font-bold text-gray-900">{roles.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-green-100 rounded-lg">
                <Key className="h-6 w-6 text-green-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Total Permissions</p>
                <p className="text-2xl font-bold text-gray-900">{permissions.length}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-yellow-100 rounded-lg">
                <Users className="h-6 w-6 text-yellow-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">Active Roles</p>
                <p className="text-2xl font-bold text-gray-900">
                  {roles.filter(role => role.isActive).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Settings className="h-6 w-6 text-purple-600" />
              </div>
              <div className="ml-4">
                <p className="text-sm font-medium text-gray-600">System Roles</p>
                <p className="text-2xl font-bold text-gray-900">
                  {roles.filter(role => role.isSystemRole).length}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="roles" className="flex items-center gap-2">
            <Shield className="h-4 w-4" />
            Roles
          </TabsTrigger>
          <TabsTrigger value="permissions" className="flex items-center gap-2">
            <Key className="h-4 w-4" />
            Permissions
          </TabsTrigger>
        </TabsList>

        <TabsContent value="roles" className="space-y-6">
          <RoleList
            onCreateRole={handleCreateRole}
            onEditRole={handleEditRole}
            onAssignPermissions={handleAssignPermissions}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />
        </TabsContent>

        <TabsContent value="permissions" className="space-y-6">
          <PermissionList
            onCreatePermission={handleCreatePermission}
            onEditPermission={handleEditPermission}
            viewMode={viewMode}
            onViewModeChange={setViewMode}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}
