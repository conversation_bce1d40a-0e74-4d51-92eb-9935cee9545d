import type { CollectionConfig } from 'payload'

export const Users: CollectionConfig = {
  slug: 'users',
  admin: {
    useAsTitle: 'email',
  },
  auth: true,
  fields: [
    // Email and password added by default with auth: true
    {
      name: 'role',
      type: 'relationship',
      relationTo: 'roles',
      required: true,
      admin: {
        position: 'sidebar',
      },
    },
    // Keep legacy role field for backward compatibility during migration
    {
      name: 'legacyRole',
      type: 'select',
      options: [
        {
          label: 'Super Admin',
          value: 'super_admin',
        },
        {
          label: 'Platform Staff',
          value: 'platform_staff',
        },
        {
          label: 'Institute Admin',
          value: 'institute_admin',
        },
        {
          label: 'Branch Manager',
          value: 'branch_manager',
        },
        {
          label: 'Trainer',
          value: 'trainer',
        },
        {
          label: 'Institute Staff',
          value: 'institute_staff',
        },
        {
          label: 'Student',
          value: 'student',
        },
      ],
      admin: {
        position: 'sidebar',
        description: 'Legacy role field - will be migrated to role relationship',
      },
    },
    {
      name: 'firstName',
      type: 'text',
      required: true,
    },
    {
      name: 'lastName',
      type: 'text',
      required: true,
    },
    {
      name: 'phone',
      type: 'text',
      validate: (val) => {
        if (val && !/^[+]?[\d\s\-\(\)]+$/.test(val)) {
          return 'Please enter a valid phone number'
        }
        return true
      },
    },
    {
      name: 'avatar',
      type: 'upload',
      relationTo: 'media',
    },
    {
      name: 'institute',
      type: 'relationship',
      relationTo: 'institutes',
      admin: {
        condition: (data) => {
          return ['institute_admin', 'branch_manager', 'trainer', 'institute_staff', 'student'].includes(data.role)
        },
      },
    },
    {
      name: 'branch',
      type: 'text',
      admin: {
        condition: (data) => {
          return ['branch_manager', 'trainer', 'institute_staff', 'student'].includes(data.role)
        },
      },
    },
    {
      name: 'isActive',
      type: 'checkbox',
      defaultValue: true,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'emailVerified',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
      },
    },
    {
      name: 'lastLogin',
      type: 'date',
      admin: {
        position: 'sidebar',
        readOnly: true,
      },
    },
    {
      name: 'loginAttempts',
      type: 'number',
      defaultValue: 0,
      admin: {
        position: 'sidebar',
        readOnly: true,
      },
    },
    {
      name: 'lockedUntil',
      type: 'date',
      admin: {
        position: 'sidebar',
        readOnly: true,
      },
    },
  ],
  access: {
    // Only super admins can create/read/update/delete users
    create: ({ req: { user } }) => {
      return user?.role === 'super_admin'
    },
    read: ({ req: { user } }) => {
      if (user?.role === 'super_admin') return true
      if (user?.role === 'institute_admin') {
        return {
          institute: {
            equals: user.institute,
          },
        }
      }
      // Users can only read their own data
      return {
        id: {
          equals: user?.id,
        },
      }
    },
    update: ({ req: { user } }) => {
      if (user?.role === 'super_admin') return true
      if (user?.role === 'institute_admin') {
        return {
          institute: {
            equals: user.institute,
          },
        }
      }
      // Users can only update their own data
      return {
        id: {
          equals: user?.id,
        },
      }
    },
    delete: ({ req: { user } }) => {
      return user?.role === 'super_admin'
    },
  },
}
