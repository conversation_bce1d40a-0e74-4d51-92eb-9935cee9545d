"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+dragon@0.28.0";
exports.ids = ["vendor-chunks/@lexical+dragon@0.28.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+dragon@0.28.0/node_modules/@lexical/dragon/LexicalDragon.dev.mjs":
/*!**********************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+dragon@0.28.0/node_modules/@lexical/dragon/LexicalDragon.dev.mjs ***!
  \**********************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   registerDragonSupport: () => (/* binding */ registerDragonSupport)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction registerDragonSupport(editor) {\n  const origin = window.location.origin;\n  const handler = event => {\n    if (event.origin !== origin) {\n      return;\n    }\n    const rootElement = editor.getRootElement();\n    if (document.activeElement !== rootElement) {\n      return;\n    }\n    const data = event.data;\n    if (typeof data === 'string') {\n      let parsedData;\n      try {\n        parsedData = JSON.parse(data);\n      } catch (e) {\n        return;\n      }\n      if (parsedData && parsedData.protocol === 'nuanria_messaging' && parsedData.type === 'request') {\n        const payload = parsedData.payload;\n        if (payload && payload.functionId === 'makeChanges') {\n          const args = payload.args;\n          if (args) {\n            const [elementStart, elementLength, text, selStart, selLength, formatCommand] = args;\n            editor.update(() => {\n              const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n              if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n                const anchor = selection.anchor;\n                let anchorNode = anchor.getNode();\n                let setSelStart = 0;\n                let setSelEnd = 0;\n                if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(anchorNode)) {\n                  // set initial selection\n                  if (elementStart >= 0 && elementLength >= 0) {\n                    setSelStart = elementStart;\n                    setSelEnd = elementStart + elementLength;\n                    // If the offset is more than the end, make it the end\n                    selection.setTextNodeRange(anchorNode, setSelStart, anchorNode, setSelEnd);\n                  }\n                }\n                if (setSelStart !== setSelEnd || text !== '') {\n                  selection.insertRawText(text);\n                  anchorNode = anchor.getNode();\n                }\n                if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(anchorNode)) {\n                  // set final selection\n                  setSelStart = selStart;\n                  setSelEnd = selStart + selLength;\n                  const anchorNodeTextLength = anchorNode.getTextContentSize();\n                  // If the offset is more than the end, make it the end\n                  setSelStart = setSelStart > anchorNodeTextLength ? anchorNodeTextLength : setSelStart;\n                  setSelEnd = setSelEnd > anchorNodeTextLength ? anchorNodeTextLength : setSelEnd;\n                  selection.setTextNodeRange(anchorNode, setSelStart, anchorNode, setSelEnd);\n                }\n\n                // block the chrome extension from handling this event\n                event.stopImmediatePropagation();\n              }\n            });\n          }\n        }\n      }\n    }\n  };\n  window.addEventListener('message', handler, true);\n  return () => {\n    window.removeEventListener('message', handler, true);\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+dragon@0.28.0/node_modules/@lexical/dragon/LexicalDragon.dev.mjs\n");

/***/ })

};
;