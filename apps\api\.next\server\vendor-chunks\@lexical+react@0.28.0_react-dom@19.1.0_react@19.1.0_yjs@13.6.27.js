"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27";
exports.ids = ["vendor-chunks/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalDecoratorBlockNode.dev.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalDecoratorBlockNode.dev.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $isDecoratorBlockNode: () => (/* binding */ $isDecoratorBlockNode),\n/* harmony export */   DecoratorBlockNode: () => (/* binding */ DecoratorBlockNode)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nclass DecoratorBlockNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.DecoratorNode {\n  constructor(format, key) {\n    super(key);\n    this.__format = format || '';\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      format: this.__format || ''\n    };\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setFormat(serializedNode.format || '');\n  }\n  canIndent() {\n    return false;\n  }\n  createDOM() {\n    return document.createElement('div');\n  }\n  updateDOM() {\n    return false;\n  }\n  setFormat(format) {\n    const self = this.getWritable();\n    self.__format = format;\n    return self;\n  }\n  isInline() {\n    return false;\n  }\n}\nfunction $isDecoratorBlockNode(node) {\n  return node instanceof DecoratorBlockNode;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalDecoratorBlockNode.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CheckListPlugin: () => (/* binding */ CheckListPlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/list */ \"(ssr)/../../node_modules/.pnpm/@lexical+list@0.28.0/node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/utils */ \"(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction CheckListPlugin() {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__.useLexicalComposerContext)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_2__.mergeRegister)(editor.registerCommand(_lexical_list__WEBPACK_IMPORTED_MODULE_3__.INSERT_CHECK_LIST_COMMAND, () => {\n      (0,_lexical_list__WEBPACK_IMPORTED_MODULE_3__.insertList)(editor, 'check');\n      return true;\n    }, lexical__WEBPACK_IMPORTED_MODULE_4__.COMMAND_PRIORITY_LOW), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_4__.KEY_ARROW_DOWN_COMMAND, event => {\n      return handleArrownUpOrDown(event, editor, false);\n    }, lexical__WEBPACK_IMPORTED_MODULE_4__.COMMAND_PRIORITY_LOW), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_4__.KEY_ARROW_UP_COMMAND, event => {\n      return handleArrownUpOrDown(event, editor, true);\n    }, lexical__WEBPACK_IMPORTED_MODULE_4__.COMMAND_PRIORITY_LOW), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_4__.KEY_ESCAPE_COMMAND, event => {\n      const activeItem = getActiveCheckListItem();\n      if (activeItem != null) {\n        const rootElement = editor.getRootElement();\n        if (rootElement != null) {\n          rootElement.focus();\n        }\n        return true;\n      }\n      return false;\n    }, lexical__WEBPACK_IMPORTED_MODULE_4__.COMMAND_PRIORITY_LOW), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_4__.KEY_SPACE_COMMAND, event => {\n      const activeItem = getActiveCheckListItem();\n      if (activeItem != null && editor.isEditable()) {\n        editor.update(() => {\n          const listItemNode = (0,lexical__WEBPACK_IMPORTED_MODULE_4__.$getNearestNodeFromDOMNode)(activeItem);\n          if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_3__.$isListItemNode)(listItemNode)) {\n            event.preventDefault();\n            listItemNode.toggleChecked();\n          }\n        });\n        return true;\n      }\n      return false;\n    }, lexical__WEBPACK_IMPORTED_MODULE_4__.COMMAND_PRIORITY_LOW), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_4__.KEY_ARROW_LEFT_COMMAND, event => {\n      return editor.getEditorState().read(() => {\n        const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_4__.$getSelection)();\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_4__.$isRangeSelection)(selection) && selection.isCollapsed()) {\n          const {\n            anchor\n          } = selection;\n          const isElement = anchor.type === 'element';\n          if (isElement || anchor.offset === 0) {\n            const anchorNode = anchor.getNode();\n            const elementNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_2__.$findMatchingParent)(anchorNode, node => (0,lexical__WEBPACK_IMPORTED_MODULE_4__.$isElementNode)(node) && !node.isInline());\n            if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_3__.$isListItemNode)(elementNode)) {\n              const parent = elementNode.getParent();\n              if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_3__.$isListNode)(parent) && parent.getListType() === 'check' && (isElement || elementNode.getFirstDescendant() === anchorNode)) {\n                const domNode = editor.getElementByKey(elementNode.__key);\n                if (domNode != null && document.activeElement !== domNode) {\n                  domNode.focus();\n                  event.preventDefault();\n                  return true;\n                }\n              }\n            }\n          }\n        }\n        return false;\n      });\n    }, lexical__WEBPACK_IMPORTED_MODULE_4__.COMMAND_PRIORITY_LOW), editor.registerRootListener((rootElement, prevElement) => {\n      if (rootElement !== null) {\n        rootElement.addEventListener('click', handleClick);\n        rootElement.addEventListener('pointerdown', handlePointerDown);\n      }\n      if (prevElement !== null) {\n        prevElement.removeEventListener('click', handleClick);\n        prevElement.removeEventListener('pointerdown', handlePointerDown);\n      }\n    }));\n  });\n  return null;\n}\nfunction handleCheckItemEvent(event, callback) {\n  const target = event.target;\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(target)) {\n    return;\n  }\n\n  // Ignore clicks on LI that have nested lists\n  const firstChild = target.firstChild;\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(firstChild) && (firstChild.tagName === 'UL' || firstChild.tagName === 'OL')) {\n    return;\n  }\n  const parentNode = target.parentNode;\n\n  // @ts-ignore internal field\n  if (!parentNode || parentNode.__lexicalListType !== 'check') {\n    return;\n  }\n  const rect = target.getBoundingClientRect();\n  const pageX = event.pageX / (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_2__.calculateZoomLevel)(target);\n  if (target.dir === 'rtl' ? pageX < rect.right && pageX > rect.right - 20 : pageX > rect.left && pageX < rect.left + 20) {\n    callback();\n  }\n}\nfunction handleClick(event) {\n  handleCheckItemEvent(event, () => {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(event.target)) {\n      const domNode = event.target;\n      const editor = (0,lexical__WEBPACK_IMPORTED_MODULE_4__.getNearestEditorFromDOMNode)(domNode);\n      if (editor != null && editor.isEditable()) {\n        editor.update(() => {\n          const node = (0,lexical__WEBPACK_IMPORTED_MODULE_4__.$getNearestNodeFromDOMNode)(domNode);\n          if ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_3__.$isListItemNode)(node)) {\n            domNode.focus();\n            node.toggleChecked();\n          }\n        });\n      }\n    }\n  });\n}\nfunction handlePointerDown(event) {\n  handleCheckItemEvent(event, () => {\n    // Prevents caret moving when clicking on check mark\n    event.preventDefault();\n  });\n}\nfunction getActiveCheckListItem() {\n  const activeElement = document.activeElement;\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_4__.isHTMLElement)(activeElement) && activeElement.tagName === 'LI' && activeElement.parentNode != null &&\n  // @ts-ignore internal field\n  activeElement.parentNode.__lexicalListType === 'check' ? activeElement : null;\n}\nfunction findCheckListItemSibling(node, backward) {\n  let sibling = backward ? node.getPreviousSibling() : node.getNextSibling();\n  let parent = node;\n\n  // Going up in a tree to get non-null sibling\n  while (sibling == null && (0,_lexical_list__WEBPACK_IMPORTED_MODULE_3__.$isListItemNode)(parent)) {\n    // Get li -> parent ul/ol -> parent li\n    parent = parent.getParentOrThrow().getParent();\n    if (parent != null) {\n      sibling = backward ? parent.getPreviousSibling() : parent.getNextSibling();\n    }\n  }\n\n  // Going down in a tree to get first non-nested list item\n  while ((0,_lexical_list__WEBPACK_IMPORTED_MODULE_3__.$isListItemNode)(sibling)) {\n    const firstChild = backward ? sibling.getLastChild() : sibling.getFirstChild();\n    if (!(0,_lexical_list__WEBPACK_IMPORTED_MODULE_3__.$isListNode)(firstChild)) {\n      return sibling;\n    }\n    sibling = backward ? firstChild.getLastChild() : firstChild.getFirstChild();\n  }\n  return null;\n}\nfunction handleArrownUpOrDown(event, editor, backward) {\n  const activeItem = getActiveCheckListItem();\n  if (activeItem != null) {\n    editor.update(() => {\n      const listItem = (0,lexical__WEBPACK_IMPORTED_MODULE_4__.$getNearestNodeFromDOMNode)(activeItem);\n      if (!(0,_lexical_list__WEBPACK_IMPORTED_MODULE_3__.$isListItemNode)(listItem)) {\n        return;\n      }\n      const nextListItem = findCheckListItemSibling(listItem, backward);\n      if (nextListItem != null) {\n        nextListItem.selectStart();\n        const dom = editor.getElementByKey(nextListItem.__key);\n        if (dom != null) {\n          event.preventDefault();\n          setTimeout(() => {\n            dom.focus();\n          }, 0);\n        }\n      }\n    });\n  }\n  return false;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalCheckListPlugin.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalClickableLinkPlugin.dev.mjs":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalClickableLinkPlugin.dev.mjs ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ClickableLinkPlugin: () => (/* binding */ ClickableLinkPlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/link */ \"(ssr)/../../node_modules/.pnpm/@lexical+link@0.28.0/node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/utils */ \"(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction findMatchingDOM(startNode, predicate) {\n  let node = startNode;\n  while (node != null) {\n    if (predicate(node)) {\n      return node;\n    }\n    node = node.parentNode;\n  }\n  return null;\n}\nfunction ClickableLinkPlugin({\n  newTab = true,\n  disabled = false\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__.useLexicalComposerContext)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const onClick = event => {\n      const target = event.target;\n      if (!(0,lexical__WEBPACK_IMPORTED_MODULE_2__.isDOMNode)(target)) {\n        return;\n      }\n      const nearestEditor = (0,lexical__WEBPACK_IMPORTED_MODULE_2__.getNearestEditorFromDOMNode)(target);\n      if (nearestEditor === null) {\n        return;\n      }\n      let url = null;\n      let urlTarget = null;\n      nearestEditor.update(() => {\n        const clickedNode = (0,lexical__WEBPACK_IMPORTED_MODULE_2__.$getNearestNodeFromDOMNode)(target);\n        if (clickedNode !== null) {\n          const maybeLinkNode = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_3__.$findMatchingParent)(clickedNode, lexical__WEBPACK_IMPORTED_MODULE_2__.$isElementNode);\n          if (!disabled) {\n            if ((0,_lexical_link__WEBPACK_IMPORTED_MODULE_4__.$isLinkNode)(maybeLinkNode)) {\n              url = maybeLinkNode.sanitizeUrl(maybeLinkNode.getURL());\n              urlTarget = maybeLinkNode.getTarget();\n            } else {\n              const a = findMatchingDOM(target, lexical__WEBPACK_IMPORTED_MODULE_2__.isHTMLAnchorElement);\n              if (a !== null) {\n                url = a.href;\n                urlTarget = a.target;\n              }\n            }\n          }\n        }\n      });\n      if (url === null || url === '') {\n        return;\n      }\n\n      // Allow user to select link text without follwing url\n      const selection = editor.getEditorState().read(lexical__WEBPACK_IMPORTED_MODULE_2__.$getSelection);\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_2__.$isRangeSelection)(selection) && !selection.isCollapsed()) {\n        event.preventDefault();\n        return;\n      }\n      const isMiddle = event.type === 'auxclick' && event.button === 1;\n      window.open(url, newTab || isMiddle || event.metaKey || event.ctrlKey || urlTarget === '_blank' ? '_blank' : '_self');\n      event.preventDefault();\n    };\n    const onMouseUp = event => {\n      if (event.button === 1) {\n        onClick(event);\n      }\n    };\n    return editor.registerRootListener((rootElement, prevRootElement) => {\n      if (prevRootElement !== null) {\n        prevRootElement.removeEventListener('click', onClick);\n        prevRootElement.removeEventListener('mouseup', onMouseUp);\n      }\n      if (rootElement !== null) {\n        rootElement.addEventListener('click', onClick);\n        rootElement.addEventListener('mouseup', onMouseUp);\n      }\n    });\n  }, [editor, newTab, disabled]);\n  return null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalClickableLinkPlugin.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposer.dev.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposer.dev.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LexicalComposer: () => (/* binding */ LexicalComposer)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst HISTORY_MERGE_OPTIONS = {\n  tag: 'history-merge'\n};\nfunction LexicalComposer({\n  initialConfig,\n  children\n}) {\n  const composerContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const {\n      theme,\n      namespace,\n      nodes,\n      onError,\n      editorState: initialEditorState,\n      html\n    } = initialConfig;\n    const context = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__.createLexicalComposerContext)(null, theme);\n    const editor = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.createEditor)({\n      editable: initialConfig.editable,\n      html,\n      namespace,\n      nodes,\n      onError: error => onError(error, editor),\n      theme\n    });\n    initializeEditor(editor, initialEditorState);\n    return [editor, context];\n  },\n  // We only do this for init\n  // eslint-disable-next-line react-hooks/exhaustive-deps\n  []);\n  useLayoutEffectImpl(() => {\n    const isEditable = initialConfig.editable;\n    const [editor] = composerContext;\n    editor.setEditable(isEditable !== undefined ? isEditable : true);\n\n    // We only do this for init\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, []);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__.LexicalComposerContext.Provider, {\n    value: composerContext,\n    children: children\n  });\n}\nfunction initializeEditor(editor, initialEditorState) {\n  if (initialEditorState === null) {\n    return;\n  } else if (initialEditorState === undefined) {\n    editor.update(() => {\n      const root = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getRoot)();\n      if (root.isEmpty()) {\n        const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$createParagraphNode)();\n        root.append(paragraph);\n        const activeElement = CAN_USE_DOM ? document.activeElement : null;\n        if ((0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getSelection)() !== null || activeElement !== null && activeElement === editor.getRootElement()) {\n          paragraph.select();\n        }\n      }\n    }, HISTORY_MERGE_OPTIONS);\n  } else if (initialEditorState !== null) {\n    switch (typeof initialEditorState) {\n      case 'string':\n        {\n          const parsedEditorState = editor.parseEditorState(initialEditorState);\n          editor.setEditorState(parsedEditorState, HISTORY_MERGE_OPTIONS);\n          break;\n        }\n      case 'object':\n        {\n          editor.setEditorState(initialEditorState, HISTORY_MERGE_OPTIONS);\n          break;\n        }\n      case 'function':\n        {\n          editor.update(() => {\n            const root = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getRoot)();\n            if (root.isEmpty()) {\n              initialEditorState(editor);\n            }\n          }, HISTORY_MERGE_OPTIONS);\n          break;\n        }\n    }\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposer.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LexicalComposerContext: () => (/* binding */ LexicalComposerContext),\n/* harmony export */   createLexicalComposerContext: () => (/* binding */ createLexicalComposerContext),\n/* harmony export */   useLexicalComposerContext: () => (/* binding */ useLexicalComposerContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n// Do not require this module directly! Use normal `invariant` calls.\n\nfunction formatDevErrorMessage(message) {\n  throw new Error(message);\n}\n\nconst LexicalComposerContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nfunction createLexicalComposerContext(parent, theme) {\n  let parentContext = null;\n  if (parent != null) {\n    parentContext = parent[1];\n  }\n  function getTheme() {\n    if (theme != null) {\n      return theme;\n    }\n    return parentContext != null ? parentContext.getTheme() : null;\n  }\n  return {\n    getTheme\n  };\n}\nfunction useLexicalComposerContext() {\n  const composerContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(LexicalComposerContext);\n  if (composerContext == null) {\n    {\n      formatDevErrorMessage(`LexicalComposerContext.useLexicalComposerContext: cannot find a LexicalComposerContext`);\n    }\n  }\n  return composerContext;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalContentEditable.dev.mjs":
/*!***********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalContentEditable.dev.mjs ***!
  \***********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ContentEditable: () => (/* binding */ ContentEditable)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _lexical_text__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/text */ \"(ssr)/../../node_modules/.pnpm/@lexical+text@0.28.0/node_modules/@lexical/text/LexicalText.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/utils */ \"(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n// Source: https://github.com/gregberge/react-merge-refs/blob/main/src/index.tsx\n\nfunction mergeRefs(...refs) {\n  return value => {\n    refs.forEach(ref => {\n      if (typeof ref === 'function') {\n        ref(value);\n      } else if (ref != null) {\n        ref.current = value;\n      }\n    });\n  };\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction ContentEditableElementImpl({\n  editor,\n  ariaActiveDescendant,\n  ariaAutoComplete,\n  ariaControls,\n  ariaDescribedBy,\n  ariaErrorMessage,\n  ariaExpanded,\n  ariaInvalid,\n  ariaLabel,\n  ariaLabelledBy,\n  ariaMultiline,\n  ariaOwns,\n  ariaRequired,\n  autoCapitalize,\n  className,\n  id,\n  role = 'textbox',\n  spellCheck = true,\n  style,\n  tabIndex,\n  'data-testid': testid,\n  ...rest\n}, ref) {\n  const [isEditable, setEditable] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(editor.isEditable());\n  const handleRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(rootElement => {\n    // defaultView is required for a root element.\n    // In multi-window setups, the defaultView may not exist at certain points.\n    if (rootElement && rootElement.ownerDocument && rootElement.ownerDocument.defaultView) {\n      editor.setRootElement(rootElement);\n    } else {\n      editor.setRootElement(null);\n    }\n  }, [editor]);\n  const mergedRefs = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => mergeRefs(ref, handleRef), [handleRef, ref]);\n  useLayoutEffectImpl(() => {\n    setEditable(editor.isEditable());\n    return editor.registerEditableListener(currentIsEditable => {\n      setEditable(currentIsEditable);\n    });\n  }, [editor]);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n    \"aria-activedescendant\": isEditable ? ariaActiveDescendant : undefined,\n    \"aria-autocomplete\": isEditable ? ariaAutoComplete : 'none',\n    \"aria-controls\": isEditable ? ariaControls : undefined,\n    \"aria-describedby\": ariaDescribedBy\n    // for compat, only override aria-errormessage if ariaErrorMessage is defined\n    ,\n    ...(ariaErrorMessage != null ? {\n      'aria-errormessage': ariaErrorMessage\n    } : {}),\n    \"aria-expanded\": isEditable && role === 'combobox' ? !!ariaExpanded : undefined\n    // for compat, only override aria-invalid if ariaInvalid is defined\n    ,\n    ...(ariaInvalid != null ? {\n      'aria-invalid': ariaInvalid\n    } : {}),\n    \"aria-label\": ariaLabel,\n    \"aria-labelledby\": ariaLabelledBy,\n    \"aria-multiline\": ariaMultiline,\n    \"aria-owns\": isEditable ? ariaOwns : undefined,\n    \"aria-readonly\": isEditable ? undefined : true,\n    \"aria-required\": ariaRequired,\n    autoCapitalize: autoCapitalize,\n    className: className,\n    contentEditable: isEditable,\n    \"data-testid\": testid,\n    id: id,\n    ref: mergedRefs,\n    role: isEditable ? role : undefined,\n    spellCheck: spellCheck,\n    style: style,\n    tabIndex: tabIndex,\n    ...rest\n  });\n}\nconst ContentEditableElement = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(ContentEditableElementImpl);\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction canShowPlaceholderFromCurrentEditorState(editor) {\n  const currentCanShowPlaceholder = editor.getEditorState().read((0,_lexical_text__WEBPACK_IMPORTED_MODULE_2__.$canShowPlaceholderCurry)(editor.isComposing()));\n  return currentCanShowPlaceholder;\n}\nfunction useCanShowPlaceholder(editor) {\n  const [canShowPlaceholder, setCanShowPlaceholder] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => canShowPlaceholderFromCurrentEditorState(editor));\n  useLayoutEffectImpl(() => {\n    function resetCanShowPlaceholder() {\n      const currentCanShowPlaceholder = canShowPlaceholderFromCurrentEditorState(editor);\n      setCanShowPlaceholder(currentCanShowPlaceholder);\n    }\n    resetCanShowPlaceholder();\n    return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_3__.mergeRegister)(editor.registerUpdateListener(() => {\n      resetCanShowPlaceholder();\n    }), editor.registerEditableListener(() => {\n      resetCanShowPlaceholder();\n    }));\n  }, [editor]);\n  return canShowPlaceholder;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst ContentEditable = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(ContentEditableImpl);\nfunction ContentEditableImpl(props, ref) {\n  const {\n    placeholder,\n    ...rest\n  } = props;\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_4__.useLexicalComposerContext)();\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.Fragment, {\n    children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ContentEditableElement, {\n      editor: editor,\n      ...rest,\n      ref: ref\n    }), placeholder != null && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(Placeholder, {\n      editor: editor,\n      content: placeholder\n    })]\n  });\n}\nfunction Placeholder({\n  content,\n  editor\n}) {\n  const showPlaceholder = useCanShowPlaceholder(editor);\n  const [isEditable, setEditable] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(editor.isEditable());\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect)(() => {\n    setEditable(editor.isEditable());\n    return editor.registerEditableListener(currentIsEditable => {\n      setEditable(currentIsEditable);\n    });\n  }, [editor]);\n  if (!showPlaceholder) {\n    return null;\n  }\n  let placeholder = null;\n  if (typeof content === 'function') {\n    placeholder = content(isEditable);\n  } else if (content !== null) {\n    placeholder = content;\n  }\n  if (placeholder === null) {\n    return null;\n  }\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n    \"aria-hidden\": true,\n    children: placeholder\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalContentEditable.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalDecoratorBlockNode.dev.mjs":
/*!**************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalDecoratorBlockNode.dev.mjs ***!
  \**************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $isDecoratorBlockNode: () => (/* binding */ $isDecoratorBlockNode),\n/* harmony export */   DecoratorBlockNode: () => (/* binding */ DecoratorBlockNode)\n/* harmony export */ });\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nclass DecoratorBlockNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.DecoratorNode {\n  constructor(format, key) {\n    super(key);\n    this.__format = format || '';\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      format: this.__format || ''\n    };\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setFormat(serializedNode.format || '');\n  }\n  canIndent() {\n    return false;\n  }\n  createDOM() {\n    return document.createElement('div');\n  }\n  updateDOM() {\n    return false;\n  }\n  setFormat(format) {\n    const self = this.getWritable();\n    self.__format = format;\n    return self;\n  }\n  isInline() {\n    return false;\n  }\n}\nfunction $isDecoratorBlockNode(node) {\n  return node instanceof DecoratorBlockNode;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalDecoratorBlockNode.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   LexicalErrorBoundary: () => (/* binding */ LexicalErrorBoundary)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\n\nfunction _inheritsLoose(subClass, superClass) {\n  subClass.prototype = Object.create(superClass.prototype);\n  subClass.prototype.constructor = subClass;\n  _setPrototypeOf(subClass, superClass);\n}\n\nvar changedArray = function changedArray(a, b) {\n  if (a === void 0) {\n    a = [];\n  }\n\n  if (b === void 0) {\n    b = [];\n  }\n\n  return a.length !== b.length || a.some(function (item, index) {\n    return !Object.is(item, b[index]);\n  });\n};\n\nvar initialState = {\n  error: null\n};\n\nvar ErrorBoundary = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ErrorBoundary, _React$Component);\n\n  function ErrorBoundary() {\n    var _this;\n\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(_args)) || this;\n    _this.state = initialState;\n\n    _this.resetErrorBoundary = function () {\n      var _this$props;\n\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      _this.props.onReset == null ? void 0 : (_this$props = _this.props).onReset.apply(_this$props, args);\n\n      _this.reset();\n    };\n\n    return _this;\n  }\n\n  ErrorBoundary.getDerivedStateFromError = function getDerivedStateFromError(error) {\n    return {\n      error: error\n    };\n  };\n\n  var _proto = ErrorBoundary.prototype;\n\n  _proto.reset = function reset() {\n    this.setState(initialState);\n  };\n\n  _proto.componentDidCatch = function componentDidCatch(error, info) {\n    var _this$props$onError, _this$props2;\n\n    (_this$props$onError = (_this$props2 = this.props).onError) == null ? void 0 : _this$props$onError.call(_this$props2, error, info);\n  };\n\n  _proto.componentDidUpdate = function componentDidUpdate(prevProps, prevState) {\n    var error = this.state.error;\n    var resetKeys = this.props.resetKeys; // There's an edge case where if the thing that triggered the error\n    // happens to *also* be in the resetKeys array, we'd end up resetting\n    // the error boundary immediately. This would likely trigger a second\n    // error to be thrown.\n    // So we make sure that we don't check the resetKeys on the first call\n    // of cDU after the error is set\n\n    if (error !== null && prevState.error !== null && changedArray(prevProps.resetKeys, resetKeys)) {\n      var _this$props$onResetKe, _this$props3;\n\n      (_this$props$onResetKe = (_this$props3 = this.props).onResetKeysChange) == null ? void 0 : _this$props$onResetKe.call(_this$props3, prevProps.resetKeys, resetKeys);\n      this.reset();\n    }\n  };\n\n  _proto.render = function render() {\n    var error = this.state.error;\n    var _this$props4 = this.props,\n        fallbackRender = _this$props4.fallbackRender,\n        FallbackComponent = _this$props4.FallbackComponent,\n        fallback = _this$props4.fallback;\n\n    if (error !== null) {\n      var _props = {\n        error: error,\n        resetErrorBoundary: this.resetErrorBoundary\n      };\n\n      if ( /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.isValidElement(fallback)) {\n        return fallback;\n      } else if (typeof fallbackRender === 'function') {\n        return fallbackRender(_props);\n      } else if (FallbackComponent) {\n        return /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createElement(FallbackComponent, _props);\n      } else {\n        throw new Error('react-error-boundary requires either a fallback, fallbackRender, or FallbackComponent prop');\n      }\n    }\n\n    return this.props.children;\n  };\n\n  return ErrorBoundary;\n}(react__WEBPACK_IMPORTED_MODULE_0__.Component);\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction LexicalErrorBoundary({\n  children,\n  onError\n}) {\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(ErrorBoundary, {\n    fallback: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"div\", {\n      style: {\n        border: '1px solid #f00',\n        color: '#f00',\n        padding: '8px'\n      },\n      children: \"An error was thrown.\"\n    }),\n    onError: onError,\n    children: children\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalErrorBoundary.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs":
/*!*********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs ***!
  \*********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   HistoryPlugin: () => (/* binding */ HistoryPlugin),\n/* harmony export */   createEmptyHistoryState: () => (/* reexport safe */ _lexical_history__WEBPACK_IMPORTED_MODULE_0__.createEmptyHistoryState)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_history__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @lexical/history */ \"(ssr)/../../node_modules/.pnpm/@lexical+history@0.28.0/node_modules/@lexical/history/LexicalHistory.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useHistory(editor, externalHistoryState, delay = 1000) {\n  const historyState = (0,react__WEBPACK_IMPORTED_MODULE_1__.useMemo)(() => externalHistoryState || (0,_lexical_history__WEBPACK_IMPORTED_MODULE_0__.createEmptyHistoryState)(), [externalHistoryState]);\n  (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(() => {\n    return (0,_lexical_history__WEBPACK_IMPORTED_MODULE_0__.registerHistory)(editor, historyState, delay);\n  }, [delay, editor, historyState]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction HistoryPlugin({\n  delay,\n  externalHistoryState\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__.useLexicalComposerContext)();\n  useHistory(editor, externalHistoryState, delay);\n  return null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalHistoryPlugin.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalListPlugin.dev.mjs":
/*!******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalListPlugin.dev.mjs ***!
  \******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ListPlugin: () => (/* binding */ ListPlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_list__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/list */ \"(ssr)/../../node_modules/.pnpm/@lexical+list@0.28.0/node_modules/@lexical/list/LexicalList.dev.mjs\");\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useList(editor) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return (0,_lexical_list__WEBPACK_IMPORTED_MODULE_1__.registerList)(editor);\n  }, [editor]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction ListPlugin() {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_2__.useLexicalComposerContext)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!editor.hasNodes([_lexical_list__WEBPACK_IMPORTED_MODULE_1__.ListNode, _lexical_list__WEBPACK_IMPORTED_MODULE_1__.ListItemNode])) {\n      throw new Error('ListPlugin: ListNode and/or ListItemNode not registered on editor');\n    }\n  }, [editor]);\n  useList(editor);\n  return null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalListPlugin.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OnChangePlugin: () => (/* binding */ OnChangePlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction OnChangePlugin({\n  ignoreHistoryMergeTagChange = true,\n  ignoreSelectionChange = false,\n  onChange\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__.useLexicalComposerContext)();\n  useLayoutEffectImpl(() => {\n    if (onChange) {\n      return editor.registerUpdateListener(({\n        editorState,\n        dirtyElements,\n        dirtyLeaves,\n        prevEditorState,\n        tags\n      }) => {\n        if (ignoreSelectionChange && dirtyElements.size === 0 && dirtyLeaves.size === 0 || ignoreHistoryMergeTagChange && tags.has('history-merge') || prevEditorState.isEmpty()) {\n          return;\n        }\n        onChange(editorState, editor, tags);\n      });\n    }\n  }, [editor, ignoreHistoryMergeTagChange, ignoreSelectionChange, onChange]);\n  return null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalOnChangePlugin.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs":
/*!**********************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs ***!
  \**********************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   RichTextPlugin: () => (/* binding */ RichTextPlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_react_useLexicalEditable__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @lexical/react/useLexicalEditable */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/useLexicalEditable.dev.mjs\");\n/* harmony import */ var _lexical_text__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/text */ \"(ssr)/../../node_modules/.pnpm/@lexical+text@0.28.0/node_modules/@lexical/text/LexicalText.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/utils */ \"(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_dom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react-dom */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-dom.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/* harmony import */ var _lexical_dragon__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/dragon */ \"(ssr)/../../node_modules/.pnpm/@lexical+dragon@0.28.0/node_modules/@lexical/dragon/LexicalDragon.dev.mjs\");\n/* harmony import */ var _lexical_rich_text__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @lexical/rich-text */ \"(ssr)/../../node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction canShowPlaceholderFromCurrentEditorState(editor) {\n  const currentCanShowPlaceholder = editor.getEditorState().read((0,_lexical_text__WEBPACK_IMPORTED_MODULE_3__.$canShowPlaceholderCurry)(editor.isComposing()));\n  return currentCanShowPlaceholder;\n}\nfunction useCanShowPlaceholder(editor) {\n  const [canShowPlaceholder, setCanShowPlaceholder] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => canShowPlaceholderFromCurrentEditorState(editor));\n  useLayoutEffectImpl(() => {\n    function resetCanShowPlaceholder() {\n      const currentCanShowPlaceholder = canShowPlaceholderFromCurrentEditorState(editor);\n      setCanShowPlaceholder(currentCanShowPlaceholder);\n    }\n    resetCanShowPlaceholder();\n    return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_4__.mergeRegister)(editor.registerUpdateListener(() => {\n      resetCanShowPlaceholder();\n    }), editor.registerEditableListener(() => {\n      resetCanShowPlaceholder();\n    }));\n  }, [editor]);\n  return canShowPlaceholder;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useDecorators(editor, ErrorBoundary) {\n  const [decorators, setDecorators] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => editor.getDecorators());\n\n  // Subscribe to changes\n  useLayoutEffectImpl(() => {\n    return editor.registerDecoratorListener(nextDecorators => {\n      (0,react_dom__WEBPACK_IMPORTED_MODULE_1__.flushSync)(() => {\n        setDecorators(nextDecorators);\n      });\n    });\n  }, [editor]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // If the content editable mounts before the subscription is added, then\n    // nothing will be rendered on initial pass. We can get around that by\n    // ensuring that we set the value.\n    setDecorators(editor.getDecorators());\n  }, [editor]);\n\n  // Return decorators defined as React Portals\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => {\n    const decoratedPortals = [];\n    const decoratorKeys = Object.keys(decorators);\n    for (let i = 0; i < decoratorKeys.length; i++) {\n      const nodeKey = decoratorKeys[i];\n      const reactDecorator = /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(ErrorBoundary, {\n        onError: e => editor._onError(e),\n        children: /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(react__WEBPACK_IMPORTED_MODULE_0__.Suspense, {\n          fallback: null,\n          children: decorators[nodeKey]\n        })\n      });\n      const element = editor.getElementByKey(nodeKey);\n      if (element !== null) {\n        decoratedPortals.push(/*#__PURE__*/(0,react_dom__WEBPACK_IMPORTED_MODULE_1__.createPortal)(reactDecorator, element, nodeKey));\n      }\n    }\n    return decoratedPortals;\n  }, [ErrorBoundary, decorators, editor]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction useRichTextSetup(editor) {\n  useLayoutEffectImpl(() => {\n    return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_4__.mergeRegister)((0,_lexical_rich_text__WEBPACK_IMPORTED_MODULE_5__.registerRichText)(editor), (0,_lexical_dragon__WEBPACK_IMPORTED_MODULE_6__.registerDragonSupport)(editor));\n\n    // We only do this for init\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n  }, [editor]);\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction RichTextPlugin({\n  contentEditable,\n  // TODO Remove. This property is now part of ContentEditable\n  placeholder = null,\n  ErrorBoundary\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_7__.useLexicalComposerContext)();\n  const decorators = useDecorators(editor, ErrorBoundary);\n  useRichTextSetup(editor);\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsxs)(react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.Fragment, {\n    children: [contentEditable, /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_2__.jsx)(Placeholder, {\n      content: placeholder\n    }), decorators]\n  });\n}\n\n// TODO remove\nfunction Placeholder({\n  content\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_7__.useLexicalComposerContext)();\n  const showPlaceholder = useCanShowPlaceholder(editor);\n  const editable = (0,_lexical_react_useLexicalEditable__WEBPACK_IMPORTED_MODULE_8__.useLexicalEditable)();\n  if (!showPlaceholder) {\n    return null;\n  }\n  if (typeof content === 'function') {\n    return content(editable);\n  } else {\n    return content;\n  }\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalRichTextPlugin.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalTabIndentationPlugin.dev.mjs":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalTabIndentationPlugin.dev.mjs ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TabIndentationPlugin: () => (/* binding */ TabIndentationPlugin),\n/* harmony export */   registerTabIndentation: () => (/* binding */ registerTabIndentation)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction $indentOverTab(selection) {\n  // const handled = new Set();\n  const nodes = selection.getNodes();\n  const canIndentBlockNodes = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$filter)(nodes, node => {\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_2__.$isBlockElementNode)(node) && node.canIndent()) {\n      return node;\n    }\n    return null;\n  });\n  // 1. If selection spans across canIndent block nodes: indent\n  if (canIndentBlockNodes.length > 0) {\n    return true;\n  }\n  // 2. If first (anchor/focus) is at block start: indent\n  const anchor = selection.anchor;\n  const focus = selection.focus;\n  const first = focus.isBefore(anchor) ? focus : anchor;\n  const firstNode = first.getNode();\n  const firstBlock = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$getNearestBlockElementAncestorOrThrow)(firstNode);\n  if (firstBlock.canIndent()) {\n    const firstBlockKey = firstBlock.getKey();\n    let selectionAtStart = (0,lexical__WEBPACK_IMPORTED_MODULE_2__.$createRangeSelection)();\n    selectionAtStart.anchor.set(firstBlockKey, 0, 'element');\n    selectionAtStart.focus.set(firstBlockKey, 0, 'element');\n    selectionAtStart = (0,lexical__WEBPACK_IMPORTED_MODULE_2__.$normalizeSelection__EXPERIMENTAL)(selectionAtStart);\n    if (selectionAtStart.anchor.is(first)) {\n      return true;\n    }\n  }\n  // 3. Else: tab\n  return false;\n}\nfunction registerTabIndentation(editor, maxIndent) {\n  return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.mergeRegister)(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_2__.KEY_TAB_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_2__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_2__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    event.preventDefault();\n    const command = $indentOverTab(selection) ? event.shiftKey ? lexical__WEBPACK_IMPORTED_MODULE_2__.OUTDENT_CONTENT_COMMAND : lexical__WEBPACK_IMPORTED_MODULE_2__.INDENT_CONTENT_COMMAND : lexical__WEBPACK_IMPORTED_MODULE_2__.INSERT_TAB_COMMAND;\n    return editor.dispatchCommand(command, undefined);\n  }, lexical__WEBPACK_IMPORTED_MODULE_2__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_2__.INDENT_CONTENT_COMMAND, () => {\n    if (maxIndent == null) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_2__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_2__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const indents = selection.getNodes().map(node => (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$getNearestBlockElementAncestorOrThrow)(node).getIndent());\n    return Math.max(...indents) + 1 >= maxIndent;\n  }, lexical__WEBPACK_IMPORTED_MODULE_2__.COMMAND_PRIORITY_CRITICAL));\n}\n\n/**\n * This plugin adds the ability to indent content using the tab key. Generally, we don't\n * recommend using this plugin as it could negatively affect acessibility for keyboard\n * users, causing focus to become trapped within the editor.\n */\nfunction TabIndentationPlugin({\n  maxIndent\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_3__.useLexicalComposerContext)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return registerTabIndentation(editor, maxIndent);\n  }, [editor, maxIndent]);\n  return null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalTabIndentationPlugin.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalTablePlugin.dev.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalTablePlugin.dev.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TablePlugin: () => (/* binding */ TablePlugin)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var _lexical_table__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/table */ \"(ssr)/../../node_modules/.pnpm/@lexical+table@0.28.0/node_modules/@lexical/table/LexicalTable.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * A plugin to enable all of the features of Lexical's TableNode.\n *\n * @param props - See type for documentation\n * @returns An element to render in your LexicalComposer\n */\nfunction TablePlugin({\n  hasCellMerge = true,\n  hasCellBackgroundColor = true,\n  hasTabHandler = true,\n  hasHorizontalScroll = false\n}) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__.useLexicalComposerContext)();\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    (0,_lexical_table__WEBPACK_IMPORTED_MODULE_2__.setScrollableTablesActive)(editor, hasHorizontalScroll);\n  }, [editor, hasHorizontalScroll]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => (0,_lexical_table__WEBPACK_IMPORTED_MODULE_2__.registerTablePlugin)(editor), [editor]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => (0,_lexical_table__WEBPACK_IMPORTED_MODULE_2__.registerTableSelectionObserver)(editor, hasTabHandler), [editor, hasTabHandler]);\n\n  // Unmerge cells when the feature isn't enabled\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!hasCellMerge) {\n      return (0,_lexical_table__WEBPACK_IMPORTED_MODULE_2__.registerTableCellUnmergeTransform)(editor);\n    }\n  }, [editor, hasCellMerge]);\n\n  // Remove cell background color when feature is disabled\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (hasCellBackgroundColor) {\n      return;\n    }\n    return editor.registerNodeTransform(_lexical_table__WEBPACK_IMPORTED_MODULE_2__.TableCellNode, node => {\n      if (node.getBackgroundColor() !== null) {\n        node.setBackgroundColor(null);\n      }\n    });\n  }, [editor, hasCellBackgroundColor, hasCellMerge]);\n  return null;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalTablePlugin.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalTreeView.dev.mjs":
/*!****************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalTreeView.dev.mjs ***!
  \****************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeView: () => (/* binding */ TreeView)\n/* harmony export */ });\n/* harmony import */ var _lexical_devtools_core__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/devtools-core */ \"(ssr)/../../node_modules/.pnpm/@lexical+devtools-core@0.28.0_react-dom@19.1.0_react@19.1.0/node_modules/@lexical/devtools-core/LexicalDevtoolsCore.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/utils */ \"(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction TreeView({\n  treeTypeButtonClassName,\n  timeTravelButtonClassName,\n  timeTravelPanelSliderClassName,\n  timeTravelPanelButtonClassName,\n  timeTravelPanelClassName,\n  viewClassName,\n  editor,\n  customPrintNode\n}) {\n  const treeElementRef = /*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0__.createRef();\n  const [editorCurrentState, setEditorCurrentState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(editor.getEditorState());\n  const commandsLog = (0,_lexical_devtools_core__WEBPACK_IMPORTED_MODULE_2__.useLexicalCommandsLog)(editor);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    // Registers listeners to update the tree view when the editor state changes\n    return (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_3__.mergeRegister)(editor.registerUpdateListener(({\n      editorState\n    }) => {\n      setEditorCurrentState(editorState);\n    }), editor.registerEditableListener(() => {\n      setEditorCurrentState(editor.getEditorState());\n    }));\n  }, [editor]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const element = treeElementRef.current;\n    if (element !== null) {\n      // Assigns the editor instance to the tree view DOM element for internal tracking\n      // @ts-ignore Internal field used by Lexical\n      element.__lexicalEditor = editor;\n      return () => {\n        // Cleans up the reference when the component is unmounted\n        // @ts-ignore Internal field used by Lexical\n        element.__lexicalEditor = null;\n      };\n    }\n  }, [editor, treeElementRef]);\n\n  /**\n   * Handles toggling the readonly state of the editor.\n   *\n   * @param {boolean} isReadonly - Whether the editor should be set to readonly.\n   */\n  const handleEditorReadOnly = isReadonly => {\n    const rootElement = editor.getRootElement();\n    if (rootElement == null) {\n      return;\n    }\n    rootElement.contentEditable = isReadonly ? 'false' : 'true';\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(_lexical_devtools_core__WEBPACK_IMPORTED_MODULE_2__.TreeView, {\n    treeTypeButtonClassName: treeTypeButtonClassName,\n    timeTravelButtonClassName: timeTravelButtonClassName,\n    timeTravelPanelSliderClassName: timeTravelPanelSliderClassName,\n    timeTravelPanelButtonClassName: timeTravelPanelButtonClassName,\n    viewClassName: viewClassName,\n    timeTravelPanelClassName: timeTravelPanelClassName,\n    setEditorReadOnly: handleEditorReadOnly,\n    editorState: editorCurrentState,\n    setEditorState: state => editor.setEditorState(state),\n    generateContent: async function (exportDOM) {\n      // Generates the content for the tree view, allowing customization with exportDOM and customPrintNode\n      return (0,_lexical_devtools_core__WEBPACK_IMPORTED_MODULE_2__.generateContent)(editor, commandsLog, exportDOM, customPrintNode);\n    },\n    ref: treeElementRef\n  });\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalTreeView.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/useLexicalEditable.dev.mjs":
/*!*******************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/useLexicalEditable.dev.mjs ***!
  \*******************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLexicalEditable: () => (/* binding */ useLexicalEditable)\n/* harmony export */ });\n/* harmony import */ var _lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/react/LexicalComposerContext */ \"(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/LexicalComposerContext.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n// This workaround is no longer necessary in React 19,\n// but we currently support React >=17.x\n// https://github.com/facebook/react/pull/26395\nconst useLayoutEffectImpl = CAN_USE_DOM ? react__WEBPACK_IMPORTED_MODULE_0__.useLayoutEffect : react__WEBPACK_IMPORTED_MODULE_0__.useEffect;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n/**\n * Shortcut to Lexical subscriptions when values are used for render.\n * @param subscription - The function to create the {@link LexicalSubscription}. This function's identity must be stable (e.g. defined at module scope or with useCallback).\n */\nfunction useLexicalSubscription(subscription) {\n  const [editor] = (0,_lexical_react_LexicalComposerContext__WEBPACK_IMPORTED_MODULE_1__.useLexicalComposerContext)();\n  const initializedSubscription = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => subscription(editor), [editor, subscription]);\n  const [value, setValue] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(() => initializedSubscription.initialValueFn());\n  const valueRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(value);\n  useLayoutEffectImpl(() => {\n    const {\n      initialValueFn,\n      subscribe\n    } = initializedSubscription;\n    const currentValue = initialValueFn();\n    if (valueRef.current !== currentValue) {\n      valueRef.current = currentValue;\n      setValue(currentValue);\n    }\n    return subscribe(newValue => {\n      valueRef.current = newValue;\n      setValue(newValue);\n    });\n  }, [initializedSubscription, subscription]);\n  return value;\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction subscription(editor) {\n  return {\n    initialValueFn: () => editor.isEditable(),\n    subscribe: callback => {\n      return editor.registerEditableListener(callback);\n    }\n  };\n}\n\n/**\n * Get the current value for {@link LexicalEditor.isEditable}\n * using {@link useLexicalSubscription}.\n * You should prefer this over manually observing the value with\n * {@link LexicalEditor.registerEditableListener},\n * which is a bit tricky to do correctly, particularly when using\n * React StrictMode (the default for development) or concurrency.\n */\nfunction useLexicalEditable() {\n  return useLexicalSubscription(subscription);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BsZXhpY2FsK3JlYWN0QDAuMjguMF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF95anNAMTMuNi4yNy9ub2RlX21vZHVsZXMvQGxleGljYWwvcmVhY3QvdXNlTGV4aWNhbEVkaXRhYmxlLmRldi5tanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRWtGO0FBQ0o7O0FBRTlFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOzs7QUFHQTtBQUNBO0FBQ0E7QUFDQSwwQ0FBMEMsa0RBQWUsR0FBRyw0Q0FBUzs7QUFFckU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBLHFEQUFxRCwwQkFBMEI7QUFDL0U7QUFDQTtBQUNBLG1CQUFtQixnR0FBeUI7QUFDNUMsa0NBQWtDLDhDQUFPO0FBQ3pDLDRCQUE0QiwrQ0FBUTtBQUNwQyxtQkFBbUIsNkNBQU07QUFDekI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNO0FBQ047QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTCxHQUFHO0FBQ0g7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsOEJBQThCO0FBQzlCLFVBQVUsNkJBQTZCO0FBQ3ZDO0FBQ0EsSUFBSSw2Q0FBNkM7QUFDakQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUU4QiIsInNvdXJjZXMiOlsiQzpcXHByb2plY3RzXFxsbXNcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBsZXhpY2FsK3JlYWN0QDAuMjguMF9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMF95anNAMTMuNi4yN1xcbm9kZV9tb2R1bGVzXFxAbGV4aWNhbFxccmVhY3RcXHVzZUxleGljYWxFZGl0YWJsZS5kZXYubWpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKi9cblxuaW1wb3J0IHsgdXNlTGV4aWNhbENvbXBvc2VyQ29udGV4dCB9IGZyb20gJ0BsZXhpY2FsL3JlYWN0L0xleGljYWxDb21wb3NlckNvbnRleHQnO1xuaW1wb3J0IHsgdXNlTGF5b3V0RWZmZWN0LCB1c2VFZmZlY3QsIHVzZU1lbW8sIHVzZVN0YXRlLCB1c2VSZWYgfSBmcm9tICdyZWFjdCc7XG5cbi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKi9cblxuY29uc3QgQ0FOX1VTRV9ET00gPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyAmJiB0eXBlb2Ygd2luZG93LmRvY3VtZW50ICE9PSAndW5kZWZpbmVkJyAmJiB0eXBlb2Ygd2luZG93LmRvY3VtZW50LmNyZWF0ZUVsZW1lbnQgIT09ICd1bmRlZmluZWQnO1xuXG4vKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICovXG5cblxuLy8gVGhpcyB3b3JrYXJvdW5kIGlzIG5vIGxvbmdlciBuZWNlc3NhcnkgaW4gUmVhY3QgMTksXG4vLyBidXQgd2UgY3VycmVudGx5IHN1cHBvcnQgUmVhY3QgPj0xNy54XG4vLyBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svcmVhY3QvcHVsbC8yNjM5NVxuY29uc3QgdXNlTGF5b3V0RWZmZWN0SW1wbCA9IENBTl9VU0VfRE9NID8gdXNlTGF5b3V0RWZmZWN0IDogdXNlRWZmZWN0O1xuXG4vKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICovXG5cbi8qKlxuICogU2hvcnRjdXQgdG8gTGV4aWNhbCBzdWJzY3JpcHRpb25zIHdoZW4gdmFsdWVzIGFyZSB1c2VkIGZvciByZW5kZXIuXG4gKiBAcGFyYW0gc3Vic2NyaXB0aW9uIC0gVGhlIGZ1bmN0aW9uIHRvIGNyZWF0ZSB0aGUge0BsaW5rIExleGljYWxTdWJzY3JpcHRpb259LiBUaGlzIGZ1bmN0aW9uJ3MgaWRlbnRpdHkgbXVzdCBiZSBzdGFibGUgKGUuZy4gZGVmaW5lZCBhdCBtb2R1bGUgc2NvcGUgb3Igd2l0aCB1c2VDYWxsYmFjaykuXG4gKi9cbmZ1bmN0aW9uIHVzZUxleGljYWxTdWJzY3JpcHRpb24oc3Vic2NyaXB0aW9uKSB7XG4gIGNvbnN0IFtlZGl0b3JdID0gdXNlTGV4aWNhbENvbXBvc2VyQ29udGV4dCgpO1xuICBjb25zdCBpbml0aWFsaXplZFN1YnNjcmlwdGlvbiA9IHVzZU1lbW8oKCkgPT4gc3Vic2NyaXB0aW9uKGVkaXRvciksIFtlZGl0b3IsIHN1YnNjcmlwdGlvbl0pO1xuICBjb25zdCBbdmFsdWUsIHNldFZhbHVlXSA9IHVzZVN0YXRlKCgpID0+IGluaXRpYWxpemVkU3Vic2NyaXB0aW9uLmluaXRpYWxWYWx1ZUZuKCkpO1xuICBjb25zdCB2YWx1ZVJlZiA9IHVzZVJlZih2YWx1ZSk7XG4gIHVzZUxheW91dEVmZmVjdEltcGwoKCkgPT4ge1xuICAgIGNvbnN0IHtcbiAgICAgIGluaXRpYWxWYWx1ZUZuLFxuICAgICAgc3Vic2NyaWJlXG4gICAgfSA9IGluaXRpYWxpemVkU3Vic2NyaXB0aW9uO1xuICAgIGNvbnN0IGN1cnJlbnRWYWx1ZSA9IGluaXRpYWxWYWx1ZUZuKCk7XG4gICAgaWYgKHZhbHVlUmVmLmN1cnJlbnQgIT09IGN1cnJlbnRWYWx1ZSkge1xuICAgICAgdmFsdWVSZWYuY3VycmVudCA9IGN1cnJlbnRWYWx1ZTtcbiAgICAgIHNldFZhbHVlKGN1cnJlbnRWYWx1ZSk7XG4gICAgfVxuICAgIHJldHVybiBzdWJzY3JpYmUobmV3VmFsdWUgPT4ge1xuICAgICAgdmFsdWVSZWYuY3VycmVudCA9IG5ld1ZhbHVlO1xuICAgICAgc2V0VmFsdWUobmV3VmFsdWUpO1xuICAgIH0pO1xuICB9LCBbaW5pdGlhbGl6ZWRTdWJzY3JpcHRpb24sIHN1YnNjcmlwdGlvbl0pO1xuICByZXR1cm4gdmFsdWU7XG59XG5cbi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKi9cblxuZnVuY3Rpb24gc3Vic2NyaXB0aW9uKGVkaXRvcikge1xuICByZXR1cm4ge1xuICAgIGluaXRpYWxWYWx1ZUZuOiAoKSA9PiBlZGl0b3IuaXNFZGl0YWJsZSgpLFxuICAgIHN1YnNjcmliZTogY2FsbGJhY2sgPT4ge1xuICAgICAgcmV0dXJuIGVkaXRvci5yZWdpc3RlckVkaXRhYmxlTGlzdGVuZXIoY2FsbGJhY2spO1xuICAgIH1cbiAgfTtcbn1cblxuLyoqXG4gKiBHZXQgdGhlIGN1cnJlbnQgdmFsdWUgZm9yIHtAbGluayBMZXhpY2FsRWRpdG9yLmlzRWRpdGFibGV9XG4gKiB1c2luZyB7QGxpbmsgdXNlTGV4aWNhbFN1YnNjcmlwdGlvbn0uXG4gKiBZb3Ugc2hvdWxkIHByZWZlciB0aGlzIG92ZXIgbWFudWFsbHkgb2JzZXJ2aW5nIHRoZSB2YWx1ZSB3aXRoXG4gKiB7QGxpbmsgTGV4aWNhbEVkaXRvci5yZWdpc3RlckVkaXRhYmxlTGlzdGVuZXJ9LFxuICogd2hpY2ggaXMgYSBiaXQgdHJpY2t5IHRvIGRvIGNvcnJlY3RseSwgcGFydGljdWxhcmx5IHdoZW4gdXNpbmdcbiAqIFJlYWN0IFN0cmljdE1vZGUgKHRoZSBkZWZhdWx0IGZvciBkZXZlbG9wbWVudCkgb3IgY29uY3VycmVuY3kuXG4gKi9cbmZ1bmN0aW9uIHVzZUxleGljYWxFZGl0YWJsZSgpIHtcbiAgcmV0dXJuIHVzZUxleGljYWxTdWJzY3JpcHRpb24oc3Vic2NyaXB0aW9uKTtcbn1cblxuZXhwb3J0IHsgdXNlTGV4aWNhbEVkaXRhYmxlIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+react@0.28.0_react-dom@19.1.0_react@19.1.0_yjs@13.6.27/node_modules/@lexical/react/useLexicalEditable.dev.mjs\n");

/***/ })

};
;