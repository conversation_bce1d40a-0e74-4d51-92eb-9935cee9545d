{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/projects/lms/apps/frontend/src/app/super-admin/page.tsx"], "sourcesContent": ["'use client'\n\nimport { useAuthStore } from '@/stores/auth/useAuthStore'\nimport { useEffect } from 'react'\nimport { useRouter } from 'next/navigation'\nimport Link from 'next/link'\n\nexport default function SuperAdminDashboard() {\n  const { user, isAuthenticated, isLoading, logout, initialize } = useAuthStore()\n  const router = useRouter()\n\n  useEffect(() => {\n    // Initialize auth state on component mount\n    initialize()\n  }, [initialize])\n\n  useEffect(() => {\n    // Only redirect if we're done loading AND definitely not authenticated\n    if (!isLoading && !isAuthenticated) {\n      router.push('/auth/admin/login')\n      return\n    }\n\n    // If authenticated but wrong role, redirect to login\n    if (!isLoading && isAuthenticated && user && user.legacyRole !== 'super_admin' && user.legacyRole !== 'platform_staff') {\n      router.push('/auth/admin/login')\n    }\n  }, [user, isAuthenticated, isLoading, router])\n\n  // Show loading while checking authentication\n  if (isLoading || !user) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto\"></div>\n          <p className=\"mt-4 text-gray-600\">Loading dashboard...</p>\n        </div>\n      </div>\n    )\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Page Header */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h1 className=\"text-2xl font-bold text-gray-900\">Super Admin Dashboard</h1>\n            <p className=\"text-gray-600\">Welcome back, {user.firstName} {user.lastName}</p>\n          </div>\n          <div className=\"text-sm text-gray-500\">\n            Role: <span className=\"font-medium capitalize\">\n              {typeof user.role === 'string' ? user.role.replace('_', ' ') : (user.legacyRole || 'Unknown').replace('_', ' ')}\n            </span>\n          </div>\n        </div>\n      </div>\n\n      {/* Stats Overview */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n        {/* Platform Overview */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-medium\">📊</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Total Institutes</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">24</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Active Users */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-green-500 rounded-md flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-medium\">👥</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Active Users</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">1,247</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Revenue */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-yellow-500 rounded-md flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-medium\">💰</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">Monthly Revenue</dt>\n                  <dd className=\"text-lg font-medium text-gray-900\">₹2,45,000</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* System Health */}\n        <div className=\"bg-white overflow-hidden shadow rounded-lg\">\n          <div className=\"p-5\">\n            <div className=\"flex items-center\">\n              <div className=\"flex-shrink-0\">\n                <div className=\"w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center\">\n                  <span className=\"text-white text-sm font-medium\">⚡</span>\n                </div>\n              </div>\n              <div className=\"ml-5 w-0 flex-1\">\n                <dl>\n                  <dt className=\"text-sm font-medium text-gray-500 truncate\">System Health</dt>\n                  <dd className=\"text-lg font-medium text-green-600\">Excellent</dd>\n                </dl>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Quick Actions */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h2 className=\"text-lg font-medium text-gray-900 mb-4\">Quick Actions</h2>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4\">\n          <Link href=\"/super-admin/institutes\" className=\"bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl mb-2\">🏢</div>\n              <div className=\"text-sm font-medium text-gray-900\">Manage Institutes</div>\n            </div>\n          </Link>\n\n          <Link href=\"/super-admin/themes\" className=\"bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl mb-2\">🎨</div>\n              <div className=\"text-sm font-medium text-gray-900\">Theme Management</div>\n            </div>\n          </Link>\n          <Link href=\"/super-admin/settings\" className=\"bg-gray-50 p-4 rounded-lg hover:bg-gray-100 transition-colors\">\n            <div className=\"text-center\">\n              <div className=\"text-2xl mb-2\">⚙️</div>\n              <div className=\"text-sm font-medium text-gray-900\">System Settings</div>\n            </div>\n          </Link>\n        </div>\n      </div>\n\n      {/* Recent Activity */}\n      <div className=\"bg-white shadow rounded-lg p-6\">\n        <h2 className=\"text-lg font-medium text-gray-900 mb-4\">Recent Activity</h2>\n        <div className=\"space-y-3\">\n          <div className=\"flex items-center space-x-3 text-sm\">\n            <div className=\"w-2 h-2 bg-green-400 rounded-full\"></div>\n            <span className=\"text-gray-600\">New institute registration: Tech Academy</span>\n            <span className=\"text-gray-400\">2 hours ago</span>\n          </div>\n          <div className=\"flex items-center space-x-3 text-sm\">\n            <div className=\"w-2 h-2 bg-blue-400 rounded-full\"></div>\n            <span className=\"text-gray-600\">System backup completed successfully</span>\n            <span className=\"text-gray-400\">4 hours ago</span>\n          </div>\n          <div className=\"flex items-center space-x-3 text-sm\">\n            <div className=\"w-2 h-2 bg-yellow-400 rounded-full\"></div>\n            <span className=\"text-gray-600\">Domain verification pending: artschool.edu</span>\n            <span className=\"text-gray-400\">6 hours ago</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,SAAS,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,yJAAA,CAAA,eAAY,AAAD;IAC5E,MAAM,SAAS,CAAA,GAAA,uOAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,2CAA2C;QAC3C;IACF,GAAG;QAAC;KAAW;IAEf,CAAA,GAAA,0SAAA,CAAA,YAAS,AAAD,EAAE;QACR,uEAAuE;QACvE,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,qDAAqD;QACrD,IAAI,CAAC,aAAa,mBAAmB,QAAQ,KAAK,UAAU,KAAK,iBAAiB,KAAK,UAAU,KAAK,kBAAkB;YACtH,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAiB;QAAW;KAAO;IAE7C,6CAA6C;IAC7C,IAAI,aAAa,CAAC,MAAM;QACtB,qBACE,mVAAC;YAAI,WAAU;sBACb,cAAA,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAI,WAAU;;;;;;kCACf,mVAAC;wBAAE,WAAU;kCAAqB;;;;;;;;;;;;;;;;;IAI1C;IAEA,qBACE,mVAAC;QAAI,WAAU;;0BAEb,mVAAC;gBAAI,WAAU;0BACb,cAAA,mVAAC;oBAAI,WAAU;;sCACb,mVAAC;;8CACC,mVAAC;oCAAG,WAAU;8CAAmC;;;;;;8CACjD,mVAAC;oCAAE,WAAU;;wCAAgB;wCAAe,KAAK,SAAS;wCAAC;wCAAE,KAAK,QAAQ;;;;;;;;;;;;;sCAE5E,mVAAC;4BAAI,WAAU;;gCAAwB;8CAC/B,mVAAC;oCAAK,WAAU;8CACnB,OAAO,KAAK,IAAI,KAAK,WAAW,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK,OAAO,CAAC,KAAK,UAAU,IAAI,SAAS,EAAE,OAAO,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;0BAOnH,mVAAC;gBAAI,WAAU;;kCAEb,mVAAC;wBAAI,WAAU;kCACb,cAAA,mVAAC;4BAAI,WAAU;sCACb,cAAA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC;4CAAI,WAAU;sDACb,cAAA,mVAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;;;;;;kDAGrD,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC;;8DACC,mVAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,mVAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5D,mVAAC;wBAAI,WAAU;kCACb,cAAA,mVAAC;4BAAI,WAAU;sCACb,cAAA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC;4CAAI,WAAU;sDACb,cAAA,mVAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;;;;;;kDAGrD,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC;;8DACC,mVAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,mVAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5D,mVAAC;wBAAI,WAAU;kCACb,cAAA,mVAAC;4BAAI,WAAU;sCACb,cAAA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC;4CAAI,WAAU;sDACb,cAAA,mVAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;;;;;;kDAGrD,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC;;8DACC,mVAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,mVAAC;oDAAG,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ5D,mVAAC;wBAAI,WAAU;kCACb,cAAA,mVAAC;4BAAI,WAAU;sCACb,cAAA,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC;4CAAI,WAAU;sDACb,cAAA,mVAAC;gDAAK,WAAU;0DAAiC;;;;;;;;;;;;;;;;kDAGrD,mVAAC;wCAAI,WAAU;kDACb,cAAA,mVAAC;;8DACC,mVAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,mVAAC;oDAAG,WAAU;8DAAqC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS/D,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,mVAAC;wBAAI,WAAU;;0CACb,mVAAC,iQAAA,CAAA,UAAI;gCAAC,MAAK;gCAA0B,WAAU;0CAC7C,cAAA,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,mVAAC;4CAAI,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAIvD,mVAAC,iQAAA,CAAA,UAAI;gCAAC,MAAK;gCAAsB,WAAU;0CACzC,cAAA,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,mVAAC;4CAAI,WAAU;sDAAoC;;;;;;;;;;;;;;;;;0CAGvD,mVAAC,iQAAA,CAAA,UAAI;gCAAC,MAAK;gCAAwB,WAAU;0CAC3C,cAAA,mVAAC;oCAAI,WAAU;;sDACb,mVAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,mVAAC;4CAAI,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO3D,mVAAC;gBAAI,WAAU;;kCACb,mVAAC;wBAAG,WAAU;kCAAyC;;;;;;kCACvD,mVAAC;wBAAI,WAAU;;0CACb,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;;;;;;kDACf,mVAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,mVAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;;;;;;kDACf,mVAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,mVAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAElC,mVAAC;gCAAI,WAAU;;kDACb,mVAAC;wCAAI,WAAU;;;;;;kDACf,mVAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,mVAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM5C", "debugId": null}}]}