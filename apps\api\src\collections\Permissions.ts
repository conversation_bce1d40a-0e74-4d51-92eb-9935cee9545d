import type { CollectionConfig } from 'payload'
import { isAdmin } from '../access/index'

export const Permissions: CollectionConfig = {
  slug: 'permissions',
  admin: {
    useAsTitle: 'name',
    defaultColumns: ['name', 'category', 'level', 'isActive'],
    group: 'Access Control'
  },
  access: {
    read: ({ req: { user } }) => {
      if (!user) return false

      // Use legacyRole field for access control (consistent with Users collection)
      if (user.legacyRole === 'super_admin') return true

      if (user.legacyRole === 'institute_admin') {
        return {
          level: { greater_than_equal: 2 }
        }
      }

      return false
    },
    create: ({ req: { user } }) => {
      if (!user) return false
      // Use legacyRole field for access control
      return user.legacyRole === 'super_admin'
    },
    update: ({ req: { user } }) => {
      if (!user) return false
      // Use legacyRole field for access control
      return user.legacyRole === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      if (!user) return false
      // Use legacyRole field for access control
      return user.legacyRole === 'super_admin'
    }
  },
  fields: [
    {
      name: 'name',
      type: 'text',
      label: 'Permission Name',
      required: true,
      unique: true,
      index: true
    },
    {
      name: 'description',
      type: 'textarea',
      label: 'Permission Description'
    },
    {
      name: 'code',
      type: 'text',
      required: true,
      unique: true,
      validate: (val) => {
        if (!val) return 'Permission code is required'
        if (!/^[a-z_:]+$/.test(val)) return 'Permission code must contain only lowercase letters, underscores, and colons'
        return true
      }
    },
    {
      name: 'category',
      type: 'select',
      label: 'Permission Category',
      required: true,
      options: [
        { label: 'Platform Management', value: 'platform' },
        { label: 'Institute Management', value: 'institute' },
        { label: 'Branch Management', value: 'branch' },
        { label: 'User Management', value: 'users' },
        { label: 'Course Management', value: 'courses' },
        { label: 'Student Management', value: 'students' },
        { label: 'Billing & Payments', value: 'billing' },
        { label: 'Reports & Analytics', value: 'reports' },
        { label: 'Settings & Configuration', value: 'settings' }
      ],
      index: true
    },
    {
      name: 'level',
      type: 'select',
      required: true,
      options: [
        { label: 'Level 1 - Super Admin Only', value: '1' },
        { label: 'Level 2 - Institute Admin', value: '2' },
        { label: 'Level 3 - Branch/User Level', value: '3' }
      ],
      defaultValue: '3'
    },
    {
      name: 'resource',
      type: 'text',
      label: 'Resource',
      required: true,
      admin: {
        description: 'The resource this permission applies to (e.g., users, courses, institutes)'
      }
    },
    {
      name: 'action',
      type: 'select',
      label: 'Action',
      required: true,
      options: [
        { label: 'Create', value: 'create' },
        { label: 'Read', value: 'read' },
        { label: 'Update', value: 'update' },
        { label: 'Delete', value: 'delete' },
        { label: 'Manage', value: 'manage' },
        { label: 'View', value: 'view' },
        { label: 'Execute', value: 'execute' },
        { label: 'Approve', value: 'approve' },
        { label: 'Export', value: 'export' },
        { label: 'Import', value: 'import' }
      ],
      index: true
    },
    {
      name: 'isSystemPermission',
      type: 'checkbox',
      label: 'Is System Permission',
      defaultValue: false,
      admin: {
        description: 'System permissions cannot be deleted and are required for core functionality'
      }
    },
    {
      name: 'isActive',
      type: 'checkbox',
      label: 'Is Active',
      defaultValue: true,
      index: true
    }
  ],
  
  timestamps: true,
  
  hooks: {
    afterChange: [
      async ({ doc, operation, req }) => {
        // Log permission changes for audit trail
        if (operation === 'update') {
          console.log(`Permission ${doc.name} was updated by user ${req.user?.email || 'system'}`)
        }
      }
    ]
  }
}

// Default permissions to be seeded
export const defaultPermissions = [
  // User Management
  { name: 'Create Users', category: 'user_management', resource: 'users', action: 'create', scope: 'global', requiredLevel: 1, isSystemPermission: true },
  { name: 'View Users', category: 'user_management', resource: 'users', action: 'read', scope: 'institute', requiredLevel: 3, isSystemPermission: true },
  { name: 'Update Users', category: 'user_management', resource: 'users', action: 'update', scope: 'institute', requiredLevel: 2, isSystemPermission: true },
  { name: 'Delete Users', category: 'user_management', resource: 'users', action: 'delete', scope: 'institute', requiredLevel: 1, isSystemPermission: true },
  
  // Institute Management
  { name: 'Manage Institute Settings', category: 'institute_management', resource: 'institute', action: 'manage', scope: 'institute', requiredLevel: 1, isSystemPermission: true },
  { name: 'View Institute Analytics', category: 'institute_management', resource: 'institute', action: 'view_reports', scope: 'institute', requiredLevel: 2, isSystemPermission: true },
  
  // Course Management
  { name: 'Create Courses', category: 'course_management', resource: 'courses', action: 'create', scope: 'institute', requiredLevel: 3, isSystemPermission: true },
  { name: 'Update Courses', category: 'course_management', resource: 'courses', action: 'update', scope: 'institute', requiredLevel: 3, isSystemPermission: true },
  { name: 'Delete Courses', category: 'course_management', resource: 'courses', action: 'delete', scope: 'institute', requiredLevel: 2, isSystemPermission: true },
  { name: 'Approve Courses', category: 'course_management', resource: 'courses', action: 'approve', scope: 'institute', requiredLevel: 2, isSystemPermission: true },
  
  // Student Management
  { name: 'View Students', category: 'student_management', resource: 'students', action: 'read', scope: 'institute', requiredLevel: 4, isSystemPermission: true },
  { name: 'Update Students', category: 'student_management', resource: 'students', action: 'update', scope: 'institute', requiredLevel: 3, isSystemPermission: true },
  { name: 'Manage Student Enrollments', category: 'student_management', resource: 'enrollments', action: 'manage', scope: 'institute', requiredLevel: 3, isSystemPermission: true },
  
  // Staff Management - REMOVED
  // { name: 'Create Staff', category: 'staff_management', resource: 'staff', action: 'create', scope: 'institute', requiredLevel: 2, isSystemPermission: true },
  // { name: 'View Staff', category: 'staff_management', resource: 'staff', action: 'read', scope: 'department', requiredLevel: 3, isSystemPermission: true },
  // { name: 'Update Staff', category: 'staff_management', resource: 'staff', action: 'update', scope: 'department', requiredLevel: 2, isSystemPermission: true },
  // { name: 'Delete Staff', category: 'staff_management', resource: 'staff', action: 'delete', scope: 'institute', requiredLevel: 1, isSystemPermission: true },
  
  // Financial Management
  { name: 'View Financial Reports', category: 'financial_management', resource: 'finances', action: 'view_reports', scope: 'institute', requiredLevel: 2, isSystemPermission: true },
  { name: 'Manage Billing', category: 'financial_management', resource: 'billing', action: 'manage', scope: 'institute', requiredLevel: 2, isSystemPermission: true },
  { name: 'Process Payments', category: 'financial_management', resource: 'payments', action: 'manage', scope: 'institute', requiredLevel: 3, isSystemPermission: true },
  
  // System Settings
  { name: 'Manage System Settings', category: 'system_settings', resource: 'settings', action: 'manage', scope: 'global', requiredLevel: 1, isSystemPermission: true },
  { name: 'View System Logs', category: 'system_settings', resource: 'logs', action: 'read', scope: 'global', requiredLevel: 1, isSystemPermission: true },
  
  // Reports & Analytics
  { name: 'View Analytics Dashboard', category: 'reports_analytics', resource: 'analytics', action: 'view_reports', scope: 'institute', requiredLevel: 3, isSystemPermission: true },
  { name: 'Export Reports', category: 'reports_analytics', resource: 'reports', action: 'export', scope: 'institute', requiredLevel: 3, isSystemPermission: true }
]
