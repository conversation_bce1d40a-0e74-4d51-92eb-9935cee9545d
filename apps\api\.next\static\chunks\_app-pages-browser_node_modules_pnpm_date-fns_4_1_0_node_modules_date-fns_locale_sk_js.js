"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_sk_js"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2lzU2FtZVdlZWsuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7OztBQUEwRDtBQUNYO0FBRS9DOztDQUVDLEdBRUQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Q0ErQkMsR0FDTSxTQUFTRSxXQUFXQyxTQUFTLEVBQUVDLFdBQVcsRUFBRUMsT0FBTztJQUN4RCxNQUFNLENBQUNDLFlBQVlDLGFBQWEsR0FBR1Asc0VBQWNBLENBQy9DSyxvQkFBQUEsOEJBQUFBLFFBQVNHLEVBQUUsRUFDWEwsV0FDQUM7SUFFRixPQUNFLENBQUNILDREQUFXQSxDQUFDSyxZQUFZRCxhQUFhLENBQUNKLDREQUFXQSxDQUFDTSxjQUFjRjtBQUVyRTtBQUVBLG9DQUFvQztBQUNwQyxpRUFBZUgsVUFBVUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXHByb2plY3RzXFxsbXNcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGRhdGUtZm5zQDQuMS4wXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxpc1NhbWVXZWVrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IG5vcm1hbGl6ZURhdGVzIH0gZnJvbSBcIi4vX2xpYi9ub3JtYWxpemVEYXRlcy5qc1wiO1xuaW1wb3J0IHsgc3RhcnRPZldlZWsgfSBmcm9tIFwiLi9zdGFydE9mV2Vlay5qc1wiO1xuXG4vKipcbiAqIFRoZSB7QGxpbmsgaXNTYW1lV2Vla30gZnVuY3Rpb24gb3B0aW9ucy5cbiAqL1xuXG4vKipcbiAqIEBuYW1lIGlzU2FtZVdlZWtcbiAqIEBjYXRlZ29yeSBXZWVrIEhlbHBlcnNcbiAqIEBzdW1tYXJ5IEFyZSB0aGUgZ2l2ZW4gZGF0ZXMgaW4gdGhlIHNhbWUgd2VlayAoYW5kIG1vbnRoIGFuZCB5ZWFyKT9cbiAqXG4gKiBAZGVzY3JpcHRpb25cbiAqIEFyZSB0aGUgZ2l2ZW4gZGF0ZXMgaW4gdGhlIHNhbWUgd2VlayAoYW5kIG1vbnRoIGFuZCB5ZWFyKT9cbiAqXG4gKiBAcGFyYW0gbGF0ZXJEYXRlIC0gVGhlIGZpcnN0IGRhdGUgdG8gY2hlY2tcbiAqIEBwYXJhbSBlYXJsaWVyRGF0ZSAtIFRoZSBzZWNvbmQgZGF0ZSB0byBjaGVja1xuICogQHBhcmFtIG9wdGlvbnMgLSBBbiBvYmplY3Qgd2l0aCBvcHRpb25zXG4gKlxuICogQHJldHVybnMgVGhlIGRhdGVzIGFyZSBpbiB0aGUgc2FtZSB3ZWVrIChhbmQgbW9udGggYW5kIHllYXIpXG4gKlxuICogQGV4YW1wbGVcbiAqIC8vIEFyZSAzMSBBdWd1c3QgMjAxNCBhbmQgNCBTZXB0ZW1iZXIgMjAxNCBpbiB0aGUgc2FtZSB3ZWVrP1xuICogY29uc3QgcmVzdWx0ID0gaXNTYW1lV2VlayhuZXcgRGF0ZSgyMDE0LCA3LCAzMSksIG5ldyBEYXRlKDIwMTQsIDgsIDQpKVxuICogLy89PiB0cnVlXG4gKlxuICogQGV4YW1wbGVcbiAqIC8vIElmIHdlZWsgc3RhcnRzIHdpdGggTW9uZGF5LFxuICogLy8gYXJlIDMxIEF1Z3VzdCAyMDE0IGFuZCA0IFNlcHRlbWJlciAyMDE0IGluIHRoZSBzYW1lIHdlZWs/XG4gKiBjb25zdCByZXN1bHQgPSBpc1NhbWVXZWVrKG5ldyBEYXRlKDIwMTQsIDcsIDMxKSwgbmV3IERhdGUoMjAxNCwgOCwgNCksIHtcbiAqICAgd2Vla1N0YXJ0c09uOiAxXG4gKiB9KVxuICogLy89PiBmYWxzZVxuICpcbiAqIEBleGFtcGxlXG4gKiAvLyBBcmUgMSBKYW51YXJ5IDIwMTQgYW5kIDEgSmFudWFyeSAyMDE1IGluIHRoZSBzYW1lIHdlZWs/XG4gKiBjb25zdCByZXN1bHQgPSBpc1NhbWVXZWVrKG5ldyBEYXRlKDIwMTQsIDAsIDEpLCBuZXcgRGF0ZSgyMDE1LCAwLCAxKSlcbiAqIC8vPT4gZmFsc2VcbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlzU2FtZVdlZWsobGF0ZXJEYXRlLCBlYXJsaWVyRGF0ZSwgb3B0aW9ucykge1xuICBjb25zdCBbbGF0ZXJEYXRlXywgZWFybGllckRhdGVfXSA9IG5vcm1hbGl6ZURhdGVzKFxuICAgIG9wdGlvbnM/LmluLFxuICAgIGxhdGVyRGF0ZSxcbiAgICBlYXJsaWVyRGF0ZSxcbiAgKTtcbiAgcmV0dXJuIChcbiAgICArc3RhcnRPZldlZWsobGF0ZXJEYXRlXywgb3B0aW9ucykgPT09ICtzdGFydE9mV2VlayhlYXJsaWVyRGF0ZV8sIG9wdGlvbnMpXG4gICk7XG59XG5cbi8vIEZhbGxiYWNrIGZvciBtb2R1bGFyaXplZCBpbXBvcnRzOlxuZXhwb3J0IGRlZmF1bHQgaXNTYW1lV2VlaztcbiJdLCJuYW1lcyI6WyJub3JtYWxpemVEYXRlcyIsInN0YXJ0T2ZXZWVrIiwiaXNTYW1lV2VlayIsImxhdGVyRGF0ZSIsImVhcmxpZXJEYXRlIiwib3B0aW9ucyIsImxhdGVyRGF0ZV8iLCJlYXJsaWVyRGF0ZV8iLCJpbiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   sk: () => (/* binding */ sk)\n/* harmony export */ });\n/* harmony import */ var _sk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./sk/_lib/formatDistance.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatDistance.js\");\n/* harmony import */ var _sk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./sk/_lib/formatLong.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatLong.js\");\n/* harmony import */ var _sk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./sk/_lib/formatRelative.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatRelative.js\");\n/* harmony import */ var _sk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./sk/_lib/localize.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/localize.js\");\n/* harmony import */ var _sk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./sk/_lib/match.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Slovak locale.\n * @language Slovak\n * @iso-639-2 slk\n * <AUTHOR> Suscak [@mareksuscak](https://github.com/mareksuscak)\n */ const sk = {\n    code: \"sk\",\n    formatDistance: _sk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _sk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _sk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _sk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _sk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (sk);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS9zay5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTZEO0FBQ1I7QUFDUTtBQUNaO0FBQ047QUFFM0M7Ozs7OztDQU1DLEdBQ00sTUFBTUssS0FBSztJQUNoQkMsTUFBTTtJQUNOTixnQkFBZ0JBLHFFQUFjQTtJQUM5QkMsWUFBWUEsNkRBQVVBO0lBQ3RCQyxnQkFBZ0JBLHFFQUFjQTtJQUM5QkMsVUFBVUEseURBQVFBO0lBQ2xCQyxPQUFPQSxtREFBS0E7SUFDWkcsU0FBUztRQUNQQyxjQUFjLEVBQUUsVUFBVTtRQUMxQkMsdUJBQXVCO0lBQ3pCO0FBQ0YsRUFBRTtBQUVGLG9DQUFvQztBQUNwQyxpRUFBZUosRUFBRUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXHByb2plY3RzXFxsbXNcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGRhdGUtZm5zQDQuMS4wXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxsb2NhbGVcXHNrLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZvcm1hdERpc3RhbmNlIH0gZnJvbSBcIi4vc2svX2xpYi9mb3JtYXREaXN0YW5jZS5qc1wiO1xuaW1wb3J0IHsgZm9ybWF0TG9uZyB9IGZyb20gXCIuL3NrL19saWIvZm9ybWF0TG9uZy5qc1wiO1xuaW1wb3J0IHsgZm9ybWF0UmVsYXRpdmUgfSBmcm9tIFwiLi9zay9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzXCI7XG5pbXBvcnQgeyBsb2NhbGl6ZSB9IGZyb20gXCIuL3NrL19saWIvbG9jYWxpemUuanNcIjtcbmltcG9ydCB7IG1hdGNoIH0gZnJvbSBcIi4vc2svX2xpYi9tYXRjaC5qc1wiO1xuXG4vKipcbiAqIEBjYXRlZ29yeSBMb2NhbGVzXG4gKiBAc3VtbWFyeSBTbG92YWsgbG9jYWxlLlxuICogQGxhbmd1YWdlIFNsb3Zha1xuICogQGlzby02MzktMiBzbGtcbiAqIEBhdXRob3IgTWFyZWsgU3VzY2FrIFtAbWFyZWtzdXNjYWtdKGh0dHBzOi8vZ2l0aHViLmNvbS9tYXJla3N1c2NhaylcbiAqL1xuZXhwb3J0IGNvbnN0IHNrID0ge1xuICBjb2RlOiBcInNrXCIsXG4gIGZvcm1hdERpc3RhbmNlOiBmb3JtYXREaXN0YW5jZSxcbiAgZm9ybWF0TG9uZzogZm9ybWF0TG9uZyxcbiAgZm9ybWF0UmVsYXRpdmU6IGZvcm1hdFJlbGF0aXZlLFxuICBsb2NhbGl6ZTogbG9jYWxpemUsXG4gIG1hdGNoOiBtYXRjaCxcbiAgb3B0aW9uczoge1xuICAgIHdlZWtTdGFydHNPbjogMSAvKiBNb25kYXkgKi8sXG4gICAgZmlyc3RXZWVrQ29udGFpbnNEYXRlOiA0LFxuICB9LFxufTtcblxuLy8gRmFsbGJhY2sgZm9yIG1vZHVsYXJpemVkIGltcG9ydHM6XG5leHBvcnQgZGVmYXVsdCBzaztcbiJdLCJuYW1lcyI6WyJmb3JtYXREaXN0YW5jZSIsImZvcm1hdExvbmciLCJmb3JtYXRSZWxhdGl2ZSIsImxvY2FsaXplIiwibWF0Y2giLCJzayIsImNvZGUiLCJvcHRpb25zIiwid2Vla1N0YXJ0c09uIiwiZmlyc3RXZWVrQ29udGFpbnNEYXRlIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatDistance.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatDistance.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nfunction declensionGroup(scheme, count) {\n    if (count === 1 && scheme.one) {\n        return scheme.one;\n    }\n    if (count >= 2 && count <= 4 && scheme.twoFour) {\n        return scheme.twoFour;\n    }\n    // if count === null || count === 0 || count >= 5\n    return scheme.other;\n}\nfunction declension(scheme, count, time) {\n    const group = declensionGroup(scheme, count);\n    const finalText = group[time];\n    return finalText.replace(\"{{count}}\", String(count));\n}\nfunction extractPreposition(token) {\n    const result = [\n        \"lessThan\",\n        \"about\",\n        \"over\",\n        \"almost\"\n    ].filter(function(preposition) {\n        return !!token.match(new RegExp(\"^\" + preposition));\n    });\n    return result[0];\n}\nfunction prefixPreposition(preposition) {\n    let translation = \"\";\n    if (preposition === \"almost\") {\n        translation = \"takmer\";\n    }\n    if (preposition === \"about\") {\n        translation = \"približne\";\n    }\n    return translation.length > 0 ? translation + \" \" : \"\";\n}\nfunction suffixPreposition(preposition) {\n    let translation = \"\";\n    if (preposition === \"lessThan\") {\n        translation = \"menej než\";\n    }\n    if (preposition === \"over\") {\n        translation = \"viac než\";\n    }\n    return translation.length > 0 ? translation + \" \" : \"\";\n}\nfunction lowercaseFirstLetter(string) {\n    return string.charAt(0).toLowerCase() + string.slice(1);\n}\nconst formatDistanceLocale = {\n    xSeconds: {\n        one: {\n            present: \"sekunda\",\n            past: \"sekundou\",\n            future: \"sekundu\"\n        },\n        twoFour: {\n            present: \"{{count}} sekundy\",\n            past: \"{{count}} sekundami\",\n            future: \"{{count}} sekundy\"\n        },\n        other: {\n            present: \"{{count}} sekúnd\",\n            past: \"{{count}} sekundami\",\n            future: \"{{count}} sekúnd\"\n        }\n    },\n    halfAMinute: {\n        other: {\n            present: \"pol minúty\",\n            past: \"pol minútou\",\n            future: \"pol minúty\"\n        }\n    },\n    xMinutes: {\n        one: {\n            present: \"minúta\",\n            past: \"minútou\",\n            future: \"minútu\"\n        },\n        twoFour: {\n            present: \"{{count}} minúty\",\n            past: \"{{count}} minútami\",\n            future: \"{{count}} minúty\"\n        },\n        other: {\n            present: \"{{count}} minút\",\n            past: \"{{count}} minútami\",\n            future: \"{{count}} minút\"\n        }\n    },\n    xHours: {\n        one: {\n            present: \"hodina\",\n            past: \"hodinou\",\n            future: \"hodinu\"\n        },\n        twoFour: {\n            present: \"{{count}} hodiny\",\n            past: \"{{count}} hodinami\",\n            future: \"{{count}} hodiny\"\n        },\n        other: {\n            present: \"{{count}} hodín\",\n            past: \"{{count}} hodinami\",\n            future: \"{{count}} hodín\"\n        }\n    },\n    xDays: {\n        one: {\n            present: \"deň\",\n            past: \"dňom\",\n            future: \"deň\"\n        },\n        twoFour: {\n            present: \"{{count}} dni\",\n            past: \"{{count}} dňami\",\n            future: \"{{count}} dni\"\n        },\n        other: {\n            present: \"{{count}} dní\",\n            past: \"{{count}} dňami\",\n            future: \"{{count}} dní\"\n        }\n    },\n    xWeeks: {\n        one: {\n            present: \"týždeň\",\n            past: \"týždňom\",\n            future: \"týždeň\"\n        },\n        twoFour: {\n            present: \"{{count}} týždne\",\n            past: \"{{count}} týždňami\",\n            future: \"{{count}} týždne\"\n        },\n        other: {\n            present: \"{{count}} týždňov\",\n            past: \"{{count}} týždňami\",\n            future: \"{{count}} týždňov\"\n        }\n    },\n    xMonths: {\n        one: {\n            present: \"mesiac\",\n            past: \"mesiacom\",\n            future: \"mesiac\"\n        },\n        twoFour: {\n            present: \"{{count}} mesiace\",\n            past: \"{{count}} mesiacmi\",\n            future: \"{{count}} mesiace\"\n        },\n        other: {\n            present: \"{{count}} mesiacov\",\n            past: \"{{count}} mesiacmi\",\n            future: \"{{count}} mesiacov\"\n        }\n    },\n    xYears: {\n        one: {\n            present: \"rok\",\n            past: \"rokom\",\n            future: \"rok\"\n        },\n        twoFour: {\n            present: \"{{count}} roky\",\n            past: \"{{count}} rokmi\",\n            future: \"{{count}} roky\"\n        },\n        other: {\n            present: \"{{count}} rokov\",\n            past: \"{{count}} rokmi\",\n            future: \"{{count}} rokov\"\n        }\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    const preposition = extractPreposition(token) || \"\";\n    const key = lowercaseFirstLetter(token.substring(preposition.length));\n    const scheme = formatDistanceLocale[key];\n    if (!(options === null || options === void 0 ? void 0 : options.addSuffix)) {\n        return prefixPreposition(preposition) + suffixPreposition(preposition) + declension(scheme, count, \"present\");\n    }\n    if (options.comparison && options.comparison > 0) {\n        return prefixPreposition(preposition) + \"o \" + suffixPreposition(preposition) + declension(scheme, count, \"future\");\n    } else {\n        return prefixPreposition(preposition) + \"pred \" + suffixPreposition(preposition) + declension(scheme, count, \"past\");\n    }\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatLong.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatLong.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1986\nconst dateFormats = {\n    full: \"EEEE d. MMMM y\",\n    long: \"d. MMMM y\",\n    medium: \"d. M. y\",\n    short: \"d. M. y\"\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#2149\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1994\nconst dateTimeFormats = {\n    full: \"{{date}}, {{time}}\",\n    long: \"{{date}}, {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatRelative.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatRelative.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\");\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html?hide#1308\nconst accusativeWeekdays = [\n    \"nedeľu\",\n    \"pondelok\",\n    \"utorok\",\n    \"stredu\",\n    \"štvrtok\",\n    \"piatok\",\n    \"sobotu\"\n];\nfunction lastWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        /* Sun */ case 3:\n        /* Wed */ case 6 /* Sat */ :\n            return \"'minulú \" + weekday + \" o' p\";\n        default:\n            return \"'minulý' eeee 'o' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    if (day === 4 /* Thu */ ) {\n        return \"'vo' eeee 'o' p\";\n    } else {\n        return \"'v \" + weekday + \" o' p\";\n    }\n}\nfunction nextWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        /* Sun */ case 4:\n        /* Wed */ case 6 /* Sat */ :\n            return \"'budúcu \" + weekday + \" o' p\";\n        default:\n            return \"'budúci' eeee 'o' p\";\n    }\n}\nconst formatRelativeLocale = {\n    lastWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return lastWeek(day);\n        }\n    },\n    yesterday: \"'včera o' p\",\n    today: \"'dnes o' p\",\n    tomorrow: \"'zajtra o' p\",\n    nextWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return nextWeek(day);\n        }\n    },\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/localize.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/localize.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1772\nconst eraValues = {\n    narrow: [\n        \"pred Kr.\",\n        \"po Kr.\"\n    ],\n    abbreviated: [\n        \"pred Kr.\",\n        \"po Kr.\"\n    ],\n    wide: [\n        \"pred Kristom\",\n        \"po Kristovi\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1780\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"Q1\",\n        \"Q2\",\n        \"Q3\",\n        \"Q4\"\n    ],\n    wide: [\n        \"1. štvrťrok\",\n        \"2. štvrťrok\",\n        \"3. štvrťrok\",\n        \"4. štvrťrok\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1804\nconst monthValues = {\n    narrow: [\n        \"j\",\n        \"f\",\n        \"m\",\n        \"a\",\n        \"m\",\n        \"j\",\n        \"j\",\n        \"a\",\n        \"s\",\n        \"o\",\n        \"n\",\n        \"d\"\n    ],\n    abbreviated: [\n        \"jan\",\n        \"feb\",\n        \"mar\",\n        \"apr\",\n        \"máj\",\n        \"jún\",\n        \"júl\",\n        \"aug\",\n        \"sep\",\n        \"okt\",\n        \"nov\",\n        \"dec\"\n    ],\n    wide: [\n        \"január\",\n        \"február\",\n        \"marec\",\n        \"apríl\",\n        \"máj\",\n        \"jún\",\n        \"júl\",\n        \"august\",\n        \"september\",\n        \"október\",\n        \"november\",\n        \"december\"\n    ]\n};\nconst formattingMonthValues = {\n    narrow: [\n        \"j\",\n        \"f\",\n        \"m\",\n        \"a\",\n        \"m\",\n        \"j\",\n        \"j\",\n        \"a\",\n        \"s\",\n        \"o\",\n        \"n\",\n        \"d\"\n    ],\n    abbreviated: [\n        \"jan\",\n        \"feb\",\n        \"mar\",\n        \"apr\",\n        \"máj\",\n        \"jún\",\n        \"júl\",\n        \"aug\",\n        \"sep\",\n        \"okt\",\n        \"nov\",\n        \"dec\"\n    ],\n    wide: [\n        \"januára\",\n        \"februára\",\n        \"marca\",\n        \"apríla\",\n        \"mája\",\n        \"júna\",\n        \"júla\",\n        \"augusta\",\n        \"septembra\",\n        \"októbra\",\n        \"novembra\",\n        \"decembra\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1876\nconst dayValues = {\n    narrow: [\n        \"n\",\n        \"p\",\n        \"u\",\n        \"s\",\n        \"š\",\n        \"p\",\n        \"s\"\n    ],\n    short: [\n        \"ne\",\n        \"po\",\n        \"ut\",\n        \"st\",\n        \"št\",\n        \"pi\",\n        \"so\"\n    ],\n    abbreviated: [\n        \"ne\",\n        \"po\",\n        \"ut\",\n        \"st\",\n        \"št\",\n        \"pi\",\n        \"so\"\n    ],\n    wide: [\n        \"nedeľa\",\n        \"pondelok\",\n        \"utorok\",\n        \"streda\",\n        \"štvrtok\",\n        \"piatok\",\n        \"sobota\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/sk.html#1932\nconst dayPeriodValues = {\n    narrow: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"poln.\",\n        noon: \"pol.\",\n        morning: \"ráno\",\n        afternoon: \"pop.\",\n        evening: \"več.\",\n        night: \"noc\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"poln.\",\n        noon: \"pol.\",\n        morning: \"ráno\",\n        afternoon: \"popol.\",\n        evening: \"večer\",\n        night: \"noc\"\n    },\n    wide: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"polnoc\",\n        noon: \"poludnie\",\n        morning: \"ráno\",\n        afternoon: \"popoludnie\",\n        evening: \"večer\",\n        night: \"noc\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o poln.\",\n        noon: \"nap.\",\n        morning: \"ráno\",\n        afternoon: \"pop.\",\n        evening: \"več.\",\n        night: \"v n.\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o poln.\",\n        noon: \"napol.\",\n        morning: \"ráno\",\n        afternoon: \"popol.\",\n        evening: \"večer\",\n        night: \"v noci\"\n    },\n    wide: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"o polnoci\",\n        noon: \"napoludnie\",\n        morning: \"ráno\",\n        afternoon: \"popoludní\",\n        evening: \"večer\",\n        night: \"v noci\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    return number + \".\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingMonthValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/match.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/match.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)\\.?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(pred Kr\\.|pred n\\. l\\.|po Kr\\.|n\\. l\\.)/i,\n    abbreviated: /^(pred Kr\\.|pred n\\. l\\.|po Kr\\.|n\\. l\\.)/i,\n    wide: /^(pred Kristom|pred na[šs][íi]m letopo[čc]tom|po Kristovi|n[áa][šs]ho letopo[čc]tu)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^pr/i,\n        /^(po|n)/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^q[1234]/i,\n    wide: /^[1234]\\. [šs]tvr[ťt]rok/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[jfmasond]/i,\n    abbreviated: /^(jan|feb|mar|apr|m[áa]j|j[úu]n|j[úu]l|aug|sep|okt|nov|dec)/i,\n    wide: /^(janu[áa]ra?|febru[áa]ra?|(marec|marca)|apr[íi]la?|m[áa]ja?|j[úu]na?|j[úu]la?|augusta?|(september|septembra)|(okt[óo]ber|okt[óo]bra)|(november|novembra)|(december|decembra))/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^j/i,\n        /^f/i,\n        /^m/i,\n        /^a/i,\n        /^m/i,\n        /^j/i,\n        /^j/i,\n        /^a/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ],\n    any: [\n        /^ja/i,\n        /^f/i,\n        /^mar/i,\n        /^ap/i,\n        /^m[áa]j/i,\n        /^j[úu]n/i,\n        /^j[úu]l/i,\n        /^au/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[npusšp]/i,\n    short: /^(ne|po|ut|st|št|pi|so)/i,\n    abbreviated: /^(ne|po|ut|st|št|pi|so)/i,\n    wide: /^(nede[ľl]a|pondelok|utorok|streda|[šs]tvrtok|piatok|sobota])/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^n/i,\n        /^p/i,\n        /^u/i,\n        /^s/i,\n        /^š/i,\n        /^p/i,\n        /^s/i\n    ],\n    any: [\n        /^n/i,\n        /^po/i,\n        /^u/i,\n        /^st/i,\n        /^(št|stv)/i,\n        /^pi/i,\n        /^so/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(am|pm|(o )?poln\\.?|(nap\\.?|pol\\.?)|r[áa]no|pop\\.?|ve[čc]\\.?|(v n\\.?|noc))/i,\n    abbreviated: /^(am|pm|(o )?poln\\.?|(napol\\.?|pol\\.?)|r[áa]no|pop\\.?|ve[čc]er|(v )?noci?)/i,\n    any: /^(am|pm|(o )?polnoci?|(na)?poludnie|r[áa]no|popoludn(ie|í|i)|ve[čc]er|(v )?noci?)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^am/i,\n        pm: /^pm/i,\n        midnight: /poln/i,\n        noon: /^(nap|(na)?pol(\\.|u))/i,\n        morning: /^r[áa]no/i,\n        afternoon: /^pop/i,\n        evening: /^ve[čc]/i,\n        night: /^(noc|v n\\.)/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/sk/_lib/match.js\n"));

/***/ })

}]);