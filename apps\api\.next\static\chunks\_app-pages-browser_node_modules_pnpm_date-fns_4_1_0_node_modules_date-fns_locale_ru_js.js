"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_ru_js"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   ru: () => (/* binding */ ru)\n/* harmony export */ });\n/* harmony import */ var _ru_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ru/_lib/formatDistance.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatDistance.js\");\n/* harmony import */ var _ru_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ru/_lib/formatLong.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatLong.js\");\n/* harmony import */ var _ru_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ru/_lib/formatRelative.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatRelative.js\");\n/* harmony import */ var _ru_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ru/_lib/localize.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/localize.js\");\n/* harmony import */ var _ru_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ru/_lib/match.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Russian locale.\n * @language Russian\n * @iso-639-2 rus\n * <AUTHOR> Koss [@kossnocorp](https://github.com/kossnocorp)\n * <AUTHOR> Koss [@leshakoss](https://github.com/leshakoss)\n */ const ru = {\n    code: \"ru\",\n    formatDistance: _ru_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _ru_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _ru_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _ru_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _ru_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ru);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatDistance.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatDistance.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nfunction declension(scheme, count) {\n    // scheme for count=1 exists\n    if (scheme.one !== undefined && count === 1) {\n        return scheme.one;\n    }\n    const rem10 = count % 10;\n    const rem100 = count % 100;\n    // 1, 21, 31, ...\n    if (rem10 === 1 && rem100 !== 11) {\n        return scheme.singularNominative.replace(\"{{count}}\", String(count));\n    // 2, 3, 4, 22, 23, 24, 32 ...\n    } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n        return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n    // 5, 6, 7, 8, 9, 10, 11, ...\n    } else {\n        return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n    }\n}\nfunction buildLocalizeTokenFn(scheme) {\n    return (count, options)=>{\n        if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n            if (options.comparison && options.comparison > 0) {\n                if (scheme.future) {\n                    return declension(scheme.future, count);\n                } else {\n                    return \"через \" + declension(scheme.regular, count);\n                }\n            } else {\n                if (scheme.past) {\n                    return declension(scheme.past, count);\n                } else {\n                    return declension(scheme.regular, count) + \" назад\";\n                }\n            }\n        } else {\n            return declension(scheme.regular, count);\n        }\n    };\n}\nconst formatDistanceLocale = {\n    lessThanXSeconds: buildLocalizeTokenFn({\n        regular: {\n            one: \"меньше секунды\",\n            singularNominative: \"меньше {{count}} секунды\",\n            singularGenitive: \"меньше {{count}} секунд\",\n            pluralGenitive: \"меньше {{count}} секунд\"\n        },\n        future: {\n            one: \"меньше, чем через секунду\",\n            singularNominative: \"меньше, чем через {{count}} секунду\",\n            singularGenitive: \"меньше, чем через {{count}} секунды\",\n            pluralGenitive: \"меньше, чем через {{count}} секунд\"\n        }\n    }),\n    xSeconds: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} секунда\",\n            singularGenitive: \"{{count}} секунды\",\n            pluralGenitive: \"{{count}} секунд\"\n        },\n        past: {\n            singularNominative: \"{{count}} секунду назад\",\n            singularGenitive: \"{{count}} секунды назад\",\n            pluralGenitive: \"{{count}} секунд назад\"\n        },\n        future: {\n            singularNominative: \"через {{count}} секунду\",\n            singularGenitive: \"через {{count}} секунды\",\n            pluralGenitive: \"через {{count}} секунд\"\n        }\n    }),\n    halfAMinute: (_count, options)=>{\n        if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n            if (options.comparison && options.comparison > 0) {\n                return \"через полминуты\";\n            } else {\n                return \"полминуты назад\";\n            }\n        }\n        return \"полминуты\";\n    },\n    lessThanXMinutes: buildLocalizeTokenFn({\n        regular: {\n            one: \"меньше минуты\",\n            singularNominative: \"меньше {{count}} минуты\",\n            singularGenitive: \"меньше {{count}} минут\",\n            pluralGenitive: \"меньше {{count}} минут\"\n        },\n        future: {\n            one: \"меньше, чем через минуту\",\n            singularNominative: \"меньше, чем через {{count}} минуту\",\n            singularGenitive: \"меньше, чем через {{count}} минуты\",\n            pluralGenitive: \"меньше, чем через {{count}} минут\"\n        }\n    }),\n    xMinutes: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} минута\",\n            singularGenitive: \"{{count}} минуты\",\n            pluralGenitive: \"{{count}} минут\"\n        },\n        past: {\n            singularNominative: \"{{count}} минуту назад\",\n            singularGenitive: \"{{count}} минуты назад\",\n            pluralGenitive: \"{{count}} минут назад\"\n        },\n        future: {\n            singularNominative: \"через {{count}} минуту\",\n            singularGenitive: \"через {{count}} минуты\",\n            pluralGenitive: \"через {{count}} минут\"\n        }\n    }),\n    aboutXHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} часа\",\n            singularGenitive: \"около {{count}} часов\",\n            pluralGenitive: \"около {{count}} часов\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} час\",\n            singularGenitive: \"приблизительно через {{count}} часа\",\n            pluralGenitive: \"приблизительно через {{count}} часов\"\n        }\n    }),\n    xHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} час\",\n            singularGenitive: \"{{count}} часа\",\n            pluralGenitive: \"{{count}} часов\"\n        }\n    }),\n    xDays: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} день\",\n            singularGenitive: \"{{count}} дня\",\n            pluralGenitive: \"{{count}} дней\"\n        }\n    }),\n    aboutXWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} недели\",\n            singularGenitive: \"около {{count}} недель\",\n            pluralGenitive: \"около {{count}} недель\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} неделю\",\n            singularGenitive: \"приблизительно через {{count}} недели\",\n            pluralGenitive: \"приблизительно через {{count}} недель\"\n        }\n    }),\n    xWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} неделя\",\n            singularGenitive: \"{{count}} недели\",\n            pluralGenitive: \"{{count}} недель\"\n        }\n    }),\n    aboutXMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} месяца\",\n            singularGenitive: \"около {{count}} месяцев\",\n            pluralGenitive: \"около {{count}} месяцев\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} месяц\",\n            singularGenitive: \"приблизительно через {{count}} месяца\",\n            pluralGenitive: \"приблизительно через {{count}} месяцев\"\n        }\n    }),\n    xMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} месяц\",\n            singularGenitive: \"{{count}} месяца\",\n            pluralGenitive: \"{{count}} месяцев\"\n        }\n    }),\n    aboutXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"около {{count}} года\",\n            singularGenitive: \"около {{count}} лет\",\n            pluralGenitive: \"около {{count}} лет\"\n        },\n        future: {\n            singularNominative: \"приблизительно через {{count}} год\",\n            singularGenitive: \"приблизительно через {{count}} года\",\n            pluralGenitive: \"приблизительно через {{count}} лет\"\n        }\n    }),\n    xYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} год\",\n            singularGenitive: \"{{count}} года\",\n            pluralGenitive: \"{{count}} лет\"\n        }\n    }),\n    overXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"больше {{count}} года\",\n            singularGenitive: \"больше {{count}} лет\",\n            pluralGenitive: \"больше {{count}} лет\"\n        },\n        future: {\n            singularNominative: \"больше, чем через {{count}} год\",\n            singularGenitive: \"больше, чем через {{count}} года\",\n            pluralGenitive: \"больше, чем через {{count}} лет\"\n        }\n    }),\n    almostXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"почти {{count}} год\",\n            singularGenitive: \"почти {{count}} года\",\n            pluralGenitive: \"почти {{count}} лет\"\n        },\n        future: {\n            singularNominative: \"почти через {{count}} год\",\n            singularGenitive: \"почти через {{count}} года\",\n            pluralGenitive: \"почти через {{count}} лет\"\n        }\n    })\n};\nconst formatDistance = (token, count, options)=>{\n    return formatDistanceLocale[token](count, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatLong.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatLong.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, d MMMM y 'г.'\",\n    long: \"d MMMM y 'г.'\",\n    medium: \"d MMM y 'г.'\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    any: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatRelative.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatRelative.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\");\n\nconst accusativeWeekdays = [\n    \"воскресенье\",\n    \"понедельник\",\n    \"вторник\",\n    \"среду\",\n    \"четверг\",\n    \"пятницу\",\n    \"субботу\"\n];\nfunction lastWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n            return \"'в прошлое \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'в прошлый \" + weekday + \" в' p\";\n        case 3:\n        case 5:\n        case 6:\n            return \"'в прошлую \" + weekday + \" в' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    if (day === 2 /* Tue */ ) {\n        return \"'во \" + weekday + \" в' p\";\n    } else {\n        return \"'в \" + weekday + \" в' p\";\n    }\n}\nfunction nextWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n            return \"'в следующее \" + weekday + \" в' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'в следующий \" + weekday + \" в' p\";\n        case 3:\n        case 5:\n        case 6:\n            return \"'в следующую \" + weekday + \" в' p\";\n    }\n}\nconst formatRelativeLocale = {\n    lastWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return lastWeek(day);\n        }\n    },\n    yesterday: \"'вчера в' p\",\n    today: \"'сегодня в' p\",\n    tomorrow: \"'завтра в' p\",\n    nextWeek: (date, baseDate, options)=>{\n        const day = date.getDay();\n        if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_0__.isSameWeek)(date, baseDate, options)) {\n            return thisWeek(day);\n        } else {\n            return nextWeek(day);\n        }\n    },\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/localize.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/localize.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"до н.э.\",\n        \"н.э.\"\n    ],\n    abbreviated: [\n        \"до н. э.\",\n        \"н. э.\"\n    ],\n    wide: [\n        \"до нашей эры\",\n        \"нашей эры\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1-й кв.\",\n        \"2-й кв.\",\n        \"3-й кв.\",\n        \"4-й кв.\"\n    ],\n    wide: [\n        \"1-й квартал\",\n        \"2-й квартал\",\n        \"3-й квартал\",\n        \"4-й квартал\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"Я\",\n        \"Ф\",\n        \"М\",\n        \"А\",\n        \"М\",\n        \"И\",\n        \"И\",\n        \"А\",\n        \"С\",\n        \"О\",\n        \"Н\",\n        \"Д\"\n    ],\n    abbreviated: [\n        \"янв.\",\n        \"фев.\",\n        \"март\",\n        \"апр.\",\n        \"май\",\n        \"июнь\",\n        \"июль\",\n        \"авг.\",\n        \"сент.\",\n        \"окт.\",\n        \"нояб.\",\n        \"дек.\"\n    ],\n    wide: [\n        \"январь\",\n        \"февраль\",\n        \"март\",\n        \"апрель\",\n        \"май\",\n        \"июнь\",\n        \"июль\",\n        \"август\",\n        \"сентябрь\",\n        \"октябрь\",\n        \"ноябрь\",\n        \"декабрь\"\n    ]\n};\nconst formattingMonthValues = {\n    narrow: [\n        \"Я\",\n        \"Ф\",\n        \"М\",\n        \"А\",\n        \"М\",\n        \"И\",\n        \"И\",\n        \"А\",\n        \"С\",\n        \"О\",\n        \"Н\",\n        \"Д\"\n    ],\n    abbreviated: [\n        \"янв.\",\n        \"фев.\",\n        \"мар.\",\n        \"апр.\",\n        \"мая\",\n        \"июн.\",\n        \"июл.\",\n        \"авг.\",\n        \"сент.\",\n        \"окт.\",\n        \"нояб.\",\n        \"дек.\"\n    ],\n    wide: [\n        \"января\",\n        \"февраля\",\n        \"марта\",\n        \"апреля\",\n        \"мая\",\n        \"июня\",\n        \"июля\",\n        \"августа\",\n        \"сентября\",\n        \"октября\",\n        \"ноября\",\n        \"декабря\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"В\",\n        \"П\",\n        \"В\",\n        \"С\",\n        \"Ч\",\n        \"П\",\n        \"С\"\n    ],\n    short: [\n        \"вс\",\n        \"пн\",\n        \"вт\",\n        \"ср\",\n        \"чт\",\n        \"пт\",\n        \"сб\"\n    ],\n    abbreviated: [\n        \"вск\",\n        \"пнд\",\n        \"втр\",\n        \"срд\",\n        \"чтв\",\n        \"птн\",\n        \"суб\"\n    ],\n    wide: [\n        \"воскресенье\",\n        \"понедельник\",\n        \"вторник\",\n        \"среда\",\n        \"четверг\",\n        \"пятница\",\n        \"суббота\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утро\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ночь\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утро\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ночь\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полночь\",\n        noon: \"полдень\",\n        morning: \"утро\",\n        afternoon: \"день\",\n        evening: \"вечер\",\n        night: \"ночь\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утра\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночи\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полн.\",\n        noon: \"полд.\",\n        morning: \"утра\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночи\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"полночь\",\n        noon: \"полдень\",\n        morning: \"утра\",\n        afternoon: \"дня\",\n        evening: \"вечера\",\n        night: \"ночи\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, options)=>{\n    const number = Number(dirtyNumber);\n    const unit = options === null || options === void 0 ? void 0 : options.unit;\n    let suffix;\n    if (unit === \"date\") {\n        suffix = \"-е\";\n    } else if (unit === \"week\" || unit === \"minute\" || unit === \"second\") {\n        suffix = \"-я\";\n    } else {\n        suffix = \"-й\";\n    }\n    return number + suffix;\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingMonthValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"any\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS9ydS9fbGliL2xvY2FsaXplLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdFO0FBRWhFLE1BQU1DLFlBQVk7SUFDaEJDLFFBQVE7UUFBQztRQUFXO0tBQU87SUFDM0JDLGFBQWE7UUFBQztRQUFZO0tBQVE7SUFDbENDLE1BQU07UUFBQztRQUFnQjtLQUFZO0FBQ3JDO0FBRUEsTUFBTUMsZ0JBQWdCO0lBQ3BCSCxRQUFRO1FBQUM7UUFBSztRQUFLO1FBQUs7S0FBSTtJQUM1QkMsYUFBYTtRQUFDO1FBQVc7UUFBVztRQUFXO0tBQVU7SUFDekRDLE1BQU07UUFBQztRQUFlO1FBQWU7UUFBZTtLQUFjO0FBQ3BFO0FBRUEsTUFBTUUsY0FBYztJQUNsQkosUUFBUTtRQUFDO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztLQUFJO0lBQ3BFQyxhQUFhO1FBQ1g7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7SUFFREMsTUFBTTtRQUNKO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0FBQ0g7QUFFQSxNQUFNRyx3QkFBd0I7SUFDNUJMLFFBQVE7UUFBQztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7S0FBSTtJQUNwRUMsYUFBYTtRQUNYO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRURDLE1BQU07UUFDSjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtBQUNIO0FBRUEsTUFBTUksWUFBWTtJQUNoQk4sUUFBUTtRQUFDO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO0tBQUk7SUFDM0NPLE9BQU87UUFBQztRQUFNO1FBQU07UUFBTTtRQUFNO1FBQU07UUFBTTtLQUFLO0lBQ2pETixhQUFhO1FBQUM7UUFBTztRQUFPO1FBQU87UUFBTztRQUFPO1FBQU87S0FBTTtJQUM5REMsTUFBTTtRQUNKO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7QUFDSDtBQUVBLE1BQU1NLGtCQUFrQjtJQUN0QlIsUUFBUTtRQUNOUyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtJQUNBZixhQUFhO1FBQ1hRLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsT0FBTztJQUNUO0lBQ0FkLE1BQU07UUFDSk8sSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxPQUFPO0lBQ1Q7QUFDRjtBQUVBLE1BQU1DLDRCQUE0QjtJQUNoQ2pCLFFBQVE7UUFDTlMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxPQUFPO0lBQ1Q7SUFDQWYsYUFBYTtRQUNYUSxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtJQUNBZCxNQUFNO1FBQ0pPLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsT0FBTztJQUNUO0FBQ0Y7QUFFQSxNQUFNRSxnQkFBZ0IsQ0FBQ0MsYUFBYUM7SUFDbEMsTUFBTUMsU0FBU0MsT0FBT0g7SUFDdEIsTUFBTUksT0FBT0gsb0JBQUFBLDhCQUFBQSxRQUFTRyxJQUFJO0lBRTFCLElBQUlDO0lBQ0osSUFBSUQsU0FBUyxRQUFRO1FBQ25CQyxTQUFTO0lBQ1gsT0FBTyxJQUFJRCxTQUFTLFVBQVVBLFNBQVMsWUFBWUEsU0FBUyxVQUFVO1FBQ3BFQyxTQUFTO0lBQ1gsT0FBTztRQUNMQSxTQUFTO0lBQ1g7SUFFQSxPQUFPSCxTQUFTRztBQUNsQjtBQUVPLE1BQU1DLFdBQVc7SUFDdEJQO0lBRUFRLEtBQUs1Qix3RUFBZUEsQ0FBQztRQUNuQjZCLFFBQVE1QjtRQUNSNkIsY0FBYztJQUNoQjtJQUVBQyxTQUFTL0Isd0VBQWVBLENBQUM7UUFDdkI2QixRQUFReEI7UUFDUnlCLGNBQWM7UUFDZEUsa0JBQWtCLENBQUNELFVBQVlBLFVBQVU7SUFDM0M7SUFFQUUsT0FBT2pDLHdFQUFlQSxDQUFDO1FBQ3JCNkIsUUFBUXZCO1FBQ1J3QixjQUFjO1FBQ2RJLGtCQUFrQjNCO1FBQ2xCNEIsd0JBQXdCO0lBQzFCO0lBRUFDLEtBQUtwQyx3RUFBZUEsQ0FBQztRQUNuQjZCLFFBQVFyQjtRQUNSc0IsY0FBYztJQUNoQjtJQUVBTyxXQUFXckMsd0VBQWVBLENBQUM7UUFDekI2QixRQUFRbkI7UUFDUm9CLGNBQWM7UUFDZEksa0JBQWtCZjtRQUNsQmdCLHdCQUF3QjtJQUMxQjtBQUNGLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxwcm9qZWN0c1xcbG1zXFxub2RlX21vZHVsZXNcXC5wbnBtXFxkYXRlLWZuc0A0LjEuMFxcbm9kZV9tb2R1bGVzXFxkYXRlLWZuc1xcbG9jYWxlXFxydVxcX2xpYlxcbG9jYWxpemUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgYnVpbGRMb2NhbGl6ZUZuIH0gZnJvbSBcIi4uLy4uL19saWIvYnVpbGRMb2NhbGl6ZUZuLmpzXCI7XG5cbmNvbnN0IGVyYVZhbHVlcyA9IHtcbiAgbmFycm93OiBbXCLQtNC+INC9LtGNLlwiLCBcItC9LtGNLlwiXSxcbiAgYWJicmV2aWF0ZWQ6IFtcItC00L4g0L0uINGNLlwiLCBcItC9LiDRjS5cIl0sXG4gIHdpZGU6IFtcItC00L4g0L3QsNGI0LXQuSDRjdGA0YtcIiwgXCLQvdCw0YjQtdC5INGN0YDRi1wiXSxcbn07XG5cbmNvbnN0IHF1YXJ0ZXJWYWx1ZXMgPSB7XG4gIG5hcnJvdzogW1wiMVwiLCBcIjJcIiwgXCIzXCIsIFwiNFwiXSxcbiAgYWJicmV2aWF0ZWQ6IFtcIjEt0Lkg0LrQsi5cIiwgXCIyLdC5INC60LIuXCIsIFwiMy3QuSDQutCyLlwiLCBcIjQt0Lkg0LrQsi5cIl0sXG4gIHdpZGU6IFtcIjEt0Lkg0LrQstCw0YDRgtCw0LtcIiwgXCIyLdC5INC60LLQsNGA0YLQsNC7XCIsIFwiMy3QuSDQutCy0LDRgNGC0LDQu1wiLCBcIjQt0Lkg0LrQstCw0YDRgtCw0LtcIl0sXG59O1xuXG5jb25zdCBtb250aFZhbHVlcyA9IHtcbiAgbmFycm93OiBbXCLQr1wiLCBcItCkXCIsIFwi0JxcIiwgXCLQkFwiLCBcItCcXCIsIFwi0JhcIiwgXCLQmFwiLCBcItCQXCIsIFwi0KFcIiwgXCLQnlwiLCBcItCdXCIsIFwi0JRcIl0sXG4gIGFiYnJldmlhdGVkOiBbXG4gICAgXCLRj9C90LIuXCIsXG4gICAgXCLRhNC10LIuXCIsXG4gICAgXCLQvNCw0YDRglwiLFxuICAgIFwi0LDQv9GALlwiLFxuICAgIFwi0LzQsNC5XCIsXG4gICAgXCLQuNGO0L3RjFwiLFxuICAgIFwi0LjRjtC70YxcIixcbiAgICBcItCw0LLQsy5cIixcbiAgICBcItGB0LXQvdGCLlwiLFxuICAgIFwi0L7QutGCLlwiLFxuICAgIFwi0L3QvtGP0LEuXCIsXG4gICAgXCLQtNC10LouXCIsXG4gIF0sXG5cbiAgd2lkZTogW1xuICAgIFwi0Y/QvdCy0LDRgNGMXCIsXG4gICAgXCLRhNC10LLRgNCw0LvRjFwiLFxuICAgIFwi0LzQsNGA0YJcIixcbiAgICBcItCw0L/RgNC10LvRjFwiLFxuICAgIFwi0LzQsNC5XCIsXG4gICAgXCLQuNGO0L3RjFwiLFxuICAgIFwi0LjRjtC70YxcIixcbiAgICBcItCw0LLQs9GD0YHRglwiLFxuICAgIFwi0YHQtdC90YLRj9Cx0YDRjFwiLFxuICAgIFwi0L7QutGC0Y/QsdGA0YxcIixcbiAgICBcItC90L7Rj9Cx0YDRjFwiLFxuICAgIFwi0LTQtdC60LDQsdGA0YxcIixcbiAgXSxcbn07XG5cbmNvbnN0IGZvcm1hdHRpbmdNb250aFZhbHVlcyA9IHtcbiAgbmFycm93OiBbXCLQr1wiLCBcItCkXCIsIFwi0JxcIiwgXCLQkFwiLCBcItCcXCIsIFwi0JhcIiwgXCLQmFwiLCBcItCQXCIsIFwi0KFcIiwgXCLQnlwiLCBcItCdXCIsIFwi0JRcIl0sXG4gIGFiYnJldmlhdGVkOiBbXG4gICAgXCLRj9C90LIuXCIsXG4gICAgXCLRhNC10LIuXCIsXG4gICAgXCLQvNCw0YAuXCIsXG4gICAgXCLQsNC/0YAuXCIsXG4gICAgXCLQvNCw0Y9cIixcbiAgICBcItC40Y7QvS5cIixcbiAgICBcItC40Y7Quy5cIixcbiAgICBcItCw0LLQsy5cIixcbiAgICBcItGB0LXQvdGCLlwiLFxuICAgIFwi0L7QutGCLlwiLFxuICAgIFwi0L3QvtGP0LEuXCIsXG4gICAgXCLQtNC10LouXCIsXG4gIF0sXG5cbiAgd2lkZTogW1xuICAgIFwi0Y/QvdCy0LDRgNGPXCIsXG4gICAgXCLRhNC10LLRgNCw0LvRj1wiLFxuICAgIFwi0LzQsNGA0YLQsFwiLFxuICAgIFwi0LDQv9GA0LXQu9GPXCIsXG4gICAgXCLQvNCw0Y9cIixcbiAgICBcItC40Y7QvdGPXCIsXG4gICAgXCLQuNGO0LvRj1wiLFxuICAgIFwi0LDQstCz0YPRgdGC0LBcIixcbiAgICBcItGB0LXQvdGC0Y/QsdGA0Y9cIixcbiAgICBcItC+0LrRgtGP0LHRgNGPXCIsXG4gICAgXCLQvdC+0Y/QsdGA0Y9cIixcbiAgICBcItC00LXQutCw0LHRgNGPXCIsXG4gIF0sXG59O1xuXG5jb25zdCBkYXlWYWx1ZXMgPSB7XG4gIG5hcnJvdzogW1wi0JJcIiwgXCLQn1wiLCBcItCSXCIsIFwi0KFcIiwgXCLQp1wiLCBcItCfXCIsIFwi0KFcIl0sXG4gIHNob3J0OiBbXCLQstGBXCIsIFwi0L/QvVwiLCBcItCy0YJcIiwgXCLRgdGAXCIsIFwi0YfRglwiLCBcItC/0YJcIiwgXCLRgdCxXCJdLFxuICBhYmJyZXZpYXRlZDogW1wi0LLRgdC6XCIsIFwi0L/QvdC0XCIsIFwi0LLRgtGAXCIsIFwi0YHRgNC0XCIsIFwi0YfRgtCyXCIsIFwi0L/RgtC9XCIsIFwi0YHRg9CxXCJdLFxuICB3aWRlOiBbXG4gICAgXCLQstC+0YHQutGA0LXRgdC10L3RjNC1XCIsXG4gICAgXCLQv9C+0L3QtdC00LXQu9GM0L3QuNC6XCIsXG4gICAgXCLQstGC0L7RgNC90LjQulwiLFxuICAgIFwi0YHRgNC10LTQsFwiLFxuICAgIFwi0YfQtdGC0LLQtdGA0LNcIixcbiAgICBcItC/0Y/RgtC90LjRhtCwXCIsXG4gICAgXCLRgdGD0LHQsdC+0YLQsFwiLFxuICBdLFxufTtcblxuY29uc3QgZGF5UGVyaW9kVmFsdWVzID0ge1xuICBuYXJyb3c6IHtcbiAgICBhbTogXCLQlNCfXCIsXG4gICAgcG06IFwi0J/Qn1wiLFxuICAgIG1pZG5pZ2h0OiBcItC/0L7Qu9C9LlwiLFxuICAgIG5vb246IFwi0L/QvtC70LQuXCIsXG4gICAgbW9ybmluZzogXCLRg9GC0YDQvlwiLFxuICAgIGFmdGVybm9vbjogXCLQtNC10L3RjFwiLFxuICAgIGV2ZW5pbmc6IFwi0LLQtdGHLlwiLFxuICAgIG5pZ2h0OiBcItC90L7Rh9GMXCIsXG4gIH0sXG4gIGFiYnJldmlhdGVkOiB7XG4gICAgYW06IFwi0JTQn1wiLFxuICAgIHBtOiBcItCf0J9cIixcbiAgICBtaWRuaWdodDogXCLQv9C+0LvQvS5cIixcbiAgICBub29uOiBcItC/0L7Qu9C0LlwiLFxuICAgIG1vcm5pbmc6IFwi0YPRgtGA0L5cIixcbiAgICBhZnRlcm5vb246IFwi0LTQtdC90YxcIixcbiAgICBldmVuaW5nOiBcItCy0LXRhy5cIixcbiAgICBuaWdodDogXCLQvdC+0YfRjFwiLFxuICB9LFxuICB3aWRlOiB7XG4gICAgYW06IFwi0JTQn1wiLFxuICAgIHBtOiBcItCf0J9cIixcbiAgICBtaWRuaWdodDogXCLQv9C+0LvQvdC+0YfRjFwiLFxuICAgIG5vb246IFwi0L/QvtC70LTQtdC90YxcIixcbiAgICBtb3JuaW5nOiBcItGD0YLRgNC+XCIsXG4gICAgYWZ0ZXJub29uOiBcItC00LXQvdGMXCIsXG4gICAgZXZlbmluZzogXCLQstC10YfQtdGAXCIsXG4gICAgbmlnaHQ6IFwi0L3QvtGH0YxcIixcbiAgfSxcbn07XG5cbmNvbnN0IGZvcm1hdHRpbmdEYXlQZXJpb2RWYWx1ZXMgPSB7XG4gIG5hcnJvdzoge1xuICAgIGFtOiBcItCU0J9cIixcbiAgICBwbTogXCLQn9CfXCIsXG4gICAgbWlkbmlnaHQ6IFwi0L/QvtC70L0uXCIsXG4gICAgbm9vbjogXCLQv9C+0LvQtC5cIixcbiAgICBtb3JuaW5nOiBcItGD0YLRgNCwXCIsXG4gICAgYWZ0ZXJub29uOiBcItC00L3Rj1wiLFxuICAgIGV2ZW5pbmc6IFwi0LLQtdGHLlwiLFxuICAgIG5pZ2h0OiBcItC90L7Rh9C4XCIsXG4gIH0sXG4gIGFiYnJldmlhdGVkOiB7XG4gICAgYW06IFwi0JTQn1wiLFxuICAgIHBtOiBcItCf0J9cIixcbiAgICBtaWRuaWdodDogXCLQv9C+0LvQvS5cIixcbiAgICBub29uOiBcItC/0L7Qu9C0LlwiLFxuICAgIG1vcm5pbmc6IFwi0YPRgtGA0LBcIixcbiAgICBhZnRlcm5vb246IFwi0LTQvdGPXCIsXG4gICAgZXZlbmluZzogXCLQstC10YcuXCIsXG4gICAgbmlnaHQ6IFwi0L3QvtGH0LhcIixcbiAgfSxcbiAgd2lkZToge1xuICAgIGFtOiBcItCU0J9cIixcbiAgICBwbTogXCLQn9CfXCIsXG4gICAgbWlkbmlnaHQ6IFwi0L/QvtC70L3QvtGH0YxcIixcbiAgICBub29uOiBcItC/0L7Qu9C00LXQvdGMXCIsXG4gICAgbW9ybmluZzogXCLRg9GC0YDQsFwiLFxuICAgIGFmdGVybm9vbjogXCLQtNC90Y9cIixcbiAgICBldmVuaW5nOiBcItCy0LXRh9C10YDQsFwiLFxuICAgIG5pZ2h0OiBcItC90L7Rh9C4XCIsXG4gIH0sXG59O1xuXG5jb25zdCBvcmRpbmFsTnVtYmVyID0gKGRpcnR5TnVtYmVyLCBvcHRpb25zKSA9PiB7XG4gIGNvbnN0IG51bWJlciA9IE51bWJlcihkaXJ0eU51bWJlcik7XG4gIGNvbnN0IHVuaXQgPSBvcHRpb25zPy51bml0O1xuXG4gIGxldCBzdWZmaXg7XG4gIGlmICh1bml0ID09PSBcImRhdGVcIikge1xuICAgIHN1ZmZpeCA9IFwiLdC1XCI7XG4gIH0gZWxzZSBpZiAodW5pdCA9PT0gXCJ3ZWVrXCIgfHwgdW5pdCA9PT0gXCJtaW51dGVcIiB8fCB1bml0ID09PSBcInNlY29uZFwiKSB7XG4gICAgc3VmZml4ID0gXCIt0Y9cIjtcbiAgfSBlbHNlIHtcbiAgICBzdWZmaXggPSBcIi3QuVwiO1xuICB9XG5cbiAgcmV0dXJuIG51bWJlciArIHN1ZmZpeDtcbn07XG5cbmV4cG9ydCBjb25zdCBsb2NhbGl6ZSA9IHtcbiAgb3JkaW5hbE51bWJlcixcblxuICBlcmE6IGJ1aWxkTG9jYWxpemVGbih7XG4gICAgdmFsdWVzOiBlcmFWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcIndpZGVcIixcbiAgfSksXG5cbiAgcXVhcnRlcjogYnVpbGRMb2NhbGl6ZUZuKHtcbiAgICB2YWx1ZXM6IHF1YXJ0ZXJWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcIndpZGVcIixcbiAgICBhcmd1bWVudENhbGxiYWNrOiAocXVhcnRlcikgPT4gcXVhcnRlciAtIDEsXG4gIH0pLFxuXG4gIG1vbnRoOiBidWlsZExvY2FsaXplRm4oe1xuICAgIHZhbHVlczogbW9udGhWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcIndpZGVcIixcbiAgICBmb3JtYXR0aW5nVmFsdWVzOiBmb3JtYXR0aW5nTW9udGhWYWx1ZXMsXG4gICAgZGVmYXVsdEZvcm1hdHRpbmdXaWR0aDogXCJ3aWRlXCIsXG4gIH0pLFxuXG4gIGRheTogYnVpbGRMb2NhbGl6ZUZuKHtcbiAgICB2YWx1ZXM6IGRheVZhbHVlcyxcbiAgICBkZWZhdWx0V2lkdGg6IFwid2lkZVwiLFxuICB9KSxcblxuICBkYXlQZXJpb2Q6IGJ1aWxkTG9jYWxpemVGbih7XG4gICAgdmFsdWVzOiBkYXlQZXJpb2RWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcImFueVwiLFxuICAgIGZvcm1hdHRpbmdWYWx1ZXM6IGZvcm1hdHRpbmdEYXlQZXJpb2RWYWx1ZXMsXG4gICAgZGVmYXVsdEZvcm1hdHRpbmdXaWR0aDogXCJ3aWRlXCIsXG4gIH0pLFxufTtcbiJdLCJuYW1lcyI6WyJidWlsZExvY2FsaXplRm4iLCJlcmFWYWx1ZXMiLCJuYXJyb3ciLCJhYmJyZXZpYXRlZCIsIndpZGUiLCJxdWFydGVyVmFsdWVzIiwibW9udGhWYWx1ZXMiLCJmb3JtYXR0aW5nTW9udGhWYWx1ZXMiLCJkYXlWYWx1ZXMiLCJzaG9ydCIsImRheVBlcmlvZFZhbHVlcyIsImFtIiwicG0iLCJtaWRuaWdodCIsIm5vb24iLCJtb3JuaW5nIiwiYWZ0ZXJub29uIiwiZXZlbmluZyIsIm5pZ2h0IiwiZm9ybWF0dGluZ0RheVBlcmlvZFZhbHVlcyIsIm9yZGluYWxOdW1iZXIiLCJkaXJ0eU51bWJlciIsIm9wdGlvbnMiLCJudW1iZXIiLCJOdW1iZXIiLCJ1bml0Iiwic3VmZml4IiwibG9jYWxpemUiLCJlcmEiLCJ2YWx1ZXMiLCJkZWZhdWx0V2lkdGgiLCJxdWFydGVyIiwiYXJndW1lbnRDYWxsYmFjayIsIm1vbnRoIiwiZm9ybWF0dGluZ1ZhbHVlcyIsImRlZmF1bHRGb3JtYXR0aW5nV2lkdGgiLCJkYXkiLCJkYXlQZXJpb2QiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/match.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/match.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(е|я|й|ое|ье|ая|ья|ый|ой|ий|ый))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^((до )?н\\.?\\s?э\\.?)/i,\n    abbreviated: /^((до )?н\\.?\\s?э\\.?)/i,\n    wide: /^(до нашей эры|нашей эры|наша эра)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^д/i,\n        /^н/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^[1234](-?[ыои]?й?)? кв.?/i,\n    wide: /^[1234](-?[ыои]?й?)? квартал/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[яфмаисонд]/i,\n    abbreviated: /^(янв|фев|март?|апр|ма[йя]|июн[ья]?|июл[ья]?|авг|сент?|окт|нояб?|дек)\\.?/i,\n    wide: /^(январ[ья]|феврал[ья]|марта?|апрел[ья]|ма[йя]|июн[ья]|июл[ья]|августа?|сентябр[ья]|октябр[ья]|октябр[ья]|ноябр[ья]|декабр[ья])/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^я/i,\n        /^ф/i,\n        /^м/i,\n        /^а/i,\n        /^м/i,\n        /^и/i,\n        /^и/i,\n        /^а/i,\n        /^с/i,\n        /^о/i,\n        /^н/i,\n        /^я/i\n    ],\n    any: [\n        /^я/i,\n        /^ф/i,\n        /^мар/i,\n        /^ап/i,\n        /^ма[йя]/i,\n        /^июн/i,\n        /^июл/i,\n        /^ав/i,\n        /^с/i,\n        /^о/i,\n        /^н/i,\n        /^д/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[впсч]/i,\n    short: /^(вс|во|пн|по|вт|ср|чт|че|пт|пя|сб|су)\\.?/i,\n    abbreviated: /^(вск|вос|пнд|пон|втр|вто|срд|сре|чтв|чет|птн|пят|суб).?/i,\n    wide: /^(воскресень[ея]|понедельника?|вторника?|сред[аы]|четверга?|пятниц[аы]|суббот[аы])/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^в/i,\n        /^п/i,\n        /^в/i,\n        /^с/i,\n        /^ч/i,\n        /^п/i,\n        /^с/i\n    ],\n    any: [\n        /^в[ос]/i,\n        /^п[он]/i,\n        /^в/i,\n        /^ср/i,\n        /^ч/i,\n        /^п[ят]/i,\n        /^с[уб]/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^([дп]п|полн\\.?|полд\\.?|утр[оа]|день|дня|веч\\.?|ноч[ьи])/i,\n    abbreviated: /^([дп]п|полн\\.?|полд\\.?|утр[оа]|день|дня|веч\\.?|ноч[ьи])/i,\n    wide: /^([дп]п|полночь|полдень|утр[оа]|день|дня|вечера?|ноч[ьи])/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^дп/i,\n        pm: /^пп/i,\n        midnight: /^полн/i,\n        noon: /^полд/i,\n        morning: /^у/i,\n        afternoon: /^д[ен]/i,\n        evening: /^в/i,\n        night: /^н/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ru/_lib/match.js\n"));

/***/ })

}]);