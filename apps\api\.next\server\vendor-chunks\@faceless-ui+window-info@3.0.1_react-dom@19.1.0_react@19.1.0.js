"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0";
exports.ids = ["vendor-chunks/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfo/index.js":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfo/index.js ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WindowInfo: () => (/* binding */ WindowInfo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useWindowInfo_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useWindowInfo/index.js */ \"(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/useWindowInfo/index.js\");\n/* __next_internal_client_entry_do_not_use__ WindowInfo auto */ \n\nconst WindowInfo = (props)=>{\n    const { children } = props;\n    const windowInfo = (0,_useWindowInfo_index_js__WEBPACK_IMPORTED_MODULE_1__.useWindowInfo)();\n    if (children) {\n        if (typeof children === 'function') {\n            return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children(windowInfo));\n        }\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, children);\n    }\n    return null;\n}; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BmYWNlbGVzcy11aSt3aW5kb3ctaW5mb0AzLjAuMV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvQGZhY2VsZXNzLXVpL3dpbmRvdy1pbmZvL2Rpc3QvV2luZG93SW5mby9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Z0VBQ3dDO0FBQ2tCO0FBU25ELE1BQU0sVUFBVSxHQUE4QixDQUFDLEtBQUssRUFBRSxFQUFFO0lBQzdELE1BQU0sRUFBRSxRQUFRLEVBQUUsR0FBRyxLQUFLLENBQUM7SUFDM0IsTUFBTSxVQUFVLEdBQUcsc0VBQWEsRUFBRSxDQUFDO0lBRW5DLElBQUksUUFBUSxFQUFFLENBQUM7UUFDYixJQUFJLE9BQU8sUUFBUSxLQUFLLFVBQVUsRUFBRSxDQUFDO1lBQ25DLE9BQU8sY0FDTCxpREFBQywyQ0FBUSxRQUNOLFFBQVEsQ0FBQyxVQUFVLENBQUMsQ0FDWixDQUNaO1FBQ0gsQ0FBQztRQUVELE9BQU8sY0FDTCxpREFBQywyQ0FBUSxRQUNOLFFBQVEsQ0FDQSxDQUNaO0lBQ0gsQ0FBQztJQUNELE9BQU8sSUFBSSxDQUFDO0FBQ2QsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJDOlxccHJvamVjdHNcXGxtc1xcc3JjXFxXaW5kb3dJbmZvXFxpbmRleC50c3giXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfo/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfoProvider/context.js":
/*!**********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfoProvider/context.js ***!
  \**********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WindowInfoContext: () => (/* binding */ WindowInfoContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* __next_internal_client_entry_do_not_use__ WindowInfoContext auto */ \nconst WindowInfoContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)({}); //# sourceMappingURL=context.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BmYWNlbGVzcy11aSt3aW5kb3ctaW5mb0AzLjAuMV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvQGZhY2VsZXNzLXVpL3dpbmRvdy1pbmZvL2Rpc3QvV2luZG93SW5mb1Byb3ZpZGVyL2NvbnRleHQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7dUVBQ3NDO0FBZS9CLE1BQU0saUJBQWlCLGlCQUFHLG9EQUFhLENBQXFCLEVBQXdCLENBQUMsQ0FBQyIsInNvdXJjZXMiOlsiQzpcXHByb2plY3RzXFxsbXNcXHNyY1xcV2luZG93SW5mb1Byb3ZpZGVyXFxjb250ZXh0LnRzeCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfoProvider/context.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfoProvider/index.js":
/*!********************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfoProvider/index.js ***!
  \********************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WindowInfoProvider: () => (/* binding */ WindowInfoProvider)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _WindowInfoProvider_context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../WindowInfoProvider/context.js */ \"(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfoProvider/context.js\");\n/* __next_internal_client_entry_do_not_use__ WindowInfoProvider auto */ \n\nconst reducer = (state, action)=>{\n    const { payload: { breakpoints, animationRef } } = action;\n    animationRef.current = null;\n    const { eventsFired: prevEventsFired } = state;\n    const { documentElement: { style, clientWidth, clientHeight } } = document;\n    const { innerWidth: windowWidth, innerHeight: windowHeight } = window;\n    const viewportWidth = `${clientWidth / 100}px`;\n    const viewportHeight = `${clientHeight / 100}px`;\n    const watchedBreakpoints = breakpoints ? Object.keys(breakpoints).reduce((matchMediaBreakpoints, key)=>Object.assign(Object.assign({}, matchMediaBreakpoints), {\n            [key]: window.matchMedia(breakpoints[key]).matches\n        }), {}) : {};\n    const newState = {\n        width: windowWidth,\n        height: windowHeight,\n        '--vw': viewportWidth,\n        '--vh': viewportHeight,\n        breakpoints: watchedBreakpoints,\n        eventsFired: prevEventsFired + 1\n    };\n    // This method is a cross-browser patch to achieve above-the-fold, fullscreen mobile experiences.\n    // The technique accounts for the collapsing bottom toolbar of some mobile browsers which are out of normal flow.\n    // It provides an alternate to the \"vw\" and \"vh\" CSS units by generating respective CSS variables.\n    // It specifically reads the size of documentElement since its height does not include the toolbar.\n    style.setProperty('--vw', viewportWidth);\n    style.setProperty('--vh', viewportHeight);\n    return newState;\n};\nconst WindowInfoProvider = (props)=>{\n    const { breakpoints, children } = props;\n    const animationRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const [state, dispatch] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useReducer)(reducer, {\n        width: undefined,\n        height: undefined,\n        '--vw': '',\n        '--vh': '',\n        breakpoints: {},\n        eventsFired: 0\n    });\n    const requestAnimation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"WindowInfoProvider.useCallback[requestAnimation]\": ()=>{\n            if (animationRef.current) cancelAnimationFrame(animationRef.current);\n            animationRef.current = requestAnimationFrame({\n                \"WindowInfoProvider.useCallback[requestAnimation]\": ()=>dispatch({\n                        type: 'UPDATE',\n                        payload: {\n                            breakpoints,\n                            animationRef\n                        }\n                    })\n            }[\"WindowInfoProvider.useCallback[requestAnimation]\"]);\n        }\n    }[\"WindowInfoProvider.useCallback[requestAnimation]\"], [\n        breakpoints\n    ]);\n    const requestThrottledAnimation = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"WindowInfoProvider.useCallback[requestThrottledAnimation]\": ()=>{\n            setTimeout({\n                \"WindowInfoProvider.useCallback[requestThrottledAnimation]\": ()=>{\n                    requestAnimation();\n                }\n            }[\"WindowInfoProvider.useCallback[requestThrottledAnimation]\"], 500);\n        }\n    }[\"WindowInfoProvider.useCallback[requestThrottledAnimation]\"], [\n        requestAnimation\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"WindowInfoProvider.useEffect\": ()=>{\n            window.addEventListener('resize', requestAnimation);\n            window.addEventListener('orientationchange', requestThrottledAnimation);\n            return ({\n                \"WindowInfoProvider.useEffect\": ()=>{\n                    window.removeEventListener('resize', requestAnimation);\n                    window.removeEventListener('orientationchange', requestThrottledAnimation);\n                }\n            })[\"WindowInfoProvider.useEffect\"];\n        }\n    }[\"WindowInfoProvider.useEffect\"], [\n        requestAnimation,\n        requestThrottledAnimation\n    ]);\n    // use this effect to test rAF debounce by requesting animation every 1ms, for a total 120ms\n    // results: ~23 requests will be cancelled, ~17 requests will be cancelled, and only ~8 will truly dispatch\n    // useEffect(() => {\n    //   const firstID = setInterval(requestAnimation, 1);\n    //   setInterval(() => clearInterval(firstID), 120);\n    // }, [requestAnimation]);\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"WindowInfoProvider.useEffect\": ()=>{\n            if (state.eventsFired === 0) {\n                dispatch({\n                    type: 'UPDATE',\n                    payload: {\n                        breakpoints,\n                        animationRef\n                    }\n                });\n            }\n        }\n    }[\"WindowInfoProvider.useEffect\"], [\n        breakpoints,\n        state\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_WindowInfoProvider_context_js__WEBPACK_IMPORTED_MODULE_1__.WindowInfoContext.Provider, {\n        value: Object.assign({}, state)\n    }, children && children);\n}; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfoProvider/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/index.js":
/*!*************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/index.js ***!
  \*************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   WindowInfo: () => (/* reexport safe */ _WindowInfo_index_js__WEBPACK_IMPORTED_MODULE_1__.WindowInfo),\n/* harmony export */   WindowInfoContext: () => (/* reexport safe */ _WindowInfoProvider_context_js__WEBPACK_IMPORTED_MODULE_2__.WindowInfoContext),\n/* harmony export */   WindowInfoProvider: () => (/* reexport safe */ _WindowInfoProvider_index_js__WEBPACK_IMPORTED_MODULE_3__.WindowInfoProvider),\n/* harmony export */   useWindowInfo: () => (/* reexport safe */ _useWindowInfo_index_js__WEBPACK_IMPORTED_MODULE_0__.useWindowInfo),\n/* harmony export */   withWindowInfo: () => (/* reexport safe */ _withWindowInfo_index_js__WEBPACK_IMPORTED_MODULE_4__.withWindowInfo)\n/* harmony export */ });\n/* harmony import */ var _useWindowInfo_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./useWindowInfo/index.js */ \"(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/useWindowInfo/index.js\");\n/* harmony import */ var _WindowInfo_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./WindowInfo/index.js */ \"(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfo/index.js\");\n/* harmony import */ var _WindowInfoProvider_context_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./WindowInfoProvider/context.js */ \"(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfoProvider/context.js\");\n/* harmony import */ var _WindowInfoProvider_index_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./WindowInfoProvider/index.js */ \"(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfoProvider/index.js\");\n/* harmony import */ var _withWindowInfo_index_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./withWindowInfo/index.js */ \"(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/withWindowInfo/index.js\");\n\n\n\n\n\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BmYWNlbGVzcy11aSt3aW5kb3ctaW5mb0AzLjAuMV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvQGZhY2VsZXNzLXVpL3dpbmRvdy1pbmZvL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUF5RDtBQUNOO0FBQ2lCO0FBQ0Q7QUFDUjtBQUMzRCIsInNvdXJjZXMiOlsiQzpcXHByb2plY3RzXFxsbXNcXG5vZGVfbW9kdWxlc1xcLnBucG1cXEBmYWNlbGVzcy11aSt3aW5kb3ctaW5mb0AzLjAuMV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMFxcbm9kZV9tb2R1bGVzXFxAZmFjZWxlc3MtdWlcXHdpbmRvdy1pbmZvXFxkaXN0XFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyB1c2VXaW5kb3dJbmZvIH0gZnJvbSAnLi91c2VXaW5kb3dJbmZvL2luZGV4LmpzJztcbmV4cG9ydCB7IFdpbmRvd0luZm8gfSBmcm9tICcuL1dpbmRvd0luZm8vaW5kZXguanMnO1xuZXhwb3J0IHsgV2luZG93SW5mb0NvbnRleHQgfSBmcm9tICcuL1dpbmRvd0luZm9Qcm92aWRlci9jb250ZXh0LmpzJztcbmV4cG9ydCB7IFdpbmRvd0luZm9Qcm92aWRlciB9IGZyb20gJy4vV2luZG93SW5mb1Byb3ZpZGVyL2luZGV4LmpzJztcbmV4cG9ydCB7IHdpdGhXaW5kb3dJbmZvIH0gZnJvbSAnLi93aXRoV2luZG93SW5mby9pbmRleC5qcyc7XG4vLyMgc291cmNlTWFwcGluZ1VSTD1pbmRleC5qcy5tYXAiXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/useWindowInfo/index.js":
/*!***************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/useWindowInfo/index.js ***!
  \***************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useWindowInfo: () => (/* binding */ useWindowInfo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _WindowInfoProvider_context_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../WindowInfoProvider/context.js */ \"(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/WindowInfoProvider/context.js\");\n/* __next_internal_client_entry_do_not_use__ useWindowInfo auto */ \n\nconst useWindowInfo = ()=>(0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(_WindowInfoProvider_context_js__WEBPACK_IMPORTED_MODULE_1__.WindowInfoContext); //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BmYWNlbGVzcy11aSt3aW5kb3ctaW5mb0AzLjAuMV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvQGZhY2VsZXNzLXVpL3dpbmRvdy1pbmZvL2Rpc3QvdXNlV2luZG93SW5mby9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7bUVBQ21DO0FBQzJEO0FBRXZGLE1BQU0sYUFBYSxHQUFHLEdBQXVCLENBQUcsaURBQVUsQ0FBQyw2RUFBaUIsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJDOlxccHJvamVjdHNcXGxtc1xcc3JjXFx1c2VXaW5kb3dJbmZvXFxpbmRleC50c3giXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/useWindowInfo/index.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/withWindowInfo/index.js":
/*!****************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/withWindowInfo/index.js ***!
  \****************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   withWindowInfo: () => (/* binding */ withWindowInfo)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var _useWindowInfo_index_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../useWindowInfo/index.js */ \"(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/useWindowInfo/index.js\");\n/* __next_internal_client_entry_do_not_use__ withWindowInfo auto */ \n\nconst withWindowInfo = (PassedComponent)=>{\n    const WindowInfoWrap = (props)=>{\n        const windowInfoContext = (0,_useWindowInfo_index_js__WEBPACK_IMPORTED_MODULE_1__.useWindowInfo)();\n        return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(PassedComponent, Object.assign({}, props, {\n            windowInfo: windowInfoContext\n        }));\n    };\n    return WindowInfoWrap;\n}; //# sourceMappingURL=index.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BmYWNlbGVzcy11aSt3aW5kb3ctaW5mb0AzLjAuMV9yZWFjdC1kb21AMTkuMS4wX3JlYWN0QDE5LjEuMC9ub2RlX21vZHVsZXMvQGZhY2VsZXNzLXVpL3dpbmRvdy1pbmZvL2Rpc3Qvd2l0aFdpbmRvd0luZm8vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7Ozs7O29FQUMwQjtBQUNnQztBQUVuRCxNQUFNLGNBQWMsR0FBRyxDQUM1QixlQUF1QyxFQUMxQixFQUFFO0lBQ2YsTUFBTSxjQUFjLEdBQWdCLENBQUMsS0FBSyxFQUFFLEVBQUU7UUFDNUMsTUFBTSxpQkFBaUIsR0FBRyxzRUFBYSxFQUFFLENBQUM7UUFFMUMsT0FBTyxjQUNMLGlEQUFDLGVBQWUsb0JBRVQsS0FBSztZQUNSLFVBQVUsRUFBRSxpQkFBaUI7UUFBQSxHQUUvQixDQUNIO0lBQ0gsQ0FBQyxDQUFDO0lBQ0YsT0FBTyxjQUFjLENBQUM7QUFDeEIsQ0FBQyxDQUFDIiwic291cmNlcyI6WyJDOlxccHJvamVjdHNcXGxtc1xcc3JjXFx3aXRoV2luZG93SW5mb1xcaW5kZXgudHN4Il0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@faceless-ui+window-info@3.0.1_react-dom@19.1.0_react@19.1.0/node_modules/@faceless-ui/window-info/dist/withWindowInfo/index.js\n");

/***/ })

};
;