"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/focus-trap@7.5.4";
exports.ids = ["vendor-chunks/focus-trap@7.5.4"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/focus-trap@7.5.4/node_modules/focus-trap/dist/focus-trap.esm.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/focus-trap@7.5.4/node_modules/focus-trap/dist/focus-trap.esm.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createFocusTrap: () => (/* binding */ createFocusTrap)\n/* harmony export */ });\n/* harmony import */ var tabbable__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! tabbable */ \"(ssr)/../../node_modules/.pnpm/tabbable@6.2.0/node_modules/tabbable/dist/index.esm.js\");\n/*!\n* focus-trap 7.5.4\n* @license MIT, https://github.com/focus-trap/focus-trap/blob/master/LICENSE\n*/\n\n\nfunction ownKeys(e, r) {\n  var t = Object.keys(e);\n  if (Object.getOwnPropertySymbols) {\n    var o = Object.getOwnPropertySymbols(e);\n    r && (o = o.filter(function (r) {\n      return Object.getOwnPropertyDescriptor(e, r).enumerable;\n    })), t.push.apply(t, o);\n  }\n  return t;\n}\nfunction _objectSpread2(e) {\n  for (var r = 1; r < arguments.length; r++) {\n    var t = null != arguments[r] ? arguments[r] : {};\n    r % 2 ? ownKeys(Object(t), !0).forEach(function (r) {\n      _defineProperty(e, r, t[r]);\n    }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) {\n      Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r));\n    });\n  }\n  return e;\n}\nfunction _defineProperty(obj, key, value) {\n  key = _toPropertyKey(key);\n  if (key in obj) {\n    Object.defineProperty(obj, key, {\n      value: value,\n      enumerable: true,\n      configurable: true,\n      writable: true\n    });\n  } else {\n    obj[key] = value;\n  }\n  return obj;\n}\nfunction _toPrimitive(input, hint) {\n  if (typeof input !== \"object\" || input === null) return input;\n  var prim = input[Symbol.toPrimitive];\n  if (prim !== undefined) {\n    var res = prim.call(input, hint || \"default\");\n    if (typeof res !== \"object\") return res;\n    throw new TypeError(\"@@toPrimitive must return a primitive value.\");\n  }\n  return (hint === \"string\" ? String : Number)(input);\n}\nfunction _toPropertyKey(arg) {\n  var key = _toPrimitive(arg, \"string\");\n  return typeof key === \"symbol\" ? key : String(key);\n}\n\nvar activeFocusTraps = {\n  activateTrap: function activateTrap(trapStack, trap) {\n    if (trapStack.length > 0) {\n      var activeTrap = trapStack[trapStack.length - 1];\n      if (activeTrap !== trap) {\n        activeTrap.pause();\n      }\n    }\n    var trapIndex = trapStack.indexOf(trap);\n    if (trapIndex === -1) {\n      trapStack.push(trap);\n    } else {\n      // move this existing trap to the front of the queue\n      trapStack.splice(trapIndex, 1);\n      trapStack.push(trap);\n    }\n  },\n  deactivateTrap: function deactivateTrap(trapStack, trap) {\n    var trapIndex = trapStack.indexOf(trap);\n    if (trapIndex !== -1) {\n      trapStack.splice(trapIndex, 1);\n    }\n    if (trapStack.length > 0) {\n      trapStack[trapStack.length - 1].unpause();\n    }\n  }\n};\nvar isSelectableInput = function isSelectableInput(node) {\n  return node.tagName && node.tagName.toLowerCase() === 'input' && typeof node.select === 'function';\n};\nvar isEscapeEvent = function isEscapeEvent(e) {\n  return (e === null || e === void 0 ? void 0 : e.key) === 'Escape' || (e === null || e === void 0 ? void 0 : e.key) === 'Esc' || (e === null || e === void 0 ? void 0 : e.keyCode) === 27;\n};\nvar isTabEvent = function isTabEvent(e) {\n  return (e === null || e === void 0 ? void 0 : e.key) === 'Tab' || (e === null || e === void 0 ? void 0 : e.keyCode) === 9;\n};\n\n// checks for TAB by default\nvar isKeyForward = function isKeyForward(e) {\n  return isTabEvent(e) && !e.shiftKey;\n};\n\n// checks for SHIFT+TAB by default\nvar isKeyBackward = function isKeyBackward(e) {\n  return isTabEvent(e) && e.shiftKey;\n};\nvar delay = function delay(fn) {\n  return setTimeout(fn, 0);\n};\n\n// Array.find/findIndex() are not supported on IE; this replicates enough\n//  of Array.findIndex() for our needs\nvar findIndex = function findIndex(arr, fn) {\n  var idx = -1;\n  arr.every(function (value, i) {\n    if (fn(value)) {\n      idx = i;\n      return false; // break\n    }\n\n    return true; // next\n  });\n\n  return idx;\n};\n\n/**\n * Get an option's value when it could be a plain value, or a handler that provides\n *  the value.\n * @param {*} value Option's value to check.\n * @param {...*} [params] Any parameters to pass to the handler, if `value` is a function.\n * @returns {*} The `value`, or the handler's returned value.\n */\nvar valueOrHandler = function valueOrHandler(value) {\n  for (var _len = arguments.length, params = new Array(_len > 1 ? _len - 1 : 0), _key = 1; _key < _len; _key++) {\n    params[_key - 1] = arguments[_key];\n  }\n  return typeof value === 'function' ? value.apply(void 0, params) : value;\n};\nvar getActualTarget = function getActualTarget(event) {\n  // NOTE: If the trap is _inside_ a shadow DOM, event.target will always be the\n  //  shadow host. However, event.target.composedPath() will be an array of\n  //  nodes \"clicked\" from inner-most (the actual element inside the shadow) to\n  //  outer-most (the host HTML document). If we have access to composedPath(),\n  //  then use its first element; otherwise, fall back to event.target (and\n  //  this only works for an _open_ shadow DOM; otherwise,\n  //  composedPath()[0] === event.target always).\n  return event.target.shadowRoot && typeof event.composedPath === 'function' ? event.composedPath()[0] : event.target;\n};\n\n// NOTE: this must be _outside_ `createFocusTrap()` to make sure all traps in this\n//  current instance use the same stack if `userOptions.trapStack` isn't specified\nvar internalTrapStack = [];\nvar createFocusTrap = function createFocusTrap(elements, userOptions) {\n  // SSR: a live trap shouldn't be created in this type of environment so this\n  //  should be safe code to execute if the `document` option isn't specified\n  var doc = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.document) || document;\n  var trapStack = (userOptions === null || userOptions === void 0 ? void 0 : userOptions.trapStack) || internalTrapStack;\n  var config = _objectSpread2({\n    returnFocusOnDeactivate: true,\n    escapeDeactivates: true,\n    delayInitialFocus: true,\n    isKeyForward: isKeyForward,\n    isKeyBackward: isKeyBackward\n  }, userOptions);\n  var state = {\n    // containers given to createFocusTrap()\n    // @type {Array<HTMLElement>}\n    containers: [],\n    // list of objects identifying tabbable nodes in `containers` in the trap\n    // NOTE: it's possible that a group has no tabbable nodes if nodes get removed while the trap\n    //  is active, but the trap should never get to a state where there isn't at least one group\n    //  with at least one tabbable node in it (that would lead to an error condition that would\n    //  result in an error being thrown)\n    // @type {Array<{\n    //   container: HTMLElement,\n    //   tabbableNodes: Array<HTMLElement>, // empty if none\n    //   focusableNodes: Array<HTMLElement>, // empty if none\n    //   posTabIndexesFound: boolean,\n    //   firstTabbableNode: HTMLElement|undefined,\n    //   lastTabbableNode: HTMLElement|undefined,\n    //   firstDomTabbableNode: HTMLElement|undefined,\n    //   lastDomTabbableNode: HTMLElement|undefined,\n    //   nextTabbableNode: (node: HTMLElement, forward: boolean) => HTMLElement|undefined\n    // }>}\n    containerGroups: [],\n    // same order/length as `containers` list\n\n    // references to objects in `containerGroups`, but only those that actually have\n    //  tabbable nodes in them\n    // NOTE: same order as `containers` and `containerGroups`, but __not necessarily__\n    //  the same length\n    tabbableGroups: [],\n    nodeFocusedBeforeActivation: null,\n    mostRecentlyFocusedNode: null,\n    active: false,\n    paused: false,\n    // timer ID for when delayInitialFocus is true and initial focus in this trap\n    //  has been delayed during activation\n    delayInitialFocusTimer: undefined,\n    // the most recent KeyboardEvent for the configured nav key (typically [SHIFT+]TAB), if any\n    recentNavEvent: undefined\n  };\n  var trap; // eslint-disable-line prefer-const -- some private functions reference it, and its methods reference private functions, so we must declare here and define later\n\n  /**\n   * Gets a configuration option value.\n   * @param {Object|undefined} configOverrideOptions If true, and option is defined in this set,\n   *  value will be taken from this object. Otherwise, value will be taken from base configuration.\n   * @param {string} optionName Name of the option whose value is sought.\n   * @param {string|undefined} [configOptionName] Name of option to use __instead of__ `optionName`\n   *  IIF `configOverrideOptions` is not defined. Otherwise, `optionName` is used.\n   */\n  var getOption = function getOption(configOverrideOptions, optionName, configOptionName) {\n    return configOverrideOptions && configOverrideOptions[optionName] !== undefined ? configOverrideOptions[optionName] : config[configOptionName || optionName];\n  };\n\n  /**\n   * Finds the index of the container that contains the element.\n   * @param {HTMLElement} element\n   * @param {Event} [event] If available, and `element` isn't directly found in any container,\n   *  the event's composed path is used to see if includes any known trap containers in the\n   *  case where the element is inside a Shadow DOM.\n   * @returns {number} Index of the container in either `state.containers` or\n   *  `state.containerGroups` (the order/length of these lists are the same); -1\n   *  if the element isn't found.\n   */\n  var findContainerIndex = function findContainerIndex(element, event) {\n    var composedPath = typeof (event === null || event === void 0 ? void 0 : event.composedPath) === 'function' ? event.composedPath() : undefined;\n    // NOTE: search `containerGroups` because it's possible a group contains no tabbable\n    //  nodes, but still contains focusable nodes (e.g. if they all have `tabindex=-1`)\n    //  and we still need to find the element in there\n    return state.containerGroups.findIndex(function (_ref) {\n      var container = _ref.container,\n        tabbableNodes = _ref.tabbableNodes;\n      return container.contains(element) || ( // fall back to explicit tabbable search which will take into consideration any\n      //  web components if the `tabbableOptions.getShadowRoot` option was used for\n      //  the trap, enabling shadow DOM support in tabbable (`Node.contains()` doesn't\n      //  look inside web components even if open)\n      composedPath === null || composedPath === void 0 ? void 0 : composedPath.includes(container)) || tabbableNodes.find(function (node) {\n        return node === element;\n      });\n    });\n  };\n\n  /**\n   * Gets the node for the given option, which is expected to be an option that\n   *  can be either a DOM node, a string that is a selector to get a node, `false`\n   *  (if a node is explicitly NOT given), or a function that returns any of these\n   *  values.\n   * @param {string} optionName\n   * @returns {undefined | false | HTMLElement | SVGElement} Returns\n   *  `undefined` if the option is not specified; `false` if the option\n   *  resolved to `false` (node explicitly not given); otherwise, the resolved\n   *  DOM node.\n   * @throws {Error} If the option is set, not `false`, and is not, or does not\n   *  resolve to a node.\n   */\n  var getNodeForOption = function getNodeForOption(optionName) {\n    var optionValue = config[optionName];\n    if (typeof optionValue === 'function') {\n      for (var _len2 = arguments.length, params = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        params[_key2 - 1] = arguments[_key2];\n      }\n      optionValue = optionValue.apply(void 0, params);\n    }\n    if (optionValue === true) {\n      optionValue = undefined; // use default value\n    }\n\n    if (!optionValue) {\n      if (optionValue === undefined || optionValue === false) {\n        return optionValue;\n      }\n      // else, empty string (invalid), null (invalid), 0 (invalid)\n\n      throw new Error(\"`\".concat(optionName, \"` was specified but was not a node, or did not return a node\"));\n    }\n    var node = optionValue; // could be HTMLElement, SVGElement, or non-empty string at this point\n\n    if (typeof optionValue === 'string') {\n      node = doc.querySelector(optionValue); // resolve to node, or null if fails\n      if (!node) {\n        throw new Error(\"`\".concat(optionName, \"` as selector refers to no known node\"));\n      }\n    }\n    return node;\n  };\n  var getInitialFocusNode = function getInitialFocusNode() {\n    var node = getNodeForOption('initialFocus');\n\n    // false explicitly indicates we want no initialFocus at all\n    if (node === false) {\n      return false;\n    }\n    if (node === undefined || !(0,tabbable__WEBPACK_IMPORTED_MODULE_0__.isFocusable)(node, config.tabbableOptions)) {\n      // option not specified nor focusable: use fallback options\n      if (findContainerIndex(doc.activeElement) >= 0) {\n        node = doc.activeElement;\n      } else {\n        var firstTabbableGroup = state.tabbableGroups[0];\n        var firstTabbableNode = firstTabbableGroup && firstTabbableGroup.firstTabbableNode;\n\n        // NOTE: `fallbackFocus` option function cannot return `false` (not supported)\n        node = firstTabbableNode || getNodeForOption('fallbackFocus');\n      }\n    }\n    if (!node) {\n      throw new Error('Your focus-trap needs to have at least one focusable element');\n    }\n    return node;\n  };\n  var updateTabbableNodes = function updateTabbableNodes() {\n    state.containerGroups = state.containers.map(function (container) {\n      var tabbableNodes = (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.tabbable)(container, config.tabbableOptions);\n\n      // NOTE: if we have tabbable nodes, we must have focusable nodes; focusable nodes\n      //  are a superset of tabbable nodes since nodes with negative `tabindex` attributes\n      //  are focusable but not tabbable\n      var focusableNodes = (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.focusable)(container, config.tabbableOptions);\n      var firstTabbableNode = tabbableNodes.length > 0 ? tabbableNodes[0] : undefined;\n      var lastTabbableNode = tabbableNodes.length > 0 ? tabbableNodes[tabbableNodes.length - 1] : undefined;\n      var firstDomTabbableNode = focusableNodes.find(function (node) {\n        return (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.isTabbable)(node);\n      });\n      var lastDomTabbableNode = focusableNodes.slice().reverse().find(function (node) {\n        return (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.isTabbable)(node);\n      });\n      var posTabIndexesFound = !!tabbableNodes.find(function (node) {\n        return (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.getTabIndex)(node) > 0;\n      });\n      return {\n        container: container,\n        tabbableNodes: tabbableNodes,\n        focusableNodes: focusableNodes,\n        /** True if at least one node with positive `tabindex` was found in this container. */\n        posTabIndexesFound: posTabIndexesFound,\n        /** First tabbable node in container, __tabindex__ order; `undefined` if none. */\n        firstTabbableNode: firstTabbableNode,\n        /** Last tabbable node in container, __tabindex__ order; `undefined` if none. */\n        lastTabbableNode: lastTabbableNode,\n        // NOTE: DOM order is NOT NECESSARILY \"document position\" order, but figuring that out\n        //  would require more than just https://developer.mozilla.org/en-US/docs/Web/API/Node/compareDocumentPosition\n        //  because that API doesn't work with Shadow DOM as well as it should (@see\n        //  https://github.com/whatwg/dom/issues/320) and since this first/last is only needed, so far,\n        //  to address an edge case related to positive tabindex support, this seems like a much easier,\n        //  \"close enough most of the time\" alternative for positive tabindexes which should generally\n        //  be avoided anyway...\n        /** First tabbable node in container, __DOM__ order; `undefined` if none. */\n        firstDomTabbableNode: firstDomTabbableNode,\n        /** Last tabbable node in container, __DOM__ order; `undefined` if none. */\n        lastDomTabbableNode: lastDomTabbableNode,\n        /**\n         * Finds the __tabbable__ node that follows the given node in the specified direction,\n         *  in this container, if any.\n         * @param {HTMLElement} node\n         * @param {boolean} [forward] True if going in forward tab order; false if going\n         *  in reverse.\n         * @returns {HTMLElement|undefined} The next tabbable node, if any.\n         */\n        nextTabbableNode: function nextTabbableNode(node) {\n          var forward = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : true;\n          var nodeIdx = tabbableNodes.indexOf(node);\n          if (nodeIdx < 0) {\n            // either not tabbable nor focusable, or was focused but not tabbable (negative tabindex):\n            //  since `node` should at least have been focusable, we assume that's the case and mimic\n            //  what browsers do, which is set focus to the next node in __document position order__,\n            //  regardless of positive tabindexes, if any -- and for reasons explained in the NOTE\n            //  above related to `firstDomTabbable` and `lastDomTabbable` properties, we fall back to\n            //  basic DOM order\n            if (forward) {\n              return focusableNodes.slice(focusableNodes.indexOf(node) + 1).find(function (el) {\n                return (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.isTabbable)(el);\n              });\n            }\n            return focusableNodes.slice(0, focusableNodes.indexOf(node)).reverse().find(function (el) {\n              return (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.isTabbable)(el);\n            });\n          }\n          return tabbableNodes[nodeIdx + (forward ? 1 : -1)];\n        }\n      };\n    });\n    state.tabbableGroups = state.containerGroups.filter(function (group) {\n      return group.tabbableNodes.length > 0;\n    });\n\n    // throw if no groups have tabbable nodes and we don't have a fallback focus node either\n    if (state.tabbableGroups.length <= 0 && !getNodeForOption('fallbackFocus') // returning false not supported for this option\n    ) {\n      throw new Error('Your focus-trap must have at least one container with at least one tabbable node in it at all times');\n    }\n\n    // NOTE: Positive tabindexes are only properly supported in single-container traps because\n    //  doing it across multiple containers where tabindexes could be all over the place\n    //  would require Tabbable to support multiple containers, would require additional\n    //  specialized Shadow DOM support, and would require Tabbable's multi-container support\n    //  to look at those containers in document position order rather than user-provided\n    //  order (as they are treated in Focus-trap, for legacy reasons). See discussion on\n    //  https://github.com/focus-trap/focus-trap/issues/375 for more details.\n    if (state.containerGroups.find(function (g) {\n      return g.posTabIndexesFound;\n    }) && state.containerGroups.length > 1) {\n      throw new Error(\"At least one node with a positive tabindex was found in one of your focus-trap's multiple containers. Positive tabindexes are only supported in single-container focus-traps.\");\n    }\n  };\n\n  /**\n   * Gets the current activeElement. If it's a web-component and has open shadow-root\n   * it will recursively search inside shadow roots for the \"true\" activeElement.\n   *\n   * @param {Document | ShadowRoot} el\n   *\n   * @returns {HTMLElement} The element that currently has the focus\n   **/\n  var getActiveElement = function getActiveElement(el) {\n    var activeElement = el.activeElement;\n    if (!activeElement) {\n      return;\n    }\n    if (activeElement.shadowRoot && activeElement.shadowRoot.activeElement !== null) {\n      return getActiveElement(activeElement.shadowRoot);\n    }\n    return activeElement;\n  };\n  var tryFocus = function tryFocus(node) {\n    if (node === false) {\n      return;\n    }\n    if (node === getActiveElement(document)) {\n      return;\n    }\n    if (!node || !node.focus) {\n      tryFocus(getInitialFocusNode());\n      return;\n    }\n    node.focus({\n      preventScroll: !!config.preventScroll\n    });\n    // NOTE: focus() API does not trigger focusIn event so set MRU node manually\n    state.mostRecentlyFocusedNode = node;\n    if (isSelectableInput(node)) {\n      node.select();\n    }\n  };\n  var getReturnFocusNode = function getReturnFocusNode(previousActiveElement) {\n    var node = getNodeForOption('setReturnFocus', previousActiveElement);\n    return node ? node : node === false ? false : previousActiveElement;\n  };\n\n  /**\n   * Finds the next node (in either direction) where focus should move according to a\n   *  keyboard focus-in event.\n   * @param {Object} params\n   * @param {Node} [params.target] Known target __from which__ to navigate, if any.\n   * @param {KeyboardEvent|FocusEvent} [params.event] Event to use if `target` isn't known (event\n   *  will be used to determine the `target`). Ignored if `target` is specified.\n   * @param {boolean} [params.isBackward] True if focus should move backward.\n   * @returns {Node|undefined} The next node, or `undefined` if a next node couldn't be\n   *  determined given the current state of the trap.\n   */\n  var findNextNavNode = function findNextNavNode(_ref2) {\n    var target = _ref2.target,\n      event = _ref2.event,\n      _ref2$isBackward = _ref2.isBackward,\n      isBackward = _ref2$isBackward === void 0 ? false : _ref2$isBackward;\n    target = target || getActualTarget(event);\n    updateTabbableNodes();\n    var destinationNode = null;\n    if (state.tabbableGroups.length > 0) {\n      // make sure the target is actually contained in a group\n      // NOTE: the target may also be the container itself if it's focusable\n      //  with tabIndex='-1' and was given initial focus\n      var containerIndex = findContainerIndex(target, event);\n      var containerGroup = containerIndex >= 0 ? state.containerGroups[containerIndex] : undefined;\n      if (containerIndex < 0) {\n        // target not found in any group: quite possible focus has escaped the trap,\n        //  so bring it back into...\n        if (isBackward) {\n          // ...the last node in the last group\n          destinationNode = state.tabbableGroups[state.tabbableGroups.length - 1].lastTabbableNode;\n        } else {\n          // ...the first node in the first group\n          destinationNode = state.tabbableGroups[0].firstTabbableNode;\n        }\n      } else if (isBackward) {\n        // REVERSE\n\n        // is the target the first tabbable node in a group?\n        var startOfGroupIndex = findIndex(state.tabbableGroups, function (_ref3) {\n          var firstTabbableNode = _ref3.firstTabbableNode;\n          return target === firstTabbableNode;\n        });\n        if (startOfGroupIndex < 0 && (containerGroup.container === target || (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.isFocusable)(target, config.tabbableOptions) && !(0,tabbable__WEBPACK_IMPORTED_MODULE_0__.isTabbable)(target, config.tabbableOptions) && !containerGroup.nextTabbableNode(target, false))) {\n          // an exception case where the target is either the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle shift+tab as if focus were on the container's\n          //  first tabbable node, and go to the last tabbable node of the LAST group\n          startOfGroupIndex = containerIndex;\n        }\n        if (startOfGroupIndex >= 0) {\n          // YES: then shift+tab should go to the last tabbable node in the\n          //  previous group (and wrap around to the last tabbable node of\n          //  the LAST group if it's the first tabbable node of the FIRST group)\n          var destinationGroupIndex = startOfGroupIndex === 0 ? state.tabbableGroups.length - 1 : startOfGroupIndex - 1;\n          var destinationGroup = state.tabbableGroups[destinationGroupIndex];\n          destinationNode = (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.getTabIndex)(target) >= 0 ? destinationGroup.lastTabbableNode : destinationGroup.lastDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target, false);\n        }\n      } else {\n        // FORWARD\n\n        // is the target the last tabbable node in a group?\n        var lastOfGroupIndex = findIndex(state.tabbableGroups, function (_ref4) {\n          var lastTabbableNode = _ref4.lastTabbableNode;\n          return target === lastTabbableNode;\n        });\n        if (lastOfGroupIndex < 0 && (containerGroup.container === target || (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.isFocusable)(target, config.tabbableOptions) && !(0,tabbable__WEBPACK_IMPORTED_MODULE_0__.isTabbable)(target, config.tabbableOptions) && !containerGroup.nextTabbableNode(target))) {\n          // an exception case where the target is the container itself, or\n          //  a non-tabbable node that was given focus (i.e. tabindex is negative\n          //  and user clicked on it or node was programmatically given focus)\n          //  and is not followed by any other tabbable node, in which\n          //  case, we should handle tab as if focus were on the container's\n          //  last tabbable node, and go to the first tabbable node of the FIRST group\n          lastOfGroupIndex = containerIndex;\n        }\n        if (lastOfGroupIndex >= 0) {\n          // YES: then tab should go to the first tabbable node in the next\n          //  group (and wrap around to the first tabbable node of the FIRST\n          //  group if it's the last tabbable node of the LAST group)\n          var _destinationGroupIndex = lastOfGroupIndex === state.tabbableGroups.length - 1 ? 0 : lastOfGroupIndex + 1;\n          var _destinationGroup = state.tabbableGroups[_destinationGroupIndex];\n          destinationNode = (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.getTabIndex)(target) >= 0 ? _destinationGroup.firstTabbableNode : _destinationGroup.firstDomTabbableNode;\n        } else if (!isTabEvent(event)) {\n          // user must have customized the nav keys so we have to move focus manually _within_\n          //  the active group: do this based on the order determined by tabbable()\n          destinationNode = containerGroup.nextTabbableNode(target);\n        }\n      }\n    } else {\n      // no groups available\n      // NOTE: the fallbackFocus option does not support returning false to opt-out\n      destinationNode = getNodeForOption('fallbackFocus');\n    }\n    return destinationNode;\n  };\n\n  // This needs to be done on mousedown and touchstart instead of click\n  // so that it precedes the focus event.\n  var checkPointerDown = function checkPointerDown(e) {\n    var target = getActualTarget(e);\n    if (findContainerIndex(target, e) >= 0) {\n      // allow the click since it ocurred inside the trap\n      return;\n    }\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      // immediately deactivate the trap\n      trap.deactivate({\n        // NOTE: by setting `returnFocus: false`, deactivate() will do nothing,\n        //  which will result in the outside click setting focus to the node\n        //  that was clicked (and if not focusable, to \"nothing\"); by setting\n        //  `returnFocus: true`, we'll attempt to re-focus the node originally-focused\n        //  on activation (or the configured `setReturnFocus` node), whether the\n        //  outside click was on a focusable node or not\n        returnFocus: config.returnFocusOnDeactivate\n      });\n      return;\n    }\n\n    // This is needed for mobile devices.\n    // (If we'll only let `click` events through,\n    // then on mobile they will be blocked anyways if `touchstart` is blocked.)\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      // allow the click outside the trap to take place\n      return;\n    }\n\n    // otherwise, prevent the click\n    e.preventDefault();\n  };\n\n  // In case focus escapes the trap for some strange reason, pull it back in.\n  // NOTE: the focusIn event is NOT cancelable, so if focus escapes, it may cause unexpected\n  //  scrolling if the node that got focused was out of view; there's nothing we can do to\n  //  prevent that from happening by the time we discover that focus escaped\n  var checkFocusIn = function checkFocusIn(event) {\n    var target = getActualTarget(event);\n    var targetContained = findContainerIndex(target, event) >= 0;\n\n    // In Firefox when you Tab out of an iframe the Document is briefly focused.\n    if (targetContained || target instanceof Document) {\n      if (targetContained) {\n        state.mostRecentlyFocusedNode = target;\n      }\n    } else {\n      // escaped! pull it back in to where it just left\n      event.stopImmediatePropagation();\n\n      // focus will escape if the MRU node had a positive tab index and user tried to nav forward;\n      //  it will also escape if the MRU node had a 0 tab index and user tried to nav backward\n      //  toward a node with a positive tab index\n      var nextNode; // next node to focus, if we find one\n      var navAcrossContainers = true;\n      if (state.mostRecentlyFocusedNode) {\n        if ((0,tabbable__WEBPACK_IMPORTED_MODULE_0__.getTabIndex)(state.mostRecentlyFocusedNode) > 0) {\n          // MRU container index must be >=0 otherwise we wouldn't have it as an MRU node...\n          var mruContainerIdx = findContainerIndex(state.mostRecentlyFocusedNode);\n          // there MAY not be any tabbable nodes in the container if there are at least 2 containers\n          //  and the MRU node is focusable but not tabbable (focus-trap requires at least 1 container\n          //  with at least one tabbable node in order to function, so this could be the other container\n          //  with nothing tabbable in it)\n          var tabbableNodes = state.containerGroups[mruContainerIdx].tabbableNodes;\n          if (tabbableNodes.length > 0) {\n            // MRU tab index MAY not be found if the MRU node is focusable but not tabbable\n            var mruTabIdx = tabbableNodes.findIndex(function (node) {\n              return node === state.mostRecentlyFocusedNode;\n            });\n            if (mruTabIdx >= 0) {\n              if (config.isKeyForward(state.recentNavEvent)) {\n                if (mruTabIdx + 1 < tabbableNodes.length) {\n                  nextNode = tabbableNodes[mruTabIdx + 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              } else {\n                if (mruTabIdx - 1 >= 0) {\n                  nextNode = tabbableNodes[mruTabIdx - 1];\n                  navAcrossContainers = false;\n                }\n                // else, don't wrap within the container as focus should move to next/previous\n                //  container\n              }\n              // else, don't find in container order without considering direction too\n            }\n          }\n          // else, no tabbable nodes in that container (which means we must have at least one other\n          //  container with at least one tabbable node in it, otherwise focus-trap would've thrown\n          //  an error the last time updateTabbableNodes() was run): find next node among all known\n          //  containers\n        } else {\n          // check to see if there's at least one tabbable node with a positive tab index inside\n          //  the trap because focus seems to escape when navigating backward from a tabbable node\n          //  with tabindex=0 when this is the case (instead of wrapping to the tabbable node with\n          //  the greatest positive tab index like it should)\n          if (!state.containerGroups.some(function (g) {\n            return g.tabbableNodes.some(function (n) {\n              return (0,tabbable__WEBPACK_IMPORTED_MODULE_0__.getTabIndex)(n) > 0;\n            });\n          })) {\n            // no containers with tabbable nodes with positive tab indexes which means the focus\n            //  escaped for some other reason and we should just execute the fallback to the\n            //  MRU node or initial focus node, if any\n            navAcrossContainers = false;\n          }\n        }\n      } else {\n        // no MRU node means we're likely in some initial condition when the trap has just\n        //  been activated and initial focus hasn't been given yet, in which case we should\n        //  fall through to trying to focus the initial focus node, which is what should\n        //  happen below at this point in the logic\n        navAcrossContainers = false;\n      }\n      if (navAcrossContainers) {\n        nextNode = findNextNavNode({\n          // move FROM the MRU node, not event-related node (which will be the node that is\n          //  outside the trap causing the focus escape we're trying to fix)\n          target: state.mostRecentlyFocusedNode,\n          isBackward: config.isKeyBackward(state.recentNavEvent)\n        });\n      }\n      if (nextNode) {\n        tryFocus(nextNode);\n      } else {\n        tryFocus(state.mostRecentlyFocusedNode || getInitialFocusNode());\n      }\n    }\n    state.recentNavEvent = undefined; // clear\n  };\n\n  // Hijack key nav events on the first and last focusable nodes of the trap,\n  // in order to prevent focus from escaping. If it escapes for even a\n  // moment it can end up scrolling the page and causing confusion so we\n  // kind of need to capture the action at the keydown phase.\n  var checkKeyNav = function checkKeyNav(event) {\n    var isBackward = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : false;\n    state.recentNavEvent = event;\n    var destinationNode = findNextNavNode({\n      event: event,\n      isBackward: isBackward\n    });\n    if (destinationNode) {\n      if (isTabEvent(event)) {\n        // since tab natively moves focus, we wouldn't have a destination node unless we\n        //  were on the edge of a container and had to move to the next/previous edge, in\n        //  which case we want to prevent default to keep the browser from moving focus\n        //  to where it normally would\n        event.preventDefault();\n      }\n      tryFocus(destinationNode);\n    }\n    // else, let the browser take care of [shift+]tab and move the focus\n  };\n\n  var checkKey = function checkKey(event) {\n    if (isEscapeEvent(event) && valueOrHandler(config.escapeDeactivates, event) !== false) {\n      event.preventDefault();\n      trap.deactivate();\n      return;\n    }\n    if (config.isKeyForward(event) || config.isKeyBackward(event)) {\n      checkKeyNav(event, config.isKeyBackward(event));\n    }\n  };\n  var checkClick = function checkClick(e) {\n    var target = getActualTarget(e);\n    if (findContainerIndex(target, e) >= 0) {\n      return;\n    }\n    if (valueOrHandler(config.clickOutsideDeactivates, e)) {\n      return;\n    }\n    if (valueOrHandler(config.allowOutsideClick, e)) {\n      return;\n    }\n    e.preventDefault();\n    e.stopImmediatePropagation();\n  };\n\n  //\n  // EVENT LISTENERS\n  //\n\n  var addListeners = function addListeners() {\n    if (!state.active) {\n      return;\n    }\n\n    // There can be only one listening focus trap at a time\n    activeFocusTraps.activateTrap(trapStack, trap);\n\n    // Delay ensures that the focused element doesn't capture the event\n    // that caused the focus trap activation.\n    state.delayInitialFocusTimer = config.delayInitialFocus ? delay(function () {\n      tryFocus(getInitialFocusNode());\n    }) : tryFocus(getInitialFocusNode());\n    doc.addEventListener('focusin', checkFocusIn, true);\n    doc.addEventListener('mousedown', checkPointerDown, {\n      capture: true,\n      passive: false\n    });\n    doc.addEventListener('touchstart', checkPointerDown, {\n      capture: true,\n      passive: false\n    });\n    doc.addEventListener('click', checkClick, {\n      capture: true,\n      passive: false\n    });\n    doc.addEventListener('keydown', checkKey, {\n      capture: true,\n      passive: false\n    });\n    return trap;\n  };\n  var removeListeners = function removeListeners() {\n    if (!state.active) {\n      return;\n    }\n    doc.removeEventListener('focusin', checkFocusIn, true);\n    doc.removeEventListener('mousedown', checkPointerDown, true);\n    doc.removeEventListener('touchstart', checkPointerDown, true);\n    doc.removeEventListener('click', checkClick, true);\n    doc.removeEventListener('keydown', checkKey, true);\n    return trap;\n  };\n\n  //\n  // MUTATION OBSERVER\n  //\n\n  var checkDomRemoval = function checkDomRemoval(mutations) {\n    var isFocusedNodeRemoved = mutations.some(function (mutation) {\n      var removedNodes = Array.from(mutation.removedNodes);\n      return removedNodes.some(function (node) {\n        return node === state.mostRecentlyFocusedNode;\n      });\n    });\n\n    // If the currently focused is removed then browsers will move focus to the\n    // <body> element. If this happens, try to move focus back into the trap.\n    if (isFocusedNodeRemoved) {\n      tryFocus(getInitialFocusNode());\n    }\n  };\n\n  // Use MutationObserver - if supported - to detect if focused node is removed\n  // from the DOM.\n  var mutationObserver = typeof window !== 'undefined' && 'MutationObserver' in window ? new MutationObserver(checkDomRemoval) : undefined;\n  var updateObservedNodes = function updateObservedNodes() {\n    if (!mutationObserver) {\n      return;\n    }\n    mutationObserver.disconnect();\n    if (state.active && !state.paused) {\n      state.containers.map(function (container) {\n        mutationObserver.observe(container, {\n          subtree: true,\n          childList: true\n        });\n      });\n    }\n  };\n\n  //\n  // TRAP DEFINITION\n  //\n\n  trap = {\n    get active() {\n      return state.active;\n    },\n    get paused() {\n      return state.paused;\n    },\n    activate: function activate(activateOptions) {\n      if (state.active) {\n        return this;\n      }\n      var onActivate = getOption(activateOptions, 'onActivate');\n      var onPostActivate = getOption(activateOptions, 'onPostActivate');\n      var checkCanFocusTrap = getOption(activateOptions, 'checkCanFocusTrap');\n      if (!checkCanFocusTrap) {\n        updateTabbableNodes();\n      }\n      state.active = true;\n      state.paused = false;\n      state.nodeFocusedBeforeActivation = doc.activeElement;\n      onActivate === null || onActivate === void 0 || onActivate();\n      var finishActivation = function finishActivation() {\n        if (checkCanFocusTrap) {\n          updateTabbableNodes();\n        }\n        addListeners();\n        updateObservedNodes();\n        onPostActivate === null || onPostActivate === void 0 || onPostActivate();\n      };\n      if (checkCanFocusTrap) {\n        checkCanFocusTrap(state.containers.concat()).then(finishActivation, finishActivation);\n        return this;\n      }\n      finishActivation();\n      return this;\n    },\n    deactivate: function deactivate(deactivateOptions) {\n      if (!state.active) {\n        return this;\n      }\n      var options = _objectSpread2({\n        onDeactivate: config.onDeactivate,\n        onPostDeactivate: config.onPostDeactivate,\n        checkCanReturnFocus: config.checkCanReturnFocus\n      }, deactivateOptions);\n      clearTimeout(state.delayInitialFocusTimer); // noop if undefined\n      state.delayInitialFocusTimer = undefined;\n      removeListeners();\n      state.active = false;\n      state.paused = false;\n      updateObservedNodes();\n      activeFocusTraps.deactivateTrap(trapStack, trap);\n      var onDeactivate = getOption(options, 'onDeactivate');\n      var onPostDeactivate = getOption(options, 'onPostDeactivate');\n      var checkCanReturnFocus = getOption(options, 'checkCanReturnFocus');\n      var returnFocus = getOption(options, 'returnFocus', 'returnFocusOnDeactivate');\n      onDeactivate === null || onDeactivate === void 0 || onDeactivate();\n      var finishDeactivation = function finishDeactivation() {\n        delay(function () {\n          if (returnFocus) {\n            tryFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation));\n          }\n          onPostDeactivate === null || onPostDeactivate === void 0 || onPostDeactivate();\n        });\n      };\n      if (returnFocus && checkCanReturnFocus) {\n        checkCanReturnFocus(getReturnFocusNode(state.nodeFocusedBeforeActivation)).then(finishDeactivation, finishDeactivation);\n        return this;\n      }\n      finishDeactivation();\n      return this;\n    },\n    pause: function pause(pauseOptions) {\n      if (state.paused || !state.active) {\n        return this;\n      }\n      var onPause = getOption(pauseOptions, 'onPause');\n      var onPostPause = getOption(pauseOptions, 'onPostPause');\n      state.paused = true;\n      onPause === null || onPause === void 0 || onPause();\n      removeListeners();\n      updateObservedNodes();\n      onPostPause === null || onPostPause === void 0 || onPostPause();\n      return this;\n    },\n    unpause: function unpause(unpauseOptions) {\n      if (!state.paused || !state.active) {\n        return this;\n      }\n      var onUnpause = getOption(unpauseOptions, 'onUnpause');\n      var onPostUnpause = getOption(unpauseOptions, 'onPostUnpause');\n      state.paused = false;\n      onUnpause === null || onUnpause === void 0 || onUnpause();\n      updateTabbableNodes();\n      addListeners();\n      updateObservedNodes();\n      onPostUnpause === null || onPostUnpause === void 0 || onPostUnpause();\n      return this;\n    },\n    updateContainerElements: function updateContainerElements(containerElements) {\n      var elementsAsArray = [].concat(containerElements).filter(Boolean);\n      state.containers = elementsAsArray.map(function (element) {\n        return typeof element === 'string' ? doc.querySelector(element) : element;\n      });\n      if (state.active) {\n        updateTabbableNodes();\n      }\n      updateObservedNodes();\n      return this;\n    }\n  };\n\n  // initialize container elements\n  trap.updateContainerElements(elements);\n  return trap;\n};\n\n\n//# sourceMappingURL=focus-trap.esm.js.map\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/focus-trap@7.5.4/node_modules/focus-trap/dist/focus-trap.esm.js\n");

/***/ })

};
;