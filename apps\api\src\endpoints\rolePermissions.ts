import { Endpoint } from 'payload'

// Helper function to authenticate user
const authenticateUser = async (req: any, payload: any) => {
  let user = null
  try {
    // Try to get user from the request (Payload should populate this)
    user = req.user

    // If not available, try to authenticate manually
    if (!user) {
      const authResult = await payload.authenticate({
        headers: req.headers,
      })
      user = authResult.user
    }
  } catch (authError) {
    console.error('Authentication error:', authError)
  }

  return user
}

// Helper function to check super admin access
const checkSuperAdminAccess = (user: any) => {
  if (!user) {
    return {
      error: Response.json({
        success: false,
        message: 'Authentication required',
      }, { status: 401 })
    }
  }

  if (user.role !== 'super_admin') {
    return {
      error: Response.json({
        success: false,
        message: 'You are not allowed to perform this action',
      }, { status: 403 })
    }
  }

  return { user }
}

export const rolePermissionsEndpoints: Endpoint[] = [
  // Get all roles with their permissions
  {
    path: '/roles-with-permissions',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { payload } = req

        // Authenticate user
        const user = await authenticateUser(req, payload)
        const authCheck = checkSuperAdminAccess(user)

        if (authCheck.error) {
          return authCheck.error
        }

        // Get all roles
        const rolesResult = await payload.find({
          collection: 'roles',
          limit: 1000,
          sort: 'name',
        })

        // Get all role-permission assignments
        const rolePermissionsResult = await payload.find({
          collection: 'role-permissions',
          limit: 10000,
          where: {
            isActive: {
              equals: true,
            },
          },
          populate: {
            permission: true,
          },
        })

        // Group permissions by role
        const rolesWithPermissions = rolesResult.docs.map(role => {
          const rolePermissions = rolePermissionsResult.docs
            .filter(rp => rp.role === role.id)
            .map(rp => rp.permission)

          return {
            ...role,
            permissions: rolePermissions,
            permissionCount: rolePermissions.length,
          }
        })

        return Response.json({
          success: true,
          data: rolesWithPermissions,
          totalDocs: rolesWithPermissions.length,
        })
      } catch (error) {
        console.error('Error fetching roles with permissions:', error)
        return Response.json({
          success: false,
          message: 'Failed to fetch roles with permissions',
          error: error.message,
        }, { status: 500 })
      }
    },
  },

  // Get permissions for a specific role
  {
    path: '/roles/:roleId/permissions',
    method: 'get',
    handler: async (req, res) => {
      try {
        const { payload } = req
        const { roleId } = req.params

        // Authenticate user
        const user = await authenticateUser(req, payload)
        const authCheck = checkSuperAdminAccess(user)

        if (authCheck.error) {
          return authCheck.error
        }

        const rolePermissionsResult = await payload.find({
          collection: 'role-permissions',
          where: {
            and: [
              {
                role: {
                  equals: roleId,
                },
              },
              {
                isActive: {
                  equals: true,
                },
              },
            ],
          },
          populate: {
            permission: true,
          },
          limit: 1000,
        })

        const permissions = rolePermissionsResult.docs.map(rp => rp.permission)

        return Response.json({
          success: true,
          data: permissions,
          totalDocs: permissions.length,
        })
      } catch (error) {
        console.error('Error fetching role permissions:', error)
        return Response.json({
          success: false,
          message: 'Failed to fetch role permissions',
          error: error.message,
        }, { status: 500 })
      }
    },
  },

  // Assign permissions to a role
  {
    path: '/roles/:roleId/permissions',
    method: 'post',
    handler: async (req, res) => {
      try {
        const { payload } = req
        const { roleId } = req.params
        const { permissionIds } = req.body

        // Authenticate user
        const user = await authenticateUser(req, payload)
        const authCheck = checkSuperAdminAccess(user)

        if (authCheck.error) {
          return authCheck.error
        }

        if (!Array.isArray(permissionIds)) {
          return Response.json({
            success: false,
            message: 'permissionIds must be an array',
          }, { status: 400 })
        }

        // First, deactivate all existing role-permission assignments for this role
        const existingAssignments = await payload.find({
          collection: 'role-permissions',
          where: {
            role: {
              equals: roleId,
            },
          },
          limit: 1000,
        })

        // Deactivate existing assignments
        for (const assignment of existingAssignments.docs) {
          await payload.update({
            collection: 'role-permissions',
            id: assignment.id,
            data: {
              isActive: false,
            },
          })
        }

        // Create new assignments
        const newAssignments = []
        for (const permissionId of permissionIds) {
          const assignment = await payload.create({
            collection: 'role-permissions',
            data: {
              role: roleId,
              permission: permissionId,
              isActive: true,
              assignedBy: req.user?.id,
              assignedAt: new Date(),
            },
          })
          newAssignments.push(assignment)
        }

        return Response.json({
          success: true,
          message: 'Permissions assigned successfully',
          data: newAssignments,
        })
      } catch (error) {
        console.error('Error assigning permissions to role:', error)
        return Response.json({
          success: false,
          message: 'Failed to assign permissions to role',
          error: error.message,
        }, { status: 500 })
      }
    },
  },

  // Remove permission from role
  {
    path: '/roles/:roleId/permissions/:permissionId',
    method: 'delete',
    handler: async (req, res) => {
      try {
        const { payload } = req
        const { roleId, permissionId } = req.params

        // Authenticate user
        const user = await authenticateUser(req, payload)
        const authCheck = checkSuperAdminAccess(user)

        if (authCheck.error) {
          return authCheck.error
        }

        const assignment = await payload.find({
          collection: 'role-permissions',
          where: {
            and: [
              {
                role: {
                  equals: roleId,
                },
              },
              {
                permission: {
                  equals: permissionId,
                },
              },
              {
                isActive: {
                  equals: true,
                },
              },
            ],
          },
          limit: 1,
        })

        if (assignment.docs.length === 0) {
          return Response.json({
            success: false,
            message: 'Role-permission assignment not found',
          }, { status: 404 })
        }

        await payload.update({
          collection: 'role-permissions',
          id: assignment.docs[0].id,
          data: {
            isActive: false,
          },
        })

        return Response.json({
          success: true,
          message: 'Permission removed from role successfully',
        })
      } catch (error) {
        console.error('Error removing permission from role:', error)
        return Response.json({
          success: false,
          message: 'Failed to remove permission from role',
          error: error.message,
        }, { status: 500 })
      }
    },
  },
]
