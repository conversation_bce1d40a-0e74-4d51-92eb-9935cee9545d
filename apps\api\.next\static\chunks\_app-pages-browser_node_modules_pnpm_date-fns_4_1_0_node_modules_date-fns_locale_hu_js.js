"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_hu_js"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hu: () => (/* binding */ hu)\n/* harmony export */ });\n/* harmony import */ var _hu_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./hu/_lib/formatDistance.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatDistance.js\");\n/* harmony import */ var _hu_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./hu/_lib/formatLong.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatLong.js\");\n/* harmony import */ var _hu_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./hu/_lib/formatRelative.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatRelative.js\");\n/* harmony import */ var _hu_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./hu/_lib/localize.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/localize.js\");\n/* harmony import */ var _hu_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./hu/_lib/match.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Hungarian locale.\n * @language Hungarian\n * @iso-639-2 hun\n * <AUTHOR> Shpak [@pshpak](https://github.com/pshpak)\n * <AUTHOR> Pardo [@eduardopsll](https://github.com/eduardopsll)\n * <AUTHOR> Szepesi [@twodcube](https://github.com/twodcube)\n */ const hu = {\n    code: \"hu\",\n    formatDistance: _hu_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _hu_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _hu_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _hu_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _hu_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (hu);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatDistance.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatDistance.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst translations = {\n    about: \"körülbelül\",\n    over: \"több mint\",\n    almost: \"majdnem\",\n    lessthan: \"kevesebb mint\"\n};\nconst withoutSuffixes = {\n    xseconds: \" másodperc\",\n    halfaminute: \"fél perc\",\n    xminutes: \" perc\",\n    xhours: \" óra\",\n    xdays: \" nap\",\n    xweeks: \" hét\",\n    xmonths: \" hónap\",\n    xyears: \" év\"\n};\nconst withSuffixes = {\n    xseconds: {\n        \"-1\": \" másodperccel ezelőtt\",\n        1: \" másodperc múlva\",\n        0: \" másodperce\"\n    },\n    halfaminute: {\n        \"-1\": \"fél perccel ezelőtt\",\n        1: \"fél perc múlva\",\n        0: \"fél perce\"\n    },\n    xminutes: {\n        \"-1\": \" perccel ezelőtt\",\n        1: \" perc múlva\",\n        0: \" perce\"\n    },\n    xhours: {\n        \"-1\": \" órával ezelőtt\",\n        1: \" óra múlva\",\n        0: \" órája\"\n    },\n    xdays: {\n        \"-1\": \" nappal ezelőtt\",\n        1: \" nap múlva\",\n        0: \" napja\"\n    },\n    xweeks: {\n        \"-1\": \" héttel ezelőtt\",\n        1: \" hét múlva\",\n        0: \" hete\"\n    },\n    xmonths: {\n        \"-1\": \" hónappal ezelőtt\",\n        1: \" hónap múlva\",\n        0: \" hónapja\"\n    },\n    xyears: {\n        \"-1\": \" évvel ezelőtt\",\n        1: \" év múlva\",\n        0: \" éve\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    const adverb = token.match(/about|over|almost|lessthan/i);\n    const unit = adverb ? token.replace(adverb[0], \"\") : token;\n    const addSuffix = (options === null || options === void 0 ? void 0 : options.addSuffix) === true;\n    const key = unit.toLowerCase();\n    const comparison = (options === null || options === void 0 ? void 0 : options.comparison) || 0;\n    const translated = addSuffix ? withSuffixes[key][comparison] : withoutSuffixes[key];\n    let result = key === \"halfaminute\" ? translated : count + translated;\n    if (adverb) {\n        const adv = adverb[0].toLowerCase();\n        result = translations[adv] + \" \" + result;\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatLong.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatLong.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"y. MMMM d., EEEE\",\n    long: \"y. MMMM d.\",\n    medium: \"y. MMM d.\",\n    short: \"y. MM. dd.\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} {{time}}\",\n    long: \"{{date}} {{time}}\",\n    medium: \"{{date}} {{time}}\",\n    short: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatRelative.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatRelative.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst accusativeWeekdays = [\n    \"vasárnap\",\n    \"hétfőn\",\n    \"kedden\",\n    \"szerdán\",\n    \"csütörtökön\",\n    \"pénteken\",\n    \"szombaton\"\n];\nfunction week(isFuture) {\n    return (date)=>{\n        const weekday = accusativeWeekdays[date.getDay()];\n        const prefix = isFuture ? \"\" : \"'múlt' \";\n        return \"\".concat(prefix, \"'\").concat(weekday, \"' p'-kor'\");\n    };\n}\nconst formatRelativeLocale = {\n    lastWeek: week(false),\n    yesterday: \"'tegnap' p'-kor'\",\n    today: \"'ma' p'-kor'\",\n    tomorrow: \"'holnap' p'-kor'\",\n    nextWeek: week(true),\n    other: \"P\"\n};\nconst formatRelative = (token, date)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/localize.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/localize.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"ie.\",\n        \"isz.\"\n    ],\n    abbreviated: [\n        \"i. e.\",\n        \"i. sz.\"\n    ],\n    wide: [\n        \"Krisztus előtt\",\n        \"időszámításunk szerint\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1.\",\n        \"2.\",\n        \"3.\",\n        \"4.\"\n    ],\n    abbreviated: [\n        \"1. n.év\",\n        \"2. n.év\",\n        \"3. n.év\",\n        \"4. n.év\"\n    ],\n    wide: [\n        \"1. negyedév\",\n        \"2. negyedév\",\n        \"3. negyedév\",\n        \"4. negyedév\"\n    ]\n};\nconst formattingQuarterValues = {\n    narrow: [\n        \"I.\",\n        \"II.\",\n        \"III.\",\n        \"IV.\"\n    ],\n    abbreviated: [\n        \"I. n.év\",\n        \"II. n.év\",\n        \"III. n.év\",\n        \"IV. n.év\"\n    ],\n    wide: [\n        \"I. negyedév\",\n        \"II. negyedév\",\n        \"III. negyedév\",\n        \"IV. negyedév\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"J\",\n        \"F\",\n        \"M\",\n        \"Á\",\n        \"M\",\n        \"J\",\n        \"J\",\n        \"A\",\n        \"Sz\",\n        \"O\",\n        \"N\",\n        \"D\"\n    ],\n    abbreviated: [\n        \"jan.\",\n        \"febr.\",\n        \"márc.\",\n        \"ápr.\",\n        \"máj.\",\n        \"jún.\",\n        \"júl.\",\n        \"aug.\",\n        \"szept.\",\n        \"okt.\",\n        \"nov.\",\n        \"dec.\"\n    ],\n    wide: [\n        \"január\",\n        \"február\",\n        \"március\",\n        \"április\",\n        \"május\",\n        \"június\",\n        \"július\",\n        \"augusztus\",\n        \"szeptember\",\n        \"október\",\n        \"november\",\n        \"december\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"V\",\n        \"H\",\n        \"K\",\n        \"Sz\",\n        \"Cs\",\n        \"P\",\n        \"Sz\"\n    ],\n    short: [\n        \"V\",\n        \"H\",\n        \"K\",\n        \"Sze\",\n        \"Cs\",\n        \"P\",\n        \"Szo\"\n    ],\n    abbreviated: [\n        \"V\",\n        \"H\",\n        \"K\",\n        \"Sze\",\n        \"Cs\",\n        \"P\",\n        \"Szo\"\n    ],\n    wide: [\n        \"vasárnap\",\n        \"hétfő\",\n        \"kedd\",\n        \"szerda\",\n        \"csütörtök\",\n        \"péntek\",\n        \"szombat\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"de.\",\n        pm: \"du.\",\n        midnight: \"éjfél\",\n        noon: \"dél\",\n        morning: \"reggel\",\n        afternoon: \"du.\",\n        evening: \"este\",\n        night: \"éjjel\"\n    },\n    abbreviated: {\n        am: \"de.\",\n        pm: \"du.\",\n        midnight: \"éjfél\",\n        noon: \"dél\",\n        morning: \"reggel\",\n        afternoon: \"du.\",\n        evening: \"este\",\n        night: \"éjjel\"\n    },\n    wide: {\n        am: \"de.\",\n        pm: \"du.\",\n        midnight: \"éjfél\",\n        noon: \"dél\",\n        morning: \"reggel\",\n        afternoon: \"délután\",\n        evening: \"este\",\n        night: \"éjjel\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    return number + \".\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1,\n        formattingValues: formattingQuarterValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/match.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/match.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)\\.?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(ie\\.|isz\\.)/i,\n    abbreviated: /^(i\\.\\s?e\\.?|b?\\s?c\\s?e|i\\.\\s?sz\\.?)/i,\n    wide: /^(Krisztus előtt|időszámításunk előtt|időszámításunk szerint|i\\. sz\\.)/i\n};\nconst parseEraPatterns = {\n    narrow: [\n        /ie/i,\n        /isz/i\n    ],\n    abbreviated: [\n        /^(i\\.?\\s?e\\.?|b\\s?ce)/i,\n        /^(i\\.?\\s?sz\\.?|c\\s?e)/i\n    ],\n    any: [\n        /előtt/i,\n        /(szerint|i. sz.)/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]\\.?/i,\n    abbreviated: /^[1234]?\\.?\\s?n\\.év/i,\n    wide: /^([1234]|I|II|III|IV)?\\.?\\s?negyedév/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1|I$/i,\n        /2|II$/i,\n        /3|III/i,\n        /4|IV/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[jfmaásond]|sz/i,\n    abbreviated: /^(jan\\.?|febr\\.?|márc\\.?|ápr\\.?|máj\\.?|jún\\.?|júl\\.?|aug\\.?|szept\\.?|okt\\.?|nov\\.?|dec\\.?)/i,\n    wide: /^(január|február|március|április|május|június|július|augusztus|szeptember|október|november|december)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^j/i,\n        /^f/i,\n        /^m/i,\n        /^a|á/i,\n        /^m/i,\n        /^j/i,\n        /^j/i,\n        /^a/i,\n        /^s|sz/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ],\n    any: [\n        /^ja/i,\n        /^f/i,\n        /^már/i,\n        /^áp/i,\n        /^máj/i,\n        /^jún/i,\n        /^júl/i,\n        /^au/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^([vhkpc]|sz|cs|sz)/i,\n    short: /^([vhkp]|sze|cs|szo)/i,\n    abbreviated: /^([vhkp]|sze|cs|szo)/i,\n    wide: /^(vasárnap|hétfő|kedd|szerda|csütörtök|péntek|szombat)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^v/i,\n        /^h/i,\n        /^k/i,\n        /^sz/i,\n        /^c/i,\n        /^p/i,\n        /^sz/i\n    ],\n    any: [\n        /^v/i,\n        /^h/i,\n        /^k/i,\n        /^sze/i,\n        /^c/i,\n        /^p/i,\n        /^szo/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    any: /^((de|du)\\.?|éjfél|délután|dél|reggel|este|éjjel)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^de\\.?/i,\n        pm: /^du\\.?/i,\n        midnight: /^éjf/i,\n        noon: /^dé/i,\n        morning: /reg/i,\n        afternoon: /^délu\\.?/i,\n        evening: /es/i,\n        night: /éjj/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/hu/_lib/match.js\n"));

/***/ })

}]);