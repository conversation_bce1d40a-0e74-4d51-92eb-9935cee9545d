'use client'

import { useState, useCallback } from 'react'
import { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Search, Filter, X, LayoutGrid, List } from 'lucide-react'
import { debounce } from '@/utils/debounce'

interface RolePermissionsFiltersProps {
  activeTab: 'roles' | 'permissions'
}

export function RolePermissionsFilters({ activeTab }: RolePermissionsFiltersProps) {
  const [showAdvanced, setShowAdvanced] = useState(false)

  const {
    rolesFilters,
    permissionsFilters,
    viewMode,
    setRolesFilters,
    setPermissionsFilters,
    setViewMode,
    fetchRoles,
    fetchPermissions
  } = useRolePermissionsStore()

  const currentFilters = activeTab === 'roles' ? rolesFilters : permissionsFilters
  const setCurrentFilters = activeTab === 'roles' ? setRolesFilters : setPermissionsFilters

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce((searchValue: string) => {
      setCurrentFilters({ search: searchValue })
      
      if (activeTab === 'roles') {
        fetchRoles()
      } else {
        fetchPermissions()
      }
    }, 300),
    [activeTab, setCurrentFilters, fetchRoles, fetchPermissions]
  )

  const handleSearchChange = (value: string) => {
    debouncedSearch(value)
  }

  const handleFilterChange = (key: string, value: any) => {
    setCurrentFilters({ [key]: value })
    
    if (activeTab === 'roles') {
      fetchRoles()
    } else {
      fetchPermissions()
    }
  }

  const resetFilters = () => {
    const defaultFilters = {
      search: '',
      isActive: undefined,
      level: '',
      scope: '',
      resource: '',
      action: '',
    }
    
    setCurrentFilters(defaultFilters)
    
    if (activeTab === 'roles') {
      fetchRoles()
    } else {
      fetchPermissions()
    }
  }

  const getActiveFiltersCount = () => {
    let count = 0
    if (currentFilters.search) count++
    if (currentFilters.isActive !== undefined) count++
    if (currentFilters.level) count++
    if (currentFilters.scope) count++
    if (currentFilters.resource) count++
    if (currentFilters.action) count++
    return count
  }

  const activeFiltersCount = getActiveFiltersCount()

  return (
    <div className="space-y-4">
      {/* Main Filter Bar */}
      <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between">
        <div className="flex flex-1 gap-2 items-center">
          {/* Search Input */}
          <div className="relative flex-1 max-w-sm">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder={`Search ${activeTab}...`}
              defaultValue={currentFilters.search}
              onChange={(e) => handleSearchChange(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Advanced Filters Toggle */}
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="gap-2"
          >
            <Filter className="h-4 w-4" />
            Filters
            {activeFiltersCount > 0 && (
              <Badge variant="secondary" className="ml-1">
                {activeFiltersCount}
              </Badge>
            )}
          </Button>

          {/* Reset Filters */}
          {activeFiltersCount > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={resetFilters}
              className="gap-2"
            >
              <X className="h-4 w-4" />
              Reset
            </Button>
          )}
        </div>

        {/* View Mode Toggle */}
        <div className="flex items-center gap-1 border rounded-lg p-1">
          <Button
            variant={viewMode === 'list' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('list')}
            className="h-8 w-8 p-0"
          >
            <List className="h-4 w-4" />
          </Button>
          <Button
            variant={viewMode === 'card' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('card')}
            className="h-8 w-8 p-0"
          >
            <LayoutGrid className="h-4 w-4" />
          </Button>
        </div>
      </div>

      {/* Advanced Filters */}
      {showAdvanced && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 p-4 bg-gray-50 rounded-lg">
          {/* Status Filter */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-1 block">
              Status
            </label>
            <Select
              value={currentFilters.isActive === undefined ? 'all' : currentFilters.isActive.toString()}
              onValueChange={(value) => 
                handleFilterChange('isActive', value === 'all' ? undefined : value === 'true')
              }
            >
              <SelectTrigger>
                <SelectValue placeholder="All statuses" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All statuses</SelectItem>
                <SelectItem value="true">Active</SelectItem>
                <SelectItem value="false">Inactive</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Role-specific filters */}
          {activeTab === 'roles' && (
            <>
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">
                  Level
                </label>
                <Select
                  value={currentFilters.level || 'all'}
                  onValueChange={(value) => 
                    handleFilterChange('level', value === 'all' ? '' : value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All levels" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All levels</SelectItem>
                    <SelectItem value="1">Level 1 - Super Admin</SelectItem>
                    <SelectItem value="2">Level 2 - Institute Admin</SelectItem>
                    <SelectItem value="3">Level 3 - User Level</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">
                  Scope
                </label>
                <Input
                  placeholder="Filter by scope"
                  value={currentFilters.scope || ''}
                  onChange={(e) => handleFilterChange('scope', e.target.value)}
                />
              </div>
            </>
          )}

          {/* Permission-specific filters */}
          {activeTab === 'permissions' && (
            <>
              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">
                  Resource
                </label>
                <Select
                  value={currentFilters.resource || 'all'}
                  onValueChange={(value) => 
                    handleFilterChange('resource', value === 'all' ? '' : value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All resources" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All resources</SelectItem>
                    <SelectItem value="users">Users</SelectItem>
                    <SelectItem value="roles">Roles</SelectItem>
                    <SelectItem value="permissions">Permissions</SelectItem>
                    <SelectItem value="institutes">Institutes</SelectItem>
                    <SelectItem value="courses">Courses</SelectItem>
                    <SelectItem value="settings">Settings</SelectItem>
                    <SelectItem value="analytics">Analytics</SelectItem>
                    <SelectItem value="billing">Billing</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <label className="text-sm font-medium text-gray-700 mb-1 block">
                  Action
                </label>
                <Select
                  value={currentFilters.action || 'all'}
                  onValueChange={(value) => 
                    handleFilterChange('action', value === 'all' ? '' : value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="All actions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All actions</SelectItem>
                    <SelectItem value="create">Create</SelectItem>
                    <SelectItem value="read">Read</SelectItem>
                    <SelectItem value="update">Update</SelectItem>
                    <SelectItem value="delete">Delete</SelectItem>
                    <SelectItem value="manage">Manage</SelectItem>
                    <SelectItem value="view">View</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </>
          )}
        </div>
      )}

      {/* Active Filters Display */}
      {activeFiltersCount > 0 && (
        <div className="flex flex-wrap gap-2">
          {currentFilters.search && (
            <Badge variant="secondary" className="gap-1">
              Search: {currentFilters.search}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleFilterChange('search', '')}
              />
            </Badge>
          )}
          {currentFilters.isActive !== undefined && (
            <Badge variant="secondary" className="gap-1">
              Status: {currentFilters.isActive ? 'Active' : 'Inactive'}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleFilterChange('isActive', undefined)}
              />
            </Badge>
          )}
          {currentFilters.level && (
            <Badge variant="secondary" className="gap-1">
              Level: {currentFilters.level}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleFilterChange('level', '')}
              />
            </Badge>
          )}
          {currentFilters.resource && (
            <Badge variant="secondary" className="gap-1">
              Resource: {currentFilters.resource}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleFilterChange('resource', '')}
              />
            </Badge>
          )}
          {currentFilters.action && (
            <Badge variant="secondary" className="gap-1">
              Action: {currentFilters.action}
              <X 
                className="h-3 w-3 cursor-pointer" 
                onClick={() => handleFilterChange('action', '')}
              />
            </Badge>
          )}
        </div>
      )}
    </div>
  )
}
