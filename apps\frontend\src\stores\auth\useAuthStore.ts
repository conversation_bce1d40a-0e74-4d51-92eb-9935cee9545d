import { create } from 'zustand'
import { persist } from 'zustand/middleware'

interface User {
  id: string
  email: string
  firstName: string
  lastName: string
  role: 'super_admin' | 'platform_staff' | 'institute_admin' | 'branch_manager' | 'trainer' | 'institute_staff' | 'student'
  avatar?: string
  institute?: string
  isActive: boolean
  lastLogin?: string
}

interface AuthState {
  user: User | null
  token: string | null
  isLoading: boolean
  isAuthenticated: boolean

  // Actions
  login: (email: string, password: string, userType?: string) => Promise<void>
  register: (userData: any, userType?: string) => Promise<void>
  logout: () => void
  setUser: (user: User | null) => void
  setToken: (token: string | null) => void
  setLoading: (loading: boolean) => void
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isLoading: false, // Start with false to prevent infinite loading
      isAuthenticated: false,

      login: async (email: string, password: string, userType = 'student') => {
        set({ isLoading: true })

        try {
          // Use the auth login endpoint
          const endpoint = '/api/auth/login'

          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include', // Include cookies for cross-origin requests
            body: JSON.stringify({
              email,
              password,
              userType
            }),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.message || errorData.error || 'Login failed')
          }

          const data = await response.json()

          // Validate that the user has the expected role
          if (userType === 'super_admin' && !['super_admin', 'platform_staff'].includes(data.user.role)) {
            throw new Error('Access denied. Super admin privileges required.')
          }
          if (userType === 'institute_admin' && !['institute_admin', 'branch_manager', 'trainer', 'institute_staff'].includes(data.user.role)) {
            throw new Error('Access denied. Institute admin privileges required.')
          }
          if (userType === 'student' && data.user.role !== 'student') {
            throw new Error('Access denied. Student account required.')
          }

          set({
            user: data.user,
            token: data.token,
            isAuthenticated: true,
            isLoading: false
          })

          // Store token in localStorage as backup
          if (data.token) {
            localStorage.setItem('auth_token', data.token)
          }

        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      register: async (userData: any, userType = 'student') => {
        set({ isLoading: true })

        try {
          // Determine the correct API endpoint based on user type
          let endpoint = '/api/auth/register'
          if (userType === 'institute') {
            endpoint = '/api/auth/register' // Institute registration endpoint
          }

          const requestData = {
            ...userData,
            role: userType === 'institute' ? 'institute_admin' : 'student'
          }

          // Debug: Log the data being sent
          console.log('Registration data being sent:', requestData)
          console.log('Endpoint:', `${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`)

          const response = await fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}${endpoint}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
            },
            credentials: 'include',
            body: JSON.stringify(requestData),
          })

          if (!response.ok) {
            const errorData = await response.json()
            throw new Error(errorData.message || errorData.error || 'Registration failed')
          }

          const data = await response.json()

          // For institute registration, don't auto-login (needs approval)
          if (userType === 'institute') {
            set({ isLoading: false })
            return
          }

          // For student registration, auto-login
          set({
            user: data.user,
            token: data.token,
            isAuthenticated: true,
            isLoading: false
          })

          if (data.token) {
            localStorage.setItem('auth_token', data.token)
          }

        } catch (error) {
          set({ isLoading: false })
          throw error
        }
      },

      logout: () => {
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          isLoading: false
        })
        
        // Clear localStorage
        localStorage.removeItem('auth_token')
        
        // Redirect based on user role
        const { user } = get()
        if (user?.role === 'super_admin' || user?.role === 'platform_staff') {
          window.location.href = '/auth/admin/login'
        } else if (user?.role === 'student') {
          window.location.href = '/auth/user-login'
        } else {
          window.location.href = '/auth/login'
        }
      },

      setUser: (user: User | null) => {
        set({ user, isAuthenticated: !!user })
      },

      setToken: (token: string | null) => {
        set({ token })
        if (token) {
          localStorage.setItem('auth_token', token)
        } else {
          localStorage.removeItem('auth_token')
        }
      },

      setLoading: (loading: boolean) => {
        set({ isLoading: loading })
      },

      // Initialize auth state from localStorage
      initialize: () => {
        set({ isLoading: true })

        const token = localStorage.getItem('auth_token')
        if (token) {
          // If we have a token, verify it's still valid
          fetch(`${process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001'}/api/auth/verify`, {
            method: 'POST',
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            },
            credentials: 'include'
          })
          .then(response => response.json())
          .then(data => {
            if (data.success && data.user) {
              set({
                user: data.user,
                token: token,
                isAuthenticated: true,
                isLoading: false
              })
            } else {
              // Token is invalid, clear it
              localStorage.removeItem('auth_token')
              set({
                user: null,
                token: null,
                isAuthenticated: false,
                isLoading: false
              })
            }
          })
          .catch((error) => {
            console.error('Token verification failed:', error)
            // Error verifying token, clear it
            localStorage.removeItem('auth_token')
            set({
              user: null,
              token: null,
              isAuthenticated: false,
              isLoading: false
            })
          })
        } else {
          // No token found
          set({
            user: null,
            token: null,
            isAuthenticated: false,
            isLoading: false
          })
        }
      }
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated
      })
    }
  )
)
