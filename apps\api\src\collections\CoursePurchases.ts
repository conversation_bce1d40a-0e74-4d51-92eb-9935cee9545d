import { CollectionConfig } from 'payload/types'

const CoursePurchases: CollectionConfig = {
  slug: 'course-purchases',
  admin: {
    useAsTitle: 'id',
    defaultColumns: ['student', 'course', 'amount', 'commissionAmount', 'purchaseDate'],
    group: 'Billing Management',
  },
  access: {
    read: ({ req: { user } }) => {
      if (!user) return false

      // Use legacyRole field for access control
      if (user.legacyRole === 'super_admin') return true

      if (user.legacyRole === 'institute_admin') {
        return { 'course.institute': { equals: user.institute } }
      }

      if (user.legacyRole === 'branch_admin') {
        return { 'course.branch': { equals: user.branch } }
      }

      if (user.legacyRole === 'student') {
        return { student: { equals: user.id } }
      }

      return false
    },
    create: () => true, // Students can create purchases
    update: ({ req: { user } }) => {
      if (!user) return false
      return user.legacyRole === 'super_admin'
    },
    delete: ({ req: { user } }) => {
      if (!user) return false
      return user.legacyRole === 'super_admin'
    },
  },
  fields: [
    {
      name: 'student',
      type: 'relationship',
      relationTo: 'users',
      required: true,
      index: true,
      filterOptions: {
        role: { equals: 'student' },
      },
    },
    {
      name: 'course',
      type: 'relationship',
      relationTo: 'courses',
      required: true,
      index: true,
    },
    {
      name: 'branch',
      type: 'relationship',
      relationTo: 'branches',
      required: true,
      index: true,
    },
    {
      name: 'purchaseDetails',
      type: 'group',
      fields: [
        {
          name: 'originalPrice',
          type: 'number',
          required: true,
          min: 0,
        },
        {
          name: 'discountAmount',
          type: 'number',
          defaultValue: 0,
          min: 0,
        },
        {
          name: 'finalAmount',
          type: 'number',
          required: true,
          min: 0,
        },
        {
          name: 'currency',
          type: 'select',
          required: true,
          defaultValue: 'INR',
          options: [
            { label: 'Indian Rupee (₹)', value: 'INR' },
            { label: 'US Dollar ($)', value: 'USD' },
            { label: 'Euro (€)', value: 'EUR' },
          ],
        },
      ],
    },
    {
      name: 'commissionDetails',
      type: 'group',
      fields: [
        {
          name: 'commissionRate',
          type: 'number',
          required: true,
          min: 0,
          max: 100,
          admin: {
            description: 'Commission rate as percentage',
          },
        },
        {
          name: 'commissionAmount',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            readOnly: true,
            description: 'Calculated commission amount',
          },
        },
        {
          name: 'branchReceives',
          type: 'number',
          required: true,
          min: 0,
          admin: {
            readOnly: true,
            description: 'Amount branch receives after commission',
          },
        },
      ],
    },
    {
      name: 'paymentDetails',
      type: 'group',
      fields: [
        {
          name: 'paymentMethod',
          type: 'select',
          required: true,
          options: [
            { label: 'Razorpay', value: 'razorpay' },
            { label: 'Stripe', value: 'stripe' },
            { label: 'PayPal', value: 'paypal' },
            { label: 'UPI', value: 'upi' },
            { label: 'Credit Card', value: 'credit_card' },
            { label: 'Debit Card', value: 'debit_card' },
          ],
        },
        {
          name: 'transactionId',
          type: 'text',
          required: true,
          index: true,
        },
        {
          name: 'paymentGatewayResponse',
          type: 'json',
        },
        {
          name: 'paymentStatus',
          type: 'select',
          required: true,
          defaultValue: 'pending',
          options: [
            { label: 'Pending', value: 'pending' },
            { label: 'Processing', value: 'processing' },
            { label: 'Completed', value: 'completed' },
            { label: 'Failed', value: 'failed' },
            { label: 'Refunded', value: 'refunded' },
          ],
          index: true,
        },
      ],
    },
    {
      name: 'billingInfo',
      type: 'group',
      fields: [
        {
          name: 'addedToBill',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Whether commission is added to monthly bill',
          },
        },
        {
          name: 'billId',
          type: 'relationship',
          relationTo: 'bills',
          admin: {
            condition: (data) => data.billingInfo?.addedToBill,
          },
        },
        {
          name: 'billingMonth',
          type: 'number',
          min: 1,
          max: 12,
        },
        {
          name: 'billingYear',
          type: 'number',
        },
      ],
    },
    {
      name: 'purchaseDate',
      type: 'date',
      required: true,
      defaultValue: () => new Date(),
      index: true,
    },
    {
      name: 'accessDetails',
      type: 'group',
      fields: [
        {
          name: 'accessGranted',
          type: 'checkbox',
          defaultValue: false,
        },
        {
          name: 'accessStartDate',
          type: 'date',
        },
        {
          name: 'accessEndDate',
          type: 'date',
        },
        {
          name: 'isActive',
          type: 'checkbox',
          defaultValue: true,
        },
      ],
    },
  ],
  hooks: {
    beforeChange: [
      async ({ req, operation, data }) => {
        if (operation === 'create' || operation === 'update') {
          // Calculate commission amounts
          if (data.purchaseDetails?.finalAmount && data.commissionDetails?.commissionRate) {
            const finalAmount = data.purchaseDetails.finalAmount
            const commissionRate = data.commissionDetails.commissionRate

            data.commissionDetails.commissionAmount = (finalAmount * commissionRate) / 100
            data.commissionDetails.branchReceives = finalAmount - data.commissionDetails.commissionAmount
          }

          // Set billing month/year based on purchase date
          if (data.purchaseDate) {
            const purchaseDate = new Date(data.purchaseDate)
            data.billingInfo = {
              ...data.billingInfo,
              billingMonth: purchaseDate.getMonth() + 1,
              billingYear: purchaseDate.getFullYear()
            }
          }

          // Grant access if payment is completed
          if (data.paymentDetails?.paymentStatus === 'completed' && !data.accessDetails?.accessGranted) {
            const accessStart = new Date()
            const accessEnd = new Date()
            accessEnd.setFullYear(accessEnd.getFullYear() + 1) // 1 year access

            data.accessDetails = {
              ...data.accessDetails,
              accessGranted: true,
              accessStartDate: accessStart,
              accessEndDate: accessEnd,
              isActive: true
            }
          }
        }
        return data
      },
    ],
  },
  timestamps: true,
}

export default CoursePurchases
