"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_uk_js"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js":
/*!***********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js ***!
  \***********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   isSameWeek: () => (/* binding */ isSameWeek)\n/* harmony export */ });\n/* harmony import */ var _lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./_lib/normalizeDates.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/_lib/normalizeDates.js\");\n/* harmony import */ var _startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./startOfWeek.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/startOfWeek.js\");\n\n\n/**\n * The {@link isSameWeek} function options.\n */ /**\n * @name isSameWeek\n * @category Week Helpers\n * @summary Are the given dates in the same week (and month and year)?\n *\n * @description\n * Are the given dates in the same week (and month and year)?\n *\n * @param laterDate - The first date to check\n * @param earlierDate - The second date to check\n * @param options - An object with options\n *\n * @returns The dates are in the same week (and month and year)\n *\n * @example\n * // Are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4))\n * //=> true\n *\n * @example\n * // If week starts with Monday,\n * // are 31 August 2014 and 4 September 2014 in the same week?\n * const result = isSameWeek(new Date(2014, 7, 31), new Date(2014, 8, 4), {\n *   weekStartsOn: 1\n * })\n * //=> false\n *\n * @example\n * // Are 1 January 2014 and 1 January 2015 in the same week?\n * const result = isSameWeek(new Date(2014, 0, 1), new Date(2015, 0, 1))\n * //=> false\n */ function isSameWeek(laterDate, earlierDate, options) {\n    const [laterDate_, earlierDate_] = (0,_lib_normalizeDates_js__WEBPACK_IMPORTED_MODULE_0__.normalizeDates)(options === null || options === void 0 ? void 0 : options.in, laterDate, earlierDate);\n    return +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(laterDate_, options) === +(0,_startOfWeek_js__WEBPACK_IMPORTED_MODULE_1__.startOfWeek)(earlierDate_, options);\n}\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (isSameWeek);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   uk: () => (/* binding */ uk)\n/* harmony export */ });\n/* harmony import */ var _uk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./uk/_lib/formatDistance.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatDistance.js\");\n/* harmony import */ var _uk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./uk/_lib/formatLong.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatLong.js\");\n/* harmony import */ var _uk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./uk/_lib/formatRelative.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatRelative.js\");\n/* harmony import */ var _uk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./uk/_lib/localize.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/localize.js\");\n/* harmony import */ var _uk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./uk/_lib/match.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Ukrainian locale.\n * @language Ukrainian\n * @iso-639-2 ukr\n * <AUTHOR> Korzh [@korzhyk](https://github.com/korzhyk)\n * <AUTHOR> Shcherbyak [@shcherbyakdev](https://github.com/shcherbyakdev)\n */ const uk = {\n    code: \"uk\",\n    formatDistance: _uk_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _uk_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _uk_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _uk_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _uk_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (uk);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatDistance.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatDistance.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nfunction declension(scheme, count) {\n    // scheme for count=1 exists\n    if (scheme.one !== undefined && count === 1) {\n        return scheme.one;\n    }\n    const rem10 = count % 10;\n    const rem100 = count % 100;\n    // 1, 21, 31, ...\n    if (rem10 === 1 && rem100 !== 11) {\n        return scheme.singularNominative.replace(\"{{count}}\", String(count));\n    // 2, 3, 4, 22, 23, 24, 32 ...\n    } else if (rem10 >= 2 && rem10 <= 4 && (rem100 < 10 || rem100 > 20)) {\n        return scheme.singularGenitive.replace(\"{{count}}\", String(count));\n    // 5, 6, 7, 8, 9, 10, 11, ...\n    } else {\n        return scheme.pluralGenitive.replace(\"{{count}}\", String(count));\n    }\n}\nfunction buildLocalizeTokenFn(scheme) {\n    return (count, options)=>{\n        if (options && options.addSuffix) {\n            if (options.comparison && options.comparison > 0) {\n                if (scheme.future) {\n                    return declension(scheme.future, count);\n                } else {\n                    return \"за \" + declension(scheme.regular, count);\n                }\n            } else {\n                if (scheme.past) {\n                    return declension(scheme.past, count);\n                } else {\n                    return declension(scheme.regular, count) + \" тому\";\n                }\n            }\n        } else {\n            return declension(scheme.regular, count);\n        }\n    };\n}\nconst halfAtMinute = (_, options)=>{\n    if (options && options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"за півхвилини\";\n        } else {\n            return \"півхвилини тому\";\n        }\n    }\n    return \"півхвилини\";\n};\nconst formatDistanceLocale = {\n    lessThanXSeconds: buildLocalizeTokenFn({\n        regular: {\n            one: \"менше секунди\",\n            singularNominative: \"менше {{count}} секунди\",\n            singularGenitive: \"менше {{count}} секунд\",\n            pluralGenitive: \"менше {{count}} секунд\"\n        },\n        future: {\n            one: \"менше, ніж за секунду\",\n            singularNominative: \"менше, ніж за {{count}} секунду\",\n            singularGenitive: \"менше, ніж за {{count}} секунди\",\n            pluralGenitive: \"менше, ніж за {{count}} секунд\"\n        }\n    }),\n    xSeconds: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} секунда\",\n            singularGenitive: \"{{count}} секунди\",\n            pluralGenitive: \"{{count}} секунд\"\n        },\n        past: {\n            singularNominative: \"{{count}} секунду тому\",\n            singularGenitive: \"{{count}} секунди тому\",\n            pluralGenitive: \"{{count}} секунд тому\"\n        },\n        future: {\n            singularNominative: \"за {{count}} секунду\",\n            singularGenitive: \"за {{count}} секунди\",\n            pluralGenitive: \"за {{count}} секунд\"\n        }\n    }),\n    halfAMinute: halfAtMinute,\n    lessThanXMinutes: buildLocalizeTokenFn({\n        regular: {\n            one: \"менше хвилини\",\n            singularNominative: \"менше {{count}} хвилини\",\n            singularGenitive: \"менше {{count}} хвилин\",\n            pluralGenitive: \"менше {{count}} хвилин\"\n        },\n        future: {\n            one: \"менше, ніж за хвилину\",\n            singularNominative: \"менше, ніж за {{count}} хвилину\",\n            singularGenitive: \"менше, ніж за {{count}} хвилини\",\n            pluralGenitive: \"менше, ніж за {{count}} хвилин\"\n        }\n    }),\n    xMinutes: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} хвилина\",\n            singularGenitive: \"{{count}} хвилини\",\n            pluralGenitive: \"{{count}} хвилин\"\n        },\n        past: {\n            singularNominative: \"{{count}} хвилину тому\",\n            singularGenitive: \"{{count}} хвилини тому\",\n            pluralGenitive: \"{{count}} хвилин тому\"\n        },\n        future: {\n            singularNominative: \"за {{count}} хвилину\",\n            singularGenitive: \"за {{count}} хвилини\",\n            pluralGenitive: \"за {{count}} хвилин\"\n        }\n    }),\n    aboutXHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} години\",\n            singularGenitive: \"близько {{count}} годин\",\n            pluralGenitive: \"близько {{count}} годин\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} годину\",\n            singularGenitive: \"приблизно за {{count}} години\",\n            pluralGenitive: \"приблизно за {{count}} годин\"\n        }\n    }),\n    xHours: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} годину\",\n            singularGenitive: \"{{count}} години\",\n            pluralGenitive: \"{{count}} годин\"\n        }\n    }),\n    xDays: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} день\",\n            singularGenitive: \"{{count}} днi\",\n            pluralGenitive: \"{{count}} днів\"\n        }\n    }),\n    aboutXWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} тижня\",\n            singularGenitive: \"близько {{count}} тижнів\",\n            pluralGenitive: \"близько {{count}} тижнів\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} тиждень\",\n            singularGenitive: \"приблизно за {{count}} тижні\",\n            pluralGenitive: \"приблизно за {{count}} тижнів\"\n        }\n    }),\n    xWeeks: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} тиждень\",\n            singularGenitive: \"{{count}} тижні\",\n            pluralGenitive: \"{{count}} тижнів\"\n        }\n    }),\n    aboutXMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} місяця\",\n            singularGenitive: \"близько {{count}} місяців\",\n            pluralGenitive: \"близько {{count}} місяців\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} місяць\",\n            singularGenitive: \"приблизно за {{count}} місяці\",\n            pluralGenitive: \"приблизно за {{count}} місяців\"\n        }\n    }),\n    xMonths: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} місяць\",\n            singularGenitive: \"{{count}} місяці\",\n            pluralGenitive: \"{{count}} місяців\"\n        }\n    }),\n    aboutXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"близько {{count}} року\",\n            singularGenitive: \"близько {{count}} років\",\n            pluralGenitive: \"близько {{count}} років\"\n        },\n        future: {\n            singularNominative: \"приблизно за {{count}} рік\",\n            singularGenitive: \"приблизно за {{count}} роки\",\n            pluralGenitive: \"приблизно за {{count}} років\"\n        }\n    }),\n    xYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"{{count}} рік\",\n            singularGenitive: \"{{count}} роки\",\n            pluralGenitive: \"{{count}} років\"\n        }\n    }),\n    overXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"більше {{count}} року\",\n            singularGenitive: \"більше {{count}} років\",\n            pluralGenitive: \"більше {{count}} років\"\n        },\n        future: {\n            singularNominative: \"більше, ніж за {{count}} рік\",\n            singularGenitive: \"більше, ніж за {{count}} роки\",\n            pluralGenitive: \"більше, ніж за {{count}} років\"\n        }\n    }),\n    almostXYears: buildLocalizeTokenFn({\n        regular: {\n            singularNominative: \"майже {{count}} рік\",\n            singularGenitive: \"майже {{count}} роки\",\n            pluralGenitive: \"майже {{count}} років\"\n        },\n        future: {\n            singularNominative: \"майже за {{count}} рік\",\n            singularGenitive: \"майже за {{count}} роки\",\n            pluralGenitive: \"майже за {{count}} років\"\n        }\n    })\n};\nconst formatDistance = (token, count, options)=>{\n    options = options || {};\n    return formatDistanceLocale[token](count, options);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS91ay9fbGliL2Zvcm1hdERpc3RhbmNlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxTQUFTQSxXQUFXQyxNQUFNLEVBQUVDLEtBQUs7SUFDL0IsNEJBQTRCO0lBQzVCLElBQUlELE9BQU9FLEdBQUcsS0FBS0MsYUFBYUYsVUFBVSxHQUFHO1FBQzNDLE9BQU9ELE9BQU9FLEdBQUc7SUFDbkI7SUFFQSxNQUFNRSxRQUFRSCxRQUFRO0lBQ3RCLE1BQU1JLFNBQVNKLFFBQVE7SUFFdkIsaUJBQWlCO0lBQ2pCLElBQUlHLFVBQVUsS0FBS0MsV0FBVyxJQUFJO1FBQ2hDLE9BQU9MLE9BQU9NLGtCQUFrQixDQUFDQyxPQUFPLENBQUMsYUFBYUMsT0FBT1A7SUFFN0QsOEJBQThCO0lBQ2hDLE9BQU8sSUFBSUcsU0FBUyxLQUFLQSxTQUFTLEtBQU1DLENBQUFBLFNBQVMsTUFBTUEsU0FBUyxFQUFDLEdBQUk7UUFDbkUsT0FBT0wsT0FBT1MsZ0JBQWdCLENBQUNGLE9BQU8sQ0FBQyxhQUFhQyxPQUFPUDtJQUUzRCw2QkFBNkI7SUFDL0IsT0FBTztRQUNMLE9BQU9ELE9BQU9VLGNBQWMsQ0FBQ0gsT0FBTyxDQUFDLGFBQWFDLE9BQU9QO0lBQzNEO0FBQ0Y7QUFFQSxTQUFTVSxxQkFBcUJYLE1BQU07SUFDbEMsT0FBTyxDQUFDQyxPQUFPVztRQUNiLElBQUlBLFdBQVdBLFFBQVFDLFNBQVMsRUFBRTtZQUNoQyxJQUFJRCxRQUFRRSxVQUFVLElBQUlGLFFBQVFFLFVBQVUsR0FBRyxHQUFHO2dCQUNoRCxJQUFJZCxPQUFPZSxNQUFNLEVBQUU7b0JBQ2pCLE9BQU9oQixXQUFXQyxPQUFPZSxNQUFNLEVBQUVkO2dCQUNuQyxPQUFPO29CQUNMLE9BQU8sUUFBUUYsV0FBV0MsT0FBT2dCLE9BQU8sRUFBRWY7Z0JBQzVDO1lBQ0YsT0FBTztnQkFDTCxJQUFJRCxPQUFPaUIsSUFBSSxFQUFFO29CQUNmLE9BQU9sQixXQUFXQyxPQUFPaUIsSUFBSSxFQUFFaEI7Z0JBQ2pDLE9BQU87b0JBQ0wsT0FBT0YsV0FBV0MsT0FBT2dCLE9BQU8sRUFBRWYsU0FBUztnQkFDN0M7WUFDRjtRQUNGLE9BQU87WUFDTCxPQUFPRixXQUFXQyxPQUFPZ0IsT0FBTyxFQUFFZjtRQUNwQztJQUNGO0FBQ0Y7QUFFQSxNQUFNaUIsZUFBZSxDQUFDQyxHQUFHUDtJQUN2QixJQUFJQSxXQUFXQSxRQUFRQyxTQUFTLEVBQUU7UUFDaEMsSUFBSUQsUUFBUUUsVUFBVSxJQUFJRixRQUFRRSxVQUFVLEdBQUcsR0FBRztZQUNoRCxPQUFPO1FBQ1QsT0FBTztZQUNMLE9BQU87UUFDVDtJQUNGO0lBRUEsT0FBTztBQUNUO0FBRUEsTUFBTU0sdUJBQXVCO0lBQzNCQyxrQkFBa0JWLHFCQUFxQjtRQUNyQ0ssU0FBUztZQUNQZCxLQUFLO1lBQ0xJLG9CQUFvQjtZQUNwQkcsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7UUFDQUssUUFBUTtZQUNOYixLQUFLO1lBQ0xJLG9CQUFvQjtZQUNwQkcsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBWSxVQUFVWCxxQkFBcUI7UUFDN0JLLFNBQVM7WUFDUFYsb0JBQW9CO1lBQ3BCRyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtRQUNBTyxNQUFNO1lBQ0pYLG9CQUFvQjtZQUNwQkcsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7UUFDQUssUUFBUTtZQUNOVCxvQkFBb0I7WUFDcEJHLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQWEsYUFBYUw7SUFFYk0sa0JBQWtCYixxQkFBcUI7UUFDckNLLFNBQVM7WUFDUGQsS0FBSztZQUNMSSxvQkFBb0I7WUFDcEJHLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO1FBQ0FLLFFBQVE7WUFDTmIsS0FBSztZQUNMSSxvQkFBb0I7WUFDcEJHLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQWUsVUFBVWQscUJBQXFCO1FBQzdCSyxTQUFTO1lBQ1BWLG9CQUFvQjtZQUNwQkcsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7UUFDQU8sTUFBTTtZQUNKWCxvQkFBb0I7WUFDcEJHLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO1FBQ0FLLFFBQVE7WUFDTlQsb0JBQW9CO1lBQ3BCRyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUFnQixhQUFhZixxQkFBcUI7UUFDaENLLFNBQVM7WUFDUFYsb0JBQW9CO1lBQ3BCRyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtRQUNBSyxRQUFRO1lBQ05ULG9CQUFvQjtZQUNwQkcsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7SUFDRjtJQUVBaUIsUUFBUWhCLHFCQUFxQjtRQUMzQkssU0FBUztZQUNQVixvQkFBb0I7WUFDcEJHLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQWtCLE9BQU9qQixxQkFBcUI7UUFDMUJLLFNBQVM7WUFDUFYsb0JBQW9CO1lBQ3BCRyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUFtQixhQUFhbEIscUJBQXFCO1FBQ2hDSyxTQUFTO1lBQ1BWLG9CQUFvQjtZQUNwQkcsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7UUFDQUssUUFBUTtZQUNOVCxvQkFBb0I7WUFDcEJHLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQW9CLFFBQVFuQixxQkFBcUI7UUFDM0JLLFNBQVM7WUFDUFYsb0JBQW9CO1lBQ3BCRyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUFxQixjQUFjcEIscUJBQXFCO1FBQ2pDSyxTQUFTO1lBQ1BWLG9CQUFvQjtZQUNwQkcsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7UUFDQUssUUFBUTtZQUNOVCxvQkFBb0I7WUFDcEJHLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQXNCLFNBQVNyQixxQkFBcUI7UUFDNUJLLFNBQVM7WUFDUFYsb0JBQW9CO1lBQ3BCRyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUF1QixhQUFhdEIscUJBQXFCO1FBQ2hDSyxTQUFTO1lBQ1BWLG9CQUFvQjtZQUNwQkcsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7UUFDQUssUUFBUTtZQUNOVCxvQkFBb0I7WUFDcEJHLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQXdCLFFBQVF2QixxQkFBcUI7UUFDM0JLLFNBQVM7WUFDUFYsb0JBQW9CO1lBQ3BCRyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtJQUNGO0lBRUF5QixZQUFZeEIscUJBQXFCO1FBQy9CSyxTQUFTO1lBQ1BWLG9CQUFvQjtZQUNwQkcsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7UUFDQUssUUFBUTtZQUNOVCxvQkFBb0I7WUFDcEJHLGtCQUFrQjtZQUNsQkMsZ0JBQWdCO1FBQ2xCO0lBQ0Y7SUFFQTBCLGNBQWN6QixxQkFBcUI7UUFDakNLLFNBQVM7WUFDUFYsb0JBQW9CO1lBQ3BCRyxrQkFBa0I7WUFDbEJDLGdCQUFnQjtRQUNsQjtRQUNBSyxRQUFRO1lBQ05ULG9CQUFvQjtZQUNwQkcsa0JBQWtCO1lBQ2xCQyxnQkFBZ0I7UUFDbEI7SUFDRjtBQUNGO0FBRU8sTUFBTTJCLGlCQUFpQixDQUFDQyxPQUFPckMsT0FBT1c7SUFDM0NBLFVBQVVBLFdBQVcsQ0FBQztJQUV0QixPQUFPUSxvQkFBb0IsQ0FBQ2tCLE1BQU0sQ0FBQ3JDLE9BQU9XO0FBQzVDLEVBQUUiLCJzb3VyY2VzIjpbIkM6XFxwcm9qZWN0c1xcbG1zXFxub2RlX21vZHVsZXNcXC5wbnBtXFxkYXRlLWZuc0A0LjEuMFxcbm9kZV9tb2R1bGVzXFxkYXRlLWZuc1xcbG9jYWxlXFx1a1xcX2xpYlxcZm9ybWF0RGlzdGFuY2UuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZnVuY3Rpb24gZGVjbGVuc2lvbihzY2hlbWUsIGNvdW50KSB7XG4gIC8vIHNjaGVtZSBmb3IgY291bnQ9MSBleGlzdHNcbiAgaWYgKHNjaGVtZS5vbmUgIT09IHVuZGVmaW5lZCAmJiBjb3VudCA9PT0gMSkge1xuICAgIHJldHVybiBzY2hlbWUub25lO1xuICB9XG5cbiAgY29uc3QgcmVtMTAgPSBjb3VudCAlIDEwO1xuICBjb25zdCByZW0xMDAgPSBjb3VudCAlIDEwMDtcblxuICAvLyAxLCAyMSwgMzEsIC4uLlxuICBpZiAocmVtMTAgPT09IDEgJiYgcmVtMTAwICE9PSAxMSkge1xuICAgIHJldHVybiBzY2hlbWUuc2luZ3VsYXJOb21pbmF0aXZlLnJlcGxhY2UoXCJ7e2NvdW50fX1cIiwgU3RyaW5nKGNvdW50KSk7XG5cbiAgICAvLyAyLCAzLCA0LCAyMiwgMjMsIDI0LCAzMiAuLi5cbiAgfSBlbHNlIGlmIChyZW0xMCA+PSAyICYmIHJlbTEwIDw9IDQgJiYgKHJlbTEwMCA8IDEwIHx8IHJlbTEwMCA+IDIwKSkge1xuICAgIHJldHVybiBzY2hlbWUuc2luZ3VsYXJHZW5pdGl2ZS5yZXBsYWNlKFwie3tjb3VudH19XCIsIFN0cmluZyhjb3VudCkpO1xuXG4gICAgLy8gNSwgNiwgNywgOCwgOSwgMTAsIDExLCAuLi5cbiAgfSBlbHNlIHtcbiAgICByZXR1cm4gc2NoZW1lLnBsdXJhbEdlbml0aXZlLnJlcGxhY2UoXCJ7e2NvdW50fX1cIiwgU3RyaW5nKGNvdW50KSk7XG4gIH1cbn1cblxuZnVuY3Rpb24gYnVpbGRMb2NhbGl6ZVRva2VuRm4oc2NoZW1lKSB7XG4gIHJldHVybiAoY291bnQsIG9wdGlvbnMpID0+IHtcbiAgICBpZiAob3B0aW9ucyAmJiBvcHRpb25zLmFkZFN1ZmZpeCkge1xuICAgICAgaWYgKG9wdGlvbnMuY29tcGFyaXNvbiAmJiBvcHRpb25zLmNvbXBhcmlzb24gPiAwKSB7XG4gICAgICAgIGlmIChzY2hlbWUuZnV0dXJlKSB7XG4gICAgICAgICAgcmV0dXJuIGRlY2xlbnNpb24oc2NoZW1lLmZ1dHVyZSwgY291bnQpO1xuICAgICAgICB9IGVsc2Uge1xuICAgICAgICAgIHJldHVybiBcItC30LAgXCIgKyBkZWNsZW5zaW9uKHNjaGVtZS5yZWd1bGFyLCBjb3VudCk7XG4gICAgICAgIH1cbiAgICAgIH0gZWxzZSB7XG4gICAgICAgIGlmIChzY2hlbWUucGFzdCkge1xuICAgICAgICAgIHJldHVybiBkZWNsZW5zaW9uKHNjaGVtZS5wYXN0LCBjb3VudCk7XG4gICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgcmV0dXJuIGRlY2xlbnNpb24oc2NoZW1lLnJlZ3VsYXIsIGNvdW50KSArIFwiINGC0L7QvNGDXCI7XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICB9IGVsc2Uge1xuICAgICAgcmV0dXJuIGRlY2xlbnNpb24oc2NoZW1lLnJlZ3VsYXIsIGNvdW50KTtcbiAgICB9XG4gIH07XG59XG5cbmNvbnN0IGhhbGZBdE1pbnV0ZSA9IChfLCBvcHRpb25zKSA9PiB7XG4gIGlmIChvcHRpb25zICYmIG9wdGlvbnMuYWRkU3VmZml4KSB7XG4gICAgaWYgKG9wdGlvbnMuY29tcGFyaXNvbiAmJiBvcHRpb25zLmNvbXBhcmlzb24gPiAwKSB7XG4gICAgICByZXR1cm4gXCLQt9CwINC/0ZbQstGF0LLQuNC70LjQvdC4XCI7XG4gICAgfSBlbHNlIHtcbiAgICAgIHJldHVybiBcItC/0ZbQstGF0LLQuNC70LjQvdC4INGC0L7QvNGDXCI7XG4gICAgfVxuICB9XG5cbiAgcmV0dXJuIFwi0L/RltCy0YXQstC40LvQuNC90LhcIjtcbn07XG5cbmNvbnN0IGZvcm1hdERpc3RhbmNlTG9jYWxlID0ge1xuICBsZXNzVGhhblhTZWNvbmRzOiBidWlsZExvY2FsaXplVG9rZW5Gbih7XG4gICAgcmVndWxhcjoge1xuICAgICAgb25lOiBcItC80LXQvdGI0LUg0YHQtdC60YPQvdC00LhcIixcbiAgICAgIHNpbmd1bGFyTm9taW5hdGl2ZTogXCLQvNC10L3RiNC1IHt7Y291bnR9fSDRgdC10LrRg9C90LTQuFwiLFxuICAgICAgc2luZ3VsYXJHZW5pdGl2ZTogXCLQvNC10L3RiNC1IHt7Y291bnR9fSDRgdC10LrRg9C90LRcIixcbiAgICAgIHBsdXJhbEdlbml0aXZlOiBcItC80LXQvdGI0LUge3tjb3VudH19INGB0LXQutGD0L3QtFwiLFxuICAgIH0sXG4gICAgZnV0dXJlOiB7XG4gICAgICBvbmU6IFwi0LzQtdC90YjQtSwg0L3RltC2INC30LAg0YHQtdC60YPQvdC00YNcIixcbiAgICAgIHNpbmd1bGFyTm9taW5hdGl2ZTogXCLQvNC10L3RiNC1LCDQvdGW0LYg0LfQsCB7e2NvdW50fX0g0YHQtdC60YPQvdC00YNcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwi0LzQtdC90YjQtSwg0L3RltC2INC30LAge3tjb3VudH19INGB0LXQutGD0L3QtNC4XCIsXG4gICAgICBwbHVyYWxHZW5pdGl2ZTogXCLQvNC10L3RiNC1LCDQvdGW0LYg0LfQsCB7e2NvdW50fX0g0YHQtdC60YPQvdC0XCIsXG4gICAgfSxcbiAgfSksXG5cbiAgeFNlY29uZHM6IGJ1aWxkTG9jYWxpemVUb2tlbkZuKHtcbiAgICByZWd1bGFyOiB7XG4gICAgICBzaW5ndWxhck5vbWluYXRpdmU6IFwie3tjb3VudH19INGB0LXQutGD0L3QtNCwXCIsXG4gICAgICBzaW5ndWxhckdlbml0aXZlOiBcInt7Y291bnR9fSDRgdC10LrRg9C90LTQuFwiLFxuICAgICAgcGx1cmFsR2VuaXRpdmU6IFwie3tjb3VudH19INGB0LXQutGD0L3QtFwiLFxuICAgIH0sXG4gICAgcGFzdDoge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcInt7Y291bnR9fSDRgdC10LrRg9C90LTRgyDRgtC+0LzRg1wiLFxuICAgICAgc2luZ3VsYXJHZW5pdGl2ZTogXCJ7e2NvdW50fX0g0YHQtdC60YPQvdC00Lgg0YLQvtC80YNcIixcbiAgICAgIHBsdXJhbEdlbml0aXZlOiBcInt7Y291bnR9fSDRgdC10LrRg9C90LQg0YLQvtC80YNcIixcbiAgICB9LFxuICAgIGZ1dHVyZToge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItC30LAge3tjb3VudH19INGB0LXQutGD0L3QtNGDXCIsXG4gICAgICBzaW5ndWxhckdlbml0aXZlOiBcItC30LAge3tjb3VudH19INGB0LXQutGD0L3QtNC4XCIsXG4gICAgICBwbHVyYWxHZW5pdGl2ZTogXCLQt9CwIHt7Y291bnR9fSDRgdC10LrRg9C90LRcIixcbiAgICB9LFxuICB9KSxcblxuICBoYWxmQU1pbnV0ZTogaGFsZkF0TWludXRlLFxuXG4gIGxlc3NUaGFuWE1pbnV0ZXM6IGJ1aWxkTG9jYWxpemVUb2tlbkZuKHtcbiAgICByZWd1bGFyOiB7XG4gICAgICBvbmU6IFwi0LzQtdC90YjQtSDRhdCy0LjQu9C40L3QuFwiLFxuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItC80LXQvdGI0LUge3tjb3VudH19INGF0LLQuNC70LjQvdC4XCIsXG4gICAgICBzaW5ndWxhckdlbml0aXZlOiBcItC80LXQvdGI0LUge3tjb3VudH19INGF0LLQuNC70LjQvVwiLFxuICAgICAgcGx1cmFsR2VuaXRpdmU6IFwi0LzQtdC90YjQtSB7e2NvdW50fX0g0YXQstC40LvQuNC9XCIsXG4gICAgfSxcbiAgICBmdXR1cmU6IHtcbiAgICAgIG9uZTogXCLQvNC10L3RiNC1LCDQvdGW0LYg0LfQsCDRhdCy0LjQu9C40L3Rg1wiLFxuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItC80LXQvdGI0LUsINC90ZbQtiDQt9CwIHt7Y291bnR9fSDRhdCy0LjQu9C40L3Rg1wiLFxuICAgICAgc2luZ3VsYXJHZW5pdGl2ZTogXCLQvNC10L3RiNC1LCDQvdGW0LYg0LfQsCB7e2NvdW50fX0g0YXQstC40LvQuNC90LhcIixcbiAgICAgIHBsdXJhbEdlbml0aXZlOiBcItC80LXQvdGI0LUsINC90ZbQtiDQt9CwIHt7Y291bnR9fSDRhdCy0LjQu9C40L1cIixcbiAgICB9LFxuICB9KSxcblxuICB4TWludXRlczogYnVpbGRMb2NhbGl6ZVRva2VuRm4oe1xuICAgIHJlZ3VsYXI6IHtcbiAgICAgIHNpbmd1bGFyTm9taW5hdGl2ZTogXCJ7e2NvdW50fX0g0YXQstC40LvQuNC90LBcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwie3tjb3VudH19INGF0LLQuNC70LjQvdC4XCIsXG4gICAgICBwbHVyYWxHZW5pdGl2ZTogXCJ7e2NvdW50fX0g0YXQstC40LvQuNC9XCIsXG4gICAgfSxcbiAgICBwYXN0OiB7XG4gICAgICBzaW5ndWxhck5vbWluYXRpdmU6IFwie3tjb3VudH19INGF0LLQuNC70LjQvdGDINGC0L7QvNGDXCIsXG4gICAgICBzaW5ndWxhckdlbml0aXZlOiBcInt7Y291bnR9fSDRhdCy0LjQu9C40L3QuCDRgtC+0LzRg1wiLFxuICAgICAgcGx1cmFsR2VuaXRpdmU6IFwie3tjb3VudH19INGF0LLQuNC70LjQvSDRgtC+0LzRg1wiLFxuICAgIH0sXG4gICAgZnV0dXJlOiB7XG4gICAgICBzaW5ndWxhck5vbWluYXRpdmU6IFwi0LfQsCB7e2NvdW50fX0g0YXQstC40LvQuNC90YNcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwi0LfQsCB7e2NvdW50fX0g0YXQstC40LvQuNC90LhcIixcbiAgICAgIHBsdXJhbEdlbml0aXZlOiBcItC30LAge3tjb3VudH19INGF0LLQuNC70LjQvVwiLFxuICAgIH0sXG4gIH0pLFxuXG4gIGFib3V0WEhvdXJzOiBidWlsZExvY2FsaXplVG9rZW5Gbih7XG4gICAgcmVndWxhcjoge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItCx0LvQuNC30YzQutC+IHt7Y291bnR9fSDQs9C+0LTQuNC90LhcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwi0LHQu9C40LfRjNC60L4ge3tjb3VudH19INCz0L7QtNC40L1cIixcbiAgICAgIHBsdXJhbEdlbml0aXZlOiBcItCx0LvQuNC30YzQutC+IHt7Y291bnR9fSDQs9C+0LTQuNC9XCIsXG4gICAgfSxcbiAgICBmdXR1cmU6IHtcbiAgICAgIHNpbmd1bGFyTm9taW5hdGl2ZTogXCLQv9GA0LjQsdC70LjQt9C90L4g0LfQsCB7e2NvdW50fX0g0LPQvtC00LjQvdGDXCIsXG4gICAgICBzaW5ndWxhckdlbml0aXZlOiBcItC/0YDQuNCx0LvQuNC30L3QviDQt9CwIHt7Y291bnR9fSDQs9C+0LTQuNC90LhcIixcbiAgICAgIHBsdXJhbEdlbml0aXZlOiBcItC/0YDQuNCx0LvQuNC30L3QviDQt9CwIHt7Y291bnR9fSDQs9C+0LTQuNC9XCIsXG4gICAgfSxcbiAgfSksXG5cbiAgeEhvdXJzOiBidWlsZExvY2FsaXplVG9rZW5Gbih7XG4gICAgcmVndWxhcjoge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcInt7Y291bnR9fSDQs9C+0LTQuNC90YNcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwie3tjb3VudH19INCz0L7QtNC40L3QuFwiLFxuICAgICAgcGx1cmFsR2VuaXRpdmU6IFwie3tjb3VudH19INCz0L7QtNC40L1cIixcbiAgICB9LFxuICB9KSxcblxuICB4RGF5czogYnVpbGRMb2NhbGl6ZVRva2VuRm4oe1xuICAgIHJlZ3VsYXI6IHtcbiAgICAgIHNpbmd1bGFyTm9taW5hdGl2ZTogXCJ7e2NvdW50fX0g0LTQtdC90YxcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwie3tjb3VudH19INC00L1pXCIsXG4gICAgICBwbHVyYWxHZW5pdGl2ZTogXCJ7e2NvdW50fX0g0LTQvdGW0LJcIixcbiAgICB9LFxuICB9KSxcblxuICBhYm91dFhXZWVrczogYnVpbGRMb2NhbGl6ZVRva2VuRm4oe1xuICAgIHJlZ3VsYXI6IHtcbiAgICAgIHNpbmd1bGFyTm9taW5hdGl2ZTogXCLQsdC70LjQt9GM0LrQviB7e2NvdW50fX0g0YLQuNC20L3Rj1wiLFxuICAgICAgc2luZ3VsYXJHZW5pdGl2ZTogXCLQsdC70LjQt9GM0LrQviB7e2NvdW50fX0g0YLQuNC20L3RltCyXCIsXG4gICAgICBwbHVyYWxHZW5pdGl2ZTogXCLQsdC70LjQt9GM0LrQviB7e2NvdW50fX0g0YLQuNC20L3RltCyXCIsXG4gICAgfSxcbiAgICBmdXR1cmU6IHtcbiAgICAgIHNpbmd1bGFyTm9taW5hdGl2ZTogXCLQv9GA0LjQsdC70LjQt9C90L4g0LfQsCB7e2NvdW50fX0g0YLQuNC20LTQtdC90YxcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwi0L/RgNC40LHQu9C40LfQvdC+INC30LAge3tjb3VudH19INGC0LjQttC90ZZcIixcbiAgICAgIHBsdXJhbEdlbml0aXZlOiBcItC/0YDQuNCx0LvQuNC30L3QviDQt9CwIHt7Y291bnR9fSDRgtC40LbQvdGW0LJcIixcbiAgICB9LFxuICB9KSxcblxuICB4V2Vla3M6IGJ1aWxkTG9jYWxpemVUb2tlbkZuKHtcbiAgICByZWd1bGFyOiB7XG4gICAgICBzaW5ndWxhck5vbWluYXRpdmU6IFwie3tjb3VudH19INGC0LjQttC00LXQvdGMXCIsXG4gICAgICBzaW5ndWxhckdlbml0aXZlOiBcInt7Y291bnR9fSDRgtC40LbQvdGWXCIsXG4gICAgICBwbHVyYWxHZW5pdGl2ZTogXCJ7e2NvdW50fX0g0YLQuNC20L3RltCyXCIsXG4gICAgfSxcbiAgfSksXG5cbiAgYWJvdXRYTW9udGhzOiBidWlsZExvY2FsaXplVG9rZW5Gbih7XG4gICAgcmVndWxhcjoge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItCx0LvQuNC30YzQutC+IHt7Y291bnR9fSDQvNGW0YHRj9GG0Y9cIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwi0LHQu9C40LfRjNC60L4ge3tjb3VudH19INC80ZbRgdGP0YbRltCyXCIsXG4gICAgICBwbHVyYWxHZW5pdGl2ZTogXCLQsdC70LjQt9GM0LrQviB7e2NvdW50fX0g0LzRltGB0Y/RhtGW0LJcIixcbiAgICB9LFxuICAgIGZ1dHVyZToge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItC/0YDQuNCx0LvQuNC30L3QviDQt9CwIHt7Y291bnR9fSDQvNGW0YHRj9GG0YxcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwi0L/RgNC40LHQu9C40LfQvdC+INC30LAge3tjb3VudH19INC80ZbRgdGP0YbRllwiLFxuICAgICAgcGx1cmFsR2VuaXRpdmU6IFwi0L/RgNC40LHQu9C40LfQvdC+INC30LAge3tjb3VudH19INC80ZbRgdGP0YbRltCyXCIsXG4gICAgfSxcbiAgfSksXG5cbiAgeE1vbnRoczogYnVpbGRMb2NhbGl6ZVRva2VuRm4oe1xuICAgIHJlZ3VsYXI6IHtcbiAgICAgIHNpbmd1bGFyTm9taW5hdGl2ZTogXCJ7e2NvdW50fX0g0LzRltGB0Y/RhtGMXCIsXG4gICAgICBzaW5ndWxhckdlbml0aXZlOiBcInt7Y291bnR9fSDQvNGW0YHRj9GG0ZZcIixcbiAgICAgIHBsdXJhbEdlbml0aXZlOiBcInt7Y291bnR9fSDQvNGW0YHRj9GG0ZbQslwiLFxuICAgIH0sXG4gIH0pLFxuXG4gIGFib3V0WFllYXJzOiBidWlsZExvY2FsaXplVG9rZW5Gbih7XG4gICAgcmVndWxhcjoge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItCx0LvQuNC30YzQutC+IHt7Y291bnR9fSDRgNC+0LrRg1wiLFxuICAgICAgc2luZ3VsYXJHZW5pdGl2ZTogXCLQsdC70LjQt9GM0LrQviB7e2NvdW50fX0g0YDQvtC60ZbQslwiLFxuICAgICAgcGx1cmFsR2VuaXRpdmU6IFwi0LHQu9C40LfRjNC60L4ge3tjb3VudH19INGA0L7QutGW0LJcIixcbiAgICB9LFxuICAgIGZ1dHVyZToge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItC/0YDQuNCx0LvQuNC30L3QviDQt9CwIHt7Y291bnR9fSDRgNGW0LpcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwi0L/RgNC40LHQu9C40LfQvdC+INC30LAge3tjb3VudH19INGA0L7QutC4XCIsXG4gICAgICBwbHVyYWxHZW5pdGl2ZTogXCLQv9GA0LjQsdC70LjQt9C90L4g0LfQsCB7e2NvdW50fX0g0YDQvtC60ZbQslwiLFxuICAgIH0sXG4gIH0pLFxuXG4gIHhZZWFyczogYnVpbGRMb2NhbGl6ZVRva2VuRm4oe1xuICAgIHJlZ3VsYXI6IHtcbiAgICAgIHNpbmd1bGFyTm9taW5hdGl2ZTogXCJ7e2NvdW50fX0g0YDRltC6XCIsXG4gICAgICBzaW5ndWxhckdlbml0aXZlOiBcInt7Y291bnR9fSDRgNC+0LrQuFwiLFxuICAgICAgcGx1cmFsR2VuaXRpdmU6IFwie3tjb3VudH19INGA0L7QutGW0LJcIixcbiAgICB9LFxuICB9KSxcblxuICBvdmVyWFllYXJzOiBidWlsZExvY2FsaXplVG9rZW5Gbih7XG4gICAgcmVndWxhcjoge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItCx0ZbQu9GM0YjQtSB7e2NvdW50fX0g0YDQvtC60YNcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwi0LHRltC70YzRiNC1IHt7Y291bnR9fSDRgNC+0LrRltCyXCIsXG4gICAgICBwbHVyYWxHZW5pdGl2ZTogXCLQsdGW0LvRjNGI0LUge3tjb3VudH19INGA0L7QutGW0LJcIixcbiAgICB9LFxuICAgIGZ1dHVyZToge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItCx0ZbQu9GM0YjQtSwg0L3RltC2INC30LAge3tjb3VudH19INGA0ZbQulwiLFxuICAgICAgc2luZ3VsYXJHZW5pdGl2ZTogXCLQsdGW0LvRjNGI0LUsINC90ZbQtiDQt9CwIHt7Y291bnR9fSDRgNC+0LrQuFwiLFxuICAgICAgcGx1cmFsR2VuaXRpdmU6IFwi0LHRltC70YzRiNC1LCDQvdGW0LYg0LfQsCB7e2NvdW50fX0g0YDQvtC60ZbQslwiLFxuICAgIH0sXG4gIH0pLFxuXG4gIGFsbW9zdFhZZWFyczogYnVpbGRMb2NhbGl6ZVRva2VuRm4oe1xuICAgIHJlZ3VsYXI6IHtcbiAgICAgIHNpbmd1bGFyTm9taW5hdGl2ZTogXCLQvNCw0LnQttC1IHt7Y291bnR9fSDRgNGW0LpcIixcbiAgICAgIHNpbmd1bGFyR2VuaXRpdmU6IFwi0LzQsNC50LbQtSB7e2NvdW50fX0g0YDQvtC60LhcIixcbiAgICAgIHBsdXJhbEdlbml0aXZlOiBcItC80LDQudC20LUge3tjb3VudH19INGA0L7QutGW0LJcIixcbiAgICB9LFxuICAgIGZ1dHVyZToge1xuICAgICAgc2luZ3VsYXJOb21pbmF0aXZlOiBcItC80LDQudC20LUg0LfQsCB7e2NvdW50fX0g0YDRltC6XCIsXG4gICAgICBzaW5ndWxhckdlbml0aXZlOiBcItC80LDQudC20LUg0LfQsCB7e2NvdW50fX0g0YDQvtC60LhcIixcbiAgICAgIHBsdXJhbEdlbml0aXZlOiBcItC80LDQudC20LUg0LfQsCB7e2NvdW50fX0g0YDQvtC60ZbQslwiLFxuICAgIH0sXG4gIH0pLFxufTtcblxuZXhwb3J0IGNvbnN0IGZvcm1hdERpc3RhbmNlID0gKHRva2VuLCBjb3VudCwgb3B0aW9ucykgPT4ge1xuICBvcHRpb25zID0gb3B0aW9ucyB8fCB7fTtcblxuICByZXR1cm4gZm9ybWF0RGlzdGFuY2VMb2NhbGVbdG9rZW5dKGNvdW50LCBvcHRpb25zKTtcbn07XG4iXSwibmFtZXMiOlsiZGVjbGVuc2lvbiIsInNjaGVtZSIsImNvdW50Iiwib25lIiwidW5kZWZpbmVkIiwicmVtMTAiLCJyZW0xMDAiLCJzaW5ndWxhck5vbWluYXRpdmUiLCJyZXBsYWNlIiwiU3RyaW5nIiwic2luZ3VsYXJHZW5pdGl2ZSIsInBsdXJhbEdlbml0aXZlIiwiYnVpbGRMb2NhbGl6ZVRva2VuRm4iLCJvcHRpb25zIiwiYWRkU3VmZml4IiwiY29tcGFyaXNvbiIsImZ1dHVyZSIsInJlZ3VsYXIiLCJwYXN0IiwiaGFsZkF0TWludXRlIiwiXyIsImZvcm1hdERpc3RhbmNlTG9jYWxlIiwibGVzc1RoYW5YU2Vjb25kcyIsInhTZWNvbmRzIiwiaGFsZkFNaW51dGUiLCJsZXNzVGhhblhNaW51dGVzIiwieE1pbnV0ZXMiLCJhYm91dFhIb3VycyIsInhIb3VycyIsInhEYXlzIiwiYWJvdXRYV2Vla3MiLCJ4V2Vla3MiLCJhYm91dFhNb250aHMiLCJ4TW9udGhzIiwiYWJvdXRYWWVhcnMiLCJ4WWVhcnMiLCJvdmVyWFllYXJzIiwiYWxtb3N0WFllYXJzIiwiZm9ybWF0RGlzdGFuY2UiLCJ0b2tlbiJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatLong.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatLong.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, do MMMM y 'р.'\",\n    long: \"do MMMM y 'р.'\",\n    medium: \"d MMM y 'р.'\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'о' {{time}}\",\n    long: \"{{date}} 'о' {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatRelative.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatRelative.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\n/* harmony import */ var _isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../../isSameWeek.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/isSameWeek.js\");\n/* harmony import */ var _toDate_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../../toDate.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/toDate.js\");\n\n\nconst accusativeWeekdays = [\n    \"неділю\",\n    \"понеділок\",\n    \"вівторок\",\n    \"середу\",\n    \"четвер\",\n    \"п’ятницю\",\n    \"суботу\"\n];\nfunction lastWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 5:\n        case 6:\n            return \"'у минулу \" + weekday + \" о' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'у минулий \" + weekday + \" о' p\";\n    }\n}\nfunction thisWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    return \"'у \" + weekday + \" о' p\";\n}\nfunction nextWeek(day) {\n    const weekday = accusativeWeekdays[day];\n    switch(day){\n        case 0:\n        case 3:\n        case 5:\n        case 6:\n            return \"'у наступну \" + weekday + \" о' p\";\n        case 1:\n        case 2:\n        case 4:\n            return \"'у наступний \" + weekday + \" о' p\";\n    }\n}\nconst lastWeekFormat = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return lastWeek(day);\n    }\n};\nconst nextWeekFormat = (dirtyDate, baseDate, options)=>{\n    const date = (0,_toDate_js__WEBPACK_IMPORTED_MODULE_0__.toDate)(dirtyDate);\n    const day = date.getDay();\n    if ((0,_isSameWeek_js__WEBPACK_IMPORTED_MODULE_1__.isSameWeek)(date, baseDate, options)) {\n        return thisWeek(day);\n    } else {\n        return nextWeek(day);\n    }\n};\nconst formatRelativeLocale = {\n    lastWeek: lastWeekFormat,\n    yesterday: \"'вчора о' p\",\n    today: \"'сьогодні о' p\",\n    tomorrow: \"'завтра о' p\",\n    nextWeek: nextWeekFormat,\n    other: \"P\"\n};\nconst formatRelative = (token, date, baseDate, options)=>{\n    const format = formatRelativeLocale[token];\n    if (typeof format === \"function\") {\n        return format(date, baseDate, options);\n    }\n    return format;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/localize.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/localize.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"до н.е.\",\n        \"н.е.\"\n    ],\n    abbreviated: [\n        \"до н. е.\",\n        \"н. е.\"\n    ],\n    wide: [\n        \"до нашої ери\",\n        \"нашої ери\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"1-й кв.\",\n        \"2-й кв.\",\n        \"3-й кв.\",\n        \"4-й кв.\"\n    ],\n    wide: [\n        \"1-й квартал\",\n        \"2-й квартал\",\n        \"3-й квартал\",\n        \"4-й квартал\"\n    ]\n};\nconst monthValues = {\n    // ДСТУ 3582:2013\n    narrow: [\n        \"С\",\n        \"Л\",\n        \"Б\",\n        \"К\",\n        \"Т\",\n        \"Ч\",\n        \"Л\",\n        \"С\",\n        \"В\",\n        \"Ж\",\n        \"Л\",\n        \"Г\"\n    ],\n    abbreviated: [\n        \"січ.\",\n        \"лют.\",\n        \"берез.\",\n        \"квіт.\",\n        \"трав.\",\n        \"черв.\",\n        \"лип.\",\n        \"серп.\",\n        \"верес.\",\n        \"жовт.\",\n        \"листоп.\",\n        \"груд.\"\n    ],\n    wide: [\n        \"січень\",\n        \"лютий\",\n        \"березень\",\n        \"квітень\",\n        \"травень\",\n        \"червень\",\n        \"липень\",\n        \"серпень\",\n        \"вересень\",\n        \"жовтень\",\n        \"листопад\",\n        \"грудень\"\n    ]\n};\nconst formattingMonthValues = {\n    narrow: [\n        \"С\",\n        \"Л\",\n        \"Б\",\n        \"К\",\n        \"Т\",\n        \"Ч\",\n        \"Л\",\n        \"С\",\n        \"В\",\n        \"Ж\",\n        \"Л\",\n        \"Г\"\n    ],\n    abbreviated: [\n        \"січ.\",\n        \"лют.\",\n        \"берез.\",\n        \"квіт.\",\n        \"трав.\",\n        \"черв.\",\n        \"лип.\",\n        \"серп.\",\n        \"верес.\",\n        \"жовт.\",\n        \"листоп.\",\n        \"груд.\"\n    ],\n    wide: [\n        \"січня\",\n        \"лютого\",\n        \"березня\",\n        \"квітня\",\n        \"травня\",\n        \"червня\",\n        \"липня\",\n        \"серпня\",\n        \"вересня\",\n        \"жовтня\",\n        \"листопада\",\n        \"грудня\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"Н\",\n        \"П\",\n        \"В\",\n        \"С\",\n        \"Ч\",\n        \"П\",\n        \"С\"\n    ],\n    short: [\n        \"нд\",\n        \"пн\",\n        \"вт\",\n        \"ср\",\n        \"чт\",\n        \"пт\",\n        \"сб\"\n    ],\n    abbreviated: [\n        \"нед\",\n        \"пон\",\n        \"вів\",\n        \"сер\",\n        \"чтв\",\n        \"птн\",\n        \"суб\"\n    ],\n    wide: [\n        \"неділя\",\n        \"понеділок\",\n        \"вівторок\",\n        \"середа\",\n        \"четвер\",\n        \"п’ятниця\",\n        \"субота\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранок\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ніч\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранок\",\n        afternoon: \"день\",\n        evening: \"веч.\",\n        night: \"ніч\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"північ\",\n        noon: \"полудень\",\n        morning: \"ранок\",\n        afternoon: \"день\",\n        evening: \"вечір\",\n        night: \"ніч\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранку\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночі\"\n    },\n    abbreviated: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"півн.\",\n        noon: \"пол.\",\n        morning: \"ранку\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночі\"\n    },\n    wide: {\n        am: \"ДП\",\n        pm: \"ПП\",\n        midnight: \"північ\",\n        noon: \"полудень\",\n        morning: \"ранку\",\n        afternoon: \"дня\",\n        evening: \"веч.\",\n        night: \"ночі\"\n    }\n};\nconst ordinalNumber = (dirtyNumber, options)=>{\n    const unit = String(options === null || options === void 0 ? void 0 : options.unit);\n    const number = Number(dirtyNumber);\n    let suffix;\n    if (unit === \"date\") {\n        if (number === 3 || number === 23) {\n            suffix = \"-є\";\n        } else {\n            suffix = \"-е\";\n        }\n    } else if (unit === \"minute\" || unit === \"second\" || unit === \"hour\") {\n        suffix = \"-а\";\n    } else {\n        suffix = \"-й\";\n    }\n    return number + suffix;\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingMonthValues,\n        defaultFormattingWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"any\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS91ay9fbGliL2xvY2FsaXplLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQWdFO0FBRWhFLE1BQU1DLFlBQVk7SUFDaEJDLFFBQVE7UUFBQztRQUFXO0tBQU87SUFDM0JDLGFBQWE7UUFBQztRQUFZO0tBQVE7SUFDbENDLE1BQU07UUFBQztRQUFnQjtLQUFZO0FBQ3JDO0FBRUEsTUFBTUMsZ0JBQWdCO0lBQ3BCSCxRQUFRO1FBQUM7UUFBSztRQUFLO1FBQUs7S0FBSTtJQUM1QkMsYUFBYTtRQUFDO1FBQVc7UUFBVztRQUFXO0tBQVU7SUFDekRDLE1BQU07UUFBQztRQUFlO1FBQWU7UUFBZTtLQUFjO0FBQ3BFO0FBRUEsTUFBTUUsY0FBYztJQUNsQixpQkFBaUI7SUFDakJKLFFBQVE7UUFBQztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7S0FBSTtJQUNwRUMsYUFBYTtRQUNYO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0lBRURDLE1BQU07UUFDSjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtBQUNIO0FBRUEsTUFBTUcsd0JBQXdCO0lBQzVCTCxRQUFRO1FBQUM7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztRQUFLO0tBQUk7SUFDcEVDLGFBQWE7UUFDWDtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7S0FDRDtJQUVEQyxNQUFNO1FBQ0o7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtRQUNBO0tBQ0Q7QUFDSDtBQUVBLE1BQU1JLFlBQVk7SUFDaEJOLFFBQVE7UUFBQztRQUFLO1FBQUs7UUFBSztRQUFLO1FBQUs7UUFBSztLQUFJO0lBQzNDTyxPQUFPO1FBQUM7UUFBTTtRQUFNO1FBQU07UUFBTTtRQUFNO1FBQU07S0FBSztJQUNqRE4sYUFBYTtRQUFDO1FBQU87UUFBTztRQUFPO1FBQU87UUFBTztRQUFPO0tBQU07SUFDOURDLE1BQU07UUFDSjtRQUNBO1FBQ0E7UUFDQTtRQUNBO1FBQ0E7UUFDQTtLQUNEO0FBQ0g7QUFFQSxNQUFNTSxrQkFBa0I7SUFDdEJSLFFBQVE7UUFDTlMsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxPQUFPO0lBQ1Q7SUFDQWYsYUFBYTtRQUNYUSxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtJQUNBZCxNQUFNO1FBQ0pPLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsT0FBTztJQUNUO0FBQ0Y7QUFFQSxNQUFNQyw0QkFBNEI7SUFDaENqQixRQUFRO1FBQ05TLElBQUk7UUFDSkMsSUFBSTtRQUNKQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLFNBQVM7UUFDVEMsT0FBTztJQUNUO0lBQ0FmLGFBQWE7UUFDWFEsSUFBSTtRQUNKQyxJQUFJO1FBQ0pDLFVBQVU7UUFDVkMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxPQUFPO0lBQ1Q7SUFDQWQsTUFBTTtRQUNKTyxJQUFJO1FBQ0pDLElBQUk7UUFDSkMsVUFBVTtRQUNWQyxNQUFNO1FBQ05DLFNBQVM7UUFDVEMsV0FBVztRQUNYQyxTQUFTO1FBQ1RDLE9BQU87SUFDVDtBQUNGO0FBRUEsTUFBTUUsZ0JBQWdCLENBQUNDLGFBQWFDO0lBQ2xDLE1BQU1DLE9BQU9DLE9BQU9GLG9CQUFBQSw4QkFBQUEsUUFBU0MsSUFBSTtJQUNqQyxNQUFNRSxTQUFTQyxPQUFPTDtJQUN0QixJQUFJTTtJQUVKLElBQUlKLFNBQVMsUUFBUTtRQUNuQixJQUFJRSxXQUFXLEtBQUtBLFdBQVcsSUFBSTtZQUNqQ0UsU0FBUztRQUNYLE9BQU87WUFDTEEsU0FBUztRQUNYO0lBQ0YsT0FBTyxJQUFJSixTQUFTLFlBQVlBLFNBQVMsWUFBWUEsU0FBUyxRQUFRO1FBQ3BFSSxTQUFTO0lBQ1gsT0FBTztRQUNMQSxTQUFTO0lBQ1g7SUFFQSxPQUFPRixTQUFTRTtBQUNsQjtBQUVPLE1BQU1DLFdBQVc7SUFDdEJSO0lBRUFTLEtBQUs3Qix3RUFBZUEsQ0FBQztRQUNuQjhCLFFBQVE3QjtRQUNSOEIsY0FBYztJQUNoQjtJQUVBQyxTQUFTaEMsd0VBQWVBLENBQUM7UUFDdkI4QixRQUFRekI7UUFDUjBCLGNBQWM7UUFDZEUsa0JBQWtCLENBQUNELFVBQVlBLFVBQVU7SUFDM0M7SUFFQUUsT0FBT2xDLHdFQUFlQSxDQUFDO1FBQ3JCOEIsUUFBUXhCO1FBQ1J5QixjQUFjO1FBQ2RJLGtCQUFrQjVCO1FBQ2xCNkIsd0JBQXdCO0lBQzFCO0lBRUFDLEtBQUtyQyx3RUFBZUEsQ0FBQztRQUNuQjhCLFFBQVF0QjtRQUNSdUIsY0FBYztJQUNoQjtJQUVBTyxXQUFXdEMsd0VBQWVBLENBQUM7UUFDekI4QixRQUFRcEI7UUFDUnFCLGNBQWM7UUFDZEksa0JBQWtCaEI7UUFDbEJpQix3QkFBd0I7SUFDMUI7QUFDRixFQUFFIiwic291cmNlcyI6WyJDOlxccHJvamVjdHNcXGxtc1xcbm9kZV9tb2R1bGVzXFwucG5wbVxcZGF0ZS1mbnNANC4xLjBcXG5vZGVfbW9kdWxlc1xcZGF0ZS1mbnNcXGxvY2FsZVxcdWtcXF9saWJcXGxvY2FsaXplLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGJ1aWxkTG9jYWxpemVGbiB9IGZyb20gXCIuLi8uLi9fbGliL2J1aWxkTG9jYWxpemVGbi5qc1wiO1xuXG5jb25zdCBlcmFWYWx1ZXMgPSB7XG4gIG5hcnJvdzogW1wi0LTQviDQvS7QtS5cIiwgXCLQvS7QtS5cIl0sXG4gIGFiYnJldmlhdGVkOiBbXCLQtNC+INC9LiDQtS5cIiwgXCLQvS4g0LUuXCJdLFxuICB3aWRlOiBbXCLQtNC+INC90LDRiNC+0Zcg0LXRgNC4XCIsIFwi0L3QsNGI0L7RlyDQtdGA0LhcIl0sXG59O1xuXG5jb25zdCBxdWFydGVyVmFsdWVzID0ge1xuICBuYXJyb3c6IFtcIjFcIiwgXCIyXCIsIFwiM1wiLCBcIjRcIl0sXG4gIGFiYnJldmlhdGVkOiBbXCIxLdC5INC60LIuXCIsIFwiMi3QuSDQutCyLlwiLCBcIjMt0Lkg0LrQsi5cIiwgXCI0LdC5INC60LIuXCJdLFxuICB3aWRlOiBbXCIxLdC5INC60LLQsNGA0YLQsNC7XCIsIFwiMi3QuSDQutCy0LDRgNGC0LDQu1wiLCBcIjMt0Lkg0LrQstCw0YDRgtCw0LtcIiwgXCI0LdC5INC60LLQsNGA0YLQsNC7XCJdLFxufTtcblxuY29uc3QgbW9udGhWYWx1ZXMgPSB7XG4gIC8vINCU0KHQotCjIDM1ODI6MjAxM1xuICBuYXJyb3c6IFtcItChXCIsIFwi0JtcIiwgXCLQkVwiLCBcItCaXCIsIFwi0KJcIiwgXCLQp1wiLCBcItCbXCIsIFwi0KFcIiwgXCLQklwiLCBcItCWXCIsIFwi0JtcIiwgXCLQk1wiXSxcbiAgYWJicmV2aWF0ZWQ6IFtcbiAgICBcItGB0ZbRhy5cIixcbiAgICBcItC70Y7Rgi5cIixcbiAgICBcItCx0LXRgNC10LcuXCIsXG4gICAgXCLQutCy0ZbRgi5cIixcbiAgICBcItGC0YDQsNCyLlwiLFxuICAgIFwi0YfQtdGA0LIuXCIsXG4gICAgXCLQu9C40L8uXCIsXG4gICAgXCLRgdC10YDQvy5cIixcbiAgICBcItCy0LXRgNC10YEuXCIsXG4gICAgXCLQttC+0LLRgi5cIixcbiAgICBcItC70LjRgdGC0L7Qvy5cIixcbiAgICBcItCz0YDRg9C0LlwiLFxuICBdLFxuXG4gIHdpZGU6IFtcbiAgICBcItGB0ZbRh9C10L3RjFwiLFxuICAgIFwi0LvRjtGC0LjQuVwiLFxuICAgIFwi0LHQtdGA0LXQt9C10L3RjFwiLFxuICAgIFwi0LrQstGW0YLQtdC90YxcIixcbiAgICBcItGC0YDQsNCy0LXQvdGMXCIsXG4gICAgXCLRh9C10YDQstC10L3RjFwiLFxuICAgIFwi0LvQuNC/0LXQvdGMXCIsXG4gICAgXCLRgdC10YDQv9C10L3RjFwiLFxuICAgIFwi0LLQtdGA0LXRgdC10L3RjFwiLFxuICAgIFwi0LbQvtCy0YLQtdC90YxcIixcbiAgICBcItC70LjRgdGC0L7Qv9Cw0LRcIixcbiAgICBcItCz0YDRg9C00LXQvdGMXCIsXG4gIF0sXG59O1xuXG5jb25zdCBmb3JtYXR0aW5nTW9udGhWYWx1ZXMgPSB7XG4gIG5hcnJvdzogW1wi0KFcIiwgXCLQm1wiLCBcItCRXCIsIFwi0JpcIiwgXCLQolwiLCBcItCnXCIsIFwi0JtcIiwgXCLQoVwiLCBcItCSXCIsIFwi0JZcIiwgXCLQm1wiLCBcItCTXCJdLFxuICBhYmJyZXZpYXRlZDogW1xuICAgIFwi0YHRltGHLlwiLFxuICAgIFwi0LvRjtGCLlwiLFxuICAgIFwi0LHQtdGA0LXQty5cIixcbiAgICBcItC60LLRltGCLlwiLFxuICAgIFwi0YLRgNCw0LIuXCIsXG4gICAgXCLRh9C10YDQsi5cIixcbiAgICBcItC70LjQvy5cIixcbiAgICBcItGB0LXRgNC/LlwiLFxuICAgIFwi0LLQtdGA0LXRgS5cIixcbiAgICBcItC20L7QstGCLlwiLFxuICAgIFwi0LvQuNGB0YLQvtC/LlwiLFxuICAgIFwi0LPRgNGD0LQuXCIsXG4gIF0sXG5cbiAgd2lkZTogW1xuICAgIFwi0YHRltGH0L3Rj1wiLFxuICAgIFwi0LvRjtGC0L7Qs9C+XCIsXG4gICAgXCLQsdC10YDQtdC30L3Rj1wiLFxuICAgIFwi0LrQstGW0YLQvdGPXCIsXG4gICAgXCLRgtGA0LDQstC90Y9cIixcbiAgICBcItGH0LXRgNCy0L3Rj1wiLFxuICAgIFwi0LvQuNC/0L3Rj1wiLFxuICAgIFwi0YHQtdGA0L/QvdGPXCIsXG4gICAgXCLQstC10YDQtdGB0L3Rj1wiLFxuICAgIFwi0LbQvtCy0YLQvdGPXCIsXG4gICAgXCLQu9C40YHRgtC+0L/QsNC00LBcIixcbiAgICBcItCz0YDRg9C00L3Rj1wiLFxuICBdLFxufTtcblxuY29uc3QgZGF5VmFsdWVzID0ge1xuICBuYXJyb3c6IFtcItCdXCIsIFwi0J9cIiwgXCLQklwiLCBcItChXCIsIFwi0KdcIiwgXCLQn1wiLCBcItChXCJdLFxuICBzaG9ydDogW1wi0L3QtFwiLCBcItC/0L1cIiwgXCLQstGCXCIsIFwi0YHRgFwiLCBcItGH0YJcIiwgXCLQv9GCXCIsIFwi0YHQsVwiXSxcbiAgYWJicmV2aWF0ZWQ6IFtcItC90LXQtFwiLCBcItC/0L7QvVwiLCBcItCy0ZbQslwiLCBcItGB0LXRgFwiLCBcItGH0YLQslwiLCBcItC/0YLQvVwiLCBcItGB0YPQsVwiXSxcbiAgd2lkZTogW1xuICAgIFwi0L3QtdC00ZbQu9GPXCIsXG4gICAgXCLQv9C+0L3QtdC00ZbQu9C+0LpcIixcbiAgICBcItCy0ZbQstGC0L7RgNC+0LpcIixcbiAgICBcItGB0LXRgNC10LTQsFwiLFxuICAgIFwi0YfQtdGC0LLQtdGAXCIsXG4gICAgXCLQv+KAmdGP0YLQvdC40YbRj1wiLFxuICAgIFwi0YHRg9Cx0L7RgtCwXCIsXG4gIF0sXG59O1xuXG5jb25zdCBkYXlQZXJpb2RWYWx1ZXMgPSB7XG4gIG5hcnJvdzoge1xuICAgIGFtOiBcItCU0J9cIixcbiAgICBwbTogXCLQn9CfXCIsXG4gICAgbWlkbmlnaHQ6IFwi0L/RltCy0L0uXCIsXG4gICAgbm9vbjogXCLQv9C+0LsuXCIsXG4gICAgbW9ybmluZzogXCLRgNCw0L3QvtC6XCIsXG4gICAgYWZ0ZXJub29uOiBcItC00LXQvdGMXCIsXG4gICAgZXZlbmluZzogXCLQstC10YcuXCIsXG4gICAgbmlnaHQ6IFwi0L3RltGHXCIsXG4gIH0sXG4gIGFiYnJldmlhdGVkOiB7XG4gICAgYW06IFwi0JTQn1wiLFxuICAgIHBtOiBcItCf0J9cIixcbiAgICBtaWRuaWdodDogXCLQv9GW0LLQvS5cIixcbiAgICBub29uOiBcItC/0L7Quy5cIixcbiAgICBtb3JuaW5nOiBcItGA0LDQvdC+0LpcIixcbiAgICBhZnRlcm5vb246IFwi0LTQtdC90YxcIixcbiAgICBldmVuaW5nOiBcItCy0LXRhy5cIixcbiAgICBuaWdodDogXCLQvdGW0YdcIixcbiAgfSxcbiAgd2lkZToge1xuICAgIGFtOiBcItCU0J9cIixcbiAgICBwbTogXCLQn9CfXCIsXG4gICAgbWlkbmlnaHQ6IFwi0L/RltCy0L3RltGHXCIsXG4gICAgbm9vbjogXCLQv9C+0LvRg9C00LXQvdGMXCIsXG4gICAgbW9ybmluZzogXCLRgNCw0L3QvtC6XCIsXG4gICAgYWZ0ZXJub29uOiBcItC00LXQvdGMXCIsXG4gICAgZXZlbmluZzogXCLQstC10YfRltGAXCIsXG4gICAgbmlnaHQ6IFwi0L3RltGHXCIsXG4gIH0sXG59O1xuXG5jb25zdCBmb3JtYXR0aW5nRGF5UGVyaW9kVmFsdWVzID0ge1xuICBuYXJyb3c6IHtcbiAgICBhbTogXCLQlNCfXCIsXG4gICAgcG06IFwi0J/Qn1wiLFxuICAgIG1pZG5pZ2h0OiBcItC/0ZbQstC9LlwiLFxuICAgIG5vb246IFwi0L/QvtC7LlwiLFxuICAgIG1vcm5pbmc6IFwi0YDQsNC90LrRg1wiLFxuICAgIGFmdGVybm9vbjogXCLQtNC90Y9cIixcbiAgICBldmVuaW5nOiBcItCy0LXRhy5cIixcbiAgICBuaWdodDogXCLQvdC+0YfRllwiLFxuICB9LFxuICBhYmJyZXZpYXRlZDoge1xuICAgIGFtOiBcItCU0J9cIixcbiAgICBwbTogXCLQn9CfXCIsXG4gICAgbWlkbmlnaHQ6IFwi0L/RltCy0L0uXCIsXG4gICAgbm9vbjogXCLQv9C+0LsuXCIsXG4gICAgbW9ybmluZzogXCLRgNCw0L3QutGDXCIsXG4gICAgYWZ0ZXJub29uOiBcItC00L3Rj1wiLFxuICAgIGV2ZW5pbmc6IFwi0LLQtdGHLlwiLFxuICAgIG5pZ2h0OiBcItC90L7Rh9GWXCIsXG4gIH0sXG4gIHdpZGU6IHtcbiAgICBhbTogXCLQlNCfXCIsXG4gICAgcG06IFwi0J/Qn1wiLFxuICAgIG1pZG5pZ2h0OiBcItC/0ZbQstC90ZbRh1wiLFxuICAgIG5vb246IFwi0L/QvtC70YPQtNC10L3RjFwiLFxuICAgIG1vcm5pbmc6IFwi0YDQsNC90LrRg1wiLFxuICAgIGFmdGVybm9vbjogXCLQtNC90Y9cIixcbiAgICBldmVuaW5nOiBcItCy0LXRhy5cIixcbiAgICBuaWdodDogXCLQvdC+0YfRllwiLFxuICB9LFxufTtcblxuY29uc3Qgb3JkaW5hbE51bWJlciA9IChkaXJ0eU51bWJlciwgb3B0aW9ucykgPT4ge1xuICBjb25zdCB1bml0ID0gU3RyaW5nKG9wdGlvbnM/LnVuaXQpO1xuICBjb25zdCBudW1iZXIgPSBOdW1iZXIoZGlydHlOdW1iZXIpO1xuICBsZXQgc3VmZml4O1xuXG4gIGlmICh1bml0ID09PSBcImRhdGVcIikge1xuICAgIGlmIChudW1iZXIgPT09IDMgfHwgbnVtYmVyID09PSAyMykge1xuICAgICAgc3VmZml4ID0gXCIt0ZRcIjtcbiAgICB9IGVsc2Uge1xuICAgICAgc3VmZml4ID0gXCIt0LVcIjtcbiAgICB9XG4gIH0gZWxzZSBpZiAodW5pdCA9PT0gXCJtaW51dGVcIiB8fCB1bml0ID09PSBcInNlY29uZFwiIHx8IHVuaXQgPT09IFwiaG91clwiKSB7XG4gICAgc3VmZml4ID0gXCIt0LBcIjtcbiAgfSBlbHNlIHtcbiAgICBzdWZmaXggPSBcIi3QuVwiO1xuICB9XG5cbiAgcmV0dXJuIG51bWJlciArIHN1ZmZpeDtcbn07XG5cbmV4cG9ydCBjb25zdCBsb2NhbGl6ZSA9IHtcbiAgb3JkaW5hbE51bWJlcixcblxuICBlcmE6IGJ1aWxkTG9jYWxpemVGbih7XG4gICAgdmFsdWVzOiBlcmFWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcIndpZGVcIixcbiAgfSksXG5cbiAgcXVhcnRlcjogYnVpbGRMb2NhbGl6ZUZuKHtcbiAgICB2YWx1ZXM6IHF1YXJ0ZXJWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcIndpZGVcIixcbiAgICBhcmd1bWVudENhbGxiYWNrOiAocXVhcnRlcikgPT4gcXVhcnRlciAtIDEsXG4gIH0pLFxuXG4gIG1vbnRoOiBidWlsZExvY2FsaXplRm4oe1xuICAgIHZhbHVlczogbW9udGhWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcIndpZGVcIixcbiAgICBmb3JtYXR0aW5nVmFsdWVzOiBmb3JtYXR0aW5nTW9udGhWYWx1ZXMsXG4gICAgZGVmYXVsdEZvcm1hdHRpbmdXaWR0aDogXCJ3aWRlXCIsXG4gIH0pLFxuXG4gIGRheTogYnVpbGRMb2NhbGl6ZUZuKHtcbiAgICB2YWx1ZXM6IGRheVZhbHVlcyxcbiAgICBkZWZhdWx0V2lkdGg6IFwid2lkZVwiLFxuICB9KSxcblxuICBkYXlQZXJpb2Q6IGJ1aWxkTG9jYWxpemVGbih7XG4gICAgdmFsdWVzOiBkYXlQZXJpb2RWYWx1ZXMsXG4gICAgZGVmYXVsdFdpZHRoOiBcImFueVwiLFxuICAgIGZvcm1hdHRpbmdWYWx1ZXM6IGZvcm1hdHRpbmdEYXlQZXJpb2RWYWx1ZXMsXG4gICAgZGVmYXVsdEZvcm1hdHRpbmdXaWR0aDogXCJ3aWRlXCIsXG4gIH0pLFxufTtcbiJdLCJuYW1lcyI6WyJidWlsZExvY2FsaXplRm4iLCJlcmFWYWx1ZXMiLCJuYXJyb3ciLCJhYmJyZXZpYXRlZCIsIndpZGUiLCJxdWFydGVyVmFsdWVzIiwibW9udGhWYWx1ZXMiLCJmb3JtYXR0aW5nTW9udGhWYWx1ZXMiLCJkYXlWYWx1ZXMiLCJzaG9ydCIsImRheVBlcmlvZFZhbHVlcyIsImFtIiwicG0iLCJtaWRuaWdodCIsIm5vb24iLCJtb3JuaW5nIiwiYWZ0ZXJub29uIiwiZXZlbmluZyIsIm5pZ2h0IiwiZm9ybWF0dGluZ0RheVBlcmlvZFZhbHVlcyIsIm9yZGluYWxOdW1iZXIiLCJkaXJ0eU51bWJlciIsIm9wdGlvbnMiLCJ1bml0IiwiU3RyaW5nIiwibnVtYmVyIiwiTnVtYmVyIiwic3VmZml4IiwibG9jYWxpemUiLCJlcmEiLCJ2YWx1ZXMiLCJkZWZhdWx0V2lkdGgiLCJxdWFydGVyIiwiYXJndW1lbnRDYWxsYmFjayIsIm1vbnRoIiwiZm9ybWF0dGluZ1ZhbHVlcyIsImRlZmF1bHRGb3JtYXR0aW5nV2lkdGgiLCJkYXkiLCJkYXlQZXJpb2QiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/match.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/match.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(е|й|є|а|я))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^((до )?н\\.?\\s?е\\.?)/i,\n    abbreviated: /^((до )?н\\.?\\s?е\\.?)/i,\n    wide: /^(до нашої ери|нашої ери|наша ера)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^д/i,\n        /^н/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^[1234](-?[иі]?й?)? кв.?/i,\n    wide: /^[1234](-?[иі]?й?)? квартал/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[слбктчвжг]/i,\n    abbreviated: /^(січ|лют|бер(ез)?|квіт|трав|черв|лип|серп|вер(ес)?|жовт|лис(топ)?|груд)\\.?/i,\n    wide: /^(січень|січня|лютий|лютого|березень|березня|квітень|квітня|травень|травня|червня|червень|липень|липня|серпень|серпня|вересень|вересня|жовтень|жовтня|листопад[а]?|грудень|грудня)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^с/i,\n        /^л/i,\n        /^б/i,\n        /^к/i,\n        /^т/i,\n        /^ч/i,\n        /^л/i,\n        /^с/i,\n        /^в/i,\n        /^ж/i,\n        /^л/i,\n        /^г/i\n    ],\n    any: [\n        /^сі/i,\n        /^лю/i,\n        /^б/i,\n        /^к/i,\n        /^т/i,\n        /^ч/i,\n        /^лип/i,\n        /^се/i,\n        /^в/i,\n        /^ж/i,\n        /^лис/i,\n        /^г/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[нпвсч]/i,\n    short: /^(нд|пн|вт|ср|чт|пт|сб)\\.?/i,\n    abbreviated: /^(нед|пон|вів|сер|че?тв|птн?|суб)\\.?/i,\n    wide: /^(неділ[яі]|понеділ[ок][ка]|вівтор[ок][ка]|серед[аи]|четвер(га)?|п\\W*?ятниц[яі]|субот[аи])/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^н/i,\n        /^п/i,\n        /^в/i,\n        /^с/i,\n        /^ч/i,\n        /^п/i,\n        /^с/i\n    ],\n    any: [\n        /^н/i,\n        /^п[он]/i,\n        /^в/i,\n        /^с[ер]/i,\n        /^ч/i,\n        /^п\\W*?[ят]/i,\n        /^с[уб]/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n    abbreviated: /^([дп]п|півн\\.?|пол\\.?|ранок|ранку|день|дня|веч\\.?|ніч|ночі)/i,\n    wide: /^([дп]п|північ|полудень|ранок|ранку|день|дня|вечір|вечора|ніч|ночі)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^дп/i,\n        pm: /^пп/i,\n        midnight: /^півн/i,\n        noon: /^пол/i,\n        morning: /^р/i,\n        afternoon: /^д[ен]/i,\n        evening: /^в/i,\n        night: /^н/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/uk/_lib/match.js\n"));

/***/ })

}]);