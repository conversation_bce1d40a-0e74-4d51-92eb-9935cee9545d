'use client'

import { Role } from '@/stores/useRolePermissionsStore'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { RoleForm } from './RoleForm'
import { 
  Shield, 
  Edit, 
  Trash2, 
  Users, 
  Settings,
  MoreVertical
} from 'lucide-react'
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useRolePermissionsStore } from '@/stores/useRolePermissionsStore'
import { toast } from 'sonner'

interface RoleCardProps {
  role: Role
  onSelect: (role: Role) => void
}

export function RoleCard({ role, onSelect }: RoleCardProps) {
  const { deleteRole, fetchRoles } = useRolePermissionsStore()

  const handleDelete = async () => {
    if (window.confirm(`Are you sure you want to delete the role "${role.name}"?`)) {
      const success = await deleteRole(role.id)
      if (success) {
        fetchRoles()
      }
    }
  }

  const getLevelColor = (level: string) => {
    switch (level) {
      case '1': return 'bg-red-100 text-red-800'
      case '2': return 'bg-orange-100 text-orange-800'
      case '3': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  const getLevelLabel = (level: string) => {
    switch (level) {
      case '1': return 'Super Admin'
      case '2': return 'Institute Admin'
      case '3': return 'User Level'
      default: return 'Unknown'
    }
  }

  return (
    <Card className="hover:shadow-md transition-shadow cursor-pointer group">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-100 rounded-lg">
              <Shield className="h-4 w-4 text-blue-600" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                {role.name}
              </h3>
              <p className="text-sm text-gray-600">{role.code}</p>
            </div>
          </div>
          
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                <MoreVertical className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={() => onSelect(role)}>
                <Users className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <RoleForm 
                mode="edit" 
                role={role}
                trigger={
                  <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
                    <Edit className="h-4 w-4 mr-2" />
                    Edit Role
                  </DropdownMenuItem>
                }
                onSuccess={() => fetchRoles()}
              />
              {!role.isSystemRole && (
                <DropdownMenuItem onClick={handleDelete} className="text-red-600">
                  <Trash2 className="h-4 w-4 mr-2" />
                  Delete Role
                </DropdownMenuItem>
              )}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      
      <CardContent className="pt-0">
        <div className="space-y-3">
          {/* Description */}
          {role.description && (
            <p className="text-sm text-gray-600 line-clamp-2">
              {role.description}
            </p>
          )}
          
          {/* Badges */}
          <div className="flex flex-wrap gap-2">
            <Badge className={getLevelColor(role.level)}>
              {getLevelLabel(role.level)}
            </Badge>
            
            <Badge variant={role.isActive ? "default" : "secondary"}>
              {role.isActive ? "Active" : "Inactive"}
            </Badge>
            
            {role.isSystemRole && (
              <Badge variant="outline" className="text-purple-600 border-purple-200">
                System Role
              </Badge>
            )}
            
            {role.scope && (
              <Badge variant="outline">
                {role.scope}
              </Badge>
            )}
          </div>
          
          {/* Permissions count */}
          <div className="flex items-center gap-2 text-sm text-gray-600">
            <Settings className="h-4 w-4" />
            <span>{role.permissions?.length || 0} permissions</span>
          </div>
          
          {/* Priority */}
          <div className="text-xs text-gray-500">
            Priority: {role.priority}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
