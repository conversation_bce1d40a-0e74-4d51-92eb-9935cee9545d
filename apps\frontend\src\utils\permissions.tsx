import React from 'react'
import { UserType } from '@/stores/sidebar/useSidebarStore'

// Permission types
export interface Permission {
  id: string
  name: string
  category: string
  resource: string
  action: string
  scope: 'global' | 'institute' | 'department' | 'own'
  requiredLevel: 1 | 2 | 3 | 4 | 5
}

export interface UserPermissions {
  role: string
  level: number
  permissions: Permission[]
  department?: string
  instituteId?: string
}

// Default permissions for each user type
export const DEFAULT_PERMISSIONS = {
  super_admin: {
    level: 1,
    permissions: [
      // User & Role Management
      'manage_users',
      'manage_roles',
      'manage_permissions',
      'view_users',
      'create_users',
      'update_users',
      'delete_users',

      // Institute Management
      'manage_institutes',
      'view_institutes',
      'create_institutes',
      'update_institutes',
      'delete_institutes',

      // System Management
      'manage_system_settings',
      'manage_themes',
      'manage_billing',
      'view_analytics',
      'view_all_reports',

      // All other permissions
      'manage_courses',
      'manage_students',
      'manage_branches',
      'manage_website',
      'access_all'
    ]
  },
  institute_admin: {
    level: 2,
    permissions: [
      'manage_courses',
      'manage_students',
      'manage_branches',
      'manage_institute_billing',
      'view_institute_analytics',
      'manage_website',
      'manage_institute_settings'
    ]
  },
  student: {
    level: 5,
    permissions: [
      'view_courses',
      'enroll_courses',
      'view_assignments',
      'submit_assignments',
      'join_live_classes',
      'view_progress',
      'manage_account',
      'access_community'
    ]
  }
}

// Navigation permissions mapping
export const NAVIGATION_PERMISSIONS = {
  // Super Admin
  'super-admin-dashboard': [],
  'super-admin-institutes': ['manage_institutes'],
  'super-admin-users': ['manage_users'],

  'super-admin-billing': ['manage_billing'],
  'super-admin-themes': ['manage_themes'],
  'super-admin-analytics': ['view_analytics'],
  'super-admin-settings': ['manage_system_settings'],
  'super-admin-role-permissions': ['manage_users'], // Add role-permissions navigation

  // Institute Admin
  'institute-admin-dashboard': [],
  'institute-admin-courses': ['manage_courses'],
  'institute-admin-students': ['manage_students'],
  'institute-admin-branches': ['manage_branches'],
  'institute-admin-billing': ['manage_institute_billing'],
  'institute-admin-analytics': ['view_institute_analytics'],
  'institute-admin-website': ['manage_website'],
  'institute-admin-settings': ['manage_institute_settings'],

  // Student
  'student-dashboard': [],
  'student-courses': ['view_courses'],
  'student-marketplace': ['view_courses'],
  'student-assignments': ['view_assignments'],
  'student-live-classes': ['join_live_classes'],
  'student-progress': ['view_progress'],
  'student-community': ['access_community'],
  'student-payments': ['manage_account'],
  'student-account': ['manage_account'],
  'student-support': []
}

/**
 * Check if user has a specific permission
 */
export function hasPermission(
  userPermissions: UserPermissions,
  requiredPermission: string,
  resource?: string,
  scope?: 'global' | 'institute' | 'department' | 'own'
): boolean {
  // Super admin and platform staff have all permissions
  if (userPermissions.role === 'super_admin' || userPermissions.role === 'platform_staff') {
    return true
  }

  // Check if user has the specific permission by name or code
  const hasDirectPermission = userPermissions.permissions.some(
    permission =>
      permission.name === requiredPermission ||
      permission.id === requiredPermission ||
      permission.name.toLowerCase() === requiredPermission.toLowerCase()
  )

  if (!hasDirectPermission) {
    return false
  }

  // If scope is specified, check scope restrictions
  if (scope) {
    const permission = userPermissions.permissions.find(
      p => p.name === requiredPermission || p.id === requiredPermission
    )

    if (!permission) return false

    // Check scope hierarchy: global > institute > department > own
    const scopeHierarchy = ['global', 'institute', 'department', 'own']
    const userScopeIndex = scopeHierarchy.indexOf(permission.scope)
    const requiredScopeIndex = scopeHierarchy.indexOf(scope)

    return userScopeIndex <= requiredScopeIndex
  }

  return true
}

/**
 * Check if user can access a navigation item
 */
export function canAccessNavigation(
  userPermissions: UserPermissions,
  navigationId: string
): boolean {
  const requiredPermissions = NAVIGATION_PERMISSIONS[navigationId as keyof typeof NAVIGATION_PERMISSIONS]
  
  // If no permissions required, allow access
  if (!requiredPermissions || requiredPermissions.length === 0) {
    return true
  }

  // Check if user has any of the required permissions
  return requiredPermissions.some(permission =>
    hasPermission(userPermissions, permission)
  )
}

/**
 * Filter navigation items based on user permissions
 */
export function filterNavigationByPermissions(
  navigationItems: any[],
  userPermissions: UserPermissions
): any[] {
  // Super admin has access to all navigation items
  if (userPermissions.role === 'super_admin') {
    return navigationItems
  }

  // Prevent infinite loops by checking if navigationItems is valid
  if (!Array.isArray(navigationItems) || navigationItems.length === 0) {
    return []
  }

  return navigationItems.filter(item => {
    // Ensure item has required properties
    if (!item || !item.href) {
      return false
    }

    // Generate navigation ID from href
    const navigationId = item.href.replace('/', '').replace(/\//g, '-')

    // Check if user can access this navigation item
    const canAccess = canAccessNavigation(userPermissions, navigationId)

    // If item has children, filter them recursively
    if (item.children && Array.isArray(item.children) && item.children.length > 0) {
      const filteredChildren = filterNavigationByPermissions(item.children, userPermissions)

      // If no children are accessible, hide the parent too
      if (filteredChildren.length === 0 && !canAccess) {
        return false
      }

      // Update children with filtered list (avoid mutation)
      item = { ...item, children: filteredChildren }
    }

    return canAccess
  })
}

/**
 * Check if user has minimum required level
 */
export function hasMinimumLevel(
  userLevel: number,
  requiredLevel: number
): boolean {
  // Lower numbers = higher level (1 = highest, 5 = lowest)
  return userLevel <= requiredLevel
}

/**
 * Get user permissions from user object
 */
export function getUserPermissions(user: any): UserPermissions {
  if (!user) {
    return {
      role: 'guest',
      level: 5,
      permissions: [],
      department: undefined,
      instituteId: undefined
    }
  }

  // Use legacyRole for role checking (consistent with auth system)
  const role = user.legacyRole || user.role || 'student'

  // Get level from role relationship or default
  let level = 5
  if (user.role && typeof user.role === 'object' && user.role.level) {
    level = parseInt(user.role.level) || 5
  } else {
    level = DEFAULT_PERMISSIONS[role as keyof typeof DEFAULT_PERMISSIONS]?.level || 5
  }

  // Extract permissions - handle both simple strings and complex objects
  let permissions: Permission[] = []
  if (user.role && typeof user.role === 'object' && user.role.permissions) {
    // Convert backend permission objects to frontend format
    permissions = user.role.permissions.map((perm: any) => ({
      id: perm.id || perm.code,
      name: perm.name || perm.code,
      category: perm.resource || 'general',
      resource: perm.resource || 'general',
      action: perm.action || 'access',
      scope: 'global' as const,
      requiredLevel: level
    }))
  } else if (user.permissions && Array.isArray(user.permissions)) {
    // Handle simple permission strings
    permissions = user.permissions.map((perm: string) => ({
      id: perm,
      name: perm,
      category: 'general',
      resource: 'general',
      action: 'access',
      scope: 'global' as const,
      requiredLevel: level
    }))
  }

  const department = user.employment?.department || user.department
  const instituteId = user.institute?.id || user.institute

  return {
    role,
    level,
    permissions,
    department,
    instituteId
  }
}

/**
 * Check if user can perform CRUD operations
 */
export function canPerformAction(
  userPermissions: UserPermissions,
  action: 'create' | 'read' | 'update' | 'delete',
  resource: string,
  scope?: 'global' | 'institute' | 'department' | 'own'
): boolean {
  const permissionName = `${action}_${resource}`
  return hasPermission(userPermissions, permissionName, resource, scope)
}

/**
 * Get allowed actions for a resource
 */
export function getAllowedActions(
  userPermissions: UserPermissions,
  resource: string
): string[] {
  const actions = ['create', 'read', 'update', 'delete']
  
  return actions.filter(action =>
    canPerformAction(userPermissions, action as any, resource)
  )
}

/**
 * Check if user is in same department
 */
export function isSameDepartment(
  userDepartment: string | undefined,
  targetDepartment: string | undefined
): boolean {
  if (!userDepartment || !targetDepartment) return false
  return userDepartment === targetDepartment
}

/**
 * Check if user is in same institute
 */
export function isSameInstitute(
  userInstituteId: string | undefined,
  targetInstituteId: string | undefined
): boolean {
  if (!userInstituteId || !targetInstituteId) return false
  return userInstituteId === targetInstituteId
}

/**
 * Permission-based component wrapper
 */
export function withPermission<T extends object>(
  Component: React.ComponentType<T>,
  requiredPermission: string,
  fallback?: React.ComponentType<T>
) {
  return function PermissionWrapper(props: T) {
    // This would be used with a permission context or hook
    // For now, returning the component as-is
    return <Component {...props} />
  }
}

/**
 * Generate permission key for caching
 */
export function generatePermissionKey(
  userId: string,
  permission: string,
  resource?: string,
  scope?: string
): string {
  return `${userId}:${permission}:${resource || 'any'}:${scope || 'any'}`
}

/**
 * Validate permission structure
 */
export function validatePermission(permission: any): permission is Permission {
  return (
    permission &&
    typeof permission.id === 'string' &&
    typeof permission.name === 'string' &&
    typeof permission.category === 'string' &&
    typeof permission.resource === 'string' &&
    typeof permission.action === 'string' &&
    ['global', 'institute', 'department', 'own'].includes(permission.scope) &&
    [1, 2, 3, 4, 5].includes(permission.requiredLevel)
  )
}

/**
 * Debug function to test super admin permissions
 */
export function debugSuperAdminPermissions(user: any) {
  console.log('=== SUPER ADMIN PERMISSION DEBUG ===')
  console.log('User object:', user)

  const userPermissions = getUserPermissions(user)
  console.log('Processed user permissions:', userPermissions)

  // Test key permissions
  const testPermissions = [
    'manage_users',
    'manage_roles',
    'manage_permissions',
    'view_users',
    'access_all'
  ]

  console.log('Permission test results:')
  testPermissions.forEach(permission => {
    const hasAccess = hasPermission(userPermissions, permission)
    console.log(`  ${permission}: ${hasAccess}`)
  })

  // Test navigation access
  const testNavigation = [
    'super-admin-role-permissions',
    'super-admin-users',
    'super-admin-institutes'
  ]

  console.log('Navigation access test results:')
  testNavigation.forEach(navId => {
    const hasAccess = canAccessNavigation(userPermissions, navId)
    console.log(`  ${navId}: ${hasAccess}`)
  })

  console.log('=== END DEBUG ===')

  return userPermissions
}
