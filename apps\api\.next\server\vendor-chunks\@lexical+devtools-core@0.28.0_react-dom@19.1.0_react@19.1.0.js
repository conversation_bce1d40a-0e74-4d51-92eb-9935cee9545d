"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+devtools-core@0.28.0_react-dom@19.1.0_react@19.1.0";
exports.ids = ["vendor-chunks/@lexical+devtools-core@0.28.0_react-dom@19.1.0_react@19.1.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+devtools-core@0.28.0_react-dom@19.1.0_react@19.1.0/node_modules/@lexical/devtools-core/LexicalDevtoolsCore.dev.mjs":
/*!************************************************************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+devtools-core@0.28.0_react-dom@19.1.0_react@19.1.0/node_modules/@lexical/devtools-core/LexicalDevtoolsCore.dev.mjs ***!
  \************************************************************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TreeView: () => (/* binding */ TreeView),\n/* harmony export */   generateContent: () => (/* binding */ generateContent),\n/* harmony export */   registerLexicalCommandLogger: () => (/* binding */ registerLexicalCommandLogger),\n/* harmony export */   useLexicalCommandsLog: () => (/* binding */ useLexicalCommandsLog)\n/* harmony export */ });\n/* harmony import */ var _lexical_html__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/html */ \"(ssr)/../../node_modules/.pnpm/@lexical+html@0.28.0/node_modules/@lexical/html/LexicalHtml.dev.mjs\");\n/* harmony import */ var _lexical_link__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @lexical/link */ \"(ssr)/../../node_modules/.pnpm/@lexical+link@0.28.0/node_modules/@lexical/link/LexicalLink.dev.mjs\");\n/* harmony import */ var _lexical_mark__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @lexical/mark */ \"(ssr)/../../node_modules/.pnpm/@lexical+mark@0.28.0/node_modules/@lexical/mark/LexicalMark.dev.mjs\");\n/* harmony import */ var _lexical_table__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @lexical/table */ \"(ssr)/../../node_modules/.pnpm/@lexical+table@0.28.0/node_modules/@lexical/table/LexicalTable.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react/jsx-runtime */ \"(ssr)/../../node_modules/.pnpm/next@15.3.0_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-runtime.js\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst NON_SINGLE_WIDTH_CHARS_REPLACEMENT = Object.freeze({\n  '\\t': '\\\\t',\n  '\\n': '\\\\n'\n});\nconst NON_SINGLE_WIDTH_CHARS_REGEX = new RegExp(Object.keys(NON_SINGLE_WIDTH_CHARS_REPLACEMENT).join('|'), 'g');\nconst SYMBOLS = Object.freeze({\n  ancestorHasNextSibling: '|',\n  ancestorIsLastChild: ' ',\n  hasNextSibling: '├',\n  isLastChild: '└',\n  selectedChar: '^',\n  selectedLine: '>'\n});\nconst FORMAT_PREDICATES = [node => node.hasFormat('bold') && 'Bold', node => node.hasFormat('code') && 'Code', node => node.hasFormat('italic') && 'Italic', node => node.hasFormat('strikethrough') && 'Strikethrough', node => node.hasFormat('subscript') && 'Subscript', node => node.hasFormat('superscript') && 'Superscript', node => node.hasFormat('underline') && 'Underline', node => node.hasFormat('highlight') && 'Highlight'];\nconst FORMAT_PREDICATES_PARAGRAPH = [node => node.hasTextFormat('bold') && 'Bold', node => node.hasTextFormat('code') && 'Code', node => node.hasTextFormat('italic') && 'Italic', node => node.hasTextFormat('strikethrough') && 'Strikethrough', node => node.hasTextFormat('subscript') && 'Subscript', node => node.hasTextFormat('superscript') && 'Superscript', node => node.hasTextFormat('underline') && 'Underline', node => node.hasTextFormat('highlight') && 'Highlight'];\nconst DETAIL_PREDICATES = [node => node.isDirectionless() && 'Directionless', node => node.isUnmergeable() && 'Unmergeable'];\nconst MODE_PREDICATES = [node => node.isToken() && 'Token', node => node.isSegmented() && 'Segmented'];\nfunction generateContent(editor, commandsLog, exportDOM, customPrintNode, obfuscateText = false) {\n  const editorState = editor.getEditorState();\n  const editorConfig = editor._config;\n  const compositionKey = editor._compositionKey;\n  const editable = editor._editable;\n  if (exportDOM) {\n    let htmlString = '';\n    editorState.read(() => {\n      htmlString = printPrettyHTML((0,_lexical_html__WEBPACK_IMPORTED_MODULE_2__.$generateHtmlFromNodes)(editor));\n    });\n    return htmlString;\n  }\n  let res = ' root\\n';\n  const selectionString = editorState.read(() => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getSelection)();\n    visitTree((0,lexical__WEBPACK_IMPORTED_MODULE_3__.$getRoot)(), (node, indent) => {\n      const nodeKey = node.getKey();\n      const nodeKeyDisplay = `(${nodeKey})`;\n      const typeDisplay = node.getType() || '';\n      const isSelected = node.isSelected();\n      res += `${isSelected ? SYMBOLS.selectedLine : ' '} ${indent.join(' ')} ${nodeKeyDisplay} ${typeDisplay} ${printNode(node, customPrintNode, obfuscateText)}\\n`;\n      res += $printSelectedCharsLine({\n        indent,\n        isSelected,\n        node,\n        nodeKeyDisplay,\n        selection,\n        typeDisplay\n      });\n    });\n    return selection === null ? ': null' : (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$isRangeSelection)(selection) ? printRangeSelection(selection) : (0,_lexical_table__WEBPACK_IMPORTED_MODULE_4__.$isTableSelection)(selection) ? printTableSelection(selection) : printNodeSelection(selection);\n  });\n  res += '\\n selection' + selectionString;\n  res += '\\n\\n commands:';\n  if (commandsLog.length) {\n    for (const {\n      index,\n      type,\n      payload\n    } of commandsLog) {\n      res += `\\n  └ ${index}. { type: ${type}, payload: ${payload instanceof Event ? payload.constructor.name : payload} }`;\n    }\n  } else {\n    res += '\\n  └ None dispatched.';\n  }\n  const {\n    version\n  } = editor.constructor;\n  res += `\\n\\n editor${version ? ` (v${version})` : ''}:`;\n  res += `\\n  └ namespace ${editorConfig.namespace}`;\n  if (compositionKey !== null) {\n    res += `\\n  └ compositionKey ${compositionKey}`;\n  }\n  res += `\\n  └ editable ${String(editable)}`;\n  return res;\n}\nfunction printRangeSelection(selection) {\n  let res = '';\n  const formatText = printFormatProperties(selection);\n  res += `: range ${formatText !== '' ? `{ ${formatText} }` : ''} ${selection.style !== '' ? `{ style: ${selection.style} } ` : ''}`;\n  const anchor = selection.anchor;\n  const focus = selection.focus;\n  const anchorOffset = anchor.offset;\n  const focusOffset = focus.offset;\n  res += `\\n  ├ anchor { key: ${anchor.key}, offset: ${anchorOffset === null ? 'null' : anchorOffset}, type: ${anchor.type} }`;\n  res += `\\n  └ focus { key: ${focus.key}, offset: ${focusOffset === null ? 'null' : focusOffset}, type: ${focus.type} }`;\n  return res;\n}\nfunction printNodeSelection(selection) {\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_3__.$isNodeSelection)(selection)) {\n    return '';\n  }\n  return `: node\\n  └ [${Array.from(selection._nodes).join(', ')}]`;\n}\nfunction printTableSelection(selection) {\n  return `: table\\n  └ { table: ${selection.tableKey}, anchorCell: ${selection.anchor.key}, focusCell: ${selection.focus.key} }`;\n}\nfunction visitTree(currentNode, visitor, indent = []) {\n  const childNodes = currentNode.getChildren();\n  const childNodesLength = childNodes.length;\n  childNodes.forEach((childNode, i) => {\n    visitor(childNode, indent.concat(i === childNodesLength - 1 ? SYMBOLS.isLastChild : SYMBOLS.hasNextSibling));\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_3__.$isElementNode)(childNode)) {\n      visitTree(childNode, visitor, indent.concat(i === childNodesLength - 1 ? SYMBOLS.ancestorIsLastChild : SYMBOLS.ancestorHasNextSibling));\n    }\n  });\n}\nfunction normalize(text, obfuscateText = false) {\n  const textToPrint = Object.entries(NON_SINGLE_WIDTH_CHARS_REPLACEMENT).reduce((acc, [key, value]) => acc.replace(new RegExp(key, 'g'), String(value)), text);\n  if (obfuscateText) {\n    return textToPrint.replace(/[^\\s]/g, '*');\n  }\n  return textToPrint;\n}\nfunction printNode(node, customPrintNode, obfuscateText = false) {\n  const customPrint = customPrintNode ? customPrintNode(node, obfuscateText) : undefined;\n  if (customPrint !== undefined && customPrint.length > 0) {\n    return customPrint;\n  }\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_3__.$isTextNode)(node)) {\n    const text = node.getTextContent();\n    const title = text.length === 0 ? '(empty)' : `\"${normalize(text, obfuscateText)}\"`;\n    const properties = printAllTextNodeProperties(node);\n    return [title, properties.length !== 0 ? `{ ${properties} }` : null].filter(Boolean).join(' ').trim();\n  } else if ((0,_lexical_link__WEBPACK_IMPORTED_MODULE_5__.$isLinkNode)(node)) {\n    const link = node.getURL();\n    const title = link.length === 0 ? '(empty)' : `\"${normalize(link, obfuscateText)}\"`;\n    const properties = printAllLinkNodeProperties(node);\n    return [title, properties.length !== 0 ? `{ ${properties} }` : null].filter(Boolean).join(' ').trim();\n  } else if ((0,_lexical_mark__WEBPACK_IMPORTED_MODULE_6__.$isMarkNode)(node)) {\n    return `ids: [ ${node.getIDs().join(', ')} ]`;\n  } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_3__.$isParagraphNode)(node)) {\n    const formatText = printTextFormatProperties(node);\n    let paragraphData = formatText !== '' ? `{ ${formatText} }` : '';\n    paragraphData += node.__style ? `(${node.__style})` : '';\n    return paragraphData;\n  } else {\n    return '';\n  }\n}\nfunction printTextFormatProperties(nodeOrSelection) {\n  let str = FORMAT_PREDICATES_PARAGRAPH.map(predicate => predicate(nodeOrSelection)).filter(Boolean).join(', ').toLocaleLowerCase();\n  if (str !== '') {\n    str = 'format: ' + str;\n  }\n  return str;\n}\nfunction printAllTextNodeProperties(node) {\n  return [printFormatProperties(node), printDetailProperties(node), printModeProperties(node)].filter(Boolean).join(', ');\n}\nfunction printAllLinkNodeProperties(node) {\n  return [printTargetProperties(node), printRelProperties(node), printTitleProperties(node)].filter(Boolean).join(', ');\n}\nfunction printDetailProperties(nodeOrSelection) {\n  let str = DETAIL_PREDICATES.map(predicate => predicate(nodeOrSelection)).filter(Boolean).join(', ').toLocaleLowerCase();\n  if (str !== '') {\n    str = 'detail: ' + str;\n  }\n  return str;\n}\nfunction printModeProperties(nodeOrSelection) {\n  let str = MODE_PREDICATES.map(predicate => predicate(nodeOrSelection)).filter(Boolean).join(', ').toLocaleLowerCase();\n  if (str !== '') {\n    str = 'mode: ' + str;\n  }\n  return str;\n}\nfunction printFormatProperties(nodeOrSelection) {\n  let str = FORMAT_PREDICATES.map(predicate => predicate(nodeOrSelection)).filter(Boolean).join(', ').toLocaleLowerCase();\n  if (str !== '') {\n    str = 'format: ' + str;\n  }\n  return str;\n}\nfunction printTargetProperties(node) {\n  let str = node.getTarget();\n  // TODO Fix nullish on LinkNode\n  if (str != null) {\n    str = 'target: ' + str;\n  }\n  return str;\n}\nfunction printRelProperties(node) {\n  let str = node.getRel();\n  // TODO Fix nullish on LinkNode\n  if (str != null) {\n    str = 'rel: ' + str;\n  }\n  return str;\n}\nfunction printTitleProperties(node) {\n  let str = node.getTitle();\n  // TODO Fix nullish on LinkNode\n  if (str != null) {\n    str = 'title: ' + str;\n  }\n  return str;\n}\nfunction $printSelectedCharsLine({\n  indent,\n  isSelected,\n  node,\n  nodeKeyDisplay,\n  selection,\n  typeDisplay\n}) {\n  // No selection or node is not selected.\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_3__.$isTextNode)(node) || !(0,lexical__WEBPACK_IMPORTED_MODULE_3__.$isRangeSelection)(selection) || !isSelected || (0,lexical__WEBPACK_IMPORTED_MODULE_3__.$isElementNode)(node)) {\n    return '';\n  }\n\n  // No selected characters.\n  const anchor = selection.anchor;\n  const focus = selection.focus;\n  if (node.getTextContent() === '' || anchor.getNode() === selection.focus.getNode() && anchor.offset === focus.offset) {\n    return '';\n  }\n  const [start, end] = $getSelectionStartEnd(node, selection);\n  if (start === end) {\n    return '';\n  }\n  const selectionLastIndent = indent[indent.length - 1] === SYMBOLS.hasNextSibling ? SYMBOLS.ancestorHasNextSibling : SYMBOLS.ancestorIsLastChild;\n  const indentionChars = [...indent.slice(0, indent.length - 1), selectionLastIndent];\n  const unselectedChars = Array(start + 1).fill(' ');\n  const selectedChars = Array(end - start).fill(SYMBOLS.selectedChar);\n  const paddingLength = typeDisplay.length + 2; // 1 for the space after + 1 for the double quote.\n\n  const nodePrintSpaces = Array(nodeKeyDisplay.length + paddingLength).fill(' ');\n  return [SYMBOLS.selectedLine, indentionChars.join(' '), [...nodePrintSpaces, ...unselectedChars, ...selectedChars].join('')].join(' ') + '\\n';\n}\nfunction printPrettyHTML(str) {\n  const div = document.createElement('div');\n  div.innerHTML = str.trim();\n  return prettifyHTML(div, 0).innerHTML;\n}\nfunction prettifyHTML(node, level) {\n  const indentBefore = new Array(level++ + 1).join('  ');\n  const indentAfter = new Array(level - 1).join('  ');\n  let textNode;\n  for (let i = 0; i < node.children.length; i++) {\n    textNode = document.createTextNode('\\n' + indentBefore);\n    node.insertBefore(textNode, node.children[i]);\n    prettifyHTML(node.children[i], level);\n    if (node.lastElementChild === node.children[i]) {\n      textNode = document.createTextNode('\\n' + indentAfter);\n      node.appendChild(textNode);\n    }\n  }\n  return node;\n}\nfunction $getSelectionStartEnd(node, selection) {\n  const anchorAndFocus = selection.getStartEndPoints();\n  if ((0,lexical__WEBPACK_IMPORTED_MODULE_3__.$isNodeSelection)(selection) || anchorAndFocus === null) {\n    return [-1, -1];\n  }\n  const [anchor, focus] = anchorAndFocus;\n  const textContent = node.getTextContent();\n  const textLength = textContent.length;\n  let start = -1;\n  let end = -1;\n\n  // Only one node is being selected.\n  if (anchor.type === 'text' && focus.type === 'text') {\n    const anchorNode = anchor.getNode();\n    const focusNode = focus.getNode();\n    if (anchorNode === focusNode && node === anchorNode && anchor.offset !== focus.offset) {\n      [start, end] = anchor.offset < focus.offset ? [anchor.offset, focus.offset] : [focus.offset, anchor.offset];\n    } else if (node === anchorNode) {\n      [start, end] = anchorNode.isBefore(focusNode) ? [anchor.offset, textLength] : [0, anchor.offset];\n    } else if (node === focusNode) {\n      [start, end] = focusNode.isBefore(anchorNode) ? [focus.offset, textLength] : [0, focus.offset];\n    } else {\n      // Node is within selection but not the anchor nor focus.\n      [start, end] = [0, textLength];\n    }\n  }\n\n  // Account for non-single width characters.\n  const numNonSingleWidthCharBeforeSelection = (textContent.slice(0, start).match(NON_SINGLE_WIDTH_CHARS_REGEX) || []).length;\n  const numNonSingleWidthCharInSelection = (textContent.slice(start, end).match(NON_SINGLE_WIDTH_CHARS_REGEX) || []).length;\n  return [start + numNonSingleWidthCharBeforeSelection, end + numNonSingleWidthCharBeforeSelection + numNonSingleWidthCharInSelection];\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst LARGE_EDITOR_STATE_SIZE = 1000;\nconst TreeView = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)(function TreeViewWrapped({\n  treeTypeButtonClassName,\n  timeTravelButtonClassName,\n  timeTravelPanelSliderClassName,\n  timeTravelPanelButtonClassName,\n  viewClassName,\n  timeTravelPanelClassName,\n  editorState,\n  setEditorState,\n  setEditorReadOnly,\n  generateContent\n}, ref) {\n  const [timeStampedEditorStates, setTimeStampedEditorStates] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n  const [timeTravelEnabled, setTimeTravelEnabled] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [showExportDOM, setShowExportDOM] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const playingIndexRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n  const inputRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [isPlaying, setIsPlaying] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [isLimited, setIsLimited] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [showLimited, setShowLimited] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const lastEditorStateRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n  const lastGenerationID = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n  const generateTree = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(exportDOM => {\n    const myID = ++lastGenerationID.current;\n    generateContent(exportDOM).then(treeText => {\n      if (myID === lastGenerationID.current) {\n        setContent(treeText);\n      }\n    }).catch(err => {\n      if (myID === lastGenerationID.current) {\n        setContent(`Error rendering tree: ${err.message}\\n\\nStack:\\n${err.stack}`);\n      }\n    });\n  }, [generateContent]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!showLimited && editorState._nodeMap.size > LARGE_EDITOR_STATE_SIZE) {\n      setIsLimited(true);\n      if (!showLimited) {\n        return;\n      }\n    }\n\n    // Prevent re-rendering if the editor state hasn't changed\n    if (lastEditorStateRef.current !== editorState) {\n      lastEditorStateRef.current = editorState;\n      generateTree(showExportDOM);\n      if (!timeTravelEnabled) {\n        setTimeStampedEditorStates(currentEditorStates => [...currentEditorStates, [Date.now(), editorState]]);\n      }\n    }\n  }, [editorState, generateTree, showExportDOM, showLimited, timeTravelEnabled]);\n  const totalEditorStates = timeStampedEditorStates.length;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (isPlaying) {\n      let timeoutId;\n      const play = () => {\n        const currentIndex = playingIndexRef.current;\n        if (currentIndex === totalEditorStates - 1) {\n          setIsPlaying(false);\n          return;\n        }\n        const currentTime = timeStampedEditorStates[currentIndex][0];\n        const nextTime = timeStampedEditorStates[currentIndex + 1][0];\n        const timeDiff = nextTime - currentTime;\n        timeoutId = setTimeout(() => {\n          playingIndexRef.current++;\n          const index = playingIndexRef.current;\n          const input = inputRef.current;\n          if (input !== null) {\n            input.value = String(index);\n          }\n          setEditorState(timeStampedEditorStates[index][1]);\n          play();\n        }, timeDiff);\n      };\n      play();\n      return () => {\n        clearTimeout(timeoutId);\n      };\n    }\n  }, [timeStampedEditorStates, isPlaying, totalEditorStates, setEditorState]);\n  const handleExportModeToggleClick = () => {\n    generateTree(!showExportDOM);\n    setShowExportDOM(!showExportDOM);\n  };\n  return /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", {\n    className: viewClassName,\n    children: [!showLimited && isLimited ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", {\n      style: {\n        padding: 20\n      },\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"span\", {\n        style: {\n          marginRight: 20\n        },\n        children: \"Detected large EditorState, this can impact debugging performance.\"\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"button\", {\n        onClick: () => {\n          setShowLimited(true);\n        },\n        style: {\n          background: 'transparent',\n          border: '1px solid white',\n          color: 'white',\n          cursor: 'pointer',\n          padding: 5\n        },\n        children: \"Show full tree\"\n      })]\n    }) : null, !showLimited ? /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"button\", {\n      onClick: () => handleExportModeToggleClick(),\n      className: treeTypeButtonClassName,\n      type: \"button\",\n      children: showExportDOM ? 'Tree' : 'Export DOM'\n    }) : null, !timeTravelEnabled && (showLimited || !isLimited) && totalEditorStates > 2 && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"button\", {\n      onClick: () => {\n        setEditorReadOnly(true);\n        playingIndexRef.current = totalEditorStates - 1;\n        setTimeTravelEnabled(true);\n      },\n      className: timeTravelButtonClassName,\n      type: \"button\",\n      children: \"Time Travel\"\n    }), (showLimited || !isLimited) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"pre\", {\n      ref: ref,\n      children: content\n    }), timeTravelEnabled && (showLimited || !isLimited) && /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsxs)(\"div\", {\n      className: timeTravelPanelClassName,\n      children: [/*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"button\", {\n        className: timeTravelPanelButtonClassName,\n        onClick: () => {\n          if (playingIndexRef.current === totalEditorStates - 1) {\n            playingIndexRef.current = 1;\n          }\n          setIsPlaying(!isPlaying);\n        },\n        type: \"button\",\n        children: isPlaying ? 'Pause' : 'Play'\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"input\", {\n        className: timeTravelPanelSliderClassName,\n        ref: inputRef,\n        onChange: event => {\n          const editorStateIndex = Number(event.target.value);\n          const timeStampedEditorState = timeStampedEditorStates[editorStateIndex];\n          if (timeStampedEditorState) {\n            playingIndexRef.current = editorStateIndex;\n            setEditorState(timeStampedEditorState[1]);\n          }\n        },\n        type: \"range\",\n        min: \"1\",\n        max: totalEditorStates - 1\n      }), /*#__PURE__*/(0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_1__.jsx)(\"button\", {\n        className: timeTravelPanelButtonClassName,\n        onClick: () => {\n          setEditorReadOnly(false);\n          const index = timeStampedEditorStates.length - 1;\n          const timeStampedEditorState = timeStampedEditorStates[index];\n          setEditorState(timeStampedEditorState[1]);\n          const input = inputRef.current;\n          if (input !== null) {\n            input.value = String(index);\n          }\n          setTimeTravelEnabled(false);\n          setIsPlaying(false);\n        },\n        type: \"button\",\n        children: \"Exit\"\n      })]\n    })]\n  });\n});\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction registerLexicalCommandLogger(editor, setLoggedCommands) {\n  const unregisterCommandListeners = new Set();\n  let i = 0;\n  for (const [command] of editor._commands) {\n    unregisterCommandListeners.add(editor.registerCommand(command, payload => {\n      setLoggedCommands(state => {\n        i += 1;\n        const newState = [...state];\n        newState.push({\n          index: i,\n          payload,\n          type: command.type ? command.type : 'UNKNOWN'\n        });\n        if (newState.length > 10) {\n          newState.shift();\n        }\n        return newState;\n      });\n      return false;\n    }, lexical__WEBPACK_IMPORTED_MODULE_3__.COMMAND_PRIORITY_CRITICAL));\n  }\n  return () => unregisterCommandListeners.forEach(unregister => unregister());\n}\nfunction useLexicalCommandsLog(editor) {\n  const [loggedCommands, setLoggedCommands] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    return registerLexicalCommandLogger(editor, setLoggedCommands);\n  }, [editor]);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => loggedCommands, [loggedCommands]);\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+devtools-core@0.28.0_react-dom@19.1.0_react@19.1.0/node_modules/@lexical/devtools-core/LexicalDevtoolsCore.dev.mjs\n");

/***/ })

};
;