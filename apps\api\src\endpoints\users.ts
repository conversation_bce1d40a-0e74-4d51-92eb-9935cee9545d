import type { Endpoint } from 'payload'
import { requireAuth, requireSuperAdmin, requireInstituteAdmin } from '../middleware/auth'
import bcrypt from 'bcrypt'

// Get current user profile
export const getCurrentUserEndpoint: Endpoint = {
  path: '/users/me',
  method: 'get',
  handler: async (req) => {
    const authCheck = await requireAuth()(req)
    if (authCheck) return authCheck

    try {
      const user = await req.payload.findByID({
        collection: 'users',
        id: req.user.id,
      })

      if (!user) {
        return Response.json(
          { message: 'User not found' },
          { status: 404 }
        )
      }

      // Remove password from response
      const { password: _, ...userWithoutPassword } = user

      return Response.json({
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('Get current user error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Update current user profile
export const updateCurrentUserEndpoint: Endpoint = {
  path: '/users/me',
  method: 'patch',
  handler: async (req) => {
    const authCheck = await requireAuth()(req)
    if (authCheck) return authCheck

    const { firstName, lastName, phone, avatar } = req.body

    try {
      const updatedUser = await req.payload.update({
        collection: 'users',
        id: req.user.id,
        data: {
          firstName,
          lastName,
          phone,
          avatar,
        },
      })

      // Remove password from response
      const { password: _, ...userWithoutPassword } = updatedUser

      return Response.json({
        message: 'Profile updated successfully',
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('Update user error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Get all users (Super Admin only)
export const getAllUsersEndpoint: Endpoint = {
  path: '/users',
  method: 'get',
  handler: async (req) => {
    const authCheck = await requireSuperAdmin(req)
    if (authCheck) return authCheck

    const url = new URL(req.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const role = url.searchParams.get('role')
    const search = url.searchParams.get('search')

    try {
      const where: any = {}

      if (role) {
        where.role = { equals: role }
      }

      if (search) {
        where.or = [
          { firstName: { contains: search } },
          { lastName: { contains: search } },
          { email: { contains: search } },
        ]
      }

      const users = await req.payload.find({
        collection: 'users',
        where,
        page,
        limit,
        sort: '-createdAt',
      })

      // Remove passwords from response
      const usersWithoutPasswords = users.docs.map(user => {
        const { password: _, ...userWithoutPassword } = user
        return userWithoutPassword
      })

      return Response.json({
        users: usersWithoutPasswords,
        totalDocs: users.totalDocs,
        totalPages: users.totalPages,
        page: users.page,
        limit: users.limit,
        hasNextPage: users.hasNextPage,
        hasPrevPage: users.hasPrevPage,
      })

    } catch (error) {
      console.error('Get users error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Get institute users (Institute Admin only)
export const getInstituteUsersEndpoint: Endpoint = {
  path: '/users/institute',
  method: 'get',
  handler: async (req) => {
    const authCheck = await requireInstituteAdmin(req)
    if (authCheck) return authCheck

    const url = new URL(req.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '10')
    const role = url.searchParams.get('role')
    const search = url.searchParams.get('search')

    try {
      // Get current user's institute
      const currentUser = await req.payload.findByID({
        collection: 'users',
        id: req.user.id,
      })

      if (!currentUser.institute) {
        return Response.json(
          { message: 'User not associated with any institute' },
          { status: 400 }
        )
      }

      const where: any = {
        institute: { equals: currentUser.institute },
      }

      if (role) {
        where.role = { equals: role }
      }

      if (search) {
        where.or = [
          { firstName: { contains: search } },
          { lastName: { contains: search } },
          { email: { contains: search } },
        ]
      }

      const users = await req.payload.find({
        collection: 'users',
        where,
        page,
        limit,
        sort: '-createdAt',
      })

      // Remove passwords from response
      const usersWithoutPasswords = users.docs.map(user => {
        const { password: _, ...userWithoutPassword } = user
        return userWithoutPassword
      })

      return Response.json({
        users: usersWithoutPasswords,
        totalDocs: users.totalDocs,
        totalPages: users.totalPages,
        page: users.page,
        limit: users.limit,
        hasNextPage: users.hasNextPage,
        hasPrevPage: users.hasPrevPage,
      })

    } catch (error) {
      console.error('Get institute users error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}

// Create user (Super Admin or Institute Admin)
export const createUserEndpoint: Endpoint = {
  path: '/users',
  method: 'post',
  handler: async (req) => {
    const authCheck = await requireAuth(['super_admin', 'platform_staff', 'institute_admin'])(req)
    if (authCheck) return authCheck

    const { email, password, firstName, lastName, phone, role, institute, branch } = req.body

    if (!email || !password || !firstName || !lastName || !role) {
      return Response.json(
        { message: 'Required fields are missing' },
        { status: 400 }
      )
    }

    try {
      // Check permissions based on user role
      const currentUser = await req.payload.findByID({
        collection: 'users',
        id: req.user.id,
      })

      // Super admins can create any user
      // Institute admins can only create users for their institute
      if (currentUser.legacyRole === 'institute_admin') {
        if (!currentUser.institute) {
          return Response.json(
            { message: 'Institute admin must be associated with an institute' },
            { status: 400 }
          )
        }
        
        // Force institute to be the same as current user's institute
        if (institute && institute !== currentUser.institute) {
          return Response.json(
            { message: 'Cannot create users for other institutes' },
            { status: 403 }
          )
        }
      }

      // Check if email already exists
      const existingUsers = await req.payload.find({
        collection: 'users',
        where: {
          email: { equals: email.toLowerCase() },
        },
      })

      if (existingUsers.docs.length > 0) {
        return Response.json(
          { message: 'Email already exists' },
          { status: 400 }
        )
      }

      // Hash password
      const hashedPassword = await bcrypt.hash(password, 10)

      const userData: any = {
        email: email.toLowerCase(),
        password: hashedPassword,
        firstName,
        lastName,
        phone,
        role,
        isActive: true,
        emailVerified: false,
      }

      if (institute) {
        userData.institute = institute
      } else if (currentUser.legacyRole === 'institute_admin') {
        userData.institute = currentUser.institute
      }

      if (branch) {
        userData.branch = branch
      }

      const newUser = await req.payload.create({
        collection: 'users',
        data: userData,
      })

      // Remove password from response
      const { password: _, ...userWithoutPassword } = newUser

      return Response.json({
        message: 'User created successfully',
        user: userWithoutPassword,
      })

    } catch (error) {
      console.error('Create user error:', error)
      return Response.json(
        { message: 'Internal server error' },
        { status: 500 }
      )
    }
  },
}
