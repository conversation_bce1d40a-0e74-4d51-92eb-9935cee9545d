import { create } from 'zustand'
import { devtools } from 'zustand/middleware'
import { toast } from 'sonner'

// Helper function to get auth headers
const getAuthHeaders = () => {
  const token = localStorage.getItem('auth_token')
  return {
    'Content-Type': 'application/json',
    ...(token && { 'Authorization': `Bear<PERSON> ${token}` }),
  }
}

// Types
export interface Permission {
  id: string
  name: string
  description?: string
  category: string
  resource: string
  action: string
  scope: string
  requiredLevel: number
  isSystemPermission: boolean
  createdAt: string
  updatedAt: string
}

export interface Role {
  id: string
  name: string
  description?: string
  level: number
  isSystemRole: boolean
  isActive: boolean
  createdAt: string
  updatedAt: string
  permissions?: Permission[]
  permissionCount?: number
}

export interface RolePermissionAssignment {
  id: string
  role: string | Role
  permission: string | Permission
  isActive: boolean
  assignedBy?: string
  assignedAt: string
  notes?: string
}

interface Filters {
  search?: string
  category?: string
  level?: number
  isActive?: boolean
  isSystemRole?: boolean
}

interface Pagination {
  page: number
  limit: number
  totalDocs: number
  totalPages: number
  hasNextPage: boolean
  hasPrevPage: boolean
}

interface RolePermissionsState {
  // Data
  roles: Role[]
  permissions: Permission[]
  rolePermissions: RolePermissionAssignment[]
  selectedRole: Role | null
  selectedPermissions: string[]
  
  // UI State
  isLoading: boolean
  error: string | null
  viewMode: 'list' | 'cards'
  
  // Filters and Pagination
  filters: Filters
  pagination: Pagination
  
  // Actions
  setViewMode: (mode: 'list' | 'cards') => void
  setFilters: (filters: Partial<Filters>) => void
  setSelectedRole: (role: Role | null) => void
  setSelectedPermissions: (permissionIds: string[]) => void
  clearError: () => void
  
  // API Actions
  fetchRoles: (page?: number) => Promise<void>
  fetchPermissions: (page?: number) => Promise<void>
  fetchRolesWithPermissions: () => Promise<void>
  fetchRolePermissions: (roleId: string) => Promise<void>
  
  // CRUD Operations
  createRole: (roleData: Partial<Role>) => Promise<boolean>
  updateRole: (id: string, roleData: Partial<Role>) => Promise<boolean>
  deleteRole: (id: string) => Promise<boolean>
  
  createPermission: (permissionData: Partial<Permission>) => Promise<boolean>
  updatePermission: (id: string, permissionData: Partial<Permission>) => Promise<boolean>
  deletePermission: (id: string) => Promise<boolean>
  
  assignPermissionsToRole: (roleId: string, permissionIds: string[]) => Promise<boolean>
  removePermissionFromRole: (roleId: string, permissionId: string) => Promise<boolean>
}

const initialFilters: Filters = {
  search: '',
  category: '',
  level: undefined,
  isActive: undefined,
  isSystemRole: undefined,
}

const initialPagination: Pagination = {
  page: 1,
  limit: 20,
  totalDocs: 0,
  totalPages: 0,
  hasNextPage: false,
  hasPrevPage: false,
}

export const useRolePermissionsStore = create<RolePermissionsState>()(
  devtools(
    (set, get) => ({
      // Initial State
      roles: [],
      permissions: [],
      rolePermissions: [],
      selectedRole: null,
      selectedPermissions: [],
      isLoading: false,
      error: null,
      viewMode: 'list',
      filters: initialFilters,
      pagination: initialPagination,

      // UI Actions
      setViewMode: (mode) => set({ viewMode: mode }),
      
      setFilters: (newFilters) => set((state) => ({
        filters: { ...state.filters, ...newFilters }
      })),
      
      setSelectedRole: (role) => set({ selectedRole: role }),
      
      setSelectedPermissions: (permissionIds) => set({ selectedPermissions: permissionIds }),
      
      clearError: () => set({ error: null }),

      // API Actions
      fetchRoles: async (page = 1) => {
        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const queryParams = new URLSearchParams({
            page: page.toString(),
            limit: '20',
            ...(filters.search && { search: filters.search }),
            ...(filters.level && { level: filters.level.toString() }),
            ...(filters.isActive !== undefined && { isActive: filters.isActive.toString() }),
            ...(filters.isSystemRole !== undefined && { isSystemRole: filters.isSystemRole.toString() }),
          })

          const response = await fetch(`/api/roles?${queryParams}`, {
            headers: getAuthHeaders(),
            credentials: 'include',
          })
          const data = await response.json()

          if (data.success) {
            set({
              roles: data.docs,
              pagination: {
                page: data.page,
                limit: data.limit,
                totalDocs: data.totalDocs,
                totalPages: data.totalPages,
                hasNextPage: data.hasNextPage,
                hasPrevPage: data.hasPrevPage,
              },
            })
          } else {
            throw new Error(data.message || 'Failed to fetch roles')
          }
        } catch (error) {
          console.error('Error fetching roles:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to fetch roles')
        } finally {
          set({ isLoading: false })
        }
      },

      fetchPermissions: async (page = 1) => {
        set({ isLoading: true, error: null })
        try {
          const { filters } = get()
          const queryParams = new URLSearchParams({
            page: page.toString(),
            limit: '100', // Get more permissions for assignment
            ...(filters.search && { search: filters.search }),
            ...(filters.category && { category: filters.category }),
          })

          const response = await fetch(`/api/permissions?${queryParams}`, {
            headers: getAuthHeaders(),
            credentials: 'include',
          })
          const data = await response.json()

          if (data.success) {
            set({ permissions: data.docs })
          } else {
            throw new Error(data.message || 'Failed to fetch permissions')
          }
        } catch (error) {
          console.error('Error fetching permissions:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to fetch permissions')
        } finally {
          set({ isLoading: false })
        }
      },

      fetchRolesWithPermissions: async () => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/roles-with-permissions', {
            headers: getAuthHeaders(),
            credentials: 'include',
          })
          const data = await response.json()

          if (data.success) {
            set({ roles: data.data })
          } else {
            throw new Error(data.message || 'Failed to fetch roles with permissions')
          }
        } catch (error) {
          console.error('Error fetching roles with permissions:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to fetch roles with permissions')
        } finally {
          set({ isLoading: false })
        }
      },

      fetchRolePermissions: async (roleId: string) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/roles/${roleId}/permissions`, {
            headers: getAuthHeaders(),
            credentials: 'include',
          })
          const data = await response.json()

          if (data.success) {
            const permissionIds = data.data.map((p: Permission) => p.id)
            set({ selectedPermissions: permissionIds })
          } else {
            throw new Error(data.message || 'Failed to fetch role permissions')
          }
        } catch (error) {
          console.error('Error fetching role permissions:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to fetch role permissions')
        } finally {
          set({ isLoading: false })
        }
      },

      // CRUD Operations
      createRole: async (roleData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/roles', {
            method: 'POST',
            headers: getAuthHeaders(),
            credentials: 'include',
            body: JSON.stringify(roleData),
          })
          const data = await response.json()

          if (data.success || response.ok) {
            toast.success('Role created successfully')
            // Refresh data to get updated list
            get().fetchRolesWithPermissions()
            return true
          } else {
            throw new Error(data.message || 'Failed to create role')
          }
        } catch (error) {
          console.error('Error creating role:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to create role')
          return false
        } finally {
          set({ isLoading: false })
        }
      },

      updateRole: async (id, roleData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/roles/${id}`, {
            method: 'PATCH',
            headers: getAuthHeaders(),
            credentials: 'include',
            body: JSON.stringify(roleData),
          })
          const data = await response.json()

          if (data.success || response.ok) {
            toast.success('Role updated successfully')
            // Refresh data to get updated list
            get().fetchRolesWithPermissions()
            return true
          } else {
            throw new Error(data.message || 'Failed to update role')
          }
        } catch (error) {
          console.error('Error updating role:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to update role')
          return false
        } finally {
          set({ isLoading: false })
        }
      },

      deleteRole: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/roles/${id}`, {
            method: 'DELETE',
            headers: getAuthHeaders(),
            credentials: 'include',
          })
          const data = await response.json()

          if (data.success || response.ok) {
            toast.success('Role deleted successfully')
            // Refresh data to get updated list
            get().fetchRolesWithPermissions()
            return true
          } else {
            throw new Error(data.message || 'Failed to delete role')
          }
        } catch (error) {
          console.error('Error deleting role:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to delete role')
          return false
        } finally {
          set({ isLoading: false })
        }
      },

      createPermission: async (permissionData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch('/api/permissions', {
            method: 'POST',
            headers: getAuthHeaders(),
            credentials: 'include',
            body: JSON.stringify(permissionData),
          })
          const data = await response.json()

          if (data.success || response.ok) {
            toast.success('Permission created successfully')
            // Refresh permissions list
            get().fetchPermissions()
            return true
          } else {
            throw new Error(data.message || 'Failed to create permission')
          }
        } catch (error) {
          console.error('Error creating permission:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to create permission')
          return false
        } finally {
          set({ isLoading: false })
        }
      },

      updatePermission: async (id, permissionData) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/permissions/${id}`, {
            method: 'PATCH',
            headers: getAuthHeaders(),
            credentials: 'include',
            body: JSON.stringify(permissionData),
          })
          const data = await response.json()

          if (data.success || response.ok) {
            toast.success('Permission updated successfully')
            get().fetchPermissions()
            return true
          } else {
            throw new Error(data.message || 'Failed to update permission')
          }
        } catch (error) {
          console.error('Error updating permission:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to update permission')
          return false
        } finally {
          set({ isLoading: false })
        }
      },

      deletePermission: async (id) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/permissions/${id}`, {
            method: 'DELETE',
            headers: getAuthHeaders(),
            credentials: 'include',
          })
          const data = await response.json()

          if (data.success || response.ok) {
            toast.success('Permission deleted successfully')
            get().fetchPermissions()
            return true
          } else {
            throw new Error(data.message || 'Failed to delete permission')
          }
        } catch (error) {
          console.error('Error deleting permission:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to delete permission')
          return false
        } finally {
          set({ isLoading: false })
        }
      },

      assignPermissionsToRole: async (roleId, permissionIds) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/roles/${roleId}/permissions`, {
            method: 'POST',
            headers: getAuthHeaders(),
            credentials: 'include',
            body: JSON.stringify({ permissionIds }),
          })
          const data = await response.json()

          if (data.success || response.ok) {
            toast.success('Permissions assigned successfully')
            get().fetchRolesWithPermissions()
            return true
          } else {
            throw new Error(data.message || 'Failed to assign permissions')
          }
        } catch (error) {
          console.error('Error assigning permissions:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to assign permissions')
          return false
        } finally {
          set({ isLoading: false })
        }
      },

      removePermissionFromRole: async (roleId, permissionId) => {
        set({ isLoading: true, error: null })
        try {
          const response = await fetch(`/api/roles/${roleId}/permissions/${permissionId}`, {
            method: 'DELETE',
            headers: getAuthHeaders(),
            credentials: 'include',
          })
          const data = await response.json()

          if (data.success || response.ok) {
            toast.success('Permission removed successfully')
            get().fetchRolesWithPermissions()
            return true
          } else {
            throw new Error(data.message || 'Failed to remove permission')
          }
        } catch (error) {
          console.error('Error removing permission:', error)
          const errorMessage = error instanceof Error ? error.message : 'Unknown error'
          set({ error: errorMessage })
          toast.error('Failed to remove permission')
          return false
        } finally {
          set({ isLoading: false })
        }
      },
    }),
    {
      name: 'role-permissions-store',
    }
  )
)
