"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+history@0.28.0";
exports.ids = ["vendor-chunks/@lexical+history@0.28.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+history@0.28.0/node_modules/@lexical/history/LexicalHistory.dev.mjs":
/*!*************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+history@0.28.0/node_modules/@lexical/history/LexicalHistory.dev.mjs ***!
  \*************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createEmptyHistoryState: () => (/* binding */ createEmptyHistoryState),\n/* harmony export */   registerHistory: () => (/* binding */ registerHistory)\n/* harmony export */ });\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst HISTORY_MERGE = 0;\nconst HISTORY_PUSH = 1;\nconst DISCARD_HISTORY_CANDIDATE = 2;\nconst OTHER = 0;\nconst COMPOSING_CHARACTER = 1;\nconst INSERT_CHARACTER_AFTER_SELECTION = 2;\nconst DELETE_CHARACTER_BEFORE_SELECTION = 3;\nconst DELETE_CHARACTER_AFTER_SELECTION = 4;\nfunction getDirtyNodes(editorState, dirtyLeaves, dirtyElements) {\n  const nodeMap = editorState._nodeMap;\n  const nodes = [];\n  for (const dirtyLeafKey of dirtyLeaves) {\n    const dirtyLeaf = nodeMap.get(dirtyLeafKey);\n    if (dirtyLeaf !== undefined) {\n      nodes.push(dirtyLeaf);\n    }\n  }\n  for (const [dirtyElementKey, intentionallyMarkedAsDirty] of dirtyElements) {\n    if (!intentionallyMarkedAsDirty) {\n      continue;\n    }\n    const dirtyElement = nodeMap.get(dirtyElementKey);\n    if (dirtyElement !== undefined && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(dirtyElement)) {\n      nodes.push(dirtyElement);\n    }\n  }\n  return nodes;\n}\nfunction getChangeType(prevEditorState, nextEditorState, dirtyLeavesSet, dirtyElementsSet, isComposing) {\n  if (prevEditorState === null || dirtyLeavesSet.size === 0 && dirtyElementsSet.size === 0 && !isComposing) {\n    return OTHER;\n  }\n  const nextSelection = nextEditorState._selection;\n  const prevSelection = prevEditorState._selection;\n  if (isComposing) {\n    return COMPOSING_CHARACTER;\n  }\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(nextSelection) || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) || !prevSelection.isCollapsed() || !nextSelection.isCollapsed()) {\n    return OTHER;\n  }\n  const dirtyNodes = getDirtyNodes(nextEditorState, dirtyLeavesSet, dirtyElementsSet);\n  if (dirtyNodes.length === 0) {\n    return OTHER;\n  }\n\n  // Catching the case when inserting new text node into an element (e.g. first char in paragraph/list),\n  // or after existing node.\n  if (dirtyNodes.length > 1) {\n    const nextNodeMap = nextEditorState._nodeMap;\n    const nextAnchorNode = nextNodeMap.get(nextSelection.anchor.key);\n    const prevAnchorNode = nextNodeMap.get(prevSelection.anchor.key);\n    if (nextAnchorNode && prevAnchorNode && !prevEditorState._nodeMap.has(nextAnchorNode.__key) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(nextAnchorNode) && nextAnchorNode.__text.length === 1 && nextSelection.anchor.offset === 1) {\n      return INSERT_CHARACTER_AFTER_SELECTION;\n    }\n    return OTHER;\n  }\n  const nextDirtyNode = dirtyNodes[0];\n  const prevDirtyNode = prevEditorState._nodeMap.get(nextDirtyNode.__key);\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevDirtyNode) || !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(nextDirtyNode) || prevDirtyNode.__mode !== nextDirtyNode.__mode) {\n    return OTHER;\n  }\n  const prevText = prevDirtyNode.__text;\n  const nextText = nextDirtyNode.__text;\n  if (prevText === nextText) {\n    return OTHER;\n  }\n  const nextAnchor = nextSelection.anchor;\n  const prevAnchor = prevSelection.anchor;\n  if (nextAnchor.key !== prevAnchor.key || nextAnchor.type !== 'text') {\n    return OTHER;\n  }\n  const nextAnchorOffset = nextAnchor.offset;\n  const prevAnchorOffset = prevAnchor.offset;\n  const textDiff = nextText.length - prevText.length;\n  if (textDiff === 1 && prevAnchorOffset === nextAnchorOffset - 1) {\n    return INSERT_CHARACTER_AFTER_SELECTION;\n  }\n  if (textDiff === -1 && prevAnchorOffset === nextAnchorOffset + 1) {\n    return DELETE_CHARACTER_BEFORE_SELECTION;\n  }\n  if (textDiff === -1 && prevAnchorOffset === nextAnchorOffset) {\n    return DELETE_CHARACTER_AFTER_SELECTION;\n  }\n  return OTHER;\n}\nfunction isTextNodeUnchanged(key, prevEditorState, nextEditorState) {\n  const prevNode = prevEditorState._nodeMap.get(key);\n  const nextNode = nextEditorState._nodeMap.get(key);\n  const prevSelection = prevEditorState._selection;\n  const nextSelection = nextEditorState._selection;\n  const isDeletingLine = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(prevSelection) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(nextSelection) && prevSelection.anchor.type === 'element' && prevSelection.focus.type === 'element' && nextSelection.anchor.type === 'text' && nextSelection.focus.type === 'text';\n  if (!isDeletingLine && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(prevNode) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(nextNode) && prevNode.__parent === nextNode.__parent) {\n    // This has the assumption that object key order won't change if the\n    // content did not change, which should normally be safe given\n    // the manner in which nodes and exportJSON are typically implemented.\n    return JSON.stringify(prevEditorState.read(() => prevNode.exportJSON())) === JSON.stringify(nextEditorState.read(() => nextNode.exportJSON()));\n  }\n  return false;\n}\nfunction createMergeActionGetter(editor, delay) {\n  let prevChangeTime = Date.now();\n  let prevChangeType = OTHER;\n  return (prevEditorState, nextEditorState, currentHistoryEntry, dirtyLeaves, dirtyElements, tags) => {\n    const changeTime = Date.now();\n\n    // If applying changes from history stack there's no need\n    // to run history logic again, as history entries already calculated\n    if (tags.has('historic')) {\n      prevChangeType = OTHER;\n      prevChangeTime = changeTime;\n      return DISCARD_HISTORY_CANDIDATE;\n    }\n    const changeType = getChangeType(prevEditorState, nextEditorState, dirtyLeaves, dirtyElements, editor.isComposing());\n    const mergeAction = (() => {\n      const isSameEditor = currentHistoryEntry === null || currentHistoryEntry.editor === editor;\n      const shouldPushHistory = tags.has('history-push');\n      const shouldMergeHistory = !shouldPushHistory && isSameEditor && tags.has('history-merge');\n      if (shouldMergeHistory) {\n        return HISTORY_MERGE;\n      }\n      if (prevEditorState === null) {\n        return HISTORY_PUSH;\n      }\n      const selection = nextEditorState._selection;\n      const hasDirtyNodes = dirtyLeaves.size > 0 || dirtyElements.size > 0;\n      if (!hasDirtyNodes) {\n        if (selection !== null) {\n          return HISTORY_MERGE;\n        }\n        return DISCARD_HISTORY_CANDIDATE;\n      }\n      if (shouldPushHistory === false && changeType !== OTHER && changeType === prevChangeType && changeTime < prevChangeTime + delay && isSameEditor) {\n        return HISTORY_MERGE;\n      }\n\n      // A single node might have been marked as dirty, but not have changed\n      // due to some node transform reverting the change.\n      if (dirtyLeaves.size === 1) {\n        const dirtyLeafKey = Array.from(dirtyLeaves)[0];\n        if (isTextNodeUnchanged(dirtyLeafKey, prevEditorState, nextEditorState)) {\n          return HISTORY_MERGE;\n        }\n      }\n      return HISTORY_PUSH;\n    })();\n    prevChangeTime = changeTime;\n    prevChangeType = changeType;\n    return mergeAction;\n  };\n}\nfunction redo(editor, historyState) {\n  const redoStack = historyState.redoStack;\n  const undoStack = historyState.undoStack;\n  if (redoStack.length !== 0) {\n    const current = historyState.current;\n    if (current !== null) {\n      undoStack.push(current);\n      editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_UNDO_COMMAND, true);\n    }\n    const historyStateEntry = redoStack.pop();\n    if (redoStack.length === 0) {\n      editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_REDO_COMMAND, false);\n    }\n    historyState.current = historyStateEntry || null;\n    if (historyStateEntry) {\n      historyStateEntry.editor.setEditorState(historyStateEntry.editorState, {\n        tag: 'historic'\n      });\n    }\n  }\n}\nfunction undo(editor, historyState) {\n  const redoStack = historyState.redoStack;\n  const undoStack = historyState.undoStack;\n  const undoStackLength = undoStack.length;\n  if (undoStackLength !== 0) {\n    const current = historyState.current;\n    const historyStateEntry = undoStack.pop();\n    if (current !== null) {\n      redoStack.push(current);\n      editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_REDO_COMMAND, true);\n    }\n    if (undoStack.length === 0) {\n      editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_UNDO_COMMAND, false);\n    }\n    historyState.current = historyStateEntry || null;\n    if (historyStateEntry) {\n      historyStateEntry.editor.setEditorState(historyStateEntry.editorState, {\n        tag: 'historic'\n      });\n    }\n  }\n}\nfunction clearHistory(historyState) {\n  historyState.undoStack = [];\n  historyState.redoStack = [];\n  historyState.current = null;\n}\n\n/**\n * Registers necessary listeners to manage undo/redo history stack and related editor commands.\n * It returns `unregister` callback that cleans up all listeners and should be called on editor unmount.\n * @param editor - The lexical editor.\n * @param historyState - The history state, containing the current state and the undo/redo stack.\n * @param delay - The time (in milliseconds) the editor should delay generating a new history stack,\n * instead of merging the current changes with the current stack.\n * @returns The listeners cleanup callback function.\n */\nfunction registerHistory(editor, historyState, delay) {\n  const getMergeAction = createMergeActionGetter(editor, delay);\n  const applyChange = ({\n    editorState,\n    prevEditorState,\n    dirtyLeaves,\n    dirtyElements,\n    tags\n  }) => {\n    const current = historyState.current;\n    const redoStack = historyState.redoStack;\n    const undoStack = historyState.undoStack;\n    const currentEditorState = current === null ? null : current.editorState;\n    if (current !== null && editorState === currentEditorState) {\n      return;\n    }\n    const mergeAction = getMergeAction(prevEditorState, editorState, current, dirtyLeaves, dirtyElements, tags);\n    if (mergeAction === HISTORY_PUSH) {\n      if (redoStack.length !== 0) {\n        historyState.redoStack = [];\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_REDO_COMMAND, false);\n      }\n      if (current !== null) {\n        undoStack.push({\n          ...current\n        });\n        editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_UNDO_COMMAND, true);\n      }\n    } else if (mergeAction === DISCARD_HISTORY_CANDIDATE) {\n      return;\n    }\n\n    // Else we merge\n    historyState.current = {\n      editor,\n      editorState\n    };\n  };\n  const unregister = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.mergeRegister)(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.UNDO_COMMAND, () => {\n    undo(editor, historyState);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.REDO_COMMAND, () => {\n    redo(editor, historyState);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CLEAR_EDITOR_COMMAND, () => {\n    clearHistory(historyState);\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CLEAR_HISTORY_COMMAND, () => {\n    clearHistory(historyState);\n    editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_REDO_COMMAND, false);\n    editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CAN_UNDO_COMMAND, false);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerUpdateListener(applyChange));\n  return unregister;\n}\n\n/**\n * Creates an empty history state.\n * @returns - The empty history state, as an object.\n */\nfunction createEmptyHistoryState() {\n  return {\n    current: null,\n    redoStack: [],\n    undoStack: []\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+history@0.28.0/node_modules/@lexical/history/LexicalHistory.dev.mjs\n");

/***/ })

};
;