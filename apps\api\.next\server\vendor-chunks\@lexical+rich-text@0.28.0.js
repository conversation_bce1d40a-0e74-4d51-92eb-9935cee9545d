"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@lexical+rich-text@0.28.0";
exports.ids = ["vendor-chunks/@lexical+rich-text@0.28.0"];
exports.modules = {

/***/ "(rsc)/../../node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs":
/*!******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $createHeadingNode: () => (/* binding */ $createHeadingNode),\n/* harmony export */   $createQuoteNode: () => (/* binding */ $createQuoteNode),\n/* harmony export */   $isHeadingNode: () => (/* binding */ $isHeadingNode),\n/* harmony export */   $isQuoteNode: () => (/* binding */ $isQuoteNode),\n/* harmony export */   DRAG_DROP_PASTE: () => (/* binding */ DRAG_DROP_PASTE),\n/* harmony export */   HeadingNode: () => (/* binding */ HeadingNode),\n/* harmony export */   QuoteNode: () => (/* binding */ QuoteNode),\n/* harmony export */   eventFiles: () => (/* binding */ eventFiles),\n/* harmony export */   registerRichText: () => (/* binding */ registerRichText)\n/* harmony export */ });\n/* harmony import */ var _lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/clipboard */ \"(rsc)/../../node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/selection */ \"(rsc)/../../node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(rsc)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(rsc)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction caretFromPoint(x, y) {\n  if (typeof document.caretRangeFromPoint !== 'undefined') {\n    const range = document.caretRangeFromPoint(x, y);\n    if (range === null) {\n      return null;\n    }\n    return {\n      node: range.startContainer,\n      offset: range.startOffset\n    };\n    // @ts-ignore\n  } else if (document.caretPositionFromPoint !== 'undefined') {\n    // @ts-ignore FF - no types\n    const range = document.caretPositionFromPoint(x, y);\n    if (range === null) {\n      return null;\n    }\n    return {\n      node: range.offsetNode,\n      offset: range.offset\n    };\n  } else {\n    // Gracefully handle IE\n    return null;\n  }\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst documentMode = CAN_USE_DOM && 'documentMode' in document ? document.documentMode : null;\nconst CAN_USE_BEFORE_INPUT = CAN_USE_DOM && 'InputEvent' in window && !documentMode ? 'getTargetRanges' in new window.InputEvent('input') : false;\nconst IS_SAFARI = CAN_USE_DOM && /Version\\/[\\d.]+.*Safari/.test(navigator.userAgent);\nconst IS_IOS = CAN_USE_DOM && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\n\n// Keep these in case we need to use them in the future.\n// export const IS_WINDOWS: boolean = CAN_USE_DOM && /Win/.test(navigator.platform);\nconst IS_CHROME = CAN_USE_DOM && /^(?=.*Chrome).*/i.test(navigator.userAgent);\nconst IS_APPLE_WEBKIT = CAN_USE_DOM && /AppleWebKit\\/[\\d.]+/.test(navigator.userAgent) && !IS_CHROME;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst DRAG_DROP_PASTE = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('DRAG_DROP_PASTE_FILE');\n/** @noInheritDoc */\nclass QuoteNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  static getType() {\n    return 'quote';\n  }\n  static clone(node) {\n    return new QuoteNode(node.__key);\n  }\n\n  // View\n\n  createDOM(config) {\n    const element = document.createElement('blockquote');\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, config.theme.quote);\n    return element;\n  }\n  updateDOM(prevNode, dom) {\n    return false;\n  }\n  static importDOM() {\n    return {\n      blockquote: node => ({\n        conversion: $convertBlockquoteElement,\n        priority: 0\n      })\n    };\n  }\n  exportDOM(editor) {\n    const {\n      element\n    } = super.exportDOM(editor);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n      if (this.isEmpty()) {\n        element.append(document.createElement('br'));\n      }\n      const formatType = this.getFormatType();\n      element.style.textAlign = formatType;\n      const direction = this.getDirection();\n      if (direction) {\n        element.dir = direction;\n      }\n    }\n    return {\n      element\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createQuoteNode().updateFromJSON(serializedNode);\n  }\n\n  // Mutation\n\n  insertNewAfter(_, restoreSelection) {\n    const newBlock = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const direction = this.getDirection();\n    newBlock.setDirection(direction);\n    this.insertAfter(newBlock, restoreSelection);\n    return newBlock;\n  }\n  collapseAtStart() {\n    const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const children = this.getChildren();\n    children.forEach(child => paragraph.append(child));\n    this.replace(paragraph);\n    return true;\n  }\n  canMergeWhenEmpty() {\n    return true;\n  }\n}\nfunction $createQuoteNode() {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new QuoteNode());\n}\nfunction $isQuoteNode(node) {\n  return node instanceof QuoteNode;\n}\n/** @noInheritDoc */\nclass HeadingNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  static getType() {\n    return 'heading';\n  }\n  static clone(node) {\n    return new HeadingNode(node.__tag, node.__key);\n  }\n  constructor(tag, key) {\n    super(key);\n    this.__tag = tag;\n  }\n  getTag() {\n    return this.__tag;\n  }\n  setTag(tag) {\n    const self = this.getWritable();\n    this.__tag = tag;\n    return self;\n  }\n\n  // View\n\n  createDOM(config) {\n    const tag = this.__tag;\n    const element = document.createElement(tag);\n    const theme = config.theme;\n    const classNames = theme.heading;\n    if (classNames !== undefined) {\n      const className = classNames[tag];\n      (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, className);\n    }\n    return element;\n  }\n  updateDOM(prevNode, dom, config) {\n    return prevNode.__tag !== this.__tag;\n  }\n  static importDOM() {\n    return {\n      h1: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h2: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h3: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h4: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h5: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h6: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      p: node => {\n        // domNode is a <p> since we matched it by nodeName\n        const paragraph = node;\n        const firstChild = paragraph.firstChild;\n        if (firstChild !== null && isGoogleDocsTitle(firstChild)) {\n          return {\n            conversion: () => ({\n              node: null\n            }),\n            priority: 3\n          };\n        }\n        return null;\n      },\n      span: node => {\n        if (isGoogleDocsTitle(node)) {\n          return {\n            conversion: domNode => {\n              return {\n                node: $createHeadingNode('h1')\n              };\n            },\n            priority: 3\n          };\n        }\n        return null;\n      }\n    };\n  }\n  exportDOM(editor) {\n    const {\n      element\n    } = super.exportDOM(editor);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n      if (this.isEmpty()) {\n        element.append(document.createElement('br'));\n      }\n      const formatType = this.getFormatType();\n      element.style.textAlign = formatType;\n      const direction = this.getDirection();\n      if (direction) {\n        element.dir = direction;\n      }\n    }\n    return {\n      element\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createHeadingNode(serializedNode.tag).updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setTag(serializedNode.tag);\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      tag: this.getTag()\n    };\n  }\n\n  // Mutation\n  insertNewAfter(selection, restoreSelection = true) {\n    const anchorOffet = selection ? selection.anchor.offset : 0;\n    const lastDesc = this.getLastDescendant();\n    const isAtEnd = !lastDesc || selection && selection.anchor.key === lastDesc.getKey() && anchorOffet === lastDesc.getTextContentSize();\n    const newElement = isAtEnd || !selection ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)() : $createHeadingNode(this.getTag());\n    const direction = this.getDirection();\n    newElement.setDirection(direction);\n    this.insertAfter(newElement, restoreSelection);\n    if (anchorOffet === 0 && !this.isEmpty() && selection) {\n      const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n      paragraph.select();\n      this.replace(paragraph, true);\n    }\n    return newElement;\n  }\n  collapseAtStart() {\n    const newElement = !this.isEmpty() ? $createHeadingNode(this.getTag()) : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const children = this.getChildren();\n    children.forEach(child => newElement.append(child));\n    this.replace(newElement);\n    return true;\n  }\n  extractWithChild() {\n    return true;\n  }\n}\nfunction isGoogleDocsTitle(domNode) {\n  if (domNode.nodeName.toLowerCase() === 'span') {\n    return domNode.style.fontSize === '26pt';\n  }\n  return false;\n}\nfunction $convertHeadingElement(element) {\n  const nodeName = element.nodeName.toLowerCase();\n  let node = null;\n  if (nodeName === 'h1' || nodeName === 'h2' || nodeName === 'h3' || nodeName === 'h4' || nodeName === 'h5' || nodeName === 'h6') {\n    node = $createHeadingNode(nodeName);\n    if (element.style !== null) {\n      (0,lexical__WEBPACK_IMPORTED_MODULE_0__.setNodeIndentFromDOM)(element, node);\n      node.setFormat(element.style.textAlign);\n    }\n  }\n  return {\n    node\n  };\n}\nfunction $convertBlockquoteElement(element) {\n  const node = $createQuoteNode();\n  if (element.style !== null) {\n    node.setFormat(element.style.textAlign);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.setNodeIndentFromDOM)(element, node);\n  }\n  return {\n    node\n  };\n}\nfunction $createHeadingNode(headingTag = 'h1') {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new HeadingNode(headingTag));\n}\nfunction $isHeadingNode(node) {\n  return node instanceof HeadingNode;\n}\nfunction onPasteForRichText(event, editor) {\n  event.preventDefault();\n  editor.update(() => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    const clipboardData = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, InputEvent) || (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, KeyboardEvent) ? null : event.clipboardData;\n    if (clipboardData != null && selection !== null) {\n      (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.$insertDataTransferForRichText)(clipboardData, selection, editor);\n    }\n  }, {\n    tag: 'paste'\n  });\n}\nasync function onCutForRichText(event, editor) {\n  await (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.copyToClipboard)(editor, (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent) ? event : null);\n  editor.update(() => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      selection.removeText();\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      selection.getNodes().forEach(node => node.remove());\n    }\n  });\n}\n\n// Clipboard may contain files that we aren't allowed to read. While the event is arguably useless,\n// in certain occasions, we want to know whether it was a file transfer, as opposed to text. We\n// control this with the first boolean flag.\nfunction eventFiles(event) {\n  let dataTransfer = null;\n  if ((0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, DragEvent)) {\n    dataTransfer = event.dataTransfer;\n  } else if ((0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent)) {\n    dataTransfer = event.clipboardData;\n  }\n  if (dataTransfer === null) {\n    return [false, [], false];\n  }\n  const types = dataTransfer.types;\n  const hasFiles = types.includes('Files');\n  const hasContent = types.includes('text/html') || types.includes('text/plain');\n  return [hasFiles, Array.from(dataTransfer.files), hasContent];\n}\nfunction $handleIndentAndOutdent(indentOrOutdent) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    return false;\n  }\n  const alreadyHandled = new Set();\n  const nodes = selection.getNodes();\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    const key = node.getKey();\n    if (alreadyHandled.has(key)) {\n      continue;\n    }\n    const parentBlock = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, parentNode => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(parentNode) && !parentNode.isInline());\n    if (parentBlock === null) {\n      continue;\n    }\n    const parentKey = parentBlock.getKey();\n    if (parentBlock.canIndent() && !alreadyHandled.has(parentKey)) {\n      alreadyHandled.add(parentKey);\n      indentOrOutdent(parentBlock);\n    }\n  }\n  return alreadyHandled.size > 0;\n}\nfunction $isTargetWithinDecorator(target) {\n  const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(target);\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(node);\n}\nfunction $isSelectionAtEndOfRoot(selection) {\n  const focus = selection.focus;\n  return focus.key === 'root' && focus.offset === (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)().getChildrenSize();\n}\n\n/**\n * Resets the capitalization of the selection to default.\n * Called when the user presses space, tab, or enter key.\n * @param selection The selection to reset the capitalization of.\n */\nfunction $resetCapitalization(selection) {\n  for (const format of ['lowercase', 'uppercase', 'capitalize']) {\n    if (selection.hasFormat(format)) {\n      selection.toggleFormat(format);\n    }\n  }\n}\nfunction registerRichText(editor) {\n  const removeListener = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.mergeRegister)(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CLICK_COMMAND, payload => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      selection.clear();\n      return true;\n    }\n    return false;\n  }, 0), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND, isBackward => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      selection.deleteCharacter(isBackward);\n      return true;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      selection.deleteNodes();\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_WORD_COMMAND, isBackward => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.deleteWord(isBackward);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_LINE_COMMAND, isBackward => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.deleteLine(isBackward);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CONTROLLED_TEXT_INSERTION_COMMAND, eventOrText => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (typeof eventOrText === 'string') {\n      if (selection !== null) {\n        selection.insertText(eventOrText);\n      }\n    } else {\n      if (selection === null) {\n        return false;\n      }\n      const dataTransfer = eventOrText.dataTransfer;\n      if (dataTransfer != null) {\n        (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.$insertDataTransferForRichText)(dataTransfer, selection, editor);\n      } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n        const data = eventOrText.data;\n        if (data) {\n          selection.insertText(data);\n        }\n        return true;\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.REMOVE_TEXT_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.removeText();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FORMAT_TEXT_COMMAND, format => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.formatText(format);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FORMAT_ELEMENT_COMMAND, format => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      return false;\n    }\n    const nodes = selection.getNodes();\n    for (const node of nodes) {\n      const element = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, parentNode => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(parentNode) && !parentNode.isInline());\n      if (element !== null) {\n        element.setFormat(format);\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_LINE_BREAK_COMMAND, selectStart => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.insertLineBreak(selectStart);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.insertParagraph();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_TAB_COMMAND, () => {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$insertNodes)([(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTabNode)()]);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INDENT_CONTENT_COMMAND, () => {\n    return $handleIndentAndOutdent(block => {\n      const indent = block.getIndent();\n      block.setIndent(indent + 1);\n    });\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.OUTDENT_CONTENT_COMMAND, () => {\n    return $handleIndentAndOutdent(block => {\n      const indent = block.getIndent();\n      if (indent > 0) {\n        block.setIndent(indent - 1);\n      }\n    });\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_UP_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection) && !$isTargetWithinDecorator(event.target)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        nodes[0].selectPrevious();\n        return true;\n      }\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const possibleNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentNode)(selection.focus, true);\n      if (!event.shiftKey && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(possibleNode) && !possibleNode.isIsolated() && !possibleNode.isInline()) {\n        possibleNode.selectPrevious();\n        event.preventDefault();\n        return true;\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_DOWN_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        nodes[0].selectNext(0, 0);\n        return true;\n      }\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      if ($isSelectionAtEndOfRoot(selection)) {\n        event.preventDefault();\n        return true;\n      }\n      const possibleNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentNode)(selection.focus, false);\n      if (!event.shiftKey && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(possibleNode) && !possibleNode.isIsolated() && !possibleNode.isInline()) {\n        possibleNode.selectNext();\n        event.preventDefault();\n        return true;\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_LEFT_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        event.preventDefault();\n        nodes[0].selectPrevious();\n        return true;\n      }\n    }\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    if ((0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$shouldOverrideDefaultCharacterSelection)(selection, true)) {\n      const isHoldingShift = event.shiftKey;\n      event.preventDefault();\n      (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$moveCharacter)(selection, isHoldingShift, true);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_RIGHT_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection) && !$isTargetWithinDecorator(event.target)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        event.preventDefault();\n        nodes[0].selectNext(0, 0);\n        return true;\n      }\n    }\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const isHoldingShift = event.shiftKey;\n    if ((0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$shouldOverrideDefaultCharacterSelection)(selection, false)) {\n      event.preventDefault();\n      (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$moveCharacter)(selection, isHoldingShift, false);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_BACKSPACE_COMMAND, event => {\n    if ($isTargetWithinDecorator(event.target)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const {\n        anchor\n      } = selection;\n      const anchorNode = anchor.getNode();\n      if (selection.isCollapsed() && anchor.offset === 0 && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(anchorNode)) {\n        const element = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$getNearestBlockElementAncestorOrThrow)(anchorNode);\n        if (element.getIndent() > 0) {\n          event.preventDefault();\n          return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.OUTDENT_CONTENT_COMMAND, undefined);\n        }\n      }\n\n      // Exception handling for iOS native behavior instead of Lexical's behavior when using Korean on iOS devices.\n      // more details - https://github.com/facebook/lexical/issues/5841\n      if (IS_IOS && navigator.language === 'ko-KR') {\n        return false;\n      }\n    } else if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      return false;\n    }\n    event.preventDefault();\n    return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND, true);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_DELETE_COMMAND, event => {\n    if ($isTargetWithinDecorator(event.target)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection))) {\n      return false;\n    }\n    event.preventDefault();\n    return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND, false);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ENTER_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    $resetCapitalization(selection);\n    if (event !== null) {\n      // If we have beforeinput, then we can avoid blocking\n      // the default behavior. This ensures that the iOS can\n      // intercept that we're actually inserting a paragraph,\n      // and autocomplete, autocapitalize etc work as intended.\n      // This can also cause a strange performance issue in\n      // Safari, where there is a noticeable pause due to\n      // preventing the key down of enter.\n      if ((IS_IOS || IS_SAFARI || IS_APPLE_WEBKIT) && CAN_USE_BEFORE_INPUT) {\n        return false;\n      }\n      event.preventDefault();\n      if (event.shiftKey) {\n        return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_LINE_BREAK_COMMAND, false);\n      }\n    }\n    return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, undefined);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ESCAPE_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    editor.blur();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DROP_COMMAND, event => {\n    const [, files] = eventFiles(event);\n    if (files.length > 0) {\n      const x = event.clientX;\n      const y = event.clientY;\n      const eventRange = caretFromPoint(x, y);\n      if (eventRange !== null) {\n        const {\n          offset: domOffset,\n          node: domNode\n        } = eventRange;\n        const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(domNode);\n        if (node !== null) {\n          const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelection)();\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n            selection.anchor.set(node.getKey(), domOffset, 'text');\n            selection.focus.set(node.getKey(), domOffset, 'text');\n          } else {\n            const parentKey = node.getParentOrThrow().getKey();\n            const offset = node.getIndexWithinParent() + 1;\n            selection.anchor.set(parentKey, offset, 'element');\n            selection.focus.set(parentKey, offset, 'element');\n          }\n          const normalizedSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeSelection__EXPERIMENTAL)(selection);\n          (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(normalizedSelection);\n        }\n        editor.dispatchCommand(DRAG_DROP_PASTE, files);\n      }\n      event.preventDefault();\n      return true;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DRAGSTART_COMMAND, event => {\n    const [isFileTransfer] = eventFiles(event);\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (isFileTransfer && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DRAGOVER_COMMAND, event => {\n    const [isFileTransfer] = eventFiles(event);\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (isFileTransfer && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const x = event.clientX;\n    const y = event.clientY;\n    const eventRange = caretFromPoint(x, y);\n    if (eventRange !== null) {\n      const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(eventRange.node);\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(node)) {\n        // Show browser caret as the user is dragging the media across the screen. Won't work\n        // for DecoratorNode nor it's relevant.\n        event.preventDefault();\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECT_ALL_COMMAND, () => {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$selectAll)();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.COPY_COMMAND, event => {\n    (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.copyToClipboard)(editor, (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent) ? event : null);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CUT_COMMAND, event => {\n    onCutForRichText(event, editor);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.PASTE_COMMAND, event => {\n    const [, files, hasTextContent] = eventFiles(event);\n    if (files.length > 0 && !hasTextContent) {\n      editor.dispatchCommand(DRAG_DROP_PASTE, files);\n      return true;\n    }\n\n    // if inputs then paste within the input ignore creating a new node on paste event\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(event.target) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isSelectionCapturedInDecoratorInput)(event.target)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (selection !== null) {\n      onPasteForRichText(event, editor);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_SPACE_COMMAND, _ => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      $resetCapitalization(selection);\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_TAB_COMMAND, _ => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      $resetCapitalization(selection);\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR));\n  return removeListener;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi4vLi4vbm9kZV9tb2R1bGVzLy5wbnBtL0BsZXhpY2FsK3JpY2gtdGV4dEAwLjI4LjAvbm9kZV9tb2R1bGVzL0BsZXhpY2FsL3JpY2gtdGV4dC9MZXhpY2FsUmljaFRleHQuZGV2Lm1qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRXFGO0FBQ1M7QUFDd0U7QUFDODZCOztBQUVwbEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLElBQUk7QUFDSjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUEsd0JBQXdCLHNEQUFhO0FBQ3JDO0FBQ0Esd0JBQXdCLGdEQUFXO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0EsSUFBSSxzRUFBc0I7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU07QUFDTixRQUFRLHNEQUFhO0FBQ3JCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUE7QUFDQSxxQkFBcUIsNkRBQW9CO0FBQ3pDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQiw2REFBb0I7QUFDMUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxTQUFTLDhEQUFxQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCLGdEQUFXO0FBQ3JDOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0sc0VBQXNCO0FBQzVCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBLE9BQU87QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsT0FBTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOLFFBQVEsc0RBQWE7QUFDckI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsK0NBQStDLDZEQUFvQjtBQUNuRTtBQUNBO0FBQ0E7QUFDQTtBQUNBLHdCQUF3Qiw2REFBb0I7QUFDNUM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsNkVBQTZFLDZEQUFvQjtBQUNqRztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLDZEQUFvQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsSUFBSSw2REFBb0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsU0FBUyw4REFBcUI7QUFDOUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQkFBc0Isc0RBQWE7QUFDbkMsMEJBQTBCLGlFQUFpQix1QkFBdUIsaUVBQWlCO0FBQ25GO0FBQ0EsTUFBTSxrRkFBOEI7QUFDcEM7QUFDQSxHQUFHO0FBQ0g7QUFDQSxHQUFHO0FBQ0g7QUFDQTtBQUNBLFFBQVEsbUVBQWUsU0FBUyxpRUFBaUI7QUFDakQ7QUFDQSxzQkFBc0Isc0RBQWE7QUFDbkMsUUFBUSwwREFBaUI7QUFDekI7QUFDQSxNQUFNLFNBQVMseURBQWdCO0FBQy9CO0FBQ0E7QUFDQSxHQUFHO0FBQ0g7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLE1BQU0saUVBQWlCO0FBQ3ZCO0FBQ0EsSUFBSSxTQUFTLGlFQUFpQjtBQUM5QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esb0JBQW9CLHNEQUFhO0FBQ2pDLE9BQU8sMERBQWlCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0JBQWtCLGtCQUFrQjtBQUNwQztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esd0JBQXdCLG1FQUFtQixxQkFBcUIsdURBQWM7QUFDOUU7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZUFBZSxtRUFBMEI7QUFDekMsU0FBUyx5REFBZ0I7QUFDekI7QUFDQTtBQUNBO0FBQ0Esa0RBQWtELGlEQUFRO0FBQzFEOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EseUJBQXlCLDZEQUFhLHdCQUF3QixrREFBYTtBQUMzRSxzQkFBc0Isc0RBQWE7QUFDbkMsUUFBUSx5REFBZ0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLDZCQUE2Qiw2REFBd0I7QUFDeEQsc0JBQXNCLHNEQUFhO0FBQ25DLFFBQVEsMERBQWlCO0FBQ3pCO0FBQ0E7QUFDQSxNQUFNLFNBQVMseURBQWdCO0FBQy9CO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxFQUFFLDREQUF1QiwwQkFBMEIsd0RBQW1CO0FBQ3pFLHNCQUFzQixzREFBYTtBQUNuQyxTQUFTLDBEQUFpQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLHdEQUFtQjtBQUN6RSxzQkFBc0Isc0RBQWE7QUFDbkMsU0FBUywwREFBaUI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQixzRUFBaUM7QUFDdkYsc0JBQXNCLHNEQUFhO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTTtBQUNOO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxRQUFRLGtGQUE4QjtBQUN0QyxRQUFRLFNBQVMsMERBQWlCO0FBQ2xDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQix3REFBbUI7QUFDekUsc0JBQXNCLHNEQUFhO0FBQ25DLFNBQVMsMERBQWlCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxFQUFFLDREQUF1QiwwQkFBMEIsd0RBQW1CO0FBQ3pFLHNCQUFzQixzREFBYTtBQUNuQyxTQUFTLDBEQUFpQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLDJEQUFzQjtBQUM1RSxzQkFBc0Isc0RBQWE7QUFDbkMsU0FBUywwREFBaUIsZ0JBQWdCLHlEQUFnQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixtRUFBbUIscUJBQXFCLHVEQUFjO0FBQzVFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQiw4REFBeUI7QUFDL0Usc0JBQXNCLHNEQUFhO0FBQ25DLFNBQVMsMERBQWlCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxFQUFFLDREQUF1QiwwQkFBMEIsNkRBQXdCO0FBQzlFLHNCQUFzQixzREFBYTtBQUNuQyxTQUFTLDBEQUFpQjtBQUMxQjtBQUNBO0FBQ0E7QUFDQTtBQUNBLEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLHVEQUFrQjtBQUN4RSxJQUFJLHFEQUFZLEVBQUUsdURBQWM7QUFDaEM7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQiwyREFBc0I7QUFDNUU7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLDREQUF1QjtBQUM3RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMLEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLHlEQUFvQjtBQUMxRSxzQkFBc0Isc0RBQWE7QUFDbkMsUUFBUSx5REFBZ0I7QUFDeEI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLFNBQVMsMERBQWlCO0FBQ2hDLDJCQUEyQix5REFBZ0I7QUFDM0MsNkJBQTZCLHlEQUFnQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQiwyREFBc0I7QUFDNUUsc0JBQXNCLHNEQUFhO0FBQ25DLFFBQVEseURBQWdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsTUFBTSxTQUFTLDBEQUFpQjtBQUNoQztBQUNBO0FBQ0E7QUFDQTtBQUNBLDJCQUEyQix5REFBZ0I7QUFDM0MsNkJBQTZCLHlEQUFnQjtBQUM3QztBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQiwyREFBc0I7QUFDNUUsc0JBQXNCLHNEQUFhO0FBQ25DLFFBQVEseURBQWdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsMERBQWlCO0FBQzFCO0FBQ0E7QUFDQSxRQUFRLDRGQUF3QztBQUNoRDtBQUNBO0FBQ0EsTUFBTSxrRUFBYztBQUNwQjtBQUNBO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQiw0REFBdUI7QUFDN0Usc0JBQXNCLHNEQUFhO0FBQ25DLFFBQVEseURBQWdCO0FBQ3hCO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVMsMERBQWlCO0FBQzFCO0FBQ0E7QUFDQTtBQUNBLFFBQVEsNEZBQXdDO0FBQ2hEO0FBQ0EsTUFBTSxrRUFBYztBQUNwQjtBQUNBO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQiwwREFBcUI7QUFDM0U7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLHNEQUFhO0FBQ25DLFFBQVEsMERBQWlCO0FBQ3pCO0FBQ0E7QUFDQSxRQUFRO0FBQ1I7QUFDQSw2REFBNkQsb0RBQVc7QUFDeEUsd0JBQXdCLHNGQUFzQztBQUM5RDtBQUNBO0FBQ0Esd0NBQXdDLDREQUF1QjtBQUMvRDtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxNQUFNLFVBQVUseURBQWdCO0FBQ2hDO0FBQ0E7QUFDQTtBQUNBLGtDQUFrQyw2REFBd0I7QUFDMUQsR0FBRyxFQUFFLDREQUF1QiwwQkFBMEIsdURBQWtCO0FBQ3hFO0FBQ0E7QUFDQTtBQUNBLHNCQUFzQixzREFBYTtBQUNuQyxVQUFVLDBEQUFpQixlQUFlLHlEQUFnQjtBQUMxRDtBQUNBO0FBQ0E7QUFDQSxrQ0FBa0MsNkRBQXdCO0FBQzFELEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLHNEQUFpQjtBQUN2RSxzQkFBc0Isc0RBQWE7QUFDbkMsU0FBUywwREFBaUI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsOERBQXlCO0FBQy9EO0FBQ0E7QUFDQSxrQ0FBa0MsNkRBQXdCO0FBQzFELEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLHVEQUFrQjtBQUN4RSxzQkFBc0Isc0RBQWE7QUFDbkMsU0FBUywwREFBaUI7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQixpREFBWTtBQUNsRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxVQUFVO0FBQ1YscUJBQXFCLG1FQUEwQjtBQUMvQztBQUNBLDRCQUE0Qiw4REFBcUI7QUFDakQsY0FBYyxvREFBVztBQUN6QjtBQUNBO0FBQ0EsWUFBWTtBQUNaO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzQ0FBc0MsMEVBQWlDO0FBQ3ZFLFVBQVUsc0RBQWE7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0JBQXNCLHNEQUFhO0FBQ25DLFFBQVEsMERBQWlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLHNEQUFpQjtBQUN2RTtBQUNBLHNCQUFzQixzREFBYTtBQUNuQywyQkFBMkIsMERBQWlCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBLEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLHFEQUFnQjtBQUN0RTtBQUNBLHNCQUFzQixzREFBYTtBQUNuQywyQkFBMkIsMERBQWlCO0FBQzVDO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLG1CQUFtQixtRUFBMEI7QUFDN0MsVUFBVSx5REFBZ0I7QUFDMUI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsR0FBRyxFQUFFLDREQUF1QiwwQkFBMEIsdURBQWtCO0FBQ3hFLElBQUksbURBQVU7QUFDZDtBQUNBLEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLGlEQUFZO0FBQ2xFLElBQUksbUVBQWUsU0FBUyxpRUFBaUI7QUFDN0M7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQixnREFBVztBQUNqRTtBQUNBO0FBQ0EsR0FBRyxFQUFFLDREQUF1QiwwQkFBMEIsa0RBQWE7QUFDbkU7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFFBQVEsa0RBQVMsa0JBQWtCLDRFQUFtQztBQUN0RTtBQUNBO0FBQ0Esc0JBQXNCLHNEQUFhO0FBQ25DO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCLDBCQUEwQixzREFBaUI7QUFDdkUsc0JBQXNCLHNEQUFhO0FBQ25DLFFBQVEsMERBQWlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBLEdBQUcsRUFBRSw0REFBdUIsMEJBQTBCLG9EQUFlO0FBQ3JFLHNCQUFzQixzREFBYTtBQUNuQyxRQUFRLDBEQUFpQjtBQUN6QjtBQUNBO0FBQ0E7QUFDQSxHQUFHLEVBQUUsNERBQXVCO0FBQzVCO0FBQ0E7O0FBRXFKIiwic291cmNlcyI6WyJDOlxccHJvamVjdHNcXGxtc1xcbm9kZV9tb2R1bGVzXFwucG5wbVxcQGxleGljYWwrcmljaC10ZXh0QDAuMjguMFxcbm9kZV9tb2R1bGVzXFxAbGV4aWNhbFxccmljaC10ZXh0XFxMZXhpY2FsUmljaFRleHQuZGV2Lm1qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIENvcHlyaWdodCAoYykgTWV0YSBQbGF0Zm9ybXMsIEluYy4gYW5kIGFmZmlsaWF0ZXMuXG4gKlxuICogVGhpcyBzb3VyY2UgY29kZSBpcyBsaWNlbnNlZCB1bmRlciB0aGUgTUlUIGxpY2Vuc2UgZm91bmQgaW4gdGhlXG4gKiBMSUNFTlNFIGZpbGUgaW4gdGhlIHJvb3QgZGlyZWN0b3J5IG9mIHRoaXMgc291cmNlIHRyZWUuXG4gKlxuICovXG5cbmltcG9ydCB7ICRpbnNlcnREYXRhVHJhbnNmZXJGb3JSaWNoVGV4dCwgY29weVRvQ2xpcGJvYXJkIH0gZnJvbSAnQGxleGljYWwvY2xpcGJvYXJkJztcbmltcG9ydCB7ICRzaG91bGRPdmVycmlkZURlZmF1bHRDaGFyYWN0ZXJTZWxlY3Rpb24sICRtb3ZlQ2hhcmFjdGVyIH0gZnJvbSAnQGxleGljYWwvc2VsZWN0aW9uJztcbmltcG9ydCB7IGFkZENsYXNzTmFtZXNUb0VsZW1lbnQsIGlzSFRNTEVsZW1lbnQsIG9iamVjdEtsYXNzRXF1YWxzLCBtZXJnZVJlZ2lzdGVyLCAkZmluZE1hdGNoaW5nUGFyZW50LCAkZ2V0TmVhcmVzdEJsb2NrRWxlbWVudEFuY2VzdG9yT3JUaHJvdyB9IGZyb20gJ0BsZXhpY2FsL3V0aWxzJztcbmltcG9ydCB7IGNyZWF0ZUNvbW1hbmQsIEVsZW1lbnROb2RlLCAkY3JlYXRlUGFyYWdyYXBoTm9kZSwgJGFwcGx5Tm9kZVJlcGxhY2VtZW50LCBzZXROb2RlSW5kZW50RnJvbURPTSwgQ0xJQ0tfQ09NTUFORCwgJGdldFNlbGVjdGlvbiwgJGlzTm9kZVNlbGVjdGlvbiwgREVMRVRFX0NIQVJBQ1RFUl9DT01NQU5ELCAkaXNSYW5nZVNlbGVjdGlvbiwgQ09NTUFORF9QUklPUklUWV9FRElUT1IsIERFTEVURV9XT1JEX0NPTU1BTkQsIERFTEVURV9MSU5FX0NPTU1BTkQsIENPTlRST0xMRURfVEVYVF9JTlNFUlRJT05fQ09NTUFORCwgUkVNT1ZFX1RFWFRfQ09NTUFORCwgRk9STUFUX1RFWFRfQ09NTUFORCwgRk9STUFUX0VMRU1FTlRfQ09NTUFORCwgJGlzRWxlbWVudE5vZGUsIElOU0VSVF9MSU5FX0JSRUFLX0NPTU1BTkQsIElOU0VSVF9QQVJBR1JBUEhfQ09NTUFORCwgSU5TRVJUX1RBQl9DT01NQU5ELCAkaW5zZXJ0Tm9kZXMsICRjcmVhdGVUYWJOb2RlLCBJTkRFTlRfQ09OVEVOVF9DT01NQU5ELCBPVVRERU5UX0NPTlRFTlRfQ09NTUFORCwgS0VZX0FSUk9XX1VQX0NPTU1BTkQsICRnZXRBZGphY2VudE5vZGUsICRpc0RlY29yYXRvck5vZGUsIEtFWV9BUlJPV19ET1dOX0NPTU1BTkQsIEtFWV9BUlJPV19MRUZUX0NPTU1BTkQsIEtFWV9BUlJPV19SSUdIVF9DT01NQU5ELCBLRVlfQkFDS1NQQUNFX0NPTU1BTkQsICRpc1Jvb3ROb2RlLCBLRVlfREVMRVRFX0NPTU1BTkQsIEtFWV9FTlRFUl9DT01NQU5ELCBLRVlfRVNDQVBFX0NPTU1BTkQsIERST1BfQ09NTUFORCwgJGdldE5lYXJlc3ROb2RlRnJvbURPTU5vZGUsICRjcmVhdGVSYW5nZVNlbGVjdGlvbiwgJGlzVGV4dE5vZGUsICRub3JtYWxpemVTZWxlY3Rpb25fX0VYUEVSSU1FTlRBTCwgJHNldFNlbGVjdGlvbiwgRFJBR1NUQVJUX0NPTU1BTkQsIERSQUdPVkVSX0NPTU1BTkQsIFNFTEVDVF9BTExfQ09NTUFORCwgJHNlbGVjdEFsbCwgQ09QWV9DT01NQU5ELCBDVVRfQ09NTUFORCwgUEFTVEVfQ09NTUFORCwgaXNET01Ob2RlLCBpc1NlbGVjdGlvbkNhcHR1cmVkSW5EZWNvcmF0b3JJbnB1dCwgS0VZX1NQQUNFX0NPTU1BTkQsIEtFWV9UQUJfQ09NTUFORCwgJGdldFJvb3QgfSBmcm9tICdsZXhpY2FsJztcblxuLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqL1xuXG5mdW5jdGlvbiBjYXJldEZyb21Qb2ludCh4LCB5KSB7XG4gIGlmICh0eXBlb2YgZG9jdW1lbnQuY2FyZXRSYW5nZUZyb21Qb2ludCAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICBjb25zdCByYW5nZSA9IGRvY3VtZW50LmNhcmV0UmFuZ2VGcm9tUG9pbnQoeCwgeSk7XG4gICAgaWYgKHJhbmdlID09PSBudWxsKSB7XG4gICAgICByZXR1cm4gbnVsbDtcbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIG5vZGU6IHJhbmdlLnN0YXJ0Q29udGFpbmVyLFxuICAgICAgb2Zmc2V0OiByYW5nZS5zdGFydE9mZnNldFxuICAgIH07XG4gICAgLy8gQHRzLWlnbm9yZVxuICB9IGVsc2UgaWYgKGRvY3VtZW50LmNhcmV0UG9zaXRpb25Gcm9tUG9pbnQgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgLy8gQHRzLWlnbm9yZSBGRiAtIG5vIHR5cGVzXG4gICAgY29uc3QgcmFuZ2UgPSBkb2N1bWVudC5jYXJldFBvc2l0aW9uRnJvbVBvaW50KHgsIHkpO1xuICAgIGlmIChyYW5nZSA9PT0gbnVsbCkge1xuICAgICAgcmV0dXJuIG51bGw7XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICBub2RlOiByYW5nZS5vZmZzZXROb2RlLFxuICAgICAgb2Zmc2V0OiByYW5nZS5vZmZzZXRcbiAgICB9O1xuICB9IGVsc2Uge1xuICAgIC8vIEdyYWNlZnVsbHkgaGFuZGxlIElFXG4gICAgcmV0dXJuIG51bGw7XG4gIH1cbn1cblxuLyoqXG4gKiBDb3B5cmlnaHQgKGMpIE1ldGEgUGxhdGZvcm1zLCBJbmMuIGFuZCBhZmZpbGlhdGVzLlxuICpcbiAqIFRoaXMgc291cmNlIGNvZGUgaXMgbGljZW5zZWQgdW5kZXIgdGhlIE1JVCBsaWNlbnNlIGZvdW5kIGluIHRoZVxuICogTElDRU5TRSBmaWxlIGluIHRoZSByb290IGRpcmVjdG9yeSBvZiB0aGlzIHNvdXJjZSB0cmVlLlxuICpcbiAqL1xuXG5jb25zdCBDQU5fVVNFX0RPTSA9IHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnICYmIHR5cGVvZiB3aW5kb3cuZG9jdW1lbnQgIT09ICd1bmRlZmluZWQnICYmIHR5cGVvZiB3aW5kb3cuZG9jdW1lbnQuY3JlYXRlRWxlbWVudCAhPT0gJ3VuZGVmaW5lZCc7XG5cbi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKi9cblxuY29uc3QgZG9jdW1lbnRNb2RlID0gQ0FOX1VTRV9ET00gJiYgJ2RvY3VtZW50TW9kZScgaW4gZG9jdW1lbnQgPyBkb2N1bWVudC5kb2N1bWVudE1vZGUgOiBudWxsO1xuY29uc3QgQ0FOX1VTRV9CRUZPUkVfSU5QVVQgPSBDQU5fVVNFX0RPTSAmJiAnSW5wdXRFdmVudCcgaW4gd2luZG93ICYmICFkb2N1bWVudE1vZGUgPyAnZ2V0VGFyZ2V0UmFuZ2VzJyBpbiBuZXcgd2luZG93LklucHV0RXZlbnQoJ2lucHV0JykgOiBmYWxzZTtcbmNvbnN0IElTX1NBRkFSSSA9IENBTl9VU0VfRE9NICYmIC9WZXJzaW9uXFwvW1xcZC5dKy4qU2FmYXJpLy50ZXN0KG5hdmlnYXRvci51c2VyQWdlbnQpO1xuY29uc3QgSVNfSU9TID0gQ0FOX1VTRV9ET00gJiYgL2lQYWR8aVBob25lfGlQb2QvLnRlc3QobmF2aWdhdG9yLnVzZXJBZ2VudCkgJiYgIXdpbmRvdy5NU1N0cmVhbTtcblxuLy8gS2VlcCB0aGVzZSBpbiBjYXNlIHdlIG5lZWQgdG8gdXNlIHRoZW0gaW4gdGhlIGZ1dHVyZS5cbi8vIGV4cG9ydCBjb25zdCBJU19XSU5ET1dTOiBib29sZWFuID0gQ0FOX1VTRV9ET00gJiYgL1dpbi8udGVzdChuYXZpZ2F0b3IucGxhdGZvcm0pO1xuY29uc3QgSVNfQ0hST01FID0gQ0FOX1VTRV9ET00gJiYgL14oPz0uKkNocm9tZSkuKi9pLnRlc3QobmF2aWdhdG9yLnVzZXJBZ2VudCk7XG5jb25zdCBJU19BUFBMRV9XRUJLSVQgPSBDQU5fVVNFX0RPTSAmJiAvQXBwbGVXZWJLaXRcXC9bXFxkLl0rLy50ZXN0KG5hdmlnYXRvci51c2VyQWdlbnQpICYmICFJU19DSFJPTUU7XG5cbi8qKlxuICogQ29weXJpZ2h0IChjKSBNZXRhIFBsYXRmb3JtcywgSW5jLiBhbmQgYWZmaWxpYXRlcy5cbiAqXG4gKiBUaGlzIHNvdXJjZSBjb2RlIGlzIGxpY2Vuc2VkIHVuZGVyIHRoZSBNSVQgbGljZW5zZSBmb3VuZCBpbiB0aGVcbiAqIExJQ0VOU0UgZmlsZSBpbiB0aGUgcm9vdCBkaXJlY3Rvcnkgb2YgdGhpcyBzb3VyY2UgdHJlZS5cbiAqXG4gKi9cblxuY29uc3QgRFJBR19EUk9QX1BBU1RFID0gY3JlYXRlQ29tbWFuZCgnRFJBR19EUk9QX1BBU1RFX0ZJTEUnKTtcbi8qKiBAbm9Jbmhlcml0RG9jICovXG5jbGFzcyBRdW90ZU5vZGUgZXh0ZW5kcyBFbGVtZW50Tm9kZSB7XG4gIHN0YXRpYyBnZXRUeXBlKCkge1xuICAgIHJldHVybiAncXVvdGUnO1xuICB9XG4gIHN0YXRpYyBjbG9uZShub2RlKSB7XG4gICAgcmV0dXJuIG5ldyBRdW90ZU5vZGUobm9kZS5fX2tleSk7XG4gIH1cblxuICAvLyBWaWV3XG5cbiAgY3JlYXRlRE9NKGNvbmZpZykge1xuICAgIGNvbnN0IGVsZW1lbnQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KCdibG9ja3F1b3RlJyk7XG4gICAgYWRkQ2xhc3NOYW1lc1RvRWxlbWVudChlbGVtZW50LCBjb25maWcudGhlbWUucXVvdGUpO1xuICAgIHJldHVybiBlbGVtZW50O1xuICB9XG4gIHVwZGF0ZURPTShwcmV2Tm9kZSwgZG9tKSB7XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9XG4gIHN0YXRpYyBpbXBvcnRET00oKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGJsb2NrcXVvdGU6IG5vZGUgPT4gKHtcbiAgICAgICAgY29udmVyc2lvbjogJGNvbnZlcnRCbG9ja3F1b3RlRWxlbWVudCxcbiAgICAgICAgcHJpb3JpdHk6IDBcbiAgICAgIH0pXG4gICAgfTtcbiAgfVxuICBleHBvcnRET00oZWRpdG9yKSB7XG4gICAgY29uc3Qge1xuICAgICAgZWxlbWVudFxuICAgIH0gPSBzdXBlci5leHBvcnRET00oZWRpdG9yKTtcbiAgICBpZiAoaXNIVE1MRWxlbWVudChlbGVtZW50KSkge1xuICAgICAgaWYgKHRoaXMuaXNFbXB0eSgpKSB7XG4gICAgICAgIGVsZW1lbnQuYXBwZW5kKGRvY3VtZW50LmNyZWF0ZUVsZW1lbnQoJ2JyJykpO1xuICAgICAgfVxuICAgICAgY29uc3QgZm9ybWF0VHlwZSA9IHRoaXMuZ2V0Rm9ybWF0VHlwZSgpO1xuICAgICAgZWxlbWVudC5zdHlsZS50ZXh0QWxpZ24gPSBmb3JtYXRUeXBlO1xuICAgICAgY29uc3QgZGlyZWN0aW9uID0gdGhpcy5nZXREaXJlY3Rpb24oKTtcbiAgICAgIGlmIChkaXJlY3Rpb24pIHtcbiAgICAgICAgZWxlbWVudC5kaXIgPSBkaXJlY3Rpb247XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB7XG4gICAgICBlbGVtZW50XG4gICAgfTtcbiAgfVxuICBzdGF0aWMgaW1wb3J0SlNPTihzZXJpYWxpemVkTm9kZSkge1xuICAgIHJldHVybiAkY3JlYXRlUXVvdGVOb2RlKCkudXBkYXRlRnJvbUpTT04oc2VyaWFsaXplZE5vZGUpO1xuICB9XG5cbiAgLy8gTXV0YXRpb25cblxuICBpbnNlcnROZXdBZnRlcihfLCByZXN0b3JlU2VsZWN0aW9uKSB7XG4gICAgY29uc3QgbmV3QmxvY2sgPSAkY3JlYXRlUGFyYWdyYXBoTm9kZSgpO1xuICAgIGNvbnN0IGRpcmVjdGlvbiA9IHRoaXMuZ2V0RGlyZWN0aW9uKCk7XG4gICAgbmV3QmxvY2suc2V0RGlyZWN0aW9uKGRpcmVjdGlvbik7XG4gICAgdGhpcy5pbnNlcnRBZnRlcihuZXdCbG9jaywgcmVzdG9yZVNlbGVjdGlvbik7XG4gICAgcmV0dXJuIG5ld0Jsb2NrO1xuICB9XG4gIGNvbGxhcHNlQXRTdGFydCgpIHtcbiAgICBjb25zdCBwYXJhZ3JhcGggPSAkY3JlYXRlUGFyYWdyYXBoTm9kZSgpO1xuICAgIGNvbnN0IGNoaWxkcmVuID0gdGhpcy5nZXRDaGlsZHJlbigpO1xuICAgIGNoaWxkcmVuLmZvckVhY2goY2hpbGQgPT4gcGFyYWdyYXBoLmFwcGVuZChjaGlsZCkpO1xuICAgIHRoaXMucmVwbGFjZShwYXJhZ3JhcGgpO1xuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIGNhbk1lcmdlV2hlbkVtcHR5KCkge1xuICAgIHJldHVybiB0cnVlO1xuICB9XG59XG5mdW5jdGlvbiAkY3JlYXRlUXVvdGVOb2RlKCkge1xuICByZXR1cm4gJGFwcGx5Tm9kZVJlcGxhY2VtZW50KG5ldyBRdW90ZU5vZGUoKSk7XG59XG5mdW5jdGlvbiAkaXNRdW90ZU5vZGUobm9kZSkge1xuICByZXR1cm4gbm9kZSBpbnN0YW5jZW9mIFF1b3RlTm9kZTtcbn1cbi8qKiBAbm9Jbmhlcml0RG9jICovXG5jbGFzcyBIZWFkaW5nTm9kZSBleHRlbmRzIEVsZW1lbnROb2RlIHtcbiAgLyoqIEBpbnRlcm5hbCAqL1xuXG4gIHN0YXRpYyBnZXRUeXBlKCkge1xuICAgIHJldHVybiAnaGVhZGluZyc7XG4gIH1cbiAgc3RhdGljIGNsb25lKG5vZGUpIHtcbiAgICByZXR1cm4gbmV3IEhlYWRpbmdOb2RlKG5vZGUuX190YWcsIG5vZGUuX19rZXkpO1xuICB9XG4gIGNvbnN0cnVjdG9yKHRhZywga2V5KSB7XG4gICAgc3VwZXIoa2V5KTtcbiAgICB0aGlzLl9fdGFnID0gdGFnO1xuICB9XG4gIGdldFRhZygpIHtcbiAgICByZXR1cm4gdGhpcy5fX3RhZztcbiAgfVxuICBzZXRUYWcodGFnKSB7XG4gICAgY29uc3Qgc2VsZiA9IHRoaXMuZ2V0V3JpdGFibGUoKTtcbiAgICB0aGlzLl9fdGFnID0gdGFnO1xuICAgIHJldHVybiBzZWxmO1xuICB9XG5cbiAgLy8gVmlld1xuXG4gIGNyZWF0ZURPTShjb25maWcpIHtcbiAgICBjb25zdCB0YWcgPSB0aGlzLl9fdGFnO1xuICAgIGNvbnN0IGVsZW1lbnQgPSBkb2N1bWVudC5jcmVhdGVFbGVtZW50KHRhZyk7XG4gICAgY29uc3QgdGhlbWUgPSBjb25maWcudGhlbWU7XG4gICAgY29uc3QgY2xhc3NOYW1lcyA9IHRoZW1lLmhlYWRpbmc7XG4gICAgaWYgKGNsYXNzTmFtZXMgIT09IHVuZGVmaW5lZCkge1xuICAgICAgY29uc3QgY2xhc3NOYW1lID0gY2xhc3NOYW1lc1t0YWddO1xuICAgICAgYWRkQ2xhc3NOYW1lc1RvRWxlbWVudChlbGVtZW50LCBjbGFzc05hbWUpO1xuICAgIH1cbiAgICByZXR1cm4gZWxlbWVudDtcbiAgfVxuICB1cGRhdGVET00ocHJldk5vZGUsIGRvbSwgY29uZmlnKSB7XG4gICAgcmV0dXJuIHByZXZOb2RlLl9fdGFnICE9PSB0aGlzLl9fdGFnO1xuICB9XG4gIHN0YXRpYyBpbXBvcnRET00oKSB7XG4gICAgcmV0dXJuIHtcbiAgICAgIGgxOiBub2RlID0+ICh7XG4gICAgICAgIGNvbnZlcnNpb246ICRjb252ZXJ0SGVhZGluZ0VsZW1lbnQsXG4gICAgICAgIHByaW9yaXR5OiAwXG4gICAgICB9KSxcbiAgICAgIGgyOiBub2RlID0+ICh7XG4gICAgICAgIGNvbnZlcnNpb246ICRjb252ZXJ0SGVhZGluZ0VsZW1lbnQsXG4gICAgICAgIHByaW9yaXR5OiAwXG4gICAgICB9KSxcbiAgICAgIGgzOiBub2RlID0+ICh7XG4gICAgICAgIGNvbnZlcnNpb246ICRjb252ZXJ0SGVhZGluZ0VsZW1lbnQsXG4gICAgICAgIHByaW9yaXR5OiAwXG4gICAgICB9KSxcbiAgICAgIGg0OiBub2RlID0+ICh7XG4gICAgICAgIGNvbnZlcnNpb246ICRjb252ZXJ0SGVhZGluZ0VsZW1lbnQsXG4gICAgICAgIHByaW9yaXR5OiAwXG4gICAgICB9KSxcbiAgICAgIGg1OiBub2RlID0+ICh7XG4gICAgICAgIGNvbnZlcnNpb246ICRjb252ZXJ0SGVhZGluZ0VsZW1lbnQsXG4gICAgICAgIHByaW9yaXR5OiAwXG4gICAgICB9KSxcbiAgICAgIGg2OiBub2RlID0+ICh7XG4gICAgICAgIGNvbnZlcnNpb246ICRjb252ZXJ0SGVhZGluZ0VsZW1lbnQsXG4gICAgICAgIHByaW9yaXR5OiAwXG4gICAgICB9KSxcbiAgICAgIHA6IG5vZGUgPT4ge1xuICAgICAgICAvLyBkb21Ob2RlIGlzIGEgPHA+IHNpbmNlIHdlIG1hdGNoZWQgaXQgYnkgbm9kZU5hbWVcbiAgICAgICAgY29uc3QgcGFyYWdyYXBoID0gbm9kZTtcbiAgICAgICAgY29uc3QgZmlyc3RDaGlsZCA9IHBhcmFncmFwaC5maXJzdENoaWxkO1xuICAgICAgICBpZiAoZmlyc3RDaGlsZCAhPT0gbnVsbCAmJiBpc0dvb2dsZURvY3NUaXRsZShmaXJzdENoaWxkKSkge1xuICAgICAgICAgIHJldHVybiB7XG4gICAgICAgICAgICBjb252ZXJzaW9uOiAoKSA9PiAoe1xuICAgICAgICAgICAgICBub2RlOiBudWxsXG4gICAgICAgICAgICB9KSxcbiAgICAgICAgICAgIHByaW9yaXR5OiAzXG4gICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH0sXG4gICAgICBzcGFuOiBub2RlID0+IHtcbiAgICAgICAgaWYgKGlzR29vZ2xlRG9jc1RpdGxlKG5vZGUpKSB7XG4gICAgICAgICAgcmV0dXJuIHtcbiAgICAgICAgICAgIGNvbnZlcnNpb246IGRvbU5vZGUgPT4ge1xuICAgICAgICAgICAgICByZXR1cm4ge1xuICAgICAgICAgICAgICAgIG5vZGU6ICRjcmVhdGVIZWFkaW5nTm9kZSgnaDEnKVxuICAgICAgICAgICAgICB9O1xuICAgICAgICAgICAgfSxcbiAgICAgICAgICAgIHByaW9yaXR5OiAzXG4gICAgICAgICAgfTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gbnVsbDtcbiAgICAgIH1cbiAgICB9O1xuICB9XG4gIGV4cG9ydERPTShlZGl0b3IpIHtcbiAgICBjb25zdCB7XG4gICAgICBlbGVtZW50XG4gICAgfSA9IHN1cGVyLmV4cG9ydERPTShlZGl0b3IpO1xuICAgIGlmIChpc0hUTUxFbGVtZW50KGVsZW1lbnQpKSB7XG4gICAgICBpZiAodGhpcy5pc0VtcHR5KCkpIHtcbiAgICAgICAgZWxlbWVudC5hcHBlbmQoZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgnYnInKSk7XG4gICAgICB9XG4gICAgICBjb25zdCBmb3JtYXRUeXBlID0gdGhpcy5nZXRGb3JtYXRUeXBlKCk7XG4gICAgICBlbGVtZW50LnN0eWxlLnRleHRBbGlnbiA9IGZvcm1hdFR5cGU7XG4gICAgICBjb25zdCBkaXJlY3Rpb24gPSB0aGlzLmdldERpcmVjdGlvbigpO1xuICAgICAgaWYgKGRpcmVjdGlvbikge1xuICAgICAgICBlbGVtZW50LmRpciA9IGRpcmVjdGlvbjtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIHtcbiAgICAgIGVsZW1lbnRcbiAgICB9O1xuICB9XG4gIHN0YXRpYyBpbXBvcnRKU09OKHNlcmlhbGl6ZWROb2RlKSB7XG4gICAgcmV0dXJuICRjcmVhdGVIZWFkaW5nTm9kZShzZXJpYWxpemVkTm9kZS50YWcpLnVwZGF0ZUZyb21KU09OKHNlcmlhbGl6ZWROb2RlKTtcbiAgfVxuICB1cGRhdGVGcm9tSlNPTihzZXJpYWxpemVkTm9kZSkge1xuICAgIHJldHVybiBzdXBlci51cGRhdGVGcm9tSlNPTihzZXJpYWxpemVkTm9kZSkuc2V0VGFnKHNlcmlhbGl6ZWROb2RlLnRhZyk7XG4gIH1cbiAgZXhwb3J0SlNPTigpIHtcbiAgICByZXR1cm4ge1xuICAgICAgLi4uc3VwZXIuZXhwb3J0SlNPTigpLFxuICAgICAgdGFnOiB0aGlzLmdldFRhZygpXG4gICAgfTtcbiAgfVxuXG4gIC8vIE11dGF0aW9uXG4gIGluc2VydE5ld0FmdGVyKHNlbGVjdGlvbiwgcmVzdG9yZVNlbGVjdGlvbiA9IHRydWUpIHtcbiAgICBjb25zdCBhbmNob3JPZmZldCA9IHNlbGVjdGlvbiA/IHNlbGVjdGlvbi5hbmNob3Iub2Zmc2V0IDogMDtcbiAgICBjb25zdCBsYXN0RGVzYyA9IHRoaXMuZ2V0TGFzdERlc2NlbmRhbnQoKTtcbiAgICBjb25zdCBpc0F0RW5kID0gIWxhc3REZXNjIHx8IHNlbGVjdGlvbiAmJiBzZWxlY3Rpb24uYW5jaG9yLmtleSA9PT0gbGFzdERlc2MuZ2V0S2V5KCkgJiYgYW5jaG9yT2ZmZXQgPT09IGxhc3REZXNjLmdldFRleHRDb250ZW50U2l6ZSgpO1xuICAgIGNvbnN0IG5ld0VsZW1lbnQgPSBpc0F0RW5kIHx8ICFzZWxlY3Rpb24gPyAkY3JlYXRlUGFyYWdyYXBoTm9kZSgpIDogJGNyZWF0ZUhlYWRpbmdOb2RlKHRoaXMuZ2V0VGFnKCkpO1xuICAgIGNvbnN0IGRpcmVjdGlvbiA9IHRoaXMuZ2V0RGlyZWN0aW9uKCk7XG4gICAgbmV3RWxlbWVudC5zZXREaXJlY3Rpb24oZGlyZWN0aW9uKTtcbiAgICB0aGlzLmluc2VydEFmdGVyKG5ld0VsZW1lbnQsIHJlc3RvcmVTZWxlY3Rpb24pO1xuICAgIGlmIChhbmNob3JPZmZldCA9PT0gMCAmJiAhdGhpcy5pc0VtcHR5KCkgJiYgc2VsZWN0aW9uKSB7XG4gICAgICBjb25zdCBwYXJhZ3JhcGggPSAkY3JlYXRlUGFyYWdyYXBoTm9kZSgpO1xuICAgICAgcGFyYWdyYXBoLnNlbGVjdCgpO1xuICAgICAgdGhpcy5yZXBsYWNlKHBhcmFncmFwaCwgdHJ1ZSk7XG4gICAgfVxuICAgIHJldHVybiBuZXdFbGVtZW50O1xuICB9XG4gIGNvbGxhcHNlQXRTdGFydCgpIHtcbiAgICBjb25zdCBuZXdFbGVtZW50ID0gIXRoaXMuaXNFbXB0eSgpID8gJGNyZWF0ZUhlYWRpbmdOb2RlKHRoaXMuZ2V0VGFnKCkpIDogJGNyZWF0ZVBhcmFncmFwaE5vZGUoKTtcbiAgICBjb25zdCBjaGlsZHJlbiA9IHRoaXMuZ2V0Q2hpbGRyZW4oKTtcbiAgICBjaGlsZHJlbi5mb3JFYWNoKGNoaWxkID0+IG5ld0VsZW1lbnQuYXBwZW5kKGNoaWxkKSk7XG4gICAgdGhpcy5yZXBsYWNlKG5ld0VsZW1lbnQpO1xuICAgIHJldHVybiB0cnVlO1xuICB9XG4gIGV4dHJhY3RXaXRoQ2hpbGQoKSB7XG4gICAgcmV0dXJuIHRydWU7XG4gIH1cbn1cbmZ1bmN0aW9uIGlzR29vZ2xlRG9jc1RpdGxlKGRvbU5vZGUpIHtcbiAgaWYgKGRvbU5vZGUubm9kZU5hbWUudG9Mb3dlckNhc2UoKSA9PT0gJ3NwYW4nKSB7XG4gICAgcmV0dXJuIGRvbU5vZGUuc3R5bGUuZm9udFNpemUgPT09ICcyNnB0JztcbiAgfVxuICByZXR1cm4gZmFsc2U7XG59XG5mdW5jdGlvbiAkY29udmVydEhlYWRpbmdFbGVtZW50KGVsZW1lbnQpIHtcbiAgY29uc3Qgbm9kZU5hbWUgPSBlbGVtZW50Lm5vZGVOYW1lLnRvTG93ZXJDYXNlKCk7XG4gIGxldCBub2RlID0gbnVsbDtcbiAgaWYgKG5vZGVOYW1lID09PSAnaDEnIHx8IG5vZGVOYW1lID09PSAnaDInIHx8IG5vZGVOYW1lID09PSAnaDMnIHx8IG5vZGVOYW1lID09PSAnaDQnIHx8IG5vZGVOYW1lID09PSAnaDUnIHx8IG5vZGVOYW1lID09PSAnaDYnKSB7XG4gICAgbm9kZSA9ICRjcmVhdGVIZWFkaW5nTm9kZShub2RlTmFtZSk7XG4gICAgaWYgKGVsZW1lbnQuc3R5bGUgIT09IG51bGwpIHtcbiAgICAgIHNldE5vZGVJbmRlbnRGcm9tRE9NKGVsZW1lbnQsIG5vZGUpO1xuICAgICAgbm9kZS5zZXRGb3JtYXQoZWxlbWVudC5zdHlsZS50ZXh0QWxpZ24pO1xuICAgIH1cbiAgfVxuICByZXR1cm4ge1xuICAgIG5vZGVcbiAgfTtcbn1cbmZ1bmN0aW9uICRjb252ZXJ0QmxvY2txdW90ZUVsZW1lbnQoZWxlbWVudCkge1xuICBjb25zdCBub2RlID0gJGNyZWF0ZVF1b3RlTm9kZSgpO1xuICBpZiAoZWxlbWVudC5zdHlsZSAhPT0gbnVsbCkge1xuICAgIG5vZGUuc2V0Rm9ybWF0KGVsZW1lbnQuc3R5bGUudGV4dEFsaWduKTtcbiAgICBzZXROb2RlSW5kZW50RnJvbURPTShlbGVtZW50LCBub2RlKTtcbiAgfVxuICByZXR1cm4ge1xuICAgIG5vZGVcbiAgfTtcbn1cbmZ1bmN0aW9uICRjcmVhdGVIZWFkaW5nTm9kZShoZWFkaW5nVGFnID0gJ2gxJykge1xuICByZXR1cm4gJGFwcGx5Tm9kZVJlcGxhY2VtZW50KG5ldyBIZWFkaW5nTm9kZShoZWFkaW5nVGFnKSk7XG59XG5mdW5jdGlvbiAkaXNIZWFkaW5nTm9kZShub2RlKSB7XG4gIHJldHVybiBub2RlIGluc3RhbmNlb2YgSGVhZGluZ05vZGU7XG59XG5mdW5jdGlvbiBvblBhc3RlRm9yUmljaFRleHQoZXZlbnQsIGVkaXRvcikge1xuICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICBlZGl0b3IudXBkYXRlKCgpID0+IHtcbiAgICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gICAgY29uc3QgY2xpcGJvYXJkRGF0YSA9IG9iamVjdEtsYXNzRXF1YWxzKGV2ZW50LCBJbnB1dEV2ZW50KSB8fCBvYmplY3RLbGFzc0VxdWFscyhldmVudCwgS2V5Ym9hcmRFdmVudCkgPyBudWxsIDogZXZlbnQuY2xpcGJvYXJkRGF0YTtcbiAgICBpZiAoY2xpcGJvYXJkRGF0YSAhPSBudWxsICYmIHNlbGVjdGlvbiAhPT0gbnVsbCkge1xuICAgICAgJGluc2VydERhdGFUcmFuc2ZlckZvclJpY2hUZXh0KGNsaXBib2FyZERhdGEsIHNlbGVjdGlvbiwgZWRpdG9yKTtcbiAgICB9XG4gIH0sIHtcbiAgICB0YWc6ICdwYXN0ZSdcbiAgfSk7XG59XG5hc3luYyBmdW5jdGlvbiBvbkN1dEZvclJpY2hUZXh0KGV2ZW50LCBlZGl0b3IpIHtcbiAgYXdhaXQgY29weVRvQ2xpcGJvYXJkKGVkaXRvciwgb2JqZWN0S2xhc3NFcXVhbHMoZXZlbnQsIENsaXBib2FyZEV2ZW50KSA/IGV2ZW50IDogbnVsbCk7XG4gIGVkaXRvci51cGRhdGUoKCkgPT4ge1xuICAgIGNvbnN0IHNlbGVjdGlvbiA9ICRnZXRTZWxlY3Rpb24oKTtcbiAgICBpZiAoJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgc2VsZWN0aW9uLnJlbW92ZVRleHQoKTtcbiAgICB9IGVsc2UgaWYgKCRpc05vZGVTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgc2VsZWN0aW9uLmdldE5vZGVzKCkuZm9yRWFjaChub2RlID0+IG5vZGUucmVtb3ZlKCkpO1xuICAgIH1cbiAgfSk7XG59XG5cbi8vIENsaXBib2FyZCBtYXkgY29udGFpbiBmaWxlcyB0aGF0IHdlIGFyZW4ndCBhbGxvd2VkIHRvIHJlYWQuIFdoaWxlIHRoZSBldmVudCBpcyBhcmd1YWJseSB1c2VsZXNzLFxuLy8gaW4gY2VydGFpbiBvY2Nhc2lvbnMsIHdlIHdhbnQgdG8ga25vdyB3aGV0aGVyIGl0IHdhcyBhIGZpbGUgdHJhbnNmZXIsIGFzIG9wcG9zZWQgdG8gdGV4dC4gV2Vcbi8vIGNvbnRyb2wgdGhpcyB3aXRoIHRoZSBmaXJzdCBib29sZWFuIGZsYWcuXG5mdW5jdGlvbiBldmVudEZpbGVzKGV2ZW50KSB7XG4gIGxldCBkYXRhVHJhbnNmZXIgPSBudWxsO1xuICBpZiAob2JqZWN0S2xhc3NFcXVhbHMoZXZlbnQsIERyYWdFdmVudCkpIHtcbiAgICBkYXRhVHJhbnNmZXIgPSBldmVudC5kYXRhVHJhbnNmZXI7XG4gIH0gZWxzZSBpZiAob2JqZWN0S2xhc3NFcXVhbHMoZXZlbnQsIENsaXBib2FyZEV2ZW50KSkge1xuICAgIGRhdGFUcmFuc2ZlciA9IGV2ZW50LmNsaXBib2FyZERhdGE7XG4gIH1cbiAgaWYgKGRhdGFUcmFuc2ZlciA9PT0gbnVsbCkge1xuICAgIHJldHVybiBbZmFsc2UsIFtdLCBmYWxzZV07XG4gIH1cbiAgY29uc3QgdHlwZXMgPSBkYXRhVHJhbnNmZXIudHlwZXM7XG4gIGNvbnN0IGhhc0ZpbGVzID0gdHlwZXMuaW5jbHVkZXMoJ0ZpbGVzJyk7XG4gIGNvbnN0IGhhc0NvbnRlbnQgPSB0eXBlcy5pbmNsdWRlcygndGV4dC9odG1sJykgfHwgdHlwZXMuaW5jbHVkZXMoJ3RleHQvcGxhaW4nKTtcbiAgcmV0dXJuIFtoYXNGaWxlcywgQXJyYXkuZnJvbShkYXRhVHJhbnNmZXIuZmlsZXMpLCBoYXNDb250ZW50XTtcbn1cbmZ1bmN0aW9uICRoYW5kbGVJbmRlbnRBbmRPdXRkZW50KGluZGVudE9yT3V0ZGVudCkge1xuICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gIGlmICghJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgIHJldHVybiBmYWxzZTtcbiAgfVxuICBjb25zdCBhbHJlYWR5SGFuZGxlZCA9IG5ldyBTZXQoKTtcbiAgY29uc3Qgbm9kZXMgPSBzZWxlY3Rpb24uZ2V0Tm9kZXMoKTtcbiAgZm9yIChsZXQgaSA9IDA7IGkgPCBub2Rlcy5sZW5ndGg7IGkrKykge1xuICAgIGNvbnN0IG5vZGUgPSBub2Rlc1tpXTtcbiAgICBjb25zdCBrZXkgPSBub2RlLmdldEtleSgpO1xuICAgIGlmIChhbHJlYWR5SGFuZGxlZC5oYXMoa2V5KSkge1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGNvbnN0IHBhcmVudEJsb2NrID0gJGZpbmRNYXRjaGluZ1BhcmVudChub2RlLCBwYXJlbnROb2RlID0+ICRpc0VsZW1lbnROb2RlKHBhcmVudE5vZGUpICYmICFwYXJlbnROb2RlLmlzSW5saW5lKCkpO1xuICAgIGlmIChwYXJlbnRCbG9jayA9PT0gbnVsbCkge1xuICAgICAgY29udGludWU7XG4gICAgfVxuICAgIGNvbnN0IHBhcmVudEtleSA9IHBhcmVudEJsb2NrLmdldEtleSgpO1xuICAgIGlmIChwYXJlbnRCbG9jay5jYW5JbmRlbnQoKSAmJiAhYWxyZWFkeUhhbmRsZWQuaGFzKHBhcmVudEtleSkpIHtcbiAgICAgIGFscmVhZHlIYW5kbGVkLmFkZChwYXJlbnRLZXkpO1xuICAgICAgaW5kZW50T3JPdXRkZW50KHBhcmVudEJsb2NrKTtcbiAgICB9XG4gIH1cbiAgcmV0dXJuIGFscmVhZHlIYW5kbGVkLnNpemUgPiAwO1xufVxuZnVuY3Rpb24gJGlzVGFyZ2V0V2l0aGluRGVjb3JhdG9yKHRhcmdldCkge1xuICBjb25zdCBub2RlID0gJGdldE5lYXJlc3ROb2RlRnJvbURPTU5vZGUodGFyZ2V0KTtcbiAgcmV0dXJuICRpc0RlY29yYXRvck5vZGUobm9kZSk7XG59XG5mdW5jdGlvbiAkaXNTZWxlY3Rpb25BdEVuZE9mUm9vdChzZWxlY3Rpb24pIHtcbiAgY29uc3QgZm9jdXMgPSBzZWxlY3Rpb24uZm9jdXM7XG4gIHJldHVybiBmb2N1cy5rZXkgPT09ICdyb290JyAmJiBmb2N1cy5vZmZzZXQgPT09ICRnZXRSb290KCkuZ2V0Q2hpbGRyZW5TaXplKCk7XG59XG5cbi8qKlxuICogUmVzZXRzIHRoZSBjYXBpdGFsaXphdGlvbiBvZiB0aGUgc2VsZWN0aW9uIHRvIGRlZmF1bHQuXG4gKiBDYWxsZWQgd2hlbiB0aGUgdXNlciBwcmVzc2VzIHNwYWNlLCB0YWIsIG9yIGVudGVyIGtleS5cbiAqIEBwYXJhbSBzZWxlY3Rpb24gVGhlIHNlbGVjdGlvbiB0byByZXNldCB0aGUgY2FwaXRhbGl6YXRpb24gb2YuXG4gKi9cbmZ1bmN0aW9uICRyZXNldENhcGl0YWxpemF0aW9uKHNlbGVjdGlvbikge1xuICBmb3IgKGNvbnN0IGZvcm1hdCBvZiBbJ2xvd2VyY2FzZScsICd1cHBlcmNhc2UnLCAnY2FwaXRhbGl6ZSddKSB7XG4gICAgaWYgKHNlbGVjdGlvbi5oYXNGb3JtYXQoZm9ybWF0KSkge1xuICAgICAgc2VsZWN0aW9uLnRvZ2dsZUZvcm1hdChmb3JtYXQpO1xuICAgIH1cbiAgfVxufVxuZnVuY3Rpb24gcmVnaXN0ZXJSaWNoVGV4dChlZGl0b3IpIHtcbiAgY29uc3QgcmVtb3ZlTGlzdGVuZXIgPSBtZXJnZVJlZ2lzdGVyKGVkaXRvci5yZWdpc3RlckNvbW1hbmQoQ0xJQ0tfQ09NTUFORCwgcGF5bG9hZCA9PiB7XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICgkaXNOb2RlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHNlbGVjdGlvbi5jbGVhcigpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfSwgMCksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoREVMRVRFX0NIQVJBQ1RFUl9DT01NQU5ELCBpc0JhY2t3YXJkID0+IHtcbiAgICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gICAgaWYgKCRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHNlbGVjdGlvbi5kZWxldGVDaGFyYWN0ZXIoaXNCYWNrd2FyZCk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9IGVsc2UgaWYgKCRpc05vZGVTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgc2VsZWN0aW9uLmRlbGV0ZU5vZGVzKCk7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9LCBDT01NQU5EX1BSSU9SSVRZX0VESVRPUiksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoREVMRVRFX1dPUkRfQ09NTUFORCwgaXNCYWNrd2FyZCA9PiB7XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICghJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBzZWxlY3Rpb24uZGVsZXRlV29yZChpc0JhY2t3YXJkKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKERFTEVURV9MSU5FX0NPTU1BTkQsIGlzQmFja3dhcmQgPT4ge1xuICAgIGNvbnN0IHNlbGVjdGlvbiA9ICRnZXRTZWxlY3Rpb24oKTtcbiAgICBpZiAoISRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgc2VsZWN0aW9uLmRlbGV0ZUxpbmUoaXNCYWNrd2FyZCk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChDT05UUk9MTEVEX1RFWFRfSU5TRVJUSU9OX0NPTU1BTkQsIGV2ZW50T3JUZXh0ID0+IHtcbiAgICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gICAgaWYgKHR5cGVvZiBldmVudE9yVGV4dCA9PT0gJ3N0cmluZycpIHtcbiAgICAgIGlmIChzZWxlY3Rpb24gIT09IG51bGwpIHtcbiAgICAgICAgc2VsZWN0aW9uLmluc2VydFRleHQoZXZlbnRPclRleHQpO1xuICAgICAgfVxuICAgIH0gZWxzZSB7XG4gICAgICBpZiAoc2VsZWN0aW9uID09PSBudWxsKSB7XG4gICAgICAgIHJldHVybiBmYWxzZTtcbiAgICAgIH1cbiAgICAgIGNvbnN0IGRhdGFUcmFuc2ZlciA9IGV2ZW50T3JUZXh0LmRhdGFUcmFuc2ZlcjtcbiAgICAgIGlmIChkYXRhVHJhbnNmZXIgIT0gbnVsbCkge1xuICAgICAgICAkaW5zZXJ0RGF0YVRyYW5zZmVyRm9yUmljaFRleHQoZGF0YVRyYW5zZmVyLCBzZWxlY3Rpb24sIGVkaXRvcik7XG4gICAgICB9IGVsc2UgaWYgKCRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgICAgY29uc3QgZGF0YSA9IGV2ZW50T3JUZXh0LmRhdGE7XG4gICAgICAgIGlmIChkYXRhKSB7XG4gICAgICAgICAgc2VsZWN0aW9uLmluc2VydFRleHQoZGF0YSk7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9LCBDT01NQU5EX1BSSU9SSVRZX0VESVRPUiksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoUkVNT1ZFX1RFWFRfQ09NTUFORCwgKCkgPT4ge1xuICAgIGNvbnN0IHNlbGVjdGlvbiA9ICRnZXRTZWxlY3Rpb24oKTtcbiAgICBpZiAoISRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgc2VsZWN0aW9uLnJlbW92ZVRleHQoKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKEZPUk1BVF9URVhUX0NPTU1BTkQsIGZvcm1hdCA9PiB7XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICghJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBzZWxlY3Rpb24uZm9ybWF0VGV4dChmb3JtYXQpO1xuICAgIHJldHVybiB0cnVlO1xuICB9LCBDT01NQU5EX1BSSU9SSVRZX0VESVRPUiksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoRk9STUFUX0VMRU1FTlRfQ09NTUFORCwgZm9ybWF0ID0+IHtcbiAgICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gICAgaWYgKCEkaXNSYW5nZVNlbGVjdGlvbihzZWxlY3Rpb24pICYmICEkaXNOb2RlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgY29uc3Qgbm9kZXMgPSBzZWxlY3Rpb24uZ2V0Tm9kZXMoKTtcbiAgICBmb3IgKGNvbnN0IG5vZGUgb2Ygbm9kZXMpIHtcbiAgICAgIGNvbnN0IGVsZW1lbnQgPSAkZmluZE1hdGNoaW5nUGFyZW50KG5vZGUsIHBhcmVudE5vZGUgPT4gJGlzRWxlbWVudE5vZGUocGFyZW50Tm9kZSkgJiYgIXBhcmVudE5vZGUuaXNJbmxpbmUoKSk7XG4gICAgICBpZiAoZWxlbWVudCAhPT0gbnVsbCkge1xuICAgICAgICBlbGVtZW50LnNldEZvcm1hdChmb3JtYXQpO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKElOU0VSVF9MSU5FX0JSRUFLX0NPTU1BTkQsIHNlbGVjdFN0YXJ0ID0+IHtcbiAgICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gICAgaWYgKCEkaXNSYW5nZVNlbGVjdGlvbihzZWxlY3Rpb24pKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgIHNlbGVjdGlvbi5pbnNlcnRMaW5lQnJlYWsoc2VsZWN0U3RhcnQpO1xuICAgIHJldHVybiB0cnVlO1xuICB9LCBDT01NQU5EX1BSSU9SSVRZX0VESVRPUiksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoSU5TRVJUX1BBUkFHUkFQSF9DT01NQU5ELCAoKSA9PiB7XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICghJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBzZWxlY3Rpb24uaW5zZXJ0UGFyYWdyYXBoKCk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChJTlNFUlRfVEFCX0NPTU1BTkQsICgpID0+IHtcbiAgICAkaW5zZXJ0Tm9kZXMoWyRjcmVhdGVUYWJOb2RlKCldKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKElOREVOVF9DT05URU5UX0NPTU1BTkQsICgpID0+IHtcbiAgICByZXR1cm4gJGhhbmRsZUluZGVudEFuZE91dGRlbnQoYmxvY2sgPT4ge1xuICAgICAgY29uc3QgaW5kZW50ID0gYmxvY2suZ2V0SW5kZW50KCk7XG4gICAgICBibG9jay5zZXRJbmRlbnQoaW5kZW50ICsgMSk7XG4gICAgfSk7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChPVVRERU5UX0NPTlRFTlRfQ09NTUFORCwgKCkgPT4ge1xuICAgIHJldHVybiAkaGFuZGxlSW5kZW50QW5kT3V0ZGVudChibG9jayA9PiB7XG4gICAgICBjb25zdCBpbmRlbnQgPSBibG9jay5nZXRJbmRlbnQoKTtcbiAgICAgIGlmIChpbmRlbnQgPiAwKSB7XG4gICAgICAgIGJsb2NrLnNldEluZGVudChpbmRlbnQgLSAxKTtcbiAgICAgIH1cbiAgICB9KTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKEtFWV9BUlJPV19VUF9DT01NQU5ELCBldmVudCA9PiB7XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICgkaXNOb2RlU2VsZWN0aW9uKHNlbGVjdGlvbikgJiYgISRpc1RhcmdldFdpdGhpbkRlY29yYXRvcihldmVudC50YXJnZXQpKSB7XG4gICAgICAvLyBJZiBzZWxlY3Rpb24gaXMgb24gYSBub2RlLCBsZXQncyB0cnkgYW5kIG1vdmUgc2VsZWN0aW9uXG4gICAgICAvLyBiYWNrIHRvIGJlaW5nIGEgcmFuZ2Ugc2VsZWN0aW9uLlxuICAgICAgY29uc3Qgbm9kZXMgPSBzZWxlY3Rpb24uZ2V0Tm9kZXMoKTtcbiAgICAgIGlmIChub2Rlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIG5vZGVzWzBdLnNlbGVjdFByZXZpb3VzKCk7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgY29uc3QgcG9zc2libGVOb2RlID0gJGdldEFkamFjZW50Tm9kZShzZWxlY3Rpb24uZm9jdXMsIHRydWUpO1xuICAgICAgaWYgKCFldmVudC5zaGlmdEtleSAmJiAkaXNEZWNvcmF0b3JOb2RlKHBvc3NpYmxlTm9kZSkgJiYgIXBvc3NpYmxlTm9kZS5pc0lzb2xhdGVkKCkgJiYgIXBvc3NpYmxlTm9kZS5pc0lubGluZSgpKSB7XG4gICAgICAgIHBvc3NpYmxlTm9kZS5zZWxlY3RQcmV2aW91cygpO1xuICAgICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgICByZXR1cm4gdHJ1ZTtcbiAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGZhbHNlO1xuICB9LCBDT01NQU5EX1BSSU9SSVRZX0VESVRPUiksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoS0VZX0FSUk9XX0RPV05fQ09NTUFORCwgZXZlbnQgPT4ge1xuICAgIGNvbnN0IHNlbGVjdGlvbiA9ICRnZXRTZWxlY3Rpb24oKTtcbiAgICBpZiAoJGlzTm9kZVNlbGVjdGlvbihzZWxlY3Rpb24pKSB7XG4gICAgICAvLyBJZiBzZWxlY3Rpb24gaXMgb24gYSBub2RlLCBsZXQncyB0cnkgYW5kIG1vdmUgc2VsZWN0aW9uXG4gICAgICAvLyBiYWNrIHRvIGJlaW5nIGEgcmFuZ2Ugc2VsZWN0aW9uLlxuICAgICAgY29uc3Qgbm9kZXMgPSBzZWxlY3Rpb24uZ2V0Tm9kZXMoKTtcbiAgICAgIGlmIChub2Rlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIG5vZGVzWzBdLnNlbGVjdE5leHQoMCwgMCk7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgaWYgKCRpc1NlbGVjdGlvbkF0RW5kT2ZSb290KHNlbGVjdGlvbikpIHtcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG4gICAgICBjb25zdCBwb3NzaWJsZU5vZGUgPSAkZ2V0QWRqYWNlbnROb2RlKHNlbGVjdGlvbi5mb2N1cywgZmFsc2UpO1xuICAgICAgaWYgKCFldmVudC5zaGlmdEtleSAmJiAkaXNEZWNvcmF0b3JOb2RlKHBvc3NpYmxlTm9kZSkgJiYgIXBvc3NpYmxlTm9kZS5pc0lzb2xhdGVkKCkgJiYgIXBvc3NpYmxlTm9kZS5pc0lubGluZSgpKSB7XG4gICAgICAgIHBvc3NpYmxlTm9kZS5zZWxlY3ROZXh0KCk7XG4gICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfVxuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChLRVlfQVJST1dfTEVGVF9DT01NQU5ELCBldmVudCA9PiB7XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICgkaXNOb2RlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIC8vIElmIHNlbGVjdGlvbiBpcyBvbiBhIG5vZGUsIGxldCdzIHRyeSBhbmQgbW92ZSBzZWxlY3Rpb25cbiAgICAgIC8vIGJhY2sgdG8gYmVpbmcgYSByYW5nZSBzZWxlY3Rpb24uXG4gICAgICBjb25zdCBub2RlcyA9IHNlbGVjdGlvbi5nZXROb2RlcygpO1xuICAgICAgaWYgKG5vZGVzLmxlbmd0aCA+IDApIHtcbiAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgbm9kZXNbMF0uc2VsZWN0UHJldmlvdXMoKTtcbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG4gICAgfVxuICAgIGlmICghJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBpZiAoJHNob3VsZE92ZXJyaWRlRGVmYXVsdENoYXJhY3RlclNlbGVjdGlvbihzZWxlY3Rpb24sIHRydWUpKSB7XG4gICAgICBjb25zdCBpc0hvbGRpbmdTaGlmdCA9IGV2ZW50LnNoaWZ0S2V5O1xuICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICRtb3ZlQ2hhcmFjdGVyKHNlbGVjdGlvbiwgaXNIb2xkaW5nU2hpZnQsIHRydWUpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKEtFWV9BUlJPV19SSUdIVF9DT01NQU5ELCBldmVudCA9PiB7XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICgkaXNOb2RlU2VsZWN0aW9uKHNlbGVjdGlvbikgJiYgISRpc1RhcmdldFdpdGhpbkRlY29yYXRvcihldmVudC50YXJnZXQpKSB7XG4gICAgICAvLyBJZiBzZWxlY3Rpb24gaXMgb24gYSBub2RlLCBsZXQncyB0cnkgYW5kIG1vdmUgc2VsZWN0aW9uXG4gICAgICAvLyBiYWNrIHRvIGJlaW5nIGEgcmFuZ2Ugc2VsZWN0aW9uLlxuICAgICAgY29uc3Qgbm9kZXMgPSBzZWxlY3Rpb24uZ2V0Tm9kZXMoKTtcbiAgICAgIGlmIChub2Rlcy5sZW5ndGggPiAwKSB7XG4gICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICAgIG5vZGVzWzBdLnNlbGVjdE5leHQoMCwgMCk7XG4gICAgICAgIHJldHVybiB0cnVlO1xuICAgICAgfVxuICAgIH1cbiAgICBpZiAoISRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgY29uc3QgaXNIb2xkaW5nU2hpZnQgPSBldmVudC5zaGlmdEtleTtcbiAgICBpZiAoJHNob3VsZE92ZXJyaWRlRGVmYXVsdENoYXJhY3RlclNlbGVjdGlvbihzZWxlY3Rpb24sIGZhbHNlKSkge1xuICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICRtb3ZlQ2hhcmFjdGVyKHNlbGVjdGlvbiwgaXNIb2xkaW5nU2hpZnQsIGZhbHNlKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChLRVlfQkFDS1NQQUNFX0NPTU1BTkQsIGV2ZW50ID0+IHtcbiAgICBpZiAoJGlzVGFyZ2V0V2l0aGluRGVjb3JhdG9yKGV2ZW50LnRhcmdldCkpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICgkaXNSYW5nZVNlbGVjdGlvbihzZWxlY3Rpb24pKSB7XG4gICAgICBjb25zdCB7XG4gICAgICAgIGFuY2hvclxuICAgICAgfSA9IHNlbGVjdGlvbjtcbiAgICAgIGNvbnN0IGFuY2hvck5vZGUgPSBhbmNob3IuZ2V0Tm9kZSgpO1xuICAgICAgaWYgKHNlbGVjdGlvbi5pc0NvbGxhcHNlZCgpICYmIGFuY2hvci5vZmZzZXQgPT09IDAgJiYgISRpc1Jvb3ROb2RlKGFuY2hvck5vZGUpKSB7XG4gICAgICAgIGNvbnN0IGVsZW1lbnQgPSAkZ2V0TmVhcmVzdEJsb2NrRWxlbWVudEFuY2VzdG9yT3JUaHJvdyhhbmNob3JOb2RlKTtcbiAgICAgICAgaWYgKGVsZW1lbnQuZ2V0SW5kZW50KCkgPiAwKSB7XG4gICAgICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgICAgICByZXR1cm4gZWRpdG9yLmRpc3BhdGNoQ29tbWFuZChPVVRERU5UX0NPTlRFTlRfQ09NTUFORCwgdW5kZWZpbmVkKTtcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICAvLyBFeGNlcHRpb24gaGFuZGxpbmcgZm9yIGlPUyBuYXRpdmUgYmVoYXZpb3IgaW5zdGVhZCBvZiBMZXhpY2FsJ3MgYmVoYXZpb3Igd2hlbiB1c2luZyBLb3JlYW4gb24gaU9TIGRldmljZXMuXG4gICAgICAvLyBtb3JlIGRldGFpbHMgLSBodHRwczovL2dpdGh1Yi5jb20vZmFjZWJvb2svbGV4aWNhbC9pc3N1ZXMvNTg0MVxuICAgICAgaWYgKElTX0lPUyAmJiBuYXZpZ2F0b3IubGFuZ3VhZ2UgPT09ICdrby1LUicpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgIH0gZWxzZSBpZiAoISRpc05vZGVTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHJldHVybiBlZGl0b3IuZGlzcGF0Y2hDb21tYW5kKERFTEVURV9DSEFSQUNURVJfQ09NTUFORCwgdHJ1ZSk7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChLRVlfREVMRVRFX0NPTU1BTkQsIGV2ZW50ID0+IHtcbiAgICBpZiAoJGlzVGFyZ2V0V2l0aGluRGVjb3JhdG9yKGV2ZW50LnRhcmdldCkpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICghKCRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikgfHwgJGlzTm9kZVNlbGVjdGlvbihzZWxlY3Rpb24pKSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgIHJldHVybiBlZGl0b3IuZGlzcGF0Y2hDb21tYW5kKERFTEVURV9DSEFSQUNURVJfQ09NTUFORCwgZmFsc2UpO1xuICB9LCBDT01NQU5EX1BSSU9SSVRZX0VESVRPUiksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoS0VZX0VOVEVSX0NPTU1BTkQsIGV2ZW50ID0+IHtcbiAgICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gICAgaWYgKCEkaXNSYW5nZVNlbGVjdGlvbihzZWxlY3Rpb24pKSB7XG4gICAgICByZXR1cm4gZmFsc2U7XG4gICAgfVxuICAgICRyZXNldENhcGl0YWxpemF0aW9uKHNlbGVjdGlvbik7XG4gICAgaWYgKGV2ZW50ICE9PSBudWxsKSB7XG4gICAgICAvLyBJZiB3ZSBoYXZlIGJlZm9yZWlucHV0LCB0aGVuIHdlIGNhbiBhdm9pZCBibG9ja2luZ1xuICAgICAgLy8gdGhlIGRlZmF1bHQgYmVoYXZpb3IuIFRoaXMgZW5zdXJlcyB0aGF0IHRoZSBpT1MgY2FuXG4gICAgICAvLyBpbnRlcmNlcHQgdGhhdCB3ZSdyZSBhY3R1YWxseSBpbnNlcnRpbmcgYSBwYXJhZ3JhcGgsXG4gICAgICAvLyBhbmQgYXV0b2NvbXBsZXRlLCBhdXRvY2FwaXRhbGl6ZSBldGMgd29yayBhcyBpbnRlbmRlZC5cbiAgICAgIC8vIFRoaXMgY2FuIGFsc28gY2F1c2UgYSBzdHJhbmdlIHBlcmZvcm1hbmNlIGlzc3VlIGluXG4gICAgICAvLyBTYWZhcmksIHdoZXJlIHRoZXJlIGlzIGEgbm90aWNlYWJsZSBwYXVzZSBkdWUgdG9cbiAgICAgIC8vIHByZXZlbnRpbmcgdGhlIGtleSBkb3duIG9mIGVudGVyLlxuICAgICAgaWYgKChJU19JT1MgfHwgSVNfU0FGQVJJIHx8IElTX0FQUExFX1dFQktJVCkgJiYgQ0FOX1VTRV9CRUZPUkVfSU5QVVQpIHtcbiAgICAgICAgcmV0dXJuIGZhbHNlO1xuICAgICAgfVxuICAgICAgZXZlbnQucHJldmVudERlZmF1bHQoKTtcbiAgICAgIGlmIChldmVudC5zaGlmdEtleSkge1xuICAgICAgICByZXR1cm4gZWRpdG9yLmRpc3BhdGNoQ29tbWFuZChJTlNFUlRfTElORV9CUkVBS19DT01NQU5ELCBmYWxzZSk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiBlZGl0b3IuZGlzcGF0Y2hDb21tYW5kKElOU0VSVF9QQVJBR1JBUEhfQ09NTUFORCwgdW5kZWZpbmVkKTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKEtFWV9FU0NBUEVfQ09NTUFORCwgKCkgPT4ge1xuICAgIGNvbnN0IHNlbGVjdGlvbiA9ICRnZXRTZWxlY3Rpb24oKTtcbiAgICBpZiAoISRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgZWRpdG9yLmJsdXIoKTtcbiAgICByZXR1cm4gdHJ1ZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKERST1BfQ09NTUFORCwgZXZlbnQgPT4ge1xuICAgIGNvbnN0IFssIGZpbGVzXSA9IGV2ZW50RmlsZXMoZXZlbnQpO1xuICAgIGlmIChmaWxlcy5sZW5ndGggPiAwKSB7XG4gICAgICBjb25zdCB4ID0gZXZlbnQuY2xpZW50WDtcbiAgICAgIGNvbnN0IHkgPSBldmVudC5jbGllbnRZO1xuICAgICAgY29uc3QgZXZlbnRSYW5nZSA9IGNhcmV0RnJvbVBvaW50KHgsIHkpO1xuICAgICAgaWYgKGV2ZW50UmFuZ2UgIT09IG51bGwpIHtcbiAgICAgICAgY29uc3Qge1xuICAgICAgICAgIG9mZnNldDogZG9tT2Zmc2V0LFxuICAgICAgICAgIG5vZGU6IGRvbU5vZGVcbiAgICAgICAgfSA9IGV2ZW50UmFuZ2U7XG4gICAgICAgIGNvbnN0IG5vZGUgPSAkZ2V0TmVhcmVzdE5vZGVGcm9tRE9NTm9kZShkb21Ob2RlKTtcbiAgICAgICAgaWYgKG5vZGUgIT09IG51bGwpIHtcbiAgICAgICAgICBjb25zdCBzZWxlY3Rpb24gPSAkY3JlYXRlUmFuZ2VTZWxlY3Rpb24oKTtcbiAgICAgICAgICBpZiAoJGlzVGV4dE5vZGUobm9kZSkpIHtcbiAgICAgICAgICAgIHNlbGVjdGlvbi5hbmNob3Iuc2V0KG5vZGUuZ2V0S2V5KCksIGRvbU9mZnNldCwgJ3RleHQnKTtcbiAgICAgICAgICAgIHNlbGVjdGlvbi5mb2N1cy5zZXQobm9kZS5nZXRLZXkoKSwgZG9tT2Zmc2V0LCAndGV4dCcpO1xuICAgICAgICAgIH0gZWxzZSB7XG4gICAgICAgICAgICBjb25zdCBwYXJlbnRLZXkgPSBub2RlLmdldFBhcmVudE9yVGhyb3coKS5nZXRLZXkoKTtcbiAgICAgICAgICAgIGNvbnN0IG9mZnNldCA9IG5vZGUuZ2V0SW5kZXhXaXRoaW5QYXJlbnQoKSArIDE7XG4gICAgICAgICAgICBzZWxlY3Rpb24uYW5jaG9yLnNldChwYXJlbnRLZXksIG9mZnNldCwgJ2VsZW1lbnQnKTtcbiAgICAgICAgICAgIHNlbGVjdGlvbi5mb2N1cy5zZXQocGFyZW50S2V5LCBvZmZzZXQsICdlbGVtZW50Jyk7XG4gICAgICAgICAgfVxuICAgICAgICAgIGNvbnN0IG5vcm1hbGl6ZWRTZWxlY3Rpb24gPSAkbm9ybWFsaXplU2VsZWN0aW9uX19FWFBFUklNRU5UQUwoc2VsZWN0aW9uKTtcbiAgICAgICAgICAkc2V0U2VsZWN0aW9uKG5vcm1hbGl6ZWRTZWxlY3Rpb24pO1xuICAgICAgICB9XG4gICAgICAgIGVkaXRvci5kaXNwYXRjaENvbW1hbmQoRFJBR19EUk9QX1BBU1RFLCBmaWxlcyk7XG4gICAgICB9XG4gICAgICBldmVudC5wcmV2ZW50RGVmYXVsdCgpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIGNvbnN0IHNlbGVjdGlvbiA9ICRnZXRTZWxlY3Rpb24oKTtcbiAgICBpZiAoJGlzUmFuZ2VTZWxlY3Rpb24oc2VsZWN0aW9uKSkge1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKERSQUdTVEFSVF9DT01NQU5ELCBldmVudCA9PiB7XG4gICAgY29uc3QgW2lzRmlsZVRyYW5zZmVyXSA9IGV2ZW50RmlsZXMoZXZlbnQpO1xuICAgIGNvbnN0IHNlbGVjdGlvbiA9ICRnZXRTZWxlY3Rpb24oKTtcbiAgICBpZiAoaXNGaWxlVHJhbnNmZXIgJiYgISRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgcmV0dXJuIHRydWU7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChEUkFHT1ZFUl9DT01NQU5ELCBldmVudCA9PiB7XG4gICAgY29uc3QgW2lzRmlsZVRyYW5zZmVyXSA9IGV2ZW50RmlsZXMoZXZlbnQpO1xuICAgIGNvbnN0IHNlbGVjdGlvbiA9ICRnZXRTZWxlY3Rpb24oKTtcbiAgICBpZiAoaXNGaWxlVHJhbnNmZXIgJiYgISRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgIHJldHVybiBmYWxzZTtcbiAgICB9XG4gICAgY29uc3QgeCA9IGV2ZW50LmNsaWVudFg7XG4gICAgY29uc3QgeSA9IGV2ZW50LmNsaWVudFk7XG4gICAgY29uc3QgZXZlbnRSYW5nZSA9IGNhcmV0RnJvbVBvaW50KHgsIHkpO1xuICAgIGlmIChldmVudFJhbmdlICE9PSBudWxsKSB7XG4gICAgICBjb25zdCBub2RlID0gJGdldE5lYXJlc3ROb2RlRnJvbURPTU5vZGUoZXZlbnRSYW5nZS5ub2RlKTtcbiAgICAgIGlmICgkaXNEZWNvcmF0b3JOb2RlKG5vZGUpKSB7XG4gICAgICAgIC8vIFNob3cgYnJvd3NlciBjYXJldCBhcyB0aGUgdXNlciBpcyBkcmFnZ2luZyB0aGUgbWVkaWEgYWNyb3NzIHRoZSBzY3JlZW4uIFdvbid0IHdvcmtcbiAgICAgICAgLy8gZm9yIERlY29yYXRvck5vZGUgbm9yIGl0J3MgcmVsZXZhbnQuXG4gICAgICAgIGV2ZW50LnByZXZlbnREZWZhdWx0KCk7XG4gICAgICB9XG4gICAgfVxuICAgIHJldHVybiB0cnVlO1xuICB9LCBDT01NQU5EX1BSSU9SSVRZX0VESVRPUiksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoU0VMRUNUX0FMTF9DT01NQU5ELCAoKSA9PiB7XG4gICAgJHNlbGVjdEFsbCgpO1xuICAgIHJldHVybiB0cnVlO1xuICB9LCBDT01NQU5EX1BSSU9SSVRZX0VESVRPUiksIGVkaXRvci5yZWdpc3RlckNvbW1hbmQoQ09QWV9DT01NQU5ELCBldmVudCA9PiB7XG4gICAgY29weVRvQ2xpcGJvYXJkKGVkaXRvciwgb2JqZWN0S2xhc3NFcXVhbHMoZXZlbnQsIENsaXBib2FyZEV2ZW50KSA/IGV2ZW50IDogbnVsbCk7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChDVVRfQ09NTUFORCwgZXZlbnQgPT4ge1xuICAgIG9uQ3V0Rm9yUmljaFRleHQoZXZlbnQsIGVkaXRvcik7XG4gICAgcmV0dXJuIHRydWU7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSwgZWRpdG9yLnJlZ2lzdGVyQ29tbWFuZChQQVNURV9DT01NQU5ELCBldmVudCA9PiB7XG4gICAgY29uc3QgWywgZmlsZXMsIGhhc1RleHRDb250ZW50XSA9IGV2ZW50RmlsZXMoZXZlbnQpO1xuICAgIGlmIChmaWxlcy5sZW5ndGggPiAwICYmICFoYXNUZXh0Q29udGVudCkge1xuICAgICAgZWRpdG9yLmRpc3BhdGNoQ29tbWFuZChEUkFHX0RST1BfUEFTVEUsIGZpbGVzKTtcbiAgICAgIHJldHVybiB0cnVlO1xuICAgIH1cblxuICAgIC8vIGlmIGlucHV0cyB0aGVuIHBhc3RlIHdpdGhpbiB0aGUgaW5wdXQgaWdub3JlIGNyZWF0aW5nIGEgbmV3IG5vZGUgb24gcGFzdGUgZXZlbnRcbiAgICBpZiAoaXNET01Ob2RlKGV2ZW50LnRhcmdldCkgJiYgaXNTZWxlY3Rpb25DYXB0dXJlZEluRGVjb3JhdG9ySW5wdXQoZXZlbnQudGFyZ2V0KSkge1xuICAgICAgcmV0dXJuIGZhbHNlO1xuICAgIH1cbiAgICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gICAgaWYgKHNlbGVjdGlvbiAhPT0gbnVsbCkge1xuICAgICAgb25QYXN0ZUZvclJpY2hUZXh0KGV2ZW50LCBlZGl0b3IpO1xuICAgICAgcmV0dXJuIHRydWU7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKEtFWV9TUEFDRV9DT01NQU5ELCBfID0+IHtcbiAgICBjb25zdCBzZWxlY3Rpb24gPSAkZ2V0U2VsZWN0aW9uKCk7XG4gICAgaWYgKCRpc1JhbmdlU2VsZWN0aW9uKHNlbGVjdGlvbikpIHtcbiAgICAgICRyZXNldENhcGl0YWxpemF0aW9uKHNlbGVjdGlvbik7XG4gICAgfVxuICAgIHJldHVybiBmYWxzZTtcbiAgfSwgQ09NTUFORF9QUklPUklUWV9FRElUT1IpLCBlZGl0b3IucmVnaXN0ZXJDb21tYW5kKEtFWV9UQUJfQ09NTUFORCwgXyA9PiB7XG4gICAgY29uc3Qgc2VsZWN0aW9uID0gJGdldFNlbGVjdGlvbigpO1xuICAgIGlmICgkaXNSYW5nZVNlbGVjdGlvbihzZWxlY3Rpb24pKSB7XG4gICAgICAkcmVzZXRDYXBpdGFsaXphdGlvbihzZWxlY3Rpb24pO1xuICAgIH1cbiAgICByZXR1cm4gZmFsc2U7XG4gIH0sIENPTU1BTkRfUFJJT1JJVFlfRURJVE9SKSk7XG4gIHJldHVybiByZW1vdmVMaXN0ZW5lcjtcbn1cblxuZXhwb3J0IHsgJGNyZWF0ZUhlYWRpbmdOb2RlLCAkY3JlYXRlUXVvdGVOb2RlLCAkaXNIZWFkaW5nTm9kZSwgJGlzUXVvdGVOb2RlLCBEUkFHX0RST1BfUEFTVEUsIEhlYWRpbmdOb2RlLCBRdW90ZU5vZGUsIGV2ZW50RmlsZXMsIHJlZ2lzdGVyUmljaFRleHQgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/../../node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs":
/*!******************************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs ***!
  \******************************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $createHeadingNode: () => (/* binding */ $createHeadingNode),\n/* harmony export */   $createQuoteNode: () => (/* binding */ $createQuoteNode),\n/* harmony export */   $isHeadingNode: () => (/* binding */ $isHeadingNode),\n/* harmony export */   $isQuoteNode: () => (/* binding */ $isQuoteNode),\n/* harmony export */   DRAG_DROP_PASTE: () => (/* binding */ DRAG_DROP_PASTE),\n/* harmony export */   HeadingNode: () => (/* binding */ HeadingNode),\n/* harmony export */   QuoteNode: () => (/* binding */ QuoteNode),\n/* harmony export */   eventFiles: () => (/* binding */ eventFiles),\n/* harmony export */   registerRichText: () => (/* binding */ registerRichText)\n/* harmony export */ });\n/* harmony import */ var _lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @lexical/clipboard */ \"(ssr)/../../node_modules/.pnpm/@lexical+clipboard@0.28.0/node_modules/@lexical/clipboard/LexicalClipboard.dev.mjs\");\n/* harmony import */ var _lexical_selection__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @lexical/selection */ \"(ssr)/../../node_modules/.pnpm/@lexical+selection@0.28.0/node_modules/@lexical/selection/LexicalSelection.dev.mjs\");\n/* harmony import */ var _lexical_utils__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @lexical/utils */ \"(ssr)/../../node_modules/.pnpm/@lexical+utils@0.28.0/node_modules/@lexical/utils/LexicalUtils.dev.mjs\");\n/* harmony import */ var lexical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! lexical */ \"(ssr)/../../node_modules/.pnpm/lexical@0.28.0/node_modules/lexical/Lexical.dev.mjs\");\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\n\n\n\n\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nfunction caretFromPoint(x, y) {\n  if (typeof document.caretRangeFromPoint !== 'undefined') {\n    const range = document.caretRangeFromPoint(x, y);\n    if (range === null) {\n      return null;\n    }\n    return {\n      node: range.startContainer,\n      offset: range.startOffset\n    };\n    // @ts-ignore\n  } else if (document.caretPositionFromPoint !== 'undefined') {\n    // @ts-ignore FF - no types\n    const range = document.caretPositionFromPoint(x, y);\n    if (range === null) {\n      return null;\n    }\n    return {\n      node: range.offsetNode,\n      offset: range.offset\n    };\n  } else {\n    // Gracefully handle IE\n    return null;\n  }\n}\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst CAN_USE_DOM = typeof window !== 'undefined' && typeof window.document !== 'undefined' && typeof window.document.createElement !== 'undefined';\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst documentMode = CAN_USE_DOM && 'documentMode' in document ? document.documentMode : null;\nconst CAN_USE_BEFORE_INPUT = CAN_USE_DOM && 'InputEvent' in window && !documentMode ? 'getTargetRanges' in new window.InputEvent('input') : false;\nconst IS_SAFARI = CAN_USE_DOM && /Version\\/[\\d.]+.*Safari/.test(navigator.userAgent);\nconst IS_IOS = CAN_USE_DOM && /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;\n\n// Keep these in case we need to use them in the future.\n// export const IS_WINDOWS: boolean = CAN_USE_DOM && /Win/.test(navigator.platform);\nconst IS_CHROME = CAN_USE_DOM && /^(?=.*Chrome).*/i.test(navigator.userAgent);\nconst IS_APPLE_WEBKIT = CAN_USE_DOM && /AppleWebKit\\/[\\d.]+/.test(navigator.userAgent) && !IS_CHROME;\n\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n */\n\nconst DRAG_DROP_PASTE = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.createCommand)('DRAG_DROP_PASTE_FILE');\n/** @noInheritDoc */\nclass QuoteNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  static getType() {\n    return 'quote';\n  }\n  static clone(node) {\n    return new QuoteNode(node.__key);\n  }\n\n  // View\n\n  createDOM(config) {\n    const element = document.createElement('blockquote');\n    (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, config.theme.quote);\n    return element;\n  }\n  updateDOM(prevNode, dom) {\n    return false;\n  }\n  static importDOM() {\n    return {\n      blockquote: node => ({\n        conversion: $convertBlockquoteElement,\n        priority: 0\n      })\n    };\n  }\n  exportDOM(editor) {\n    const {\n      element\n    } = super.exportDOM(editor);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n      if (this.isEmpty()) {\n        element.append(document.createElement('br'));\n      }\n      const formatType = this.getFormatType();\n      element.style.textAlign = formatType;\n      const direction = this.getDirection();\n      if (direction) {\n        element.dir = direction;\n      }\n    }\n    return {\n      element\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createQuoteNode().updateFromJSON(serializedNode);\n  }\n\n  // Mutation\n\n  insertNewAfter(_, restoreSelection) {\n    const newBlock = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const direction = this.getDirection();\n    newBlock.setDirection(direction);\n    this.insertAfter(newBlock, restoreSelection);\n    return newBlock;\n  }\n  collapseAtStart() {\n    const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const children = this.getChildren();\n    children.forEach(child => paragraph.append(child));\n    this.replace(paragraph);\n    return true;\n  }\n  canMergeWhenEmpty() {\n    return true;\n  }\n}\nfunction $createQuoteNode() {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new QuoteNode());\n}\nfunction $isQuoteNode(node) {\n  return node instanceof QuoteNode;\n}\n/** @noInheritDoc */\nclass HeadingNode extends lexical__WEBPACK_IMPORTED_MODULE_0__.ElementNode {\n  /** @internal */\n\n  static getType() {\n    return 'heading';\n  }\n  static clone(node) {\n    return new HeadingNode(node.__tag, node.__key);\n  }\n  constructor(tag, key) {\n    super(key);\n    this.__tag = tag;\n  }\n  getTag() {\n    return this.__tag;\n  }\n  setTag(tag) {\n    const self = this.getWritable();\n    this.__tag = tag;\n    return self;\n  }\n\n  // View\n\n  createDOM(config) {\n    const tag = this.__tag;\n    const element = document.createElement(tag);\n    const theme = config.theme;\n    const classNames = theme.heading;\n    if (classNames !== undefined) {\n      const className = classNames[tag];\n      (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.addClassNamesToElement)(element, className);\n    }\n    return element;\n  }\n  updateDOM(prevNode, dom, config) {\n    return prevNode.__tag !== this.__tag;\n  }\n  static importDOM() {\n    return {\n      h1: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h2: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h3: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h4: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h5: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      h6: node => ({\n        conversion: $convertHeadingElement,\n        priority: 0\n      }),\n      p: node => {\n        // domNode is a <p> since we matched it by nodeName\n        const paragraph = node;\n        const firstChild = paragraph.firstChild;\n        if (firstChild !== null && isGoogleDocsTitle(firstChild)) {\n          return {\n            conversion: () => ({\n              node: null\n            }),\n            priority: 3\n          };\n        }\n        return null;\n      },\n      span: node => {\n        if (isGoogleDocsTitle(node)) {\n          return {\n            conversion: domNode => {\n              return {\n                node: $createHeadingNode('h1')\n              };\n            },\n            priority: 3\n          };\n        }\n        return null;\n      }\n    };\n  }\n  exportDOM(editor) {\n    const {\n      element\n    } = super.exportDOM(editor);\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isHTMLElement)(element)) {\n      if (this.isEmpty()) {\n        element.append(document.createElement('br'));\n      }\n      const formatType = this.getFormatType();\n      element.style.textAlign = formatType;\n      const direction = this.getDirection();\n      if (direction) {\n        element.dir = direction;\n      }\n    }\n    return {\n      element\n    };\n  }\n  static importJSON(serializedNode) {\n    return $createHeadingNode(serializedNode.tag).updateFromJSON(serializedNode);\n  }\n  updateFromJSON(serializedNode) {\n    return super.updateFromJSON(serializedNode).setTag(serializedNode.tag);\n  }\n  exportJSON() {\n    return {\n      ...super.exportJSON(),\n      tag: this.getTag()\n    };\n  }\n\n  // Mutation\n  insertNewAfter(selection, restoreSelection = true) {\n    const anchorOffet = selection ? selection.anchor.offset : 0;\n    const lastDesc = this.getLastDescendant();\n    const isAtEnd = !lastDesc || selection && selection.anchor.key === lastDesc.getKey() && anchorOffet === lastDesc.getTextContentSize();\n    const newElement = isAtEnd || !selection ? (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)() : $createHeadingNode(this.getTag());\n    const direction = this.getDirection();\n    newElement.setDirection(direction);\n    this.insertAfter(newElement, restoreSelection);\n    if (anchorOffet === 0 && !this.isEmpty() && selection) {\n      const paragraph = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n      paragraph.select();\n      this.replace(paragraph, true);\n    }\n    return newElement;\n  }\n  collapseAtStart() {\n    const newElement = !this.isEmpty() ? $createHeadingNode(this.getTag()) : (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createParagraphNode)();\n    const children = this.getChildren();\n    children.forEach(child => newElement.append(child));\n    this.replace(newElement);\n    return true;\n  }\n  extractWithChild() {\n    return true;\n  }\n}\nfunction isGoogleDocsTitle(domNode) {\n  if (domNode.nodeName.toLowerCase() === 'span') {\n    return domNode.style.fontSize === '26pt';\n  }\n  return false;\n}\nfunction $convertHeadingElement(element) {\n  const nodeName = element.nodeName.toLowerCase();\n  let node = null;\n  if (nodeName === 'h1' || nodeName === 'h2' || nodeName === 'h3' || nodeName === 'h4' || nodeName === 'h5' || nodeName === 'h6') {\n    node = $createHeadingNode(nodeName);\n    if (element.style !== null) {\n      (0,lexical__WEBPACK_IMPORTED_MODULE_0__.setNodeIndentFromDOM)(element, node);\n      node.setFormat(element.style.textAlign);\n    }\n  }\n  return {\n    node\n  };\n}\nfunction $convertBlockquoteElement(element) {\n  const node = $createQuoteNode();\n  if (element.style !== null) {\n    node.setFormat(element.style.textAlign);\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.setNodeIndentFromDOM)(element, node);\n  }\n  return {\n    node\n  };\n}\nfunction $createHeadingNode(headingTag = 'h1') {\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$applyNodeReplacement)(new HeadingNode(headingTag));\n}\nfunction $isHeadingNode(node) {\n  return node instanceof HeadingNode;\n}\nfunction onPasteForRichText(event, editor) {\n  event.preventDefault();\n  editor.update(() => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    const clipboardData = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, InputEvent) || (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, KeyboardEvent) ? null : event.clipboardData;\n    if (clipboardData != null && selection !== null) {\n      (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.$insertDataTransferForRichText)(clipboardData, selection, editor);\n    }\n  }, {\n    tag: 'paste'\n  });\n}\nasync function onCutForRichText(event, editor) {\n  await (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.copyToClipboard)(editor, (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent) ? event : null);\n  editor.update(() => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      selection.removeText();\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      selection.getNodes().forEach(node => node.remove());\n    }\n  });\n}\n\n// Clipboard may contain files that we aren't allowed to read. While the event is arguably useless,\n// in certain occasions, we want to know whether it was a file transfer, as opposed to text. We\n// control this with the first boolean flag.\nfunction eventFiles(event) {\n  let dataTransfer = null;\n  if ((0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, DragEvent)) {\n    dataTransfer = event.dataTransfer;\n  } else if ((0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent)) {\n    dataTransfer = event.clipboardData;\n  }\n  if (dataTransfer === null) {\n    return [false, [], false];\n  }\n  const types = dataTransfer.types;\n  const hasFiles = types.includes('Files');\n  const hasContent = types.includes('text/html') || types.includes('text/plain');\n  return [hasFiles, Array.from(dataTransfer.files), hasContent];\n}\nfunction $handleIndentAndOutdent(indentOrOutdent) {\n  const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n  if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n    return false;\n  }\n  const alreadyHandled = new Set();\n  const nodes = selection.getNodes();\n  for (let i = 0; i < nodes.length; i++) {\n    const node = nodes[i];\n    const key = node.getKey();\n    if (alreadyHandled.has(key)) {\n      continue;\n    }\n    const parentBlock = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, parentNode => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(parentNode) && !parentNode.isInline());\n    if (parentBlock === null) {\n      continue;\n    }\n    const parentKey = parentBlock.getKey();\n    if (parentBlock.canIndent() && !alreadyHandled.has(parentKey)) {\n      alreadyHandled.add(parentKey);\n      indentOrOutdent(parentBlock);\n    }\n  }\n  return alreadyHandled.size > 0;\n}\nfunction $isTargetWithinDecorator(target) {\n  const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(target);\n  return (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(node);\n}\nfunction $isSelectionAtEndOfRoot(selection) {\n  const focus = selection.focus;\n  return focus.key === 'root' && focus.offset === (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getRoot)().getChildrenSize();\n}\n\n/**\n * Resets the capitalization of the selection to default.\n * Called when the user presses space, tab, or enter key.\n * @param selection The selection to reset the capitalization of.\n */\nfunction $resetCapitalization(selection) {\n  for (const format of ['lowercase', 'uppercase', 'capitalize']) {\n    if (selection.hasFormat(format)) {\n      selection.toggleFormat(format);\n    }\n  }\n}\nfunction registerRichText(editor) {\n  const removeListener = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.mergeRegister)(editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CLICK_COMMAND, payload => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      selection.clear();\n      return true;\n    }\n    return false;\n  }, 0), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND, isBackward => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      selection.deleteCharacter(isBackward);\n      return true;\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      selection.deleteNodes();\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_WORD_COMMAND, isBackward => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.deleteWord(isBackward);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_LINE_COMMAND, isBackward => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.deleteLine(isBackward);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CONTROLLED_TEXT_INSERTION_COMMAND, eventOrText => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (typeof eventOrText === 'string') {\n      if (selection !== null) {\n        selection.insertText(eventOrText);\n      }\n    } else {\n      if (selection === null) {\n        return false;\n      }\n      const dataTransfer = eventOrText.dataTransfer;\n      if (dataTransfer != null) {\n        (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.$insertDataTransferForRichText)(dataTransfer, selection, editor);\n      } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n        const data = eventOrText.data;\n        if (data) {\n          selection.insertText(data);\n        }\n        return true;\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.REMOVE_TEXT_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.removeText();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FORMAT_TEXT_COMMAND, format => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.formatText(format);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.FORMAT_ELEMENT_COMMAND, format => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      return false;\n    }\n    const nodes = selection.getNodes();\n    for (const node of nodes) {\n      const element = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$findMatchingParent)(node, parentNode => (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isElementNode)(parentNode) && !parentNode.isInline());\n      if (element !== null) {\n        element.setFormat(format);\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_LINE_BREAK_COMMAND, selectStart => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.insertLineBreak(selectStart);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    selection.insertParagraph();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_TAB_COMMAND, () => {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$insertNodes)([(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createTabNode)()]);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INDENT_CONTENT_COMMAND, () => {\n    return $handleIndentAndOutdent(block => {\n      const indent = block.getIndent();\n      block.setIndent(indent + 1);\n    });\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.OUTDENT_CONTENT_COMMAND, () => {\n    return $handleIndentAndOutdent(block => {\n      const indent = block.getIndent();\n      if (indent > 0) {\n        block.setIndent(indent - 1);\n      }\n    });\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_UP_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection) && !$isTargetWithinDecorator(event.target)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        nodes[0].selectPrevious();\n        return true;\n      }\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const possibleNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentNode)(selection.focus, true);\n      if (!event.shiftKey && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(possibleNode) && !possibleNode.isIsolated() && !possibleNode.isInline()) {\n        possibleNode.selectPrevious();\n        event.preventDefault();\n        return true;\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_DOWN_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        nodes[0].selectNext(0, 0);\n        return true;\n      }\n    } else if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      if ($isSelectionAtEndOfRoot(selection)) {\n        event.preventDefault();\n        return true;\n      }\n      const possibleNode = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getAdjacentNode)(selection.focus, false);\n      if (!event.shiftKey && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(possibleNode) && !possibleNode.isIsolated() && !possibleNode.isInline()) {\n        possibleNode.selectNext();\n        event.preventDefault();\n        return true;\n      }\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_LEFT_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        event.preventDefault();\n        nodes[0].selectPrevious();\n        return true;\n      }\n    }\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    if ((0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$shouldOverrideDefaultCharacterSelection)(selection, true)) {\n      const isHoldingShift = event.shiftKey;\n      event.preventDefault();\n      (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$moveCharacter)(selection, isHoldingShift, true);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ARROW_RIGHT_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection) && !$isTargetWithinDecorator(event.target)) {\n      // If selection is on a node, let's try and move selection\n      // back to being a range selection.\n      const nodes = selection.getNodes();\n      if (nodes.length > 0) {\n        event.preventDefault();\n        nodes[0].selectNext(0, 0);\n        return true;\n      }\n    }\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const isHoldingShift = event.shiftKey;\n    if ((0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$shouldOverrideDefaultCharacterSelection)(selection, false)) {\n      event.preventDefault();\n      (0,_lexical_selection__WEBPACK_IMPORTED_MODULE_3__.$moveCharacter)(selection, isHoldingShift, false);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_BACKSPACE_COMMAND, event => {\n    if ($isTargetWithinDecorator(event.target)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      const {\n        anchor\n      } = selection;\n      const anchorNode = anchor.getNode();\n      if (selection.isCollapsed() && anchor.offset === 0 && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRootNode)(anchorNode)) {\n        const element = (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.$getNearestBlockElementAncestorOrThrow)(anchorNode);\n        if (element.getIndent() > 0) {\n          event.preventDefault();\n          return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.OUTDENT_CONTENT_COMMAND, undefined);\n        }\n      }\n\n      // Exception handling for iOS native behavior instead of Lexical's behavior when using Korean on iOS devices.\n      // more details - https://github.com/facebook/lexical/issues/5841\n      if (IS_IOS && navigator.language === 'ko-KR') {\n        return false;\n      }\n    } else if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection)) {\n      return false;\n    }\n    event.preventDefault();\n    return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND, true);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_DELETE_COMMAND, event => {\n    if ($isTargetWithinDecorator(event.target)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection) || (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isNodeSelection)(selection))) {\n      return false;\n    }\n    event.preventDefault();\n    return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DELETE_CHARACTER_COMMAND, false);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ENTER_COMMAND, event => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    $resetCapitalization(selection);\n    if (event !== null) {\n      // If we have beforeinput, then we can avoid blocking\n      // the default behavior. This ensures that the iOS can\n      // intercept that we're actually inserting a paragraph,\n      // and autocomplete, autocapitalize etc work as intended.\n      // This can also cause a strange performance issue in\n      // Safari, where there is a noticeable pause due to\n      // preventing the key down of enter.\n      if ((IS_IOS || IS_SAFARI || IS_APPLE_WEBKIT) && CAN_USE_BEFORE_INPUT) {\n        return false;\n      }\n      event.preventDefault();\n      if (event.shiftKey) {\n        return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_LINE_BREAK_COMMAND, false);\n      }\n    }\n    return editor.dispatchCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.INSERT_PARAGRAPH_COMMAND, undefined);\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_ESCAPE_COMMAND, () => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (!(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    editor.blur();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DROP_COMMAND, event => {\n    const [, files] = eventFiles(event);\n    if (files.length > 0) {\n      const x = event.clientX;\n      const y = event.clientY;\n      const eventRange = caretFromPoint(x, y);\n      if (eventRange !== null) {\n        const {\n          offset: domOffset,\n          node: domNode\n        } = eventRange;\n        const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(domNode);\n        if (node !== null) {\n          const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$createRangeSelection)();\n          if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isTextNode)(node)) {\n            selection.anchor.set(node.getKey(), domOffset, 'text');\n            selection.focus.set(node.getKey(), domOffset, 'text');\n          } else {\n            const parentKey = node.getParentOrThrow().getKey();\n            const offset = node.getIndexWithinParent() + 1;\n            selection.anchor.set(parentKey, offset, 'element');\n            selection.focus.set(parentKey, offset, 'element');\n          }\n          const normalizedSelection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$normalizeSelection__EXPERIMENTAL)(selection);\n          (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$setSelection)(normalizedSelection);\n        }\n        editor.dispatchCommand(DRAG_DROP_PASTE, files);\n      }\n      event.preventDefault();\n      return true;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DRAGSTART_COMMAND, event => {\n    const [isFileTransfer] = eventFiles(event);\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (isFileTransfer && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.DRAGOVER_COMMAND, event => {\n    const [isFileTransfer] = eventFiles(event);\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (isFileTransfer && !(0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      return false;\n    }\n    const x = event.clientX;\n    const y = event.clientY;\n    const eventRange = caretFromPoint(x, y);\n    if (eventRange !== null) {\n      const node = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getNearestNodeFromDOMNode)(eventRange.node);\n      if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isDecoratorNode)(node)) {\n        // Show browser caret as the user is dragging the media across the screen. Won't work\n        // for DecoratorNode nor it's relevant.\n        event.preventDefault();\n      }\n    }\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.SELECT_ALL_COMMAND, () => {\n    (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$selectAll)();\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.COPY_COMMAND, event => {\n    (0,_lexical_clipboard__WEBPACK_IMPORTED_MODULE_2__.copyToClipboard)(editor, (0,_lexical_utils__WEBPACK_IMPORTED_MODULE_1__.objectKlassEquals)(event, ClipboardEvent) ? event : null);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.CUT_COMMAND, event => {\n    onCutForRichText(event, editor);\n    return true;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.PASTE_COMMAND, event => {\n    const [, files, hasTextContent] = eventFiles(event);\n    if (files.length > 0 && !hasTextContent) {\n      editor.dispatchCommand(DRAG_DROP_PASTE, files);\n      return true;\n    }\n\n    // if inputs then paste within the input ignore creating a new node on paste event\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.isDOMNode)(event.target) && (0,lexical__WEBPACK_IMPORTED_MODULE_0__.isSelectionCapturedInDecoratorInput)(event.target)) {\n      return false;\n    }\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if (selection !== null) {\n      onPasteForRichText(event, editor);\n      return true;\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_SPACE_COMMAND, _ => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      $resetCapitalization(selection);\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR), editor.registerCommand(lexical__WEBPACK_IMPORTED_MODULE_0__.KEY_TAB_COMMAND, _ => {\n    const selection = (0,lexical__WEBPACK_IMPORTED_MODULE_0__.$getSelection)();\n    if ((0,lexical__WEBPACK_IMPORTED_MODULE_0__.$isRangeSelection)(selection)) {\n      $resetCapitalization(selection);\n    }\n    return false;\n  }, lexical__WEBPACK_IMPORTED_MODULE_0__.COMMAND_PRIORITY_EDITOR));\n  return removeListener;\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@lexical+rich-text@0.28.0/node_modules/@lexical/rich-text/LexicalRichText.dev.mjs\n");

/***/ })

};
;