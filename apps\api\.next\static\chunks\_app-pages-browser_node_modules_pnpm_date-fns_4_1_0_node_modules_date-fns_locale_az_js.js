"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_az_js"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   az: () => (/* binding */ az),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _az_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./az/_lib/formatDistance.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatDistance.js\");\n/* harmony import */ var _az_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./az/_lib/formatLong.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatLong.js\");\n/* harmony import */ var _az_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./az/_lib/formatRelative.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatRelative.js\");\n/* harmony import */ var _az_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./az/_lib/localize.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/localize.js\");\n/* harmony import */ var _az_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./az/_lib/match.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Azerbaijani locale.\n * @language Azerbaijani\n * @iso-639-2 aze\n */ const az = {\n    code: \"az\",\n    formatDistance: _az_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _az_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _az_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _az_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _az_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1,\n        firstWeekContainsDate: 1\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (az);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS9hei5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBQTZEO0FBQ1I7QUFDUTtBQUNaO0FBQ047QUFFM0M7Ozs7O0NBS0MsR0FFTSxNQUFNSyxLQUFLO0lBQ2hCQyxNQUFNO0lBQ05OLGdCQUFnQkEscUVBQWNBO0lBQzlCQyxZQUFZQSw2REFBVUE7SUFDdEJDLGdCQUFnQkEscUVBQWNBO0lBQzlCQyxVQUFVQSx5REFBUUE7SUFDbEJDLE9BQU9BLG1EQUFLQTtJQUNaRyxTQUFTO1FBQ1BDLGNBQWM7UUFDZEMsdUJBQXVCO0lBQ3pCO0FBQ0YsRUFBRTtBQUVGLG9DQUFvQztBQUNwQyxpRUFBZUosRUFBRUEsRUFBQyIsInNvdXJjZXMiOlsiQzpcXHByb2plY3RzXFxsbXNcXG5vZGVfbW9kdWxlc1xcLnBucG1cXGRhdGUtZm5zQDQuMS4wXFxub2RlX21vZHVsZXNcXGRhdGUtZm5zXFxsb2NhbGVcXGF6LmpzIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IGZvcm1hdERpc3RhbmNlIH0gZnJvbSBcIi4vYXovX2xpYi9mb3JtYXREaXN0YW5jZS5qc1wiO1xuaW1wb3J0IHsgZm9ybWF0TG9uZyB9IGZyb20gXCIuL2F6L19saWIvZm9ybWF0TG9uZy5qc1wiO1xuaW1wb3J0IHsgZm9ybWF0UmVsYXRpdmUgfSBmcm9tIFwiLi9hei9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzXCI7XG5pbXBvcnQgeyBsb2NhbGl6ZSB9IGZyb20gXCIuL2F6L19saWIvbG9jYWxpemUuanNcIjtcbmltcG9ydCB7IG1hdGNoIH0gZnJvbSBcIi4vYXovX2xpYi9tYXRjaC5qc1wiO1xuXG4vKipcbiAqIEBjYXRlZ29yeSBMb2NhbGVzXG4gKiBAc3VtbWFyeSBBemVyYmFpamFuaSBsb2NhbGUuXG4gKiBAbGFuZ3VhZ2UgQXplcmJhaWphbmlcbiAqIEBpc28tNjM5LTIgYXplXG4gKi9cblxuZXhwb3J0IGNvbnN0IGF6ID0ge1xuICBjb2RlOiBcImF6XCIsXG4gIGZvcm1hdERpc3RhbmNlOiBmb3JtYXREaXN0YW5jZSxcbiAgZm9ybWF0TG9uZzogZm9ybWF0TG9uZyxcbiAgZm9ybWF0UmVsYXRpdmU6IGZvcm1hdFJlbGF0aXZlLFxuICBsb2NhbGl6ZTogbG9jYWxpemUsXG4gIG1hdGNoOiBtYXRjaCxcbiAgb3B0aW9uczoge1xuICAgIHdlZWtTdGFydHNPbjogMSxcbiAgICBmaXJzdFdlZWtDb250YWluc0RhdGU6IDEsXG4gIH0sXG59O1xuXG4vLyBGYWxsYmFjayBmb3IgbW9kdWxhcml6ZWQgaW1wb3J0czpcbmV4cG9ydCBkZWZhdWx0IGF6O1xuIl0sIm5hbWVzIjpbImZvcm1hdERpc3RhbmNlIiwiZm9ybWF0TG9uZyIsImZvcm1hdFJlbGF0aXZlIiwibG9jYWxpemUiLCJtYXRjaCIsImF6IiwiY29kZSIsIm9wdGlvbnMiLCJ3ZWVrU3RhcnRzT24iLCJmaXJzdFdlZWtDb250YWluc0RhdGUiXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatDistance.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatDistance.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"bir saniyədən az\",\n        other: \"{{count}} bir saniyədən az\"\n    },\n    xSeconds: {\n        one: \"1 saniyə\",\n        other: \"{{count}} saniyə\"\n    },\n    halfAMinute: \"yarım dəqiqə\",\n    lessThanXMinutes: {\n        one: \"bir dəqiqədən az\",\n        other: \"{{count}} bir dəqiqədən az\"\n    },\n    xMinutes: {\n        one: \"bir dəqiqə\",\n        other: \"{{count}} dəqiqə\"\n    },\n    aboutXHours: {\n        one: \"təxminən 1 saat\",\n        other: \"təxminən {{count}} saat\"\n    },\n    xHours: {\n        one: \"1 saat\",\n        other: \"{{count}} saat\"\n    },\n    xDays: {\n        one: \"1 gün\",\n        other: \"{{count}} gün\"\n    },\n    aboutXWeeks: {\n        one: \"təxminən 1 həftə\",\n        other: \"təxminən {{count}} həftə\"\n    },\n    xWeeks: {\n        one: \"1 həftə\",\n        other: \"{{count}} həftə\"\n    },\n    aboutXMonths: {\n        one: \"təxminən 1 ay\",\n        other: \"təxminən {{count}} ay\"\n    },\n    xMonths: {\n        one: \"1 ay\",\n        other: \"{{count}} ay\"\n    },\n    aboutXYears: {\n        one: \"təxminən 1 il\",\n        other: \"təxminən {{count}} il\"\n    },\n    xYears: {\n        one: \"1 il\",\n        other: \"{{count}} il\"\n    },\n    overXYears: {\n        one: \"1 ildən çox\",\n        other: \"{{count}} ildən çox\"\n    },\n    almostXYears: {\n        one: \"demək olar ki 1 il\",\n        other: \"demək olar ki {{count}} il\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return result + \" sonra\";\n        } else {\n            return result + \" əvvəl\";\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatLong.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatLong.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, do MMMM y 'il'\",\n    long: \"do MMMM y 'il'\",\n    medium: \"d MMM y 'il'\",\n    short: \"dd.MM.yyyy\"\n};\nconst timeFormats = {\n    full: \"H:mm:ss zzzz\",\n    long: \"H:mm:ss z\",\n    medium: \"H:mm:ss\",\n    short: \"H:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} {{time}} - 'də'\",\n    long: \"{{date}} {{time}} - 'də'\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatRelative.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatRelative.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"'sonuncu' eeee p -'də'\",\n    yesterday: \"'dünən' p -'də'\",\n    today: \"'bugün' p -'də'\",\n    tomorrow: \"'sabah' p -'də'\",\n    nextWeek: \"eeee p -'də'\",\n    other: \"P\"\n};\nconst formatRelative = (token, _date, _baseDate, _options)=>formatRelativeLocale[token];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS9hei9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSx1QkFBdUI7SUFDM0JDLFVBQVU7SUFDVkMsV0FBVztJQUNYQyxPQUFPO0lBQ1BDLFVBQVU7SUFDVkMsVUFBVTtJQUNWQyxPQUFPO0FBQ1Q7QUFFTyxNQUFNQyxpQkFBaUIsQ0FBQ0MsT0FBT0MsT0FBT0MsV0FBV0MsV0FDdERYLG9CQUFvQixDQUFDUSxNQUFNLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxwcm9qZWN0c1xcbG1zXFxub2RlX21vZHVsZXNcXC5wbnBtXFxkYXRlLWZuc0A0LjEuMFxcbm9kZV9tb2R1bGVzXFxkYXRlLWZuc1xcbG9jYWxlXFxhelxcX2xpYlxcZm9ybWF0UmVsYXRpdmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZm9ybWF0UmVsYXRpdmVMb2NhbGUgPSB7XG4gIGxhc3RXZWVrOiBcIidzb251bmN1JyBlZWVlIHAgLSdkyZknXCIsXG4gIHllc3RlcmRheTogXCInZMO8bsmZbicgcCAtJ2TJmSdcIixcbiAgdG9kYXk6IFwiJ2J1Z8O8bicgcCAtJ2TJmSdcIixcbiAgdG9tb3Jyb3c6IFwiJ3NhYmFoJyBwIC0nZMmZJ1wiLFxuICBuZXh0V2VlazogXCJlZWVlIHAgLSdkyZknXCIsXG4gIG90aGVyOiBcIlBcIixcbn07XG5cbmV4cG9ydCBjb25zdCBmb3JtYXRSZWxhdGl2ZSA9ICh0b2tlbiwgX2RhdGUsIF9iYXNlRGF0ZSwgX29wdGlvbnMpID0+XG4gIGZvcm1hdFJlbGF0aXZlTG9jYWxlW3Rva2VuXTtcbiJdLCJuYW1lcyI6WyJmb3JtYXRSZWxhdGl2ZUxvY2FsZSIsImxhc3RXZWVrIiwieWVzdGVyZGF5IiwidG9kYXkiLCJ0b21vcnJvdyIsIm5leHRXZWVrIiwib3RoZXIiLCJmb3JtYXRSZWxhdGl2ZSIsInRva2VuIiwiX2RhdGUiLCJfYmFzZURhdGUiLCJfb3B0aW9ucyJdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/localize.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/localize.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"e.ə\",\n        \"b.e\"\n    ],\n    abbreviated: [\n        \"e.ə\",\n        \"b.e\"\n    ],\n    wide: [\n        \"eramızdan əvvəl\",\n        \"bizim era\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"K1\",\n        \"K2\",\n        \"K3\",\n        \"K4\"\n    ],\n    wide: [\n        \"1ci kvartal\",\n        \"2ci kvartal\",\n        \"3cü kvartal\",\n        \"4cü kvartal\"\n    ]\n};\nconst monthValues = {\n    narrow: [\n        \"Y\",\n        \"F\",\n        \"M\",\n        \"A\",\n        \"M\",\n        \"İ\",\n        \"İ\",\n        \"A\",\n        \"S\",\n        \"O\",\n        \"N\",\n        \"D\"\n    ],\n    abbreviated: [\n        \"Yan\",\n        \"Fev\",\n        \"Mar\",\n        \"Apr\",\n        \"May\",\n        \"İyun\",\n        \"İyul\",\n        \"Avq\",\n        \"Sen\",\n        \"Okt\",\n        \"Noy\",\n        \"Dek\"\n    ],\n    wide: [\n        \"Yanvar\",\n        \"Fevral\",\n        \"Mart\",\n        \"Aprel\",\n        \"May\",\n        \"İyun\",\n        \"İyul\",\n        \"Avqust\",\n        \"Sentyabr\",\n        \"Oktyabr\",\n        \"Noyabr\",\n        \"Dekabr\"\n    ]\n};\nconst dayValues = {\n    narrow: [\n        \"B.\",\n        \"B.e\",\n        \"Ç.a\",\n        \"Ç.\",\n        \"C.a\",\n        \"C.\",\n        \"Ş.\"\n    ],\n    short: [\n        \"B.\",\n        \"B.e\",\n        \"Ç.a\",\n        \"Ç.\",\n        \"C.a\",\n        \"C.\",\n        \"Ş.\"\n    ],\n    abbreviated: [\n        \"Baz\",\n        \"Baz.e\",\n        \"Çər.a\",\n        \"Çər\",\n        \"Cüm.a\",\n        \"Cüm\",\n        \"Şə\"\n    ],\n    wide: [\n        \"Bazar\",\n        \"Bazar ertəsi\",\n        \"Çərşənbə axşamı\",\n        \"Çərşənbə\",\n        \"Cümə axşamı\",\n        \"Cümə\",\n        \"Şənbə\"\n    ]\n};\nconst dayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    wide: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"a\",\n        pm: \"p\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    },\n    wide: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"gecəyarı\",\n        noon: \"gün\",\n        morning: \"səhər\",\n        afternoon: \"gündüz\",\n        evening: \"axşam\",\n        night: \"gecə\"\n    }\n};\nconst suffixes = {\n    1: \"-inci\",\n    5: \"-inci\",\n    8: \"-inci\",\n    70: \"-inci\",\n    80: \"-inci\",\n    2: \"-nci\",\n    7: \"-nci\",\n    20: \"-nci\",\n    50: \"-nci\",\n    3: \"-üncü\",\n    4: \"-üncü\",\n    100: \"-üncü\",\n    6: \"-ncı\",\n    9: \"-uncu\",\n    10: \"-uncu\",\n    30: \"-uncu\",\n    60: \"-ıncı\",\n    90: \"-ıncı\"\n};\nconst getSuffix = (number)=>{\n    if (number === 0) {\n        // special case for zero\n        return number + \"-ıncı\";\n    }\n    const a = number % 10;\n    const b = number % 100 - a;\n    const c = number >= 100 ? 100 : null;\n    if (suffixes[a]) {\n        return suffixes[a];\n    } else if (suffixes[b]) {\n        return suffixes[b];\n    } else if (c !== null) {\n        return suffixes[c];\n    }\n    return \"\";\n};\nconst ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    const suffix = getSuffix(number);\n    return number + suffix;\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/match.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/match.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(-?(ci|inci|nci|uncu|üncü|ncı))?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(b|a)$/i,\n    abbreviated: /^(b\\.?\\s?c\\.?|b\\.?\\s?c\\.?\\s?e\\.?|a\\.?\\s?d\\.?|c\\.?\\s?e\\.?)$/i,\n    wide: /^(bizim eradan əvvəl|bizim era)$/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^b$/i,\n        /^(a|c)$/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]$/i,\n    abbreviated: /^K[1234]$/i,\n    wide: /^[1234](ci)? kvartal$/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[(?-i)yfmaisond]$/i,\n    abbreviated: /^(Yan|Fev|Mar|Apr|May|İyun|İyul|Avq|Sen|Okt|Noy|Dek)$/i,\n    wide: /^(Yanvar|Fevral|Mart|Aprel|May|İyun|İyul|Avgust|Sentyabr|Oktyabr|Noyabr|Dekabr)$/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^[(?-i)y]$/i,\n        /^[(?-i)f]$/i,\n        /^[(?-i)m]$/i,\n        /^[(?-i)a]$/i,\n        /^[(?-i)m]$/i,\n        /^[(?-i)i]$/i,\n        /^[(?-i)i]$/i,\n        /^[(?-i)a]$/i,\n        /^[(?-i)s]$/i,\n        /^[(?-i)o]$/i,\n        /^[(?-i)n]$/i,\n        /^[(?-i)d]$/i\n    ],\n    abbreviated: [\n        /^Yan$/i,\n        /^Fev$/i,\n        /^Mar$/i,\n        /^Apr$/i,\n        /^May$/i,\n        /^İyun$/i,\n        /^İyul$/i,\n        /^Avg$/i,\n        /^Sen$/i,\n        /^Okt$/i,\n        /^Noy$/i,\n        /^Dek$/i\n    ],\n    wide: [\n        /^Yanvar$/i,\n        /^Fevral$/i,\n        /^Mart$/i,\n        /^Aprel$/i,\n        /^May$/i,\n        /^İyun$/i,\n        /^İyul$/i,\n        /^Avgust$/i,\n        /^Sentyabr$/i,\n        /^Oktyabr$/i,\n        /^Noyabr$/i,\n        /^Dekabr$/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^(B\\.|B\\.e|Ç\\.a|Ç\\.|C\\.a|C\\.|Ş\\.)$/i,\n    short: /^(B\\.|B\\.e|Ç\\.a|Ç\\.|C\\.a|C\\.|Ş\\.)$/i,\n    abbreviated: /^(Baz\\.e|Çər|Çər\\.a|Cüm|Cüm\\.a|Şə)$/i,\n    wide: /^(Bazar|Bazar ertəsi|Çərşənbə axşamı|Çərşənbə|Cümə axşamı|Cümə|Şənbə)$/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^B\\.$/i,\n        /^B\\.e$/i,\n        /^Ç\\.a$/i,\n        /^Ç\\.$/i,\n        /^C\\.a$/i,\n        /^C\\.$/i,\n        /^Ş\\.$/i\n    ],\n    abbreviated: [\n        /^Baz$/i,\n        /^Baz\\.e$/i,\n        /^Çər\\.a$/i,\n        /^Çər$/i,\n        /^Cüm\\.a$/i,\n        /^Cüm$/i,\n        /^Şə$/i\n    ],\n    wide: [\n        /^Bazar$/i,\n        /^Bazar ertəsi$/i,\n        /^Çərşənbə axşamı$/i,\n        /^Çərşənbə$/i,\n        /^Cümə axşamı$/i,\n        /^Cümə$/i,\n        /^Şənbə$/i\n    ],\n    any: [\n        /^B\\.$/i,\n        /^B\\.e$/i,\n        /^Ç\\.a$/i,\n        /^Ç\\.$/i,\n        /^C\\.a$/i,\n        /^C\\.$/i,\n        /^Ş\\.$/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(a|p|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i,\n    any: /^(am|pm|a\\.m\\.|p\\.m\\.|AM|PM|gecəyarı|gün|səhər|gündüz|axşam|gecə)$/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^a$/i,\n        pm: /^p$/i,\n        midnight: /^gecəyarı$/i,\n        noon: /^gün$/i,\n        morning: /səhər$/i,\n        afternoon: /gündüz$/i,\n        evening: /axşam$/i,\n        night: /gecə$/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"narrow\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"any\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/az/_lib/match.js\n"));

/***/ })

}]);