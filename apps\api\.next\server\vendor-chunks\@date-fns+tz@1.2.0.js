"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@date-fns+tz@1.2.0";
exports.ids = ["vendor-chunks/@date-fns+tz@1.2.0"];
exports.modules = {

/***/ "(ssr)/../../node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/mini.js":
/*!******************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/mini.js ***!
  \******************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TZDateMini: () => (/* binding */ TZDateMini)\n/* harmony export */ });\n/* harmony import */ var _tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../tzOffset/index.js */ \"(ssr)/../../node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzOffset/index.js\");\n\nclass TZDateMini extends Date {\n  //#region static\n\n  constructor(...args) {\n    super();\n    if (args.length > 1 && typeof args[args.length - 1] === \"string\") {\n      this.timeZone = args.pop();\n    }\n    this.internal = new Date();\n    if (isNaN((0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(this.timeZone, this))) {\n      this.setTime(NaN);\n    } else {\n      if (!args.length) {\n        this.setTime(Date.now());\n      } else if (typeof args[0] === \"number\" && (args.length === 1 || args.length === 2 && typeof args[1] !== \"number\")) {\n        this.setTime(args[0]);\n      } else if (typeof args[0] === \"string\") {\n        this.setTime(+new Date(args[0]));\n      } else if (args[0] instanceof Date) {\n        this.setTime(+args[0]);\n      } else {\n        this.setTime(+new Date(...args));\n        adjustToSystemTZ(this, NaN);\n        syncToInternal(this);\n      }\n    }\n  }\n  static tz(tz, ...args) {\n    return args.length ? new TZDateMini(...args, tz) : new TZDateMini(Date.now(), tz);\n  }\n\n  //#endregion\n\n  //#region time zone\n\n  withTimeZone(timeZone) {\n    return new TZDateMini(+this, timeZone);\n  }\n  getTimezoneOffset() {\n    return -(0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(this.timeZone, this);\n  }\n\n  //#endregion\n\n  //#region time\n\n  setTime(time) {\n    Date.prototype.setTime.apply(this, arguments);\n    syncToInternal(this);\n    return +this;\n  }\n\n  //#endregion\n\n  //#region date-fns integration\n\n  [Symbol.for(\"constructDateFrom\")](date) {\n    return new TZDateMini(+new Date(date), this.timeZone);\n  }\n\n  //#endregion\n}\n\n// Assign getters and setters\nconst re = /^(get|set)(?!UTC)/;\nObject.getOwnPropertyNames(Date.prototype).forEach(method => {\n  if (!re.test(method)) return;\n  const utcMethod = method.replace(re, \"$1UTC\");\n  // Filter out methods without UTC counterparts\n  if (!TZDateMini.prototype[utcMethod]) return;\n  if (method.startsWith(\"get\")) {\n    // Delegate to internal date's UTC method\n    TZDateMini.prototype[method] = function () {\n      return this.internal[utcMethod]();\n    };\n  } else {\n    // Assign regular setter\n    TZDateMini.prototype[method] = function () {\n      Date.prototype[utcMethod].apply(this.internal, arguments);\n      syncFromInternal(this);\n      return +this;\n    };\n\n    // Assign UTC setter\n    TZDateMini.prototype[utcMethod] = function () {\n      Date.prototype[utcMethod].apply(this, arguments);\n      syncToInternal(this);\n      return +this;\n    };\n  }\n});\n\n/**\n * Function syncs time to internal date, applying the time zone offset.\n *\n * @param {Date} date - Date to sync\n */\nfunction syncToInternal(date) {\n  date.internal.setTime(+date);\n  date.internal.setUTCMinutes(date.internal.getUTCMinutes() - date.getTimezoneOffset());\n}\n\n/**\n * Function syncs the internal date UTC values to the date. It allows to get\n * accurate timestamp value.\n *\n * @param {Date} date - The date to sync\n */\nfunction syncFromInternal(date) {\n  // First we transpose the internal values\n  Date.prototype.setFullYear.call(date, date.internal.getUTCFullYear(), date.internal.getUTCMonth(), date.internal.getUTCDate());\n  Date.prototype.setHours.call(date, date.internal.getUTCHours(), date.internal.getUTCMinutes(), date.internal.getUTCSeconds(), date.internal.getUTCMilliseconds());\n\n  // Now we have to adjust the date to the system time zone\n  adjustToSystemTZ(date);\n}\n\n/**\n * Function adjusts the date to the system time zone. It uses the time zone\n * differences to calculate the offset and adjust the date.\n *\n * @param {Date} date - Date to adjust\n */\nfunction adjustToSystemTZ(date) {\n  // Save the time zone offset before all the adjustments\n  const offset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n\n  //#region System DST adjustment\n\n  // The biggest problem with using the system time zone is that when we create\n  // a date from internal values stored in UTC, the system time zone might end\n  // up on the DST hour:\n  //\n  //   $ TZ=America/New_York node\n  //   > new Date(2020, 2, 8, 1).toString()\n  //   'Sun Mar 08 2020 01:00:00 GMT-0500 (Eastern Standard Time)'\n  //   > new Date(2020, 2, 8, 2).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 3).toString()\n  //   'Sun Mar 08 2020 03:00:00 GMT-0400 (Eastern Daylight Time)'\n  //   > new Date(2020, 2, 8, 4).toString()\n  //   'Sun Mar 08 2020 04:00:00 GMT-0400 (Eastern Daylight Time)'\n  //\n  // Here we get the same hour for both 2 and 3, because the system time zone\n  // has DST beginning at 8 March 2020, 2 a.m. and jumps to 3 a.m. So we have\n  // to adjust the internal date to reflect that.\n  //\n  // However we want to adjust only if that's the DST hour the change happenes,\n  // not the hour where DST moves to.\n\n  // We calculate the previous hour to see if the time zone offset has changed\n  // and we have landed on the DST hour.\n  const prevHour = new Date(+date);\n  // We use UTC methods here as we don't want to land on the same hour again\n  // in case of DST.\n  prevHour.setUTCHours(prevHour.getUTCHours() - 1);\n\n  // Calculate if we are on the system DST hour.\n  const systemOffset = -new Date(+date).getTimezoneOffset();\n  const prevHourSystemOffset = -new Date(+prevHour).getTimezoneOffset();\n  const systemDSTChange = systemOffset - prevHourSystemOffset;\n  // Detect the DST shift. System DST change will occur both on\n  const dstShift = Date.prototype.getHours.apply(date) !== date.internal.getUTCHours();\n\n  // Move the internal date when we are on the system DST hour.\n  if (systemDSTChange && dstShift) date.internal.setUTCMinutes(date.internal.getUTCMinutes() + systemDSTChange);\n\n  //#endregion\n\n  //#region System diff adjustment\n\n  // Now we need to adjust the date, since we just applied internal values.\n  // We need to calculate the difference between the system and date time zones\n  // and apply it to the date.\n\n  const offsetDiff = systemOffset - offset;\n  if (offsetDiff) Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetDiff);\n\n  //#endregion\n\n  //#region Post-adjustment DST fix\n\n  const postOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n  const postSystemOffset = -new Date(+date).getTimezoneOffset();\n  const postOffsetDiff = postSystemOffset - postOffset;\n  const offsetChanged = postOffset !== offset;\n  const postDiff = postOffsetDiff - offsetDiff;\n  if (offsetChanged && postDiff) {\n    Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + postDiff);\n\n    // Now we need to check if got offset change during the post-adjustment.\n    // If so, we also need both dates to reflect that.\n\n    const newOffset = (0,_tzOffset_index_js__WEBPACK_IMPORTED_MODULE_0__.tzOffset)(date.timeZone, date);\n    const offsetChange = postOffset - newOffset;\n    if (offsetChange) {\n      date.internal.setUTCMinutes(date.internal.getUTCMinutes() + offsetChange);\n      Date.prototype.setUTCMinutes.call(date, Date.prototype.getUTCMinutes.call(date) + offsetChange);\n    }\n  }\n\n  //#endregion\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/date/mini.js\n");

/***/ }),

/***/ "(ssr)/../../node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzOffset/index.js":
/*!***********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzOffset/index.js ***!
  \***********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   tzOffset: () => (/* binding */ tzOffset)\n/* harmony export */ });\nconst offsetFormatCache = {};\nconst offsetCache = {};\n\n/**\n * The function extracts UTC offset in minutes from the given date in specified\n * time zone.\n *\n * Unlike `Date.prototype.getTimezoneOffset`, this function returns the value\n * mirrored to the sign of the offset in the time zone. For Asia/Singapore\n * (UTC+8), `tzOffset` returns 480, while `getTimezoneOffset` returns -480.\n *\n * @param timeZone - Time zone name (IANA or UTC offset)\n * @param date - Date to check the offset for\n *\n * @returns UTC offset in minutes\n */\nfunction tzOffset(timeZone, date) {\n  try {\n    const format = offsetFormatCache[timeZone] ||= new Intl.DateTimeFormat(\"en-GB\", {\n      timeZone,\n      hour: \"numeric\",\n      timeZoneName: \"longOffset\"\n    }).format;\n    const offsetStr = format(date).split('GMT')[1] || '';\n    if (offsetStr in offsetCache) return offsetCache[offsetStr];\n    return calcOffset(offsetStr, offsetStr.split(\":\"));\n  } catch {\n    // Fallback to manual parsing if the runtime doesn't support ±HH:MM/±HHMM/±HH\n    // See: https://github.com/nodejs/node/issues/53419\n    if (timeZone in offsetCache) return offsetCache[timeZone];\n    const captures = timeZone?.match(offsetRe);\n    if (captures) return calcOffset(timeZone, captures.slice(1));\n    return NaN;\n  }\n}\nconst offsetRe = /([+-]\\d\\d):?(\\d\\d)?/;\nfunction calcOffset(cacheStr, values) {\n  const hours = +values[0];\n  const minutes = +(values[1] || 0);\n  return offsetCache[cacheStr] = hours > 0 ? hours * 60 + minutes : hours * 60 - minutes;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/../../node_modules/.pnpm/@date-fns+tz@1.2.0/node_modules/@date-fns/tz/tzOffset/index.js\n");

/***/ })

};
;