"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_ca_js"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ca: () => (/* binding */ ca),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ca_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./ca/_lib/formatDistance.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatDistance.js\");\n/* harmony import */ var _ca_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./ca/_lib/formatLong.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatLong.js\");\n/* harmony import */ var _ca_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ca/_lib/formatRelative.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatRelative.js\");\n/* harmony import */ var _ca_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./ca/_lib/localize.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/localize.js\");\n/* harmony import */ var _ca_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./ca/_lib/match.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary Catalan locale.\n * @language Catalan\n * @iso-639-2 cat\n * <AUTHOR> Grau [@guigrpa](https://github.com/guigrpa)\n * <AUTHOR> Vizcaino [@avizcaino](https://github.com/avizcaino)\n */ const ca = {\n    code: \"ca\",\n    formatDistance: _ca_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _ca_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _ca_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _ca_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _ca_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ca);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatDistance.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatDistance.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\n/**\n * Davant de les xifres que es diuen amb vocal inicial, 1 i 11, s'apostrofen els articles el i la i la preposició de igual que si estiguessin escrits amb lletres.\n *    l'1 de juliol ('l'u')\n *    l'11 de novembre ('l'onze')\n *    l'11a clàusula del contracte ('l'onzena')\n *    la contractació d'11 jugadors ('d'onze')\n *    l'aval d'11.000 socis ('d'onze mil')\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=apostrofaci%25F3+davant+xifres&action=Principal&method=detall_completa&numPagina=1&idHit=11236&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=11236&titol=apostrofaci%F3%20davant%20de%20xifres%20%2F%20apostrofaci%F3%20davant%20de%201%20i%2011&numeroResultat=1&clickLink=detall&tipusCerca=cerca.normes\n */ const formatDistanceLocale = {\n    lessThanXSeconds: {\n        one: \"menys d'un segon\",\n        eleven: \"menys d'onze segons\",\n        other: \"menys de {{count}} segons\"\n    },\n    xSeconds: {\n        one: \"1 segon\",\n        other: \"{{count}} segons\"\n    },\n    halfAMinute: \"mig minut\",\n    lessThanXMinutes: {\n        one: \"menys d'un minut\",\n        eleven: \"menys d'onze minuts\",\n        other: \"menys de {{count}} minuts\"\n    },\n    xMinutes: {\n        one: \"1 minut\",\n        other: \"{{count}} minuts\"\n    },\n    aboutXHours: {\n        one: \"aproximadament una hora\",\n        other: \"aproximadament {{count}} hores\"\n    },\n    xHours: {\n        one: \"1 hora\",\n        other: \"{{count}} hores\"\n    },\n    xDays: {\n        one: \"1 dia\",\n        other: \"{{count}} dies\"\n    },\n    aboutXWeeks: {\n        one: \"aproximadament una setmana\",\n        other: \"aproximadament {{count}} setmanes\"\n    },\n    xWeeks: {\n        one: \"1 setmana\",\n        other: \"{{count}} setmanes\"\n    },\n    aboutXMonths: {\n        one: \"aproximadament un mes\",\n        other: \"aproximadament {{count}} mesos\"\n    },\n    xMonths: {\n        one: \"1 mes\",\n        other: \"{{count}} mesos\"\n    },\n    aboutXYears: {\n        one: \"aproximadament un any\",\n        other: \"aproximadament {{count}} anys\"\n    },\n    xYears: {\n        one: \"1 any\",\n        other: \"{{count}} anys\"\n    },\n    overXYears: {\n        one: \"més d'un any\",\n        eleven: \"més d'onze anys\",\n        other: \"més de {{count}} anys\"\n    },\n    almostXYears: {\n        one: \"gairebé un any\",\n        other: \"gairebé {{count}} anys\"\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = formatDistanceLocale[token];\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else if (count === 11 && tokenValue.eleven) {\n        result = tokenValue.eleven;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"en \" + result;\n        } else {\n            return \"fa \" + result;\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatLong.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatLong.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\nconst dateFormats = {\n    full: \"EEEE, d 'de' MMMM y\",\n    long: \"d 'de' MMMM y\",\n    medium: \"d MMM y\",\n    short: \"dd/MM/y\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'a les' {{time}}\",\n    long: \"{{date}} 'a les' {{time}}\",\n    medium: \"{{date}}, {{time}}\",\n    short: \"{{date}}, {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatRelative.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatRelative.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"'el' eeee 'passat a la' LT\",\n    yesterday: \"'ahir a la' p\",\n    today: \"'avui a la' p\",\n    tomorrow: \"'demà a la' p\",\n    nextWeek: \"eeee 'a la' p\",\n    other: \"P\"\n};\nconst formatRelativeLocalePlural = {\n    lastWeek: \"'el' eeee 'passat a les' p\",\n    yesterday: \"'ahir a les' p\",\n    today: \"'avui a les' p\",\n    tomorrow: \"'demà a les' p\",\n    nextWeek: \"eeee 'a les' p\",\n    other: \"P\"\n};\nconst formatRelative = (token, date, _baseDate, _options)=>{\n    if (date.getHours() !== 1) {\n        return formatRelativeLocalePlural[token];\n    }\n    return formatRelativeLocale[token];\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/localize.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/localize.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\n/**\n * General information\n * Reference: https://aplicacions.llengua.gencat.cat\n * Reference: https://www.uoc.edu/portal/ca/servei-linguistic/convencions/abreviacions/simbols/simbols-habituals.html\n */ /**\n * Abans de Crist: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=abans+de+crist&action=Principal&method=detall_completa&numPagina=1&idHit=6876&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=6876&titol=abans%20de%20Crist%20(abreviatura)%20/%20abans%20de%20Crist%20(sigla)&numeroResultat=1&clickLink=detall&tipusCerca=cerca.fitxes\n * Desprest de Crist: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=despr%E9s+de+crist&action=Principal&method=detall_completa&numPagina=1&idHit=6879&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=6879&titol=despr%E9s%20de%20Crist%20(sigla)%20/%20despr%E9s%20de%20Crist%20(abreviatura)&numeroResultat=1&clickLink=detall&tipusCerca=cerca.fitxes\n */ const eraValues = {\n    narrow: [\n        \"aC\",\n        \"dC\"\n    ],\n    abbreviated: [\n        \"a. de C.\",\n        \"d. de C.\"\n    ],\n    wide: [\n        \"abans de Crist\",\n        \"després de Crist\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"T1\",\n        \"T2\",\n        \"T3\",\n        \"T4\"\n    ],\n    wide: [\n        \"1r trimestre\",\n        \"2n trimestre\",\n        \"3r trimestre\",\n        \"4t trimestre\"\n    ]\n};\n/**\n * Dins d'un text convé fer servir la forma sencera dels mesos, ja que sempre és més clar el mot sencer que l'abreviatura, encara que aquesta sigui força coneguda.\n * Cal reservar, doncs, les abreviatures per a les llistes o classificacions, els gràfics, les taules o quadres estadístics, els textos publicitaris, etc.\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=abreviacions+mesos&action=Principal&method=detall_completa&numPagina=1&idHit=8402&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=8402&titol=abreviatures%20dels%20mesos%20de%20l%27any&numeroResultat=5&clickLink=detall&tipusCerca=cerca.fitxes\n */ const monthValues = {\n    narrow: [\n        \"GN\",\n        \"FB\",\n        \"MÇ\",\n        \"AB\",\n        \"MG\",\n        \"JN\",\n        \"JL\",\n        \"AG\",\n        \"ST\",\n        \"OC\",\n        \"NV\",\n        \"DS\"\n    ],\n    /**\n   * Les abreviatures dels mesos de l'any es formen seguint una de les normes generals de formació d'abreviatures.\n   * S'escriu la primera síl·laba i les consonants de la síl·laba següent anteriors a la primera vocal.\n   * Els mesos de març, maig i juny no s'abreugen perquè són paraules d'una sola síl·laba.\n   */ abbreviated: [\n        \"gen.\",\n        \"febr.\",\n        \"març\",\n        \"abr.\",\n        \"maig\",\n        \"juny\",\n        \"jul.\",\n        \"ag.\",\n        \"set.\",\n        \"oct.\",\n        \"nov.\",\n        \"des.\"\n    ],\n    wide: [\n        \"gener\",\n        \"febrer\",\n        \"març\",\n        \"abril\",\n        \"maig\",\n        \"juny\",\n        \"juliol\",\n        \"agost\",\n        \"setembre\",\n        \"octubre\",\n        \"novembre\",\n        \"desembre\"\n    ]\n};\n/**\n * Les abreviatures dels dies de la setmana comencen totes amb la lletra d.\n * Tot seguit porten la consonant següent a la i, excepte en el cas de dimarts, dimecres i diumenge, en què aquesta consonant és la m i, per tant, hi podria haver confusió.\n * Per evitar-ho, s'ha substituït la m per una t (en el cas de dimarts), una c (en el cas de dimecres) i una g (en el cas de diumenge), respectivament.\n *\n * Seguint la norma general d'ús de les abreviatures, les dels dies de la setmana sempre porten punt final.\n * Igualment, van amb la primera lletra en majúscula quan la paraula sencera també hi aniria.\n * En canvi, van amb la primera lletra en minúscula quan la inicial de la paraula sencera també hi aniria.\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?input_cercar=abreviatures+dies&action=Principal&method=detall_completa&numPagina=1&idHit=8387&database=FITXES_PUB&tipusFont=Fitxes%20de%20l%27Optimot&idFont=8387&titol=abreviatures%20dels%20dies%20de%20la%20setmana&numeroResultat=1&clickLink=detall&tipusCerca=cerca.tot\n */ const dayValues = {\n    narrow: [\n        \"dg.\",\n        \"dl.\",\n        \"dt.\",\n        \"dm.\",\n        \"dj.\",\n        \"dv.\",\n        \"ds.\"\n    ],\n    short: [\n        \"dg.\",\n        \"dl.\",\n        \"dt.\",\n        \"dm.\",\n        \"dj.\",\n        \"dv.\",\n        \"ds.\"\n    ],\n    abbreviated: [\n        \"dg.\",\n        \"dl.\",\n        \"dt.\",\n        \"dm.\",\n        \"dj.\",\n        \"dv.\",\n        \"ds.\"\n    ],\n    wide: [\n        \"diumenge\",\n        \"dilluns\",\n        \"dimarts\",\n        \"dimecres\",\n        \"dijous\",\n        \"divendres\",\n        \"dissabte\"\n    ]\n};\n/**\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/index.html?action=Principal&method=detall&input_cercar=parts+del+dia&numPagina=1&database=FITXES_PUB&idFont=12801&idHit=12801&tipusFont=Fitxes+de+l%27Optimot&numeroResultat=1&databases_avansada=&categories_avansada=&clickLink=detall&titol=Nom+de+les+parts+del+dia&tematica=&tipusCerca=cerca.fitxes\n */ const dayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"mitjanit\",\n        noon: \"migdia\",\n        morning: \"matí\",\n        afternoon: \"tarda\",\n        evening: \"vespre\",\n        night: \"nit\"\n    },\n    abbreviated: {\n        am: \"a.m.\",\n        pm: \"p.m.\",\n        midnight: \"mitjanit\",\n        noon: \"migdia\",\n        morning: \"matí\",\n        afternoon: \"tarda\",\n        evening: \"vespre\",\n        night: \"nit\"\n    },\n    wide: {\n        am: \"ante meridiem\",\n        pm: \"post meridiem\",\n        midnight: \"mitjanit\",\n        noon: \"migdia\",\n        morning: \"matí\",\n        afternoon: \"tarda\",\n        evening: \"vespre\",\n        night: \"nit\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"am\",\n        pm: \"pm\",\n        midnight: \"de la mitjanit\",\n        noon: \"del migdia\",\n        morning: \"del matí\",\n        afternoon: \"de la tarda\",\n        evening: \"del vespre\",\n        night: \"de la nit\"\n    },\n    abbreviated: {\n        am: \"AM\",\n        pm: \"PM\",\n        midnight: \"de la mitjanit\",\n        noon: \"del migdia\",\n        morning: \"del matí\",\n        afternoon: \"de la tarda\",\n        evening: \"del vespre\",\n        night: \"de la nit\"\n    },\n    wide: {\n        am: \"ante meridiem\",\n        pm: \"post meridiem\",\n        midnight: \"de la mitjanit\",\n        noon: \"del migdia\",\n        morning: \"del matí\",\n        afternoon: \"de la tarda\",\n        evening: \"del vespre\",\n        night: \"de la nit\"\n    }\n};\n/**\n * Quan van en singular, els nombres ordinals es representen, en forma d’abreviatura, amb la xifra seguida de l’última lletra del mot desplegat.\n * És optatiu posar punt després de la lletra.\n *\n * Reference: https://aplicacions.llengua.gencat.cat/llc/AppJava/pdf/abrevia.pdf#page=18\n */ const ordinalNumber = (dirtyNumber, _options)=>{\n    const number = Number(dirtyNumber);\n    const rem100 = number % 100;\n    if (rem100 > 20 || rem100 < 10) {\n        switch(rem100 % 10){\n            case 1:\n                return number + \"r\";\n            case 2:\n                return number + \"n\";\n            case 3:\n                return number + \"r\";\n            case 4:\n                return number + \"t\";\n        }\n    }\n    return number + \"è\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/match.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/match.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(è|r|n|r|t)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(aC|dC)/i,\n    abbreviated: /^(a. de C.|d. de C.)/i,\n    wide: /^(abans de Crist|despr[eé]s de Crist)/i\n};\nconst parseEraPatterns = {\n    narrow: [\n        /^aC/i,\n        /^dC/i\n    ],\n    abbreviated: [\n        /^(a. de C.)/i,\n        /^(d. de C.)/i\n    ],\n    wide: [\n        /^(abans de Crist)/i,\n        /^(despr[eé]s de Crist)/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^T[1234]/i,\n    wide: /^[1234](è|r|n|r|t)? trimestre/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^(GN|FB|MÇ|AB|MG|JN|JL|AG|ST|OC|NV|DS)/i,\n    abbreviated: /^(gen.|febr.|març|abr.|maig|juny|jul.|ag.|set.|oct.|nov.|des.)/i,\n    wide: /^(gener|febrer|març|abril|maig|juny|juliol|agost|setembre|octubre|novembre|desembre)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^GN/i,\n        /^FB/i,\n        /^MÇ/i,\n        /^AB/i,\n        /^MG/i,\n        /^JN/i,\n        /^JL/i,\n        /^AG/i,\n        /^ST/i,\n        /^OC/i,\n        /^NV/i,\n        /^DS/i\n    ],\n    abbreviated: [\n        /^gen./i,\n        /^febr./i,\n        /^març/i,\n        /^abr./i,\n        /^maig/i,\n        /^juny/i,\n        /^jul./i,\n        /^ag./i,\n        /^set./i,\n        /^oct./i,\n        /^nov./i,\n        /^des./i\n    ],\n    wide: [\n        /^gener/i,\n        /^febrer/i,\n        /^març/i,\n        /^abril/i,\n        /^maig/i,\n        /^juny/i,\n        /^juliol/i,\n        /^agost/i,\n        /^setembre/i,\n        /^octubre/i,\n        /^novembre/i,\n        /^desembre/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^(dg\\.|dl\\.|dt\\.|dm\\.|dj\\.|dv\\.|ds\\.)/i,\n    short: /^(dg\\.|dl\\.|dt\\.|dm\\.|dj\\.|dv\\.|ds\\.)/i,\n    abbreviated: /^(dg\\.|dl\\.|dt\\.|dm\\.|dj\\.|dv\\.|ds\\.)/i,\n    wide: /^(diumenge|dilluns|dimarts|dimecres|dijous|divendres|dissabte)/i\n};\nconst parseDayPatterns = {\n    narrow: [\n        /^dg./i,\n        /^dl./i,\n        /^dt./i,\n        /^dm./i,\n        /^dj./i,\n        /^dv./i,\n        /^ds./i\n    ],\n    abbreviated: [\n        /^dg./i,\n        /^dl./i,\n        /^dt./i,\n        /^dm./i,\n        /^dj./i,\n        /^dv./i,\n        /^ds./i\n    ],\n    wide: [\n        /^diumenge/i,\n        /^dilluns/i,\n        /^dimarts/i,\n        /^dimecres/i,\n        /^dijous/i,\n        /^divendres/i,\n        /^disssabte/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(a|p|mn|md|(del|de la) (matí|tarda|vespre|nit))/i,\n    abbreviated: /^([ap]\\.?\\s?m\\.?|mitjanit|migdia|(del|de la) (matí|tarda|vespre|nit))/i,\n    wide: /^(ante meridiem|post meridiem|mitjanit|migdia|(del|de la) (matí|tarda|vespre|nit))/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^a/i,\n        pm: /^p/i,\n        midnight: /^mitjanit/i,\n        noon: /^migdia/i,\n        morning: /matí/i,\n        afternoon: /tarda/i,\n        evening: /vespre/i,\n        night: /nit/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value, 10)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"wide\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/ca/_lib/match.js\n"));

/***/ })

}]);