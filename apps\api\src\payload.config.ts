// storage-adapter-import-placeholder
import { postgresAdapter } from '@payloadcms/db-postgres'
import { payloadCloudPlugin } from '@payloadcms/payload-cloud'
import { lexicalEditor } from '@payloadcms/richtext-lexical'
import path from 'path'
import { buildConfig } from 'payload'
import { fileURLToPath } from 'url'
import sharp from 'sharp'

import { Users } from './collections/Users'
import { Media } from './collections/Media'
import { Institutes } from './collections/Institutes'
import Courses from './collections/Courses'
import Themes from './collections/Themes'
import CourseCategories from './collections/CourseCategories'

import { Permissions } from './collections/Permissions'
import Roles from './collections/Roles'
import UserPermissions from './collections/UserPermissions'
import RolePermissions from './collections/RolePermissions'
import Sessions from './collections/Sessions'
import Settings from './collections/Settings'
import DomainRequests from './collections/DomainRequests'
import Countries from './collections/Countries'
import States from './collections/States'
import Districts from './collections/Districts'
import TaxComponents from './collections/TaxComponents'
import TaxGroups from './collections/TaxGroups'
import TaxRules from './collections/TaxRules'
import Branches from './collections/Branches'
import Bills from './collections/Bills'
import CoursePurchases from './collections/CoursePurchases'
import PaymentGateways from './collections/PaymentGateways'
import { loginEndpoint, registerInstituteEndpoint, registerStudentEndpoint, refreshTokenEndpoint, verifyTokenEndpoint } from './endpoints/auth'
import { getCurrentUserEndpoint, updateCurrentUserEndpoint, getAllUsersEndpoint, getInstituteUsersEndpoint, createUserEndpoint } from './endpoints/users'
import { getAllInstitutesEndpoint, getCurrentInstituteEndpoint, updateCurrentInstituteEndpoint, createInstituteEndpoint, updateInstituteEndpoint, deleteInstituteEndpoint } from './endpoints/institutes'
import courseEndpoints from './endpoints/courses/index'
import { themeEndpoints } from './endpoints/themes'
import { testEndpoints } from './endpoints/test'

import { settingsEndpoints } from './endpoints/settings'
import { sessionEndpoints } from './endpoints/sessions'
import { domainRequestEndpoints } from './endpoints/domain-requests'
import { locationEndpoints } from './endpoints/locations'
import { taxManagementEndpoints } from './endpoints/tax-management'
import rolesEndpoints from './endpoints/roles/index'
import { rolePermissionsEndpoints } from './endpoints/rolePermissions'
import { roleManagementEndpoints } from './endpoints/role-management-index'
import { notificationEndpoints } from './endpoints/notifications'
import { reportEndpoints } from './endpoints/reports'
import { studentEndpoints } from './endpoints/student'
import { getInstituteProfileEndpoint, updateInstituteProfileEndpoint, getInstituteStatsEndpoint } from './endpoints/institutes'
import { billingEndpoints } from './endpoints/billing'

const filename = fileURLToPath(import.meta.url)
const dirname = path.dirname(filename)

export default buildConfig({
  admin: {
    user: Users.slug,
    importMap: {
      baseDir: path.resolve(dirname),
    },
  },
  cors: [
    'http://localhost:3000', // Frontend development server
    'http://localhost:3001', // API server
    'http://localhost:3002', // Frontend fallback port
    'http://127.0.0.1:3000', // Alternative localhost
    'http://127.0.0.1:3001', // Alternative localhost
    'http://127.0.0.1:3002', // Alternative localhost
    process.env.FRONTEND_URL || 'http://localhost:3000',
    // Allow all origins in development
    ...(process.env.NODE_ENV === 'development' ? ['*'] : [])
  ],
  collections: [Users, Media, Institutes, Courses, Themes, CourseCategories, Permissions, Roles, UserPermissions, RolePermissions, Sessions, Settings, DomainRequests, Countries, States, Districts, TaxComponents, TaxGroups, TaxRules, Branches, Bills, CoursePurchases, PaymentGateways],
  endpoints: [
    // Authentication endpoints
    loginEndpoint,
    registerInstituteEndpoint,
    registerStudentEndpoint,
    refreshTokenEndpoint,
    verifyTokenEndpoint,

    // User management endpoints
    getCurrentUserEndpoint,
    updateCurrentUserEndpoint,
    getAllUsersEndpoint,
    getInstituteUsersEndpoint,
    createUserEndpoint,

    // Institute management endpoints
    getAllInstitutesEndpoint,
    getCurrentInstituteEndpoint,
    updateCurrentInstituteEndpoint,
    createInstituteEndpoint,
    updateInstituteEndpoint,
    deleteInstituteEndpoint,

    // Course management endpoints
    ...courseEndpoints,

    // Theme management endpoints
    ...themeEndpoints,

    // Test endpoints
    ...testEndpoints,



    // Settings management endpoints
    ...settingsEndpoints,

    // Session management endpoints
    ...sessionEndpoints,

    // Domain request endpoints
    ...domainRequestEndpoints,

    // Location management endpoints
    ...locationEndpoints,

    // Tax management endpoints
    ...taxManagementEndpoints,

    // Roles and permissions endpoints
    ...rolesEndpoints,
    ...rolePermissionsEndpoints,

    // Enhanced role management endpoints (tax management style)
    ...roleManagementEndpoints,

    // Billing endpoints
    ...billingEndpoints,

    // Notification endpoints
    ...notificationEndpoints,

    // Report endpoints
    ...reportEndpoints,

    // Student endpoints
    ...studentEndpoints,

    // Institute profile endpoints
    getInstituteProfileEndpoint,
    updateInstituteProfileEndpoint,
    getInstituteStatsEndpoint,
  ],
  editor: lexicalEditor(),
  secret: process.env.PAYLOAD_SECRET || '',
  typescript: {
    outputFile: path.resolve(dirname, 'payload-types.ts'),
  },
  db: postgresAdapter({
    pool: {
      connectionString: process.env.DATABASE_URI || '',
    },
  }),
  sharp,
  plugins: [
    payloadCloudPlugin(),
    // storage-adapter-placeholder
  ],
})
