import { useMemo } from 'react'
import { usePermissions } from '@/contexts/PermissionContext'
import { useSidebarStore, NavigationItem } from '@/stores/sidebar/useSidebarStore'

/**
 * Hook to filter navigation items based on user permissions
 */
export function usePermissionAwareNavigation() {
  const { filterNavigationByPermissions, userPermissions } = usePermissions()
  const { navigationItems } = useSidebarStore()

  // Filter navigation items based on permissions
  const filteredNavigationItems = useMemo(() => {
    // Prevent infinite loops by checking if navigationItems is valid
    if (!Array.isArray(navigationItems) || navigationItems.length === 0) {
      return []
    }

    // For super admin, return all items without filtering to prevent loops
    if (userPermissions.role === 'super_admin') {
      return navigationItems
    }

    return filterNavigationByPermissions(navigationItems)
  }, [navigationItems, filterNavigationByPermissions, userPermissions.role])

  // Get navigation items with permission status
  const navigationWithPermissions = useMemo(() => {
    if (!Array.isArray(navigationItems) || navigationItems.length === 0) {
      return []
    }

    return navigationItems.map(item => {
      if (!item || !item.href) {
        return { ...item, hasAccess: false, navigationId: '' }
      }

      const navigationId = item.href.replace('/', '').replace(/\//g, '-')
      // For super admin, always has access
      const hasAccess = userPermissions.role === 'super_admin' || filterNavigationByPermissions([item]).length > 0

      return {
        ...item,
        hasAccess,
        navigationId
      }
    })
  }, [navigationItems, filterNavigationByPermissions, userPermissions.role])

  // Get accessible navigation count
  const accessibleItemsCount = filteredNavigationItems.length
  const totalItemsCount = navigationItems.length
  const restrictedItemsCount = totalItemsCount - accessibleItemsCount

  // Check if specific navigation item is accessible
  const isNavigationAccessible = (href: string): boolean => {
    const navigationId = href.replace('/', '').replace(/\//g, '-')
    return filteredNavigationItems.some(item => item.href === href)
  }

  // Get navigation items by category/section
  const getNavigationBySection = (sectionName: string): NavigationItem[] => {
    return filteredNavigationItems.filter(item => 
      item.href.includes(sectionName.toLowerCase())
    )
  }

  // Get restricted navigation items (for debugging/admin purposes)
  const getRestrictedNavigation = (): NavigationItem[] => {
    return navigationItems.filter(item => 
      !filteredNavigationItems.some(filtered => filtered.id === item.id)
    )
  }

  return {
    // Filtered navigation
    navigationItems: filteredNavigationItems,
    navigationWithPermissions,
    
    // Statistics
    accessibleItemsCount,
    totalItemsCount,
    restrictedItemsCount,
    
    // Utility functions
    isNavigationAccessible,
    getNavigationBySection,
    getRestrictedNavigation,
    
    // User context
    userPermissions
  }
}

/**
 * Hook to check navigation permissions for specific routes
 */
export function useNavigationPermissions(routes: string[]) {
  const { canAccessNavigation } = usePermissions()

  const routePermissions = useMemo(() => {
    return routes.reduce((acc, route) => {
      const navigationId = route.replace('/', '').replace(/\//g, '-')
      acc[route] = canAccessNavigation(navigationId)
      return acc
    }, {} as Record<string, boolean>)
  }, [routes, canAccessNavigation])

  const accessibleRoutes = routes.filter(route => routePermissions[route])
  const restrictedRoutes = routes.filter(route => !routePermissions[route])

  return {
    routePermissions,
    accessibleRoutes,
    restrictedRoutes,
    hasAccessToAny: accessibleRoutes.length > 0,
    hasAccessToAll: restrictedRoutes.length === 0
  }
}

/**
 * Hook for dynamic navigation based on user role and permissions
 */
export function useDynamicNavigation() {
  const { userPermissions } = usePermissions()
  const { navigationItems, setNavigationItems } = useSidebarStore()

  // Generate role-specific navigation
  const generateRoleNavigation = useMemo(() => {
    const baseNavigation: NavigationItem[] = []

    // Add common dashboard
    baseNavigation.push({
      id: 'dashboard',
      label: 'Dashboard',
      icon: 'LayoutDashboard',
      href: `/${userPermissions.role.replace('_', '-')}`,
      description: 'Overview and analytics'
    })

    // Add role-specific navigation
    switch (userPermissions.role) {
      case 'super_admin':
        baseNavigation.push(
          {
            id: 'institutes',
            label: 'Institute Management',
            icon: 'Building2',
            href: '/super-admin/institutes',
            description: 'Manage institutes and verification'
          },
          {
            id: 'users',
            label: 'User Management',
            icon: 'Users',
            href: '/super-admin/users',
            description: 'Manage all platform users'
          },
          // {
          //   id: 'staff',
          //   label: 'Staff Management',
          //   icon: 'UserCheck',
          //   href: '/super-admin/staff',
          //   description: 'Manage platform staff and roles'
          // }
        )
        break

      case 'institute_admin':
        baseNavigation.push(
          {
            id: 'courses',
            label: 'Course Management',
            icon: 'BookOpen',
            href: '/institute-admin/courses',
            description: 'Manage courses and curriculum'
          },
          {
            id: 'students',
            label: 'Student Management',
            icon: 'GraduationCap',
            href: '/institute-admin/students',
            description: 'Manage student enrollments'
          }
        )
        break

      case 'student':
        baseNavigation.push(
          {
            id: 'my-courses',
            label: 'My Courses',
            icon: 'BookOpen',
            href: '/student/courses',
            description: 'Your enrolled courses'
          },
          {
            id: 'marketplace',
            label: 'Course Marketplace',
            icon: 'ShoppingCart',
            href: '/student/marketplace',
            description: 'Browse and purchase courses'
          }
        )
        break
    }

    return baseNavigation
  }, [userPermissions.role])

  // Update navigation when role changes
  const updateNavigationForRole = () => {
    setNavigationItems(generateRoleNavigation)
  }

  return {
    generateRoleNavigation,
    updateNavigationForRole,
    currentRole: userPermissions.role,
    currentLevel: userPermissions.level
  }
}

/**
 * Hook for permission-based feature flags
 */
export function useFeaturePermissions() {
  const { hasPermission, canPerformAction, userPermissions } = usePermissions()

  const features = useMemo(() => {
    return {
      // Staff Management Features - REMOVED
      // canCreateStaff: canPerformAction('create', 'staff'),
      // canEditStaff: canPerformAction('update', 'staff'),
      // canDeleteStaff: canPerformAction('delete', 'staff'),
      // canViewStaff: canPerformAction('read', 'staff'),
      
      // Institute Management Features
      canManageInstitutes: hasPermission('manage_institutes'),
      canApproveInstitutes: hasPermission('approve_institutes'),
      
      // Billing Features
      canViewBilling: hasPermission('view_billing'),
      canManageBilling: hasPermission('manage_billing'),
      
      // Analytics Features
      canViewAnalytics: hasPermission('view_analytics'),
      canExportReports: hasPermission('export_reports'),
      
      // System Features
      canManageSettings: hasPermission('manage_system_settings'),
      canViewLogs: hasPermission('view_system_logs'),
      
      // Theme Features
      canManageThemes: hasPermission('manage_themes'),
      canCustomizeThemes: hasPermission('customize_themes'),
      
      // Course Features
      canCreateCourses: canPerformAction('create', 'courses'),
      canEditCourses: canPerformAction('update', 'courses'),
      canDeleteCourses: canPerformAction('delete', 'courses'),
      
      // Student Features
      canEnrollStudents: hasPermission('enroll_students'),
      canViewStudentProgress: hasPermission('view_student_progress'),
      
      // Advanced Features
      canAccessAdvancedFeatures: userPermissions.level <= 2,
      canManagePermissions: userPermissions.level <= 1,
      canViewSystemHealth: userPermissions.level <= 1
    }
  }, [hasPermission, canPerformAction, userPermissions])

  return features
}

export default usePermissionAwareNavigation
