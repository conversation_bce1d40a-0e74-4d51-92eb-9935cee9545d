const CHUNK_PUBLIC_PATH = "server/app/super-admin/role-permissions/page.js";
const runtime = require("../../../chunks/ssr/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/ssr/71fdb_next_dist_a48ce0fc._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__4c58c055._.js");
runtime.loadChunk("server/chunks/ssr/apps_frontend_src_app_a0a627ec._.js");
runtime.loadChunk("server/chunks/ssr/[root-of-the-server]__36f325d5._.js");
runtime.loadChunk("server/chunks/ssr/node_modules__pnpm_d3705665._.js");
runtime.loadChunk("server/chunks/ssr/71fdb_next_dist_client_components_forbidden-error_0e48df03.js");
runtime.loadChunk("server/chunks/ssr/71fdb_next_dist_client_components_unauthorized-error_4e92b3c0.js");
runtime.loadChunk("server/chunks/ssr/apps_frontend_src_d8f9cd2a._.js");
runtime.loadChunk("server/chunks/ssr/71fdb_next_dist_1c21a31f._.js");
runtime.loadChunk("server/chunks/ssr/apps_frontend_2b3b4841._.js");
runtime.getOrInstantiateRuntimeModule("[project]/apps/frontend/.next-internal/server/app/super-admin/role-permissions/page/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/super-admin/role-permissions/page { METADATA_0 => \"[project]/apps/frontend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/apps/frontend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/frontend/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/apps/frontend/src/app/super-admin/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/apps/frontend/src/app/super-admin/role-permissions/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/esm/build/templates/app-page.js?page=/super-admin/role-permissions/page { METADATA_0 => \"[project]/apps/frontend/src/app/favicon.ico.mjs { IMAGE => \\\"[project]/apps/frontend/src/app/favicon.ico (static in ecmascript)\\\" } [app-rsc] (structured image object, ecmascript, Next.js server component)\", MODULE_1 => \"[project]/apps/frontend/src/app/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_2 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/not-found-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_3 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/forbidden-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_4 => \"[project]/node_modules/.pnpm/next@15.3.4_react-dom@19.1.0_react@19.1.0/node_modules/next/dist/client/components/unauthorized-error.js [app-rsc] (ecmascript, Next.js server component)\", MODULE_5 => \"[project]/apps/frontend/src/app/super-admin/layout.tsx [app-rsc] (ecmascript, Next.js server component)\", MODULE_6 => \"[project]/apps/frontend/src/app/super-admin/role-permissions/page.tsx [app-rsc] (ecmascript, Next.js server component)\" } [app-rsc] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
