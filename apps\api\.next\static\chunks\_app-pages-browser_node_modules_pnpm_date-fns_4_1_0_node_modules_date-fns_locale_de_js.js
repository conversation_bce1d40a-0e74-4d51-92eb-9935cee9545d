"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_app-pages-browser_node_modules_pnpm_date-fns_4_1_0_node_modules_date-fns_locale_de_js"],{

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de.js":
/*!**********************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de.js ***!
  \**********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   de: () => (/* binding */ de),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _de_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./de/_lib/formatDistance.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatDistance.js\");\n/* harmony import */ var _de_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./de/_lib/formatLong.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatLong.js\");\n/* harmony import */ var _de_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./de/_lib/formatRelative.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatRelative.js\");\n/* harmony import */ var _de_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./de/_lib/localize.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/localize.js\");\n/* harmony import */ var _de_lib_match_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./de/_lib/match.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/match.js\");\n\n\n\n\n\n/**\n * @category Locales\n * @summary German locale.\n * @language German\n * @iso-639-2 deu\n * <AUTHOR> Eilmsteiner [@DeMuu](https://github.com/DeMuu)\n * <AUTHOR> [@asia-t](https://github.com/asia-t)\n * <AUTHOR> Vuong Ngo [@vanvuongngo](https://github.com/vanvuongngo)\n * <AUTHOR> [@pex](https://github.com/pex)\n * <AUTHOR> Keck [@Philipp91](https://github.com/Philipp91)\n */ const de = {\n    code: \"de\",\n    formatDistance: _de_lib_formatDistance_js__WEBPACK_IMPORTED_MODULE_0__.formatDistance,\n    formatLong: _de_lib_formatLong_js__WEBPACK_IMPORTED_MODULE_1__.formatLong,\n    formatRelative: _de_lib_formatRelative_js__WEBPACK_IMPORTED_MODULE_2__.formatRelative,\n    localize: _de_lib_localize_js__WEBPACK_IMPORTED_MODULE_3__.localize,\n    match: _de_lib_match_js__WEBPACK_IMPORTED_MODULE_4__.match,\n    options: {\n        weekStartsOn: 1 /* Monday */ ,\n        firstWeekContainsDate: 4\n    }\n};\n// Fallback for modularized imports:\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (de);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatDistance.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatDistance.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDistance: () => (/* binding */ formatDistance)\n/* harmony export */ });\nconst formatDistanceLocale = {\n    lessThanXSeconds: {\n        standalone: {\n            one: \"weniger als 1 Sekunde\",\n            other: \"weniger als {{count}} Sekunden\"\n        },\n        withPreposition: {\n            one: \"weniger als 1 Sekunde\",\n            other: \"weniger als {{count}} Sekunden\"\n        }\n    },\n    xSeconds: {\n        standalone: {\n            one: \"1 Sekunde\",\n            other: \"{{count}} Sekunden\"\n        },\n        withPreposition: {\n            one: \"1 Sekunde\",\n            other: \"{{count}} Sekunden\"\n        }\n    },\n    halfAMinute: {\n        standalone: \"eine halbe Minute\",\n        withPreposition: \"einer halben Minute\"\n    },\n    lessThanXMinutes: {\n        standalone: {\n            one: \"weniger als 1 Minute\",\n            other: \"weniger als {{count}} Minuten\"\n        },\n        withPreposition: {\n            one: \"weniger als 1 Minute\",\n            other: \"weniger als {{count}} Minuten\"\n        }\n    },\n    xMinutes: {\n        standalone: {\n            one: \"1 Minute\",\n            other: \"{{count}} Minuten\"\n        },\n        withPreposition: {\n            one: \"1 Minute\",\n            other: \"{{count}} Minuten\"\n        }\n    },\n    aboutXHours: {\n        standalone: {\n            one: \"etwa 1 Stunde\",\n            other: \"etwa {{count}} Stunden\"\n        },\n        withPreposition: {\n            one: \"etwa 1 Stunde\",\n            other: \"etwa {{count}} Stunden\"\n        }\n    },\n    xHours: {\n        standalone: {\n            one: \"1 Stunde\",\n            other: \"{{count}} Stunden\"\n        },\n        withPreposition: {\n            one: \"1 Stunde\",\n            other: \"{{count}} Stunden\"\n        }\n    },\n    xDays: {\n        standalone: {\n            one: \"1 Tag\",\n            other: \"{{count}} Tage\"\n        },\n        withPreposition: {\n            one: \"1 Tag\",\n            other: \"{{count}} Tagen\"\n        }\n    },\n    aboutXWeeks: {\n        standalone: {\n            one: \"etwa 1 Woche\",\n            other: \"etwa {{count}} Wochen\"\n        },\n        withPreposition: {\n            one: \"etwa 1 Woche\",\n            other: \"etwa {{count}} Wochen\"\n        }\n    },\n    xWeeks: {\n        standalone: {\n            one: \"1 Woche\",\n            other: \"{{count}} Wochen\"\n        },\n        withPreposition: {\n            one: \"1 Woche\",\n            other: \"{{count}} Wochen\"\n        }\n    },\n    aboutXMonths: {\n        standalone: {\n            one: \"etwa 1 Monat\",\n            other: \"etwa {{count}} Monate\"\n        },\n        withPreposition: {\n            one: \"etwa 1 Monat\",\n            other: \"etwa {{count}} Monaten\"\n        }\n    },\n    xMonths: {\n        standalone: {\n            one: \"1 Monat\",\n            other: \"{{count}} Monate\"\n        },\n        withPreposition: {\n            one: \"1 Monat\",\n            other: \"{{count}} Monaten\"\n        }\n    },\n    aboutXYears: {\n        standalone: {\n            one: \"etwa 1 Jahr\",\n            other: \"etwa {{count}} Jahre\"\n        },\n        withPreposition: {\n            one: \"etwa 1 Jahr\",\n            other: \"etwa {{count}} Jahren\"\n        }\n    },\n    xYears: {\n        standalone: {\n            one: \"1 Jahr\",\n            other: \"{{count}} Jahre\"\n        },\n        withPreposition: {\n            one: \"1 Jahr\",\n            other: \"{{count}} Jahren\"\n        }\n    },\n    overXYears: {\n        standalone: {\n            one: \"mehr als 1 Jahr\",\n            other: \"mehr als {{count}} Jahre\"\n        },\n        withPreposition: {\n            one: \"mehr als 1 Jahr\",\n            other: \"mehr als {{count}} Jahren\"\n        }\n    },\n    almostXYears: {\n        standalone: {\n            one: \"fast 1 Jahr\",\n            other: \"fast {{count}} Jahre\"\n        },\n        withPreposition: {\n            one: \"fast 1 Jahr\",\n            other: \"fast {{count}} Jahren\"\n        }\n    }\n};\nconst formatDistance = (token, count, options)=>{\n    let result;\n    const tokenValue = (options === null || options === void 0 ? void 0 : options.addSuffix) ? formatDistanceLocale[token].withPreposition : formatDistanceLocale[token].standalone;\n    if (typeof tokenValue === \"string\") {\n        result = tokenValue;\n    } else if (count === 1) {\n        result = tokenValue.one;\n    } else {\n        result = tokenValue.other.replace(\"{{count}}\", String(count));\n    }\n    if (options === null || options === void 0 ? void 0 : options.addSuffix) {\n        if (options.comparison && options.comparison > 0) {\n            return \"in \" + result;\n        } else {\n            return \"vor \" + result;\n        }\n    }\n    return result;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatDistance.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatLong.js":
/*!**************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatLong.js ***!
  \**************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatLong: () => (/* binding */ formatLong)\n/* harmony export */ });\n/* harmony import */ var _lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildFormatLongFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildFormatLongFn.js\");\n\n// DIN 5008: https://de.wikipedia.org/wiki/Datumsformat#DIN_5008\nconst dateFormats = {\n    full: \"EEEE, do MMMM y\",\n    long: \"do MMMM y\",\n    medium: \"do MMM y\",\n    short: \"dd.MM.y\"\n};\nconst timeFormats = {\n    full: \"HH:mm:ss zzzz\",\n    long: \"HH:mm:ss z\",\n    medium: \"HH:mm:ss\",\n    short: \"HH:mm\"\n};\nconst dateTimeFormats = {\n    full: \"{{date}} 'um' {{time}}\",\n    long: \"{{date}} 'um' {{time}}\",\n    medium: \"{{date}} {{time}}\",\n    short: \"{{date}} {{time}}\"\n};\nconst formatLong = {\n    date: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateFormats,\n        defaultWidth: \"full\"\n    }),\n    time: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: timeFormats,\n        defaultWidth: \"full\"\n    }),\n    dateTime: (0,_lib_buildFormatLongFn_js__WEBPACK_IMPORTED_MODULE_0__.buildFormatLongFn)({\n        formats: dateTimeFormats,\n        defaultWidth: \"full\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatLong.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatRelative.js":
/*!******************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatRelative.js ***!
  \******************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatRelative: () => (/* binding */ formatRelative)\n/* harmony export */ });\nconst formatRelativeLocale = {\n    lastWeek: \"'letzten' eeee 'um' p\",\n    yesterday: \"'gestern um' p\",\n    today: \"'heute um' p\",\n    tomorrow: \"'morgen um' p\",\n    nextWeek: \"eeee 'um' p\",\n    other: \"P\"\n};\nconst formatRelative = (token, _date, _baseDate, _options)=>formatRelativeLocale[token];\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uLi8uLi9ub2RlX21vZHVsZXMvLnBucG0vZGF0ZS1mbnNANC4xLjAvbm9kZV9tb2R1bGVzL2RhdGUtZm5zL2xvY2FsZS9kZS9fbGliL2Zvcm1hdFJlbGF0aXZlLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxNQUFNQSx1QkFBdUI7SUFDM0JDLFVBQVU7SUFDVkMsV0FBVztJQUNYQyxPQUFPO0lBQ1BDLFVBQVU7SUFDVkMsVUFBVTtJQUNWQyxPQUFPO0FBQ1Q7QUFFTyxNQUFNQyxpQkFBaUIsQ0FBQ0MsT0FBT0MsT0FBT0MsV0FBV0MsV0FDdERYLG9CQUFvQixDQUFDUSxNQUFNLENBQUMiLCJzb3VyY2VzIjpbIkM6XFxwcm9qZWN0c1xcbG1zXFxub2RlX21vZHVsZXNcXC5wbnBtXFxkYXRlLWZuc0A0LjEuMFxcbm9kZV9tb2R1bGVzXFxkYXRlLWZuc1xcbG9jYWxlXFxkZVxcX2xpYlxcZm9ybWF0UmVsYXRpdmUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiY29uc3QgZm9ybWF0UmVsYXRpdmVMb2NhbGUgPSB7XG4gIGxhc3RXZWVrOiBcIidsZXR6dGVuJyBlZWVlICd1bScgcFwiLFxuICB5ZXN0ZXJkYXk6IFwiJ2dlc3Rlcm4gdW0nIHBcIixcbiAgdG9kYXk6IFwiJ2hldXRlIHVtJyBwXCIsXG4gIHRvbW9ycm93OiBcIidtb3JnZW4gdW0nIHBcIixcbiAgbmV4dFdlZWs6IFwiZWVlZSAndW0nIHBcIixcbiAgb3RoZXI6IFwiUFwiLFxufTtcblxuZXhwb3J0IGNvbnN0IGZvcm1hdFJlbGF0aXZlID0gKHRva2VuLCBfZGF0ZSwgX2Jhc2VEYXRlLCBfb3B0aW9ucykgPT5cbiAgZm9ybWF0UmVsYXRpdmVMb2NhbGVbdG9rZW5dO1xuIl0sIm5hbWVzIjpbImZvcm1hdFJlbGF0aXZlTG9jYWxlIiwibGFzdFdlZWsiLCJ5ZXN0ZXJkYXkiLCJ0b2RheSIsInRvbW9ycm93IiwibmV4dFdlZWsiLCJvdGhlciIsImZvcm1hdFJlbGF0aXZlIiwidG9rZW4iLCJfZGF0ZSIsIl9iYXNlRGF0ZSIsIl9vcHRpb25zIl0sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/formatRelative.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/localize.js":
/*!************************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/localize.js ***!
  \************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   localize: () => (/* binding */ localize)\n/* harmony export */ });\n/* harmony import */ var _lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildLocalizeFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildLocalizeFn.js\");\n\nconst eraValues = {\n    narrow: [\n        \"v.Chr.\",\n        \"n.Chr.\"\n    ],\n    abbreviated: [\n        \"v.Chr.\",\n        \"n.Chr.\"\n    ],\n    wide: [\n        \"vor Christus\",\n        \"nach Christus\"\n    ]\n};\nconst quarterValues = {\n    narrow: [\n        \"1\",\n        \"2\",\n        \"3\",\n        \"4\"\n    ],\n    abbreviated: [\n        \"Q1\",\n        \"Q2\",\n        \"Q3\",\n        \"Q4\"\n    ],\n    wide: [\n        \"1. Quartal\",\n        \"2. Quartal\",\n        \"3. Quartal\",\n        \"4. Quartal\"\n    ]\n};\n// Note: in German, the names of days of the week and months are capitalized.\n// If you are making a new locale based on this one, check if the same is true for the language you're working on.\n// Generally, formatted dates should look like they are in the middle of a sentence,\n// e.g. in Spanish language the weekdays and months should be in the lowercase.\nconst monthValues = {\n    narrow: [\n        \"J\",\n        \"F\",\n        \"M\",\n        \"A\",\n        \"M\",\n        \"J\",\n        \"J\",\n        \"A\",\n        \"S\",\n        \"O\",\n        \"N\",\n        \"D\"\n    ],\n    abbreviated: [\n        \"Jan\",\n        \"Feb\",\n        \"Mär\",\n        \"Apr\",\n        \"Mai\",\n        \"Jun\",\n        \"Jul\",\n        \"Aug\",\n        \"Sep\",\n        \"Okt\",\n        \"Nov\",\n        \"Dez\"\n    ],\n    wide: [\n        \"Januar\",\n        \"Februar\",\n        \"März\",\n        \"April\",\n        \"Mai\",\n        \"Juni\",\n        \"Juli\",\n        \"August\",\n        \"September\",\n        \"Oktober\",\n        \"November\",\n        \"Dezember\"\n    ]\n};\n// https://st.unicode.org/cldr-apps/v#/de/Gregorian/\nconst formattingMonthValues = {\n    narrow: monthValues.narrow,\n    abbreviated: [\n        \"Jan.\",\n        \"Feb.\",\n        \"März\",\n        \"Apr.\",\n        \"Mai\",\n        \"Juni\",\n        \"Juli\",\n        \"Aug.\",\n        \"Sep.\",\n        \"Okt.\",\n        \"Nov.\",\n        \"Dez.\"\n    ],\n    wide: monthValues.wide\n};\nconst dayValues = {\n    narrow: [\n        \"S\",\n        \"M\",\n        \"D\",\n        \"M\",\n        \"D\",\n        \"F\",\n        \"S\"\n    ],\n    short: [\n        \"So\",\n        \"Mo\",\n        \"Di\",\n        \"Mi\",\n        \"Do\",\n        \"Fr\",\n        \"Sa\"\n    ],\n    abbreviated: [\n        \"So.\",\n        \"Mo.\",\n        \"Di.\",\n        \"Mi.\",\n        \"Do.\",\n        \"Fr.\",\n        \"Sa.\"\n    ],\n    wide: [\n        \"Sonntag\",\n        \"Montag\",\n        \"Dienstag\",\n        \"Mittwoch\",\n        \"Donnerstag\",\n        \"Freitag\",\n        \"Samstag\"\n    ]\n};\n// https://www.unicode.org/cldr/charts/32/summary/de.html#1881\nconst dayPeriodValues = {\n    narrow: {\n        am: \"vm.\",\n        pm: \"nm.\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"Morgen\",\n        afternoon: \"Nachm.\",\n        evening: \"Abend\",\n        night: \"Nacht\"\n    },\n    abbreviated: {\n        am: \"vorm.\",\n        pm: \"nachm.\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"Morgen\",\n        afternoon: \"Nachmittag\",\n        evening: \"Abend\",\n        night: \"Nacht\"\n    },\n    wide: {\n        am: \"vormittags\",\n        pm: \"nachmittags\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"Morgen\",\n        afternoon: \"Nachmittag\",\n        evening: \"Abend\",\n        night: \"Nacht\"\n    }\n};\nconst formattingDayPeriodValues = {\n    narrow: {\n        am: \"vm.\",\n        pm: \"nm.\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"morgens\",\n        afternoon: \"nachm.\",\n        evening: \"abends\",\n        night: \"nachts\"\n    },\n    abbreviated: {\n        am: \"vorm.\",\n        pm: \"nachm.\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"morgens\",\n        afternoon: \"nachmittags\",\n        evening: \"abends\",\n        night: \"nachts\"\n    },\n    wide: {\n        am: \"vormittags\",\n        pm: \"nachmittags\",\n        midnight: \"Mitternacht\",\n        noon: \"Mittag\",\n        morning: \"morgens\",\n        afternoon: \"nachmittags\",\n        evening: \"abends\",\n        night: \"nachts\"\n    }\n};\nconst ordinalNumber = (dirtyNumber)=>{\n    const number = Number(dirtyNumber);\n    return number + \".\";\n};\nconst localize = {\n    ordinalNumber,\n    era: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: eraValues,\n        defaultWidth: \"wide\"\n    }),\n    quarter: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: quarterValues,\n        defaultWidth: \"wide\",\n        argumentCallback: (quarter)=>quarter - 1\n    }),\n    month: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: monthValues,\n        formattingValues: formattingMonthValues,\n        defaultWidth: \"wide\"\n    }),\n    day: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayValues,\n        defaultWidth: \"wide\"\n    }),\n    dayPeriod: (0,_lib_buildLocalizeFn_js__WEBPACK_IMPORTED_MODULE_0__.buildLocalizeFn)({\n        values: dayPeriodValues,\n        defaultWidth: \"wide\",\n        formattingValues: formattingDayPeriodValues,\n        defaultFormattingWidth: \"wide\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/localize.js\n"));

/***/ }),

/***/ "(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/match.js":
/*!*********************************************************************************************!*\
  !*** ../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/match.js ***!
  \*********************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   match: () => (/* binding */ match)\n/* harmony export */ });\n/* harmony import */ var _lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ../../_lib/buildMatchFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchFn.js\");\n/* harmony import */ var _lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../../_lib/buildMatchPatternFn.js */ \"(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/_lib/buildMatchPatternFn.js\");\n\n\nconst matchOrdinalNumberPattern = /^(\\d+)(\\.)?/i;\nconst parseOrdinalNumberPattern = /\\d+/i;\nconst matchEraPatterns = {\n    narrow: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n    abbreviated: /^(v\\.? ?Chr\\.?|n\\.? ?Chr\\.?)/i,\n    wide: /^(vor Christus|vor unserer Zeitrechnung|nach Christus|unserer Zeitrechnung)/i\n};\nconst parseEraPatterns = {\n    any: [\n        /^v/i,\n        /^n/i\n    ]\n};\nconst matchQuarterPatterns = {\n    narrow: /^[1234]/i,\n    abbreviated: /^q[1234]/i,\n    wide: /^[1234](\\.)? Quartal/i\n};\nconst parseQuarterPatterns = {\n    any: [\n        /1/i,\n        /2/i,\n        /3/i,\n        /4/i\n    ]\n};\nconst matchMonthPatterns = {\n    narrow: /^[jfmasond]/i,\n    abbreviated: /^(j[aä]n|feb|mär[z]?|apr|mai|jun[i]?|jul[i]?|aug|sep|okt|nov|dez)\\.?/i,\n    wide: /^(januar|februar|märz|april|mai|juni|juli|august|september|oktober|november|dezember)/i\n};\nconst parseMonthPatterns = {\n    narrow: [\n        /^j/i,\n        /^f/i,\n        /^m/i,\n        /^a/i,\n        /^m/i,\n        /^j/i,\n        /^j/i,\n        /^a/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ],\n    any: [\n        /^j[aä]/i,\n        /^f/i,\n        /^mär/i,\n        /^ap/i,\n        /^mai/i,\n        /^jun/i,\n        /^jul/i,\n        /^au/i,\n        /^s/i,\n        /^o/i,\n        /^n/i,\n        /^d/i\n    ]\n};\nconst matchDayPatterns = {\n    narrow: /^[smdmf]/i,\n    short: /^(so|mo|di|mi|do|fr|sa)/i,\n    abbreviated: /^(son?|mon?|die?|mit?|don?|fre?|sam?)\\.?/i,\n    wide: /^(sonntag|montag|dienstag|mittwoch|donnerstag|freitag|samstag)/i\n};\nconst parseDayPatterns = {\n    any: [\n        /^so/i,\n        /^mo/i,\n        /^di/i,\n        /^mi/i,\n        /^do/i,\n        /^f/i,\n        /^sa/i\n    ]\n};\nconst matchDayPeriodPatterns = {\n    narrow: /^(vm\\.?|nm\\.?|Mitternacht|Mittag|morgens|nachm\\.?|abends|nachts)/i,\n    abbreviated: /^(vorm\\.?|nachm\\.?|Mitternacht|Mittag|morgens|nachm\\.?|abends|nachts)/i,\n    wide: /^(vormittags|nachmittags|Mitternacht|Mittag|morgens|nachmittags|abends|nachts)/i\n};\nconst parseDayPeriodPatterns = {\n    any: {\n        am: /^v/i,\n        pm: /^n/i,\n        midnight: /^Mitte/i,\n        noon: /^Mitta/i,\n        morning: /morgens/i,\n        afternoon: /nachmittags/i,\n        evening: /abends/i,\n        night: /nachts/i\n    }\n};\nconst match = {\n    ordinalNumber: (0,_lib_buildMatchPatternFn_js__WEBPACK_IMPORTED_MODULE_0__.buildMatchPatternFn)({\n        matchPattern: matchOrdinalNumberPattern,\n        parsePattern: parseOrdinalNumberPattern,\n        valueCallback: (value)=>parseInt(value)\n    }),\n    era: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchEraPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseEraPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    quarter: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchQuarterPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseQuarterPatterns,\n        defaultParseWidth: \"any\",\n        valueCallback: (index)=>index + 1\n    }),\n    month: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchMonthPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseMonthPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    day: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPatterns,\n        defaultParseWidth: \"any\"\n    }),\n    dayPeriod: (0,_lib_buildMatchFn_js__WEBPACK_IMPORTED_MODULE_1__.buildMatchFn)({\n        matchPatterns: matchDayPeriodPatterns,\n        defaultMatchWidth: \"wide\",\n        parsePatterns: parseDayPeriodPatterns,\n        defaultParseWidth: \"any\"\n    })\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/../../node_modules/.pnpm/date-fns@4.1.0/node_modules/date-fns/locale/de/_lib/match.js\n"));

/***/ })

}]);